@file:Suppress("TooGenericExceptionThrown")

package ru.dobro.api.overview

import arrow.core.toT
import common.library.android.debug.DebugApi
import common.library.android.debug.DebugApiConfig
import common.library.core.email.Email
import common.library.core.location.GeoPoint
import common.library.core.logging.Logger
import io.reactivex.Completable
import io.reactivex.Single
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.PersistentSet
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.collections.immutable.toPersistentSet
import kotlinx.coroutines.rx2.await
import okhttp3.MultipartBody
import retrofit2.Response
import ru.dobro.api.Country
import ru.dobro.api.GrantType
import ru.dobro.api.global.AddressResponse
import ru.dobro.api.global.GlobalMockData
import ru.dobro.api.global.ResponseEvents
import ru.dobro.api.global.ResponseItems
import ru.dobro.api.global.ResponseItemsFeatures
import ru.dobro.api.global.ResponseMetaData
import ru.dobro.api.global.SuggestionsResponse
import ru.dobro.api.organization.OrganizationMockData
import ru.dobro.domain.*
import ru.dobro.domain.courses.Course
import ru.dobro.domain.event.MapEvent
import ru.dobro.domain.knowledge_base.KnowledgeBaseSection
import ru.dobro.domain.overview.VacancyRequest
import ru.dobro.domain.profile.ImageData
import ru.dobro.domain.vacancy.VacancyRequirements
import java.time.LocalDate
import ru.dobro.domain.event.Event as FullEvent

class OverviewMockApi(
    logger: Logger
) : DebugApi(DebugApiConfig(), logger), OverviewApi {
    override fun getCategoriesAsync(): Single<PersistentSet<Category>> =
        debuggableMethod(
            ::getCategoriesAsync.name,
            config.errorMode
        ) {
            (1..20).map { OverviewMockData.getCategory(it) }.toPersistentSet()
        }

    override suspend fun getCategories(): PersistentSet<Category> = (1..20)
        .map { OverviewMockData.getCategory(it) }.toPersistentSet()

    override fun getHelpTypesAsync(): Single<PersistentSet<HelpType>> =
        debuggableMethod(
            ::getHelpTypesAsync.name,
            config.errorMode
        ) {
            (1..3).map { OverviewMockData.getHelpType(it) }.toPersistentSet()
        }

    override suspend fun searchEvents(
        page: Int?,
        search: String?,
        categories: PersistentSet<CategoryId>?,
        requirementIds: PersistentSet<Long>?,
        tags: List<Long>,
        map: Map<String, Int>,
        fromDate: LocalDate?,
        toDate: LocalDate?,
        fiasId: FiasId?,
        location: String?,
        onlineStatus: OnlineStatus?,
        radius: Int?,
        ageRestriction: Int?
    ): ResponseMetaData<PersistentList<Event>> {
        throw Throwable()
    }

    override suspend fun searchMapEvents(
        fromDate: LocalDate,
        toDate: LocalDate?,
        location: String,
        query: String?,
        radius: Int?,
        ageRestriction: Int?,
        tags: List<Long>,
        categories: PersistentSet<CategoryId>?
    ): ResponseMetaData<PersistentList<EventMapLocation>> {
        throw Throwable()
    }

    override suspend fun getMultipleMapEvents(
        page: Int,
        limit: Int,
        pagination: Int,
        ids: PersistentSet<EventId>
    ): ResponseMetaData<PersistentList<MapEvent>> {
        throw Throwable()
    }

    override suspend fun searchOrganizers(
        page: Int,
        query: String?,
        fiasId: FiasId?,
        location: String?,
        categories: PersistentSet<CategoryId>?,
        type: OrganizerTypeId?,
        map: Map<String, Int>?
    ): ResponseMetaData<PersistentList<Organizer>> {
        throw Throwable()
    }

    override suspend fun getMyOrganizations(
        page: Int,
        rand: Int
    ): ResponseMetaData<PersistentList<Organizer>> {
        throw Exception("")
    }

    override fun searchVacanciesAsync(
        page: Int,
        query: String?,
        categories: PersistentSet<CategoryId>?,
        requirementIds: PersistentSet<Long>?,
        limit: Int?,
        fromDate: LocalDate?,
        toDate: LocalDate?,
    ): Single<ResponseMetaData<PersistentList<Vacancy>>> {
        return Single.error(Throwable())
    }

    override fun saveCategoriesAsync(
        userId: UserId,
        request: OverviewApi.SaveCategoriesRequest
    ): Completable = Completable.complete()

    override fun saveCategoriesAsync(
        organizationId: OrganizationId,
        request: OverviewApi.SaveCategoriesRequest
    ): Completable = Completable.complete()

    override fun deleteVolunteer(userId: UserId, reason: String): Completable {
        return Completable.complete()
    }

    override fun getAllCountries(pagination: Boolean): Single<ResponseMetaData<PersistentList<Country>>> {
        return Single.error(Throwable())
    }

    override fun getAddressSuggestionQueryAsync(
        addressSuggestionBody: OverviewApi.AddressSuggestionBody
    ): Single<SuggestionsResponse<AddressResponse>> = debuggableMethod(
        ::getAddressSuggestionQueryAsync.name,
        config.errorMode
    ) {
        SuggestionsResponse(
            persistentListOf(
                OverviewMockData.getAddressResponseCity(),
                OverviewMockData.getAddressResponseSettlement()
            )
        )
    }

    override fun getAddressSuggestionLocationAsync(
        latitude: Double,
        longitude: Double
    ): Single<SuggestionsResponse<AddressResponse>> =
        debuggableMethod(
            ::getAddressSuggestionLocationAsync.name,
            config.errorMode
        ) {
            SuggestionsResponse(
                persistentListOf(
                    OverviewMockData.getAddressResponseCity(),
                    OverviewMockData.getAddressResponseSettlement()
                )
            )
        }

    override suspend fun getAddressSuggestionLocation(
        latitude: Double,
        longitude: Double
    ) = SuggestionsResponse(
        persistentListOf(
            OverviewMockData.getAddressResponseCity(),
            OverviewMockData.getAddressResponseSettlement()
        )
    )

    override fun getHomeEventsAsync(): Single<ResponseMetaData<PersistentList<Event>>> =
        debuggableMethod(
            ::getHomeEventsAsync.name,
            config.errorMode
        ) {
            ResponseMetaData(data = (1..20).map { OverviewMockData.getEvent(it) }
                .toPersistentList(), ResponseMetaData.Meta(totalItems = 100, lastPage = 5))
        }

    override suspend fun getEvents(
        page: Int,
        categories: PersistentSet<CategoryId>?,
        tags: List<Long>,
        map: Map<String, Int>,
        coordinates: String?,
        onlineStatus: OnlineStatus?,
        limit: Int?
    ): ResponseMetaData<PersistentList<Event>> {
        throw Throwable()
    }

    override suspend fun getActualEvents(
        page: Int,
        perPage: Int,
        categories: PersistentSet<CategoryId>?,
        coordinates: String?
    ) = ResponseMetaData(data = (1..30).map { OverviewMockData.getEvent(it) }
        .toPersistentList(), ResponseMetaData.Meta(totalItems = 100, lastPage = 5))

    override suspend fun getNearestEvents(
        page: Int,
        categories: PersistentSet<CategoryId>?,
        tags: List<Long>,
        requirementIds: PersistentSet<Long>?,
        map: Map<String, Int>,
        coordinates: String?,
        limit: Int?,
        onlineStatus: OnlineStatus?
    ) = ResponseItems(persistentListOf<Event>(), 0, 0)

    override fun getMapNearestEventsAsync(coordinates: String?): Single<ResponseItemsFeatures<PersistentList<MapLocation>>> {
        return Single.error(Throwable())
    }

    override suspend fun getMapNearestEvents(coordinates: String?): ResponseItemsFeatures<PersistentList<MapLocation>> {
        throw Throwable()
    }

    override suspend fun getHomeRecommendedEvents(
        page: Int,
        limit: Int?
    ) = ResponseMetaData(data = (1..30).map { OverviewMockData.getEvent(it) }
        .toPersistentList(), ResponseMetaData.Meta(totalItems = 100, lastPage = 5))

    override suspend fun getOrganizationEvents(
        organizerId: OrganizerId,
        page: Int,
        categories: PersistentSet<CategoryId>?,
        onlineStatus: OnlineStatus?,
        limit: Int?
    ) = ResponseMetaData(data = (1..30).map { OverviewMockData.getEvent(it) }
        .toPersistentList(), ResponseMetaData.Meta(totalItems = 100, lastPage = 5))

    override fun getEventVacanciesAsync(
        eventId: EventId,
        page: Int,
        status: String?,
        limit: Int?
    ): Single<ResponseMetaData<PersistentList<Vacancy>>> =
        debuggableMethod(
            ::getEventVacanciesAsync.name,
            config.errorMode
        ) {
            ResponseMetaData(data = (1..30).map { OverviewMockData.getVacancy(it) }
                .toPersistentList(), ResponseMetaData.Meta(totalItems = 100, lastPage = 5))
        }

    override suspend fun getEventVacancies(
        eventId: EventId,
        page: Int,
        status: String?,
        limit: Int?
    ): ResponseMetaData<PersistentList<Vacancy>> = ResponseMetaData(
        data = (1..30).map { OverviewMockData.getVacancy(it) }
            .toPersistentList(), ResponseMetaData.Meta(totalItems = 100, lastPage = 5))

    override suspend fun getRecommendedEvents(
        categories: PersistentSet<CategoryId>?,
        coordinates: String?
    ): ResponseEvents = ResponseEvents.empty()

    override fun getHomeVacanciesAsync(): Single<ResponseMetaData<PersistentList<Vacancy>>> =
        debuggableMethod(
            ::getHomeVacanciesAsync.name,
            config.errorMode
        ) {
            ResponseMetaData(data = (1..5).map { OverviewMockData.getVacancy(it) }
                .toPersistentList(), ResponseMetaData.Meta(totalItems = 100, lastPage = 5))
        }

    override fun getHomeActualVacanciesAsync(coordinates: String?): Single<ResponseMetaData<PersistentList<Vacancy>>> =
        debuggableMethod(
            ::getHomeActualVacanciesAsync.name,
            config.errorMode
        ) {
            ResponseMetaData(data = (1..5).map { OverviewMockData.getVacancy(it) }
                .toPersistentList(), ResponseMetaData.Meta(totalItems = 100, lastPage = 5))
        }

    override suspend fun getHomeOrganizers() =
        ResponseMetaData(data = (1..5).map { OverviewMockData.getOrganizer(it) }
            .toPersistentList(), meta = ResponseMetaData.Meta(totalItems = 100, lastPage = 5))

    override fun getHomeCoursesAsync(
        page: Int,
        limit: Int?,
        perPage: Int,
        canBeRecommended: Boolean
    ): Single<ResponseMetaData<PersistentList<Course>>> =
        debuggableMethod(
            ::getHomeCoursesAsync.name,
            config.errorMode
        ) {
            ResponseMetaData(data = (1..5).map { OverviewMockData.getCourse(it) }
                .toPersistentList(), meta = ResponseMetaData.Meta(totalItems = 100, lastPage = 5))
        }

    override suspend fun getHomeCourses(
        page: Int,
        limit: Int?,
        perPage: Int,
        canBeRecommended: Boolean
    ) = ResponseMetaData(data = (1..5).map { OverviewMockData.getCourse(it) }
        .toPersistentList(), meta = ResponseMetaData.Meta(totalItems = 100, lastPage = 5))

    override suspend fun getHomeCoursesIfAuthorized(
        page: Int,
        limit: Int?,
        canBeRecommended: Boolean
    ) = ResponseMetaData(data = (1..2).map { OverviewMockData.getCourse(it) }
        .toPersistentList(), meta = ResponseMetaData.Meta(totalItems = 100, lastPage = 5))

    override suspend fun getOrganizations(
        page: Int,
        categories: PersistentSet<CategoryId>,
        location: Location?,
        verificationStatus: VerificationStatus?
    ) = ResponseMetaData(data = (1..30).map { OverviewMockData.getOrganization(it) }
        .toPersistentList(), meta = ResponseMetaData.Meta(totalItems = 100, lastPage = 5))

    override suspend fun getOrganizationsByFilter(
        categories: PersistentSet<CategoryId>,
        location: Location?,
        verificationStatus: VerificationStatus?
    ) = ResponseMetaData(data = (1..30).map { OverviewMockData.getOrganization(it) }
        .toPersistentList(), meta = ResponseMetaData.Meta(totalItems = 100, lastPage = 5))

    override fun getEventsByFilterAsync(
        categories: PersistentSet<CategoryId>
    ): Single<ResponseMetaData<PersistentList<Event>>> = debuggableMethod(
        ::getEventsByFilterAsync.name,
        config.errorMode
    ) {
        ResponseMetaData(data = (1..30).map { OverviewMockData.getEvent(it) }
            .toPersistentList(), meta = ResponseMetaData.Meta(totalItems = 100, lastPage = 5))
    }

    override fun getVacanciesByFilterAsync(
        categories: PersistentSet<CategoryId>
    ): Single<ResponseMetaData<PersistentList<Vacancy>>> =
        debuggableMethod(
            ::getVacanciesByFilterAsync.name,
            config.errorMode
        ) {
            ResponseMetaData(data = (1..30).map { OverviewMockData.getVacancy(it) }
                .toPersistentList(), meta = ResponseMetaData.Meta(totalItems = 100, lastPage = 5))
        }

    override fun getActualVacanciesByFilterAsync(
        categories: PersistentSet<CategoryId>
    ): Single<ResponseMetaData<PersistentList<Vacancy>>> =
        debuggableMethod(
            ::getActualVacanciesByFilterAsync.name,
            config.errorMode
        ) {
            ResponseMetaData(data = (1..30).map { OverviewMockData.getVacancy(it) }
                .toPersistentList(), meta = ResponseMetaData.Meta(totalItems = 100, lastPage = 5))
        }

    override fun authorizeAsync(
        email: Email,
        password: String,
        grantType: GrantType,
        clientId: String,
        clientSecret: String,
        scope: String
    ): Single<OverviewApi.AuthorizationResponse> =
        debuggableMethod(
            ::authorizeAsync.name,
            config.errorMode
        ) {
            OverviewApi.AuthorizationResponse(
                UserId.restore(1L),
                AccessToken.restore("MockAccessToken"),
                ExpirationTime.restore(86400L),
                RefreshToken.restore("MockRefreshToken")
            )
        }

    override suspend fun authorize(
        email: Email,
        password: String,
        grantType: GrantType,
        clientId: String,
        clientSecret: String,
        scope: String
    ) = OverviewApi.AuthorizationResponse(
        UserId.restore(1L),
        AccessToken.restore("MockAccessToken"),
        ExpirationTime.restore(86400L),
        RefreshToken.restore("MockRefreshToken")
    )

    override fun authorizeBySocialNetworkAsync(
        grantType: GrantType,
        clientId: String,
        clientSecret: String,
        scope: String,
        remoteId: String,
        socialType: String,
        redirect: String,
        remoteToken: String?
    ): Single<OverviewApi.AuthorizationResponse> =
        debuggableMethod(
            ::authorizeAsync.name,
            config.errorMode
        ) {
            OverviewApi.AuthorizationResponse(
                UserId.restore(1L),
                AccessToken.restore("MockAccessToken"),
                ExpirationTime.restore(86400L),
                RefreshToken.restore("MockRefreshToken")
            )
        }

    override suspend fun authorizeBySocialNetwork(
        grantType: GrantType,
        clientId: String,
        clientSecret: String,
        scope: String,
        remoteId: String,
        socialType: String,
        redirect: String,
        remoteToken: String?
    ) = OverviewApi.AuthorizationResponse(
        UserId.restore(1L),
        AccessToken.restore("MockAccessToken"),
        ExpirationTime.restore(86400L),
        RefreshToken.restore("MockRefreshToken")
    )

    override fun refreshTokenAsync(
        grantType: GrantType,
        refreshToken: RefreshToken,
        clientId: String,
        clientSecret: String
    ): Single<OverviewApi.AuthorizationResponse> =
        debuggableMethod(
            ::refreshTokenAsync.name,
            config.errorMode
        ) {
            OverviewApi.AuthorizationResponse(
                UserId.restore(1),
                AccessToken.restore("MockAccessToken"),
                ExpirationTime.restore(86400L),
                RefreshToken.restore("MockRefreshToken")
            )
        }

    override fun createUserAsync(body: OverviewApi.CreateUserRequestBody): Completable =
        debuggableMethod(
            ::createUnderagedUserAsync.name,
            config.errorMode
        )

    override suspend fun createUser(
        body: OverviewApi.CreateUserRequestBody
    ): Response<Any?> = Response.success(null)

    override suspend fun createUnderagedUserAsync(
        body: OverviewApi.CreateUserRequestBody
    ): Response<Any?> = Response.success(null)

    override suspend fun getNotifications(
        userId: UserId,
        page: Int,
        rand: Int
    ) = ResponseMetaData(
        data = (1..30).map { OverviewMockData.getNotification(it) }.toPersistentList(),
        meta = ResponseMetaData.Meta(totalItems = 100, lastPage = 5)
    )

    override suspend fun getNotificationsCount(
        userId: UserId,
        rand: Int
    ): Response<ResponseMetaData<Any>> {
        return Response.success(null)
    }

    override suspend fun getNotificationsSettings(userId: UserId) =
        OverviewApi.NotificationSettingsResponse(
            settings = (1..6).map {
                OverviewMockData.getNotificationSetting(
                    it
                )
            }.toPersistentList(),
            sendAfterTime = null,
            sendBeforeTime = null
        )

    override suspend fun getNotificationsPushSettings(userId: UserId) =
        OverviewApi.NotificationSettingsResponse(
            settings = (1..6).map {
                OverviewMockData.getNotificationSetting(
                    it
                )
            }.toPersistentList(),
            sendAfterTime = null,
            sendBeforeTime = null
        )

    override suspend fun saveNotificationsSettings(
        userId: UserId,
        request: OverviewApi.SaveNotificationsSettingsRequest
    ) {
        // Do nothing
    }

    override suspend fun savePushNotificationsSettingsAsync(
        userId: UserId,
        request: OverviewApi.SaveNotificationsSettingsRequest
    ) {
        // Do nothing
    }

    override suspend fun savePushNotificationsTimeInterval(
        userId: UserId,
        request: OverviewApi.SavePushNotificationsTimeIntervalRequest
    ) {
        // Do nothing
    }

    override suspend fun joinToOrganization(organizationId: OrganizationId) {
        // Do nothing
    }

    override suspend fun leaveOrganization(organizationId: OrganizationId) {
        // Do nothing
    }

    override fun getEventAsync(id: EventId): Single<FullEvent> =
        debuggableMethod(
            "getEventAsyncEventId",
            config.errorMode,
            "request" toT null
        ) { OverviewMockData.getFullEvent(id.unwrap().toInt()) }

    override suspend fun getEvent(id: EventId): ru.dobro.domain.event.Event {
        return OverviewMockData.getFullEvent(id.unwrap().toInt())
    }

    override fun getEventAsync(id: String): Single<ru.dobro.domain.event.Event> {
        return Single.error(Throwable())
    }

    override suspend fun getMapEvent(id: String): MapEvent {
        throw Throwable()
    }

    override fun getVacanciesAsync(
        page: Int,
        coordinates: String?,
        categories: PersistentSet<CategoryId>?,
        status: String?
    ): Single<ResponseMetaData<PersistentList<Vacancy>>> =
        debuggableMethod(
            ::getVacanciesAsync.name,
            config.errorMode
        ) {
            ResponseMetaData(data = (1..30).map { OverviewMockData.getVacancy(it) }
                .toPersistentList(), meta = ResponseMetaData.Meta(totalItems = 100, lastPage = 5))
        }

    override fun getActualVacanciesAsync(
        page: Int,
        coordinates: String?,
        categories: PersistentSet<CategoryId>?
    ): Single<ResponseMetaData<PersistentList<Vacancy>>> =
        debuggableMethod(
            ::getActualVacanciesAsync.name,
            config.errorMode
        ) {
            ResponseMetaData(data = (1..30).map { OverviewMockData.getVacancy(it) }
                .toPersistentList(), meta = ResponseMetaData.Meta(totalItems = 100, lastPage = 5))
        }

    override fun getEventParticipantVacanciesAsync(
        eventId: EventId,
        page: Int,
        status: String?
    ): Single<ResponseMetaData<PersistentList<Vacancy>>> =
        debuggableMethod(
            ::getEventParticipantVacanciesAsync.name,
            config.errorMode
        ) {
            ResponseMetaData(data = (1..30).map { OverviewMockData.getVacancy(it) }
                .toPersistentList(), meta = ResponseMetaData.Meta(totalItems = 100, lastPage = 5))
        }

    override fun getOrganizationVacanciesAsync(
        organizerId: OrganizerId,
        page: Int,
        status: String?
    ): Single<ResponseMetaData<PersistentList<Vacancy>>> =
        debuggableMethod(
            ::getVacanciesAsync.name,
            config.errorMode
        ) {
            ResponseMetaData(data = (1..30).map { OverviewMockData.getVacancy(it) }
                .toPersistentList(), meta = ResponseMetaData.Meta(totalItems = 100, lastPage = 5))
        }

    override fun getMyVacancyRequests(
        id: UserId,
        participant: Boolean,
        fromDate: String?,
        toDate: String?,
        page: Int,
        statuses: Set<Int>?
    ): Single<ResponseMetaData<PersistentList<VacancyRequest>>> {
        return debuggableMethod(
            ::getVacancyOnReviewRequests.name,
            config.errorMode
        ) {
            ResponseMetaData(
                data = (1..5).map { OrganizationMockData.getVacancyRequest(it) }.toPersistentList(),
                meta = ResponseMetaData.Meta(totalItems = 100, lastPage = 5)
            )
        }
    }

    override fun removeMyVacancyRequest(vacancyId: Long): Completable =
        debuggableMethod(
            ::removeMyVacancyRequest.name,
            config.errorMode
        )

    override suspend fun getVacancyOnReviewRequests(id: UserId) =
        (1..5).map { OrganizationMockData.getVacancyRequest(it) }.toPersistentList()

    override suspend fun getVacancyRequests(id: UserId): PersistentList<VacancyRequest> {
        return debuggableMethod(
            ::getVacancyRequests.name,
            config.errorMode
        ) {
            (1..5).map { OrganizationMockData.getVacancyRequest(it) }.toPersistentList()
        }.await()
    }

    override suspend fun getNowVacancyRequests(id: UserId) =
        (1..5).map { OrganizationMockData.getVacancyRequest(it) }.toPersistentList()

    override suspend fun getVacancyToVisitRequests(id: UserId) =
        (1..5).map { OrganizationMockData.getVacancyRequest(it) }.toPersistentList()

    override fun getCrmEvents(
        organizerId: OrganizerId,
        query: String?,
        page: Int,
        limit: Int?
    ): Single<ResponseMetaData<PersistentList<CrmEvent>>> {
        return Single.error(Throwable())
    }

    override fun getCrmVacancyRequests(
        vacancyId: VacancyId,
        query: String?,
        page: Int,
        status: PersistentList<Int>?,
        limit: Int?
    ): Single<ResponseMetaData<PersistentList<CrmVacancyRequest>>> {
        return Single.error(Throwable())
    }

    override fun acceptVacancy(vacancyId: VacancyId): Single<Any> {
        return Single.error(Throwable())
    }

    override fun rejectVacancy(
        vacancyId: VacancyId,
        request: OverviewApi.RejectVacancyRequest
    ): Single<Any> {
        return Single.error(Throwable())
    }

    override fun reserveVacancy(vacancyId: VacancyId): Single<Any> {
        return Single.error(Throwable())
    }

    override fun setVacancyHours(
        vacancyId: VacancyId,
        setVacancyRequest: OverviewApi.SetVacancyRequest
    ): Single<Any> {
        return Single.error(Throwable())
    }

    override fun setVacancyReview(
        vacancyId: VacancyId,
        setVacancyReview: OverviewApi.SetVacancyReview
    ): Single<Any> {
        return Single.error(Throwable())
    }

    override fun setVacancyReviewMass(
        vacancyId: VacancyId,
        setVacancyReview: OverviewApi.SetVacancyReviewMass
    ): Single<PersistentList<CrmVacancyRequest>> {
        return Single.error(Throwable())
    }

    override fun getVacancyRequest(vacancyId: VacancyId): Single<CrmVacancyRequest> {
        return Single.error(Throwable())
    }

    override fun getReviewsAboutVolunteer(
        volunteerId: Long,
        page: Int,
        limit: Int?
    ): Single<ResponseMetaData<PersistentList<ReviewAboutVolunteer>>> {
        return Single.error(Throwable())
    }

    override fun uploadImageAsync(image: MultipartBody.Part): Single<File> =
        debuggableMethod(
            ::uploadImageAsync.name,
            config.errorMode
        ) {
            File.restore(
                id = FileId.restore("1"),
                uri = GlobalMockData.getImage(1),
                name = "name"
            )
        }

    override fun uploadFilesAsync(image: MultipartBody.Part): Single<FileUpload> {
        return Single.error(Throwable())
    }

    override fun getCurrentSettlementAsync(
        latitude: Double?,
        longitude: Double?
    ): Single<OverviewApi.GetCurrentSettlementResponse> =
        debuggableMethod(
            ::getCurrentSettlementAsync.name,
            config.errorMode
        ) {
            OverviewApi.GetCurrentSettlementResponse(
                current = Location.Companion.restore(
                    title = "Московская область, город Московский",
                    rawTitle = null,
                    settlement = "г Москва",
                    country = "Россия",
                    settlementCode = "102",
                    coordinates = GeoPoint.Coordinates(55.7540471, 37.620405)
                ),
                new = Location.Companion.restore(
                    title = "Московская область, город Видное",
                    rawTitle = null,
                    settlement = "г Видное",
                    country = "Россия",
                    settlementCode = "102",
                    coordinates = GeoPoint.Coordinates(55.7540471, 37.620405)
                )
            )
        }

    override fun getCurrentSettlementByIpAsync(): Single<Location> =
        debuggableMethod(
            ::getCurrentSettlementByIpAsync.name,
            config.errorMode
        ) {
            Location.Companion.restore(
                title = "Московская область, город Московский",
                rawTitle = null,
                settlement = "г Москва",
                country = "г Москва",
                settlementCode = "102",
                coordinates = GeoPoint.Coordinates(55.7540471, 37.620405)
            )
        }

    override suspend fun getCurrentSettlementByIp() = Location.Companion.restore(
        title = "Московская область, город Московский",
        rawTitle = null,
        settlement = "г Москва",
        country = "г Москва",
        settlementCode = "102",
        coordinates = GeoPoint.Coordinates(55.7540471, 37.620405)
    )

    override fun resetPasswordAsync(body: OverviewApi.ResetPasswordBody): Completable =
        debuggableMethod(
            ::resetPasswordAsync.name,
            config.errorMode
        )

    override fun setNewPasswordAsync(body: OverviewApi.SetNewPasswordBody): Completable =
        debuggableMethod(
            ::setNewPasswordAsync.name,
            config.errorMode
        )

    override suspend fun getActualBanners() = throw Throwable()

    override fun getCategory(categoryId: Long): Single<Tag> {
        return Single.error(Throwable())
    }

    override suspend fun getTags(): PersistentSet<Tag> {
        throw Throwable()
    }

    override fun getRequirementIdAsync(title: String): Single<PersistentList<VacancyRequirements>> {
        return Single.error(Throwable())
    }

    override suspend fun getRequirementId(title: String): PersistentList<VacancyRequirements> {
        throw Throwable()
    }

    override fun getOnboardingSources(): Single<PersistentList<OverviewApi.OnboardingSource>> {
        return Single.error(Throwable())
    }

    override fun setOnboardingSources(request: OverviewApi.SetOnboardingSourcesBody): Completable =
        Completable.complete()

    override suspend fun getKnowledgeBaseCategories() = throw Throwable()

    override fun searchKnowledgeBase(
        search: String?,
        page: Int,
        limit: Int?,
    ): Single<ResponseMetaData<PersistentList<KnowledgeBaseSection>>> {
        return Single.error(Throwable())
    }

    override fun getKnowledgeBaseSections(
        id: Int,
        page: Int,
        limit: Int?
    ): Single<ResponseMetaData<PersistentList<KnowledgeBaseSection>>> {
        return Single.error(Throwable())
    }

    override suspend fun getKnowledgeBaseArticleInfo(
        articleId: Int
    ) = throw Throwable()

    override suspend fun likeKnowledgeBaseArticle(articleId: Int) {
        // Do nothing
    }

    override suspend fun getKnowledgeBaseArticle(articleId: Int) = throw Throwable()

    override fun getImageAsync(imageId: String): Single<Image> {
        return debuggableMethod(
            ::getImageAsync.name,
            config.errorMode
        ) {
            Image.restore(ImageData(null, null, null))
        }
    }
}
