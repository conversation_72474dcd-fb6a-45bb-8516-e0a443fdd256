package ru.dobro.api

import android.net.Uri
import common.library.core.entity.StatefulAbstractEntity
import kotlinx.collections.immutable.PersistentList
import ru.dobro.domain.OrganizationId
import javax.annotation.CheckReturnValue

data class OrganizationDashboardData(
    val organizer: Organizer,
    val sections: PersistentList<Sections>,
    val canEdit: Boolean,
    val referralLink: Uri,
) {
    data class Organizer(
        val iconUrl: Uri,
        val imageUrl: Uri,
        val name: String,
        val statistic: Statistic,
        val remoteId: OrganizationId,
        val verified: Boolean,
        val url: Uri
    )

    data class Statistic(
        val projectsCount: Int,
        val volunteersCount: Int,
        val organizerRatingCount: Int,
        val assistantsCount: Int,
        val vacanciesCount: Int,
        val acceptedVacancyRequestsCount: Int,
        val volunteersWithHoursCount: Int,
        val eventsCount: Int,
        val organizerRating: Float
    )

    data class Sections(
        val slug: String,
        val title: String,
        val counter: Int
    )
}

class OrganizationDashboard
constructor(data: OrganizationDashboardData) :
    StatefulAbstractEntity<OrganizationId, OrganizationDashboardData, OrganizationDashboard>(data) {

    @CheckReturnValue
    override fun create(state: OrganizationDashboardData): OrganizationDashboard =
        OrganizationDashboard(state)

    override val identity: OrganizationId get() = internalState.organizer.remoteId

    val organizer: OrganizationDashboardData.Organizer get() = internalState.organizer
    val sections: PersistentList<OrganizationDashboardData.Sections> get() = internalState.sections
    val canEdit: Boolean get() = internalState.canEdit
    val referralLink: Uri get() = internalState.referralLink

    companion object {
        @CheckReturnValue
        fun restore(data: OrganizationDashboardData) = OrganizationDashboard(data)

        @CheckReturnValue
        fun restore(
            organizer: OrganizationDashboardData.Organizer,
            sections: PersistentList<OrganizationDashboardData.Sections>,
            canEdit: Boolean,
            referralLink: Uri
        ): OrganizationDashboard =
            restore(
                OrganizationDashboardData(
                    organizer = organizer,
                    sections = sections,
                    canEdit = canEdit,
                    referralLink = referralLink
                )
            )
    }
}
