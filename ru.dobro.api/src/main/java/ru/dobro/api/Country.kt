package ru.dobro.api

import com.google.gson.annotations.SerializedName
import common.library.core.value.core.StatefulAbstractValue
import javax.annotation.CheckReturnValue

data class CountryBody(
    @SerializedName("code")
    val code: String? = null,
    @SerializedName("title")
    val title: String? = null,
    @SerializedName("titleLong")
    val titleLong: String? = null,
    @SerializedName("flagIcon")
    val flagIcon: String? = null,
)

class Country constructor(data: CountryBody) : StatefulAbstractValue<CountryBody, Country>(data) {
    @CheckReturnValue
    override fun create(state: CountryBody): Country = Country(state)

    val code: String get() = internalState.code ?: ""
    val title: String get() = internalState.title ?: ""
    val titleLong: String get() = internalState.titleLong ?: ""
    val flagIcon: String get() = internalState.flagIcon ?: ""

    companion object {
        @CheckReturnValue
        fun restore(data: CountryBody) = Country(data)

        @CheckReturnValue
        fun restore(
            code: String,
            title: String,
            titleLong: String,
            flagIcon: String,
        ): Country = restore(
            CountryBody(
                code = code,
                title = title,
                titleLong = titleLong,
                flagIcon = flagIcon,
            )
        )
    }
}
