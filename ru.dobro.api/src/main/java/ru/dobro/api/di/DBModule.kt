package ru.dobro.api.di

import android.app.Application
import androidx.room.Room
import org.kodein.di.Kodein
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.api.db.DobroDatabase
import ru.dobro.api.db.dao.HiddenVacancyRequestDao

val dbModule = Kodein.Module("dbModule") {
    bind<DobroDatabase>() with singleton {
        Room.databaseBuilder(
            instance<Application>(),
            DobroDatabase::class.java,
            "dobro-database"
        )
            .fallbackToDestructiveMigration()
            .build()
    }

    bind<HiddenVacancyRequestDao>() with singleton { instance<DobroDatabase>().hiddenVacancyRequestDao() }
}
