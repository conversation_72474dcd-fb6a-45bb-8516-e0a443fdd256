package ru.dobro.api

import ru.dobro.api.dobro_center.DobroCenterApi
import ru.dobro.api.organization.OrganizationApi
import ru.dobro.api.overview.OverviewApi
import ru.dobro.api.profile.ProfileApi
import ru.dobro.api.vacancy.VacancyApi
import javax.annotation.CheckReturnValue

interface DobroApi {
    @CheckReturnValue
    fun overview(): OverviewApi

    @CheckReturnValue
    fun profile(): ProfileApi

    @CheckReturnValue
    fun dobroCenter(): DobroCenterApi

    @CheckReturnValue
    fun organization(): OrganizationApi

    @CheckReturnValue
    fun vacancy(): Vacancy<PERSON><PERSON>
}
