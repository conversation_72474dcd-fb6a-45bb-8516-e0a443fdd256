package ru.dobro.api

import common.library.core.data.MimeType
import common.library.core.data.formatFull
import ru.dobro.domain.AccessToken
import javax.annotation.CheckReturnValue

object AuthorizationHeader {
    const val name: String = "authorization"
    const val placeholder: String = "AccessToken"

    const val template: String = "$name: @$placeholder"

    @CheckReturnValue
    fun createAuthorization(accessToken: AccessToken): String = "Bearer ${accessToken.asString()}"

    @CheckReturnValue
    fun createDefault(serviceToken: String): String = "Bearer $serviceToken"
}

object DadataAuthorizationHeader {
    const val name: String = "authorization"
    const val placeholder: String = "Token"

    const val template: String = "$name: @$placeholder"

    @CheckReturnValue
    fun create(apiKey: String): String = "Token $apiKey"
}

object MetaJsonHeader {
    const val name: String = "accept"
    const val placeholderJson: String = "json"
    const val placeholderMetaJson: String = "metaJson"

    const val templateJson: String = "${name}: @${placeholderJson}"
    const val templateMetaJson: String = "${name}: @${placeholderMetaJson}"

    @CheckReturnValue
    fun create(acceptType: AcceptType): String = when (acceptType) {
        AcceptType.Json -> MimeType.Application.json.formatFull()
        AcceptType.MetaJson -> "application/vnd.meta+json"
    }

    enum class AcceptType {
        Json,
        MetaJson
    }
}
