package ru.dobro.api

import android.net.Uri
import common.library.core.UriScheme

enum class Server(
    var scheme: UriScheme,
    var mainHost: String,
    var mainPath: String,
    val targetedHelpHost: String,
    val targetedHelpPath: String
) {
    Production(UriScheme.https, "dobro.ru", "", "help.dobro.ru", "mobile/"),
    Test(UriScheme.https, "dobro.website", "", "ahelp.test.happydesk.ru", "mobile/"),
    Develop(UriScheme.https, "dobro.zone", "", "ahelp.test.happydesk.ru", "mobile/"),

    ProductionCleartext(UriScheme.http, "dobro.ru", "", "help.dobro.ru", "mobile/"),
    TestCleartext(UriScheme.http, "dobro.website", "", "ahelp.test.happydesk.ru", "mobile/");

    fun buildSocialNetworkAuthorizationUri(socialNetworkPath: String): Uri = Uri.Builder()
        .scheme(scheme.unwrap())
        .authority(mainHost)
        .appendPath("connect")
        .appendPath(socialNetworkPath)
        .appendPath("mobileapp")
        .build()

    fun getServiceLinkBuilder(): Uri.Builder = Uri.Builder()
        .scheme(scheme.unwrap())
        .authority(mainHost)
}
