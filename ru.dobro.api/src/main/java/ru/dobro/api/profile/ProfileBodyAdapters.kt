package ru.dobro.api.profile

import android.net.Uri
import com.google.gson.GsonBuilder
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonNull
import com.google.gson.JsonObject
import com.google.gson.JsonParseException
import com.google.gson.JsonSerializationContext
import com.google.gson.annotations.SerializedName
import com.google.i18n.phonenumbers.Phonenumber.PhoneNumber
import common.library.core.PersonName
import common.library.core.collection.orEmptyList
import common.library.core.collection.orEmptySet
import common.library.core.email.Email
import common.library.core.orFalse
import common.library.core.personId.PersonId
import common.library.serialization.gson.adapter.JsonAdapter
import common.library.serialization.gson.adapter.registerTypeAdapter
import common.library.serialization.gson.deserializeTypedRequired
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.PersistentSet
import kotlinx.collections.immutable.mutate
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import ru.dobro.api.comments.AwaitingReviewVacancy
import ru.dobro.api.comments.AwaitingReviewVacancyBody
import ru.dobro.api.comments.PostedReviewBody
import ru.dobro.api.comments.PostedVacancyReview
import ru.dobro.api.profile.adapters.FriendRequestAdapter
import ru.dobro.api.profile.adapters.FriendsIncomeAdapter
import ru.dobro.api.profile.adapters.PostAdapter
import ru.dobro.api.profile.adapters.PostRecycleTypeAdapter
import ru.dobro.api.profile.adapters.PostTypeAdapter
import ru.dobro.api.profile.adapters.VolunteerAdapter
import ru.dobro.domain.Category
import ru.dobro.domain.ContactPerson
import ru.dobro.domain.Education
import ru.dobro.domain.EducationLevel
import ru.dobro.domain.EducationLevelId
import ru.dobro.domain.File
import ru.dobro.domain.HelpType
import ru.dobro.domain.Job
import ru.dobro.domain.Language
import ru.dobro.domain.LanguageId
import ru.dobro.domain.LanguageLevel
import ru.dobro.domain.LanguageLevelId
import ru.dobro.domain.Location
import ru.dobro.domain.OrganizationId
import ru.dobro.domain.OrganizerId
import ru.dobro.domain.Passport
import ru.dobro.domain.Social
import ru.dobro.domain.SocialType
import ru.dobro.domain.TypeOfEmployment
import ru.dobro.domain.TypeOfEmploymentId
import ru.dobro.domain.UserId
import ru.dobro.domain.profile.ActivityReport
import ru.dobro.domain.profile.Experience
import ru.dobro.domain.profile.Organization
import ru.dobro.domain.profile.Organizer
import ru.dobro.domain.profile.User
import ru.dobro.domain.profile.UserData
import ru.dobro.domain.profile.UserMenuItem
import ru.dobro.domain.profile.VolunteerBook
import ru.dobro.domain.profile.VolunteerBookData
import ru.dobro.domain.profile.achievements.certificate.Certificate
import ru.dobro.domain.profile.achievements.eduCertificate.EduCertificate
import ru.dobro.domain.profile.achievements.eduCertificate.EduCertificateData
import ru.dobro.domain.profile.achievements.trophy.OrganizerTrophy
import ru.dobro.domain.profile.achievements.trophy.Trophy
import ru.dobro.domain.profile.achievements.trophy.TrophyGroup
import ru.dobro.domain.profile.toFriendStatus
import java.lang.reflect.Type
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import javax.annotation.CheckReturnValue
import ru.dobro.domain.profile.Language as ProfileLanguage

@CheckReturnValue
fun GsonBuilder.registerProfileAdapters(): GsonBuilder =
    this
        .registerTypeAdapter(UserAdapter)
        .registerTypeAdapter(ActivityReportAdapter)
        .registerTypeAdapter(ExperienceAdapter)
        .registerTypeAdapter(VolunteerBookAdapter)
        .registerTypeAdapter(LanguageAdapter)
        .registerTypeAdapter(ProfileLanguageAdapter)
        .registerTypeAdapter(EducationAdapter)
        .registerTypeAdapter(EducationLevelAdapter)
        .registerTypeAdapter(TypeOfEmploymentAdapter)
        .registerTypeAdapter(JobAdapter)
        .registerTypeAdapter(OrganizationAdapter)
        .registerTypeAdapter(PassportAdapter)
        .registerTypeAdapter(LanguageLevelAdapter)
        .registerTypeAdapter(OrganizerAdapter)
        .registerTypeAdapter(ContactAdapter)
        .registerTypeAdapter(AwaitingRateVacanciesAdapter)
        .registerTypeAdapter(PostedVacancyReviewsAdapter)
        .registerTypeAdapter(TrophyAdapter)
        .registerTypeAdapter(TrophyOrganizerAdapter)
        .registerTypeAdapter(CertificateAdapter)
        .registerTypeAdapter(EduCertificateAdapter)
        .registerTypeAdapter(TrophyGroupAdapter)
        .registerTypeAdapter(FriendRequestAdapter)
        .registerTypeAdapter(VolunteerAdapter)
        .registerTypeAdapter(FriendsIncomeAdapter)
        .registerTypeAdapter(PostAdapter)
        .registerTypeAdapter(PostTypeAdapter)
        .registerTypeAdapter(PostRecycleTypeAdapter)

private data class UserBody(
    @SerializedName("id")
    val id: UserId?,
    @SerializedName("fio")
    val name: Name?,
    @SerializedName("birthday")
    val birthday: ZonedDateTime?,
    @SerializedName("telephone")
    val phone: PhoneNumber?,
    @SerializedName("categories")
    val categories: PersistentSet<Category>?,
    @SerializedName("helpCategories")
    val helpTypes: PersistentSet<HelpType>?,
    @SerializedName("iconFile")
    val avatar: File?,
    @SerializedName("createdAt")
    val createdAt: ZonedDateTime?,
    @SerializedName("settlement")
    val settlement: Location?,
    @SerializedName("actualAddress")
    val actualAddress: Location?,
    @SerializedName("region")
    val region: String?,
    @SerializedName("statistic")
    val statistic: Statistic?,
    @SerializedName("about")
    val about: String?,
    @SerializedName("aboutHtml")
    val aboutHtml: String?,
    @SerializedName("educationBackgrounds")
    val educations: PersistentList<Education>?,
    @SerializedName("educationLevel")
    val educationLevel: EducationLevel?,
    @SerializedName("languages")
    val languages: PersistentList<ProfileLanguage>?,
    @SerializedName("russiaCitizenship")
    val isRussiaCitizenship: Boolean?,
    @SerializedName("esiaConfirmed")
    val isEsiaVerified: Boolean?,
    @SerializedName("translatedGender")
    val gender: String?,
    @SerializedName("socialMedia")
    val socialMedia: Socials?,
    @SerializedName("medicalBook")
    val hasMedicalHistory: Boolean?,
    @SerializedName("driverLicense")
    val hasDriverLicence: Boolean?,
    @SerializedName("volunteerOrganization")
    val organization: Organization?,
    @SerializedName("jobs")
    val jobs: PersistentList<Job>?,
    @SerializedName("shirtSize")
    val tShirtSize: String?,
    @SerializedName("employmentType")
    val typeOfEmployment: TypeOfEmployment?,
    @SerializedName("snils")
    val snils: String?,
    @SerializedName("inn")
    val personId: PersonId?,
    @SerializedName("passport")
    val passport: Passport?,
    @SerializedName("volunteerTrophies")
    val volunteerTrophies: PersistentList<String>?,
    @SerializedName("volunteerCertificates")
    val volunteerCertificates: PersistentList<String>?,
    @SerializedName("eduCertificates")
    val eduCertificates: PersistentList<String>?,
    @SerializedName("user")
    val userData: UserData?,
    @SerializedName("organizer")
    val organizer: Organizer?,
    @SerializedName("friendInfo")
    val friendStatus: String?,
    @SerializedName("evkVisible")
    val evkVisible: Boolean?,
    @SerializedName("removable")
    val removable: Boolean?,
    @SerializedName("hasDonations")
    val hasDonations: Boolean?
) {
    data class Organizer(
        @SerializedName("id")
        val id: Long?,
        @SerializedName("fullName")
        val fullName: String?,
        @SerializedName("name")
        val name: String?,
        @SerializedName("iconFile")
        val iconFile: File?,
        @SerializedName("statistic")
        val statistic: Statistic?
    ) {
        data class Statistic(
            @SerializedName("projectsCount")
            val projectsCount: Int?,
            @SerializedName("volunteersCount")
            val volunteersCount: Int?,
            @SerializedName("organizerRatingCount")
            val organizerRatingCount: Int?,
            @SerializedName("assistantsCount")
            val assistantsCount: Int?,
            @SerializedName("vacanciesCount")
            val vacanciesCount: Int?,
            @SerializedName("acceptedVacancyRequestsCount")
            val acceptedVacancyRequestsCount: Int?,
            @SerializedName("volunteersWithHoursCount")
            val volunteersWithHoursCount: Int?,
            @SerializedName("eventsCount")
            val eventsCount: Int?,
            @SerializedName("hours")
            val hours: Int?
        )
    }

    data class UserData(
        @SerializedName("id")
        val userId: UserId?,
        @SerializedName("email")
        val email: Email?,
        @SerializedName("emailConfirmed")
        val emailConfirmed: Boolean?,
        @SerializedName("menuItems")
        val menuItems: PersistentList<MenuItems>?,
        @SerializedName("modalSettings")
        val modalSettings: ModalSettings
    ) {
        data class ModalSettings(
            @SerializedName("mp_donationModal")
            val mpDonationModal: Any?
        )
    }

    data class MenuItems(
        @SerializedName("name")
        val name: String?,
        @SerializedName("icon")
        val icon: Uri?,
        @SerializedName("type")
        val type: String?,
        @SerializedName("url")
        val url: Uri?
    )

    data class Name(
        @SerializedName("first_name")
        val firstName: String?,
        @SerializedName("last_name")
        val lastName: String?,
        @SerializedName("second_name")
        val secondName: String?,
        @SerializedName("second_name_not_available")
        val withoutSecondName: Boolean?
    )

    data class Statistic(
        @SerializedName("eventsCount")
        val eventsCount: Int?,
        @SerializedName("volunteerRating")
        val volunteerRating: Float?,
        @SerializedName("hours")
        val hours: Float?,
        @SerializedName("verifiedHours")
        val verifiedHours: Float?,
        @SerializedName("requestsCount")
        val requestsCount: Int?,
        @SerializedName("bookmarksCount")
        val bookmarksCount: Int?,
        @SerializedName("friendsCount")
        val friendsCount: Int?,
        @SerializedName("friendRequestsCount")
        val friendRequestsCount: Int?,
        @SerializedName("organizationsCount")
        val organizationsCount: Int?,
        @SerializedName("reviewsCount")
        val reviewsCount: Int?,
    )

    data class Socials(
        @SerializedName("facebook")
        val facebook: Uri?,
        @SerializedName("vk")
        val vk: Uri?,
        @SerializedName("youtube")
        val youtube: Uri?,
        @SerializedName("ok")
        val ok: Uri?,
        @SerializedName("instagram")
        val instagram: Uri?,
        @SerializedName("tiktok")
        val tiktok: Uri?,
        @SerializedName("telegram")
        val telegram: Uri?,
    )
}

private object UserAdapter : JsonDeserializer<User> {
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): User {
        val body: UserBody = context.deserializeTypedRequired(json)

        return User.restore(
            id = body.id ?: UserId.restore(-1L),
            userId = body.userData?.userId ?: UserId.restore(-1L),
            name = PersonName.empty()
                .withName(body.name?.firstName)
                .withSurname(body.name?.lastName)
                .withSecondName(body.name?.secondName)
                .withWithoutSecondName(body.name?.withoutSecondName),
            userMenuItem = body.userData?.menuItems?.filter { it.type == "dashboard" }?.map {
                UserMenuItem(
                    it.name ?: "",
                    it.icon ?: Uri.EMPTY,
                    it.type ?: "",
                    it.url ?: Uri.EMPTY
                )
            }?.toPersistentList() ?: persistentListOf(),
            mpDonationModal = body.userData?.modalSettings?.mpDonationModal,
            birthDate = body.birthday,
            phone = body.phone,
            email = body.userData?.email,
            isEmailVerified = body.userData?.emailConfirmed ?: false,
            categories = body.categories.orEmptySet(),
            helpTypes = body.helpTypes.orEmptySet(),
            avatar = body.avatar,
            registeredAt = body.createdAt ?: ZonedDateTime.now(),
            settlement = body.settlement,
            actualAddress = body.actualAddress,
            rating = body.statistic?.volunteerRating,
            about = if (body.about.isNullOrEmpty()) null else body.about,
            aboutHtml = if (body.aboutHtml.isNullOrEmpty()) null else body.aboutHtml,
            educations = body.educations.orEmptyList(),
            educationLevel = body.educationLevel,
            languages = body.languages.orEmptyList(),
            isRussiaCitizenship = body.isRussiaCitizenship ?: false,
            isEsiaVerified = body.isEsiaVerified ?: false,
            gender = body.gender,
            typeOfEmployment = body.typeOfEmployment,
            hasMedicalHistory = body.hasMedicalHistory ?: false,
            hasDriverLicence = body.hasDriverLicence ?: false,
            tShirtSize = body.tShirtSize,
            organization = body.organization,
            jobs = body.jobs.orEmptyList(),
            personId = body.personId,
            snils = body.snils,
            passport = body.passport,
            volunteerTrophies = body.volunteerTrophies,
            volunteerCertificates = body.volunteerCertificates,
            eduCertificates = body.eduCertificates,
            removable = body.removable ?: true,
            socials = persistentListOf<Social>().mutate { list ->
                // TODO(убрали на время пока Meta запрещена. Не удалять, возможно, Meta еще будет доступна)
//                body.socialMedia.facebook?.let {
//                    list.add(
//                        Social.Companion.restore(
//                            type = SocialType.Facebook,
//                            path = it
//                        )
//                    )
//                }
                body.socialMedia?.vk?.let {
                    list.add(
                        Social.Companion.restore(
                            type = SocialType.Vk,
                            path = it
                        )
                    )
                }
                body.socialMedia?.ok?.let {
                    list.add(
                        Social.Companion.restore(
                            type = SocialType.Ok,
                            path = it
                        )
                    )
                }
                body.socialMedia?.tiktok?.let {
                    list.add(
                        Social.Companion.restore(
                            type = SocialType.Tiktok,
                            path = it
                        )
                    )
                }
                body.socialMedia?.telegram?.let {
                    list.add(
                        Social.Companion.restore(
                            type = SocialType.Telegram,
                            path = it
                        )
                    )
                }
                // TODO(убрали на время пока Meta запрещена. Не удалять, возможно, Meta еще будет доступна)
//                body.socialMedia.instagram?.let {
//                    list.add(
//                        Social.Companion.restore(
//                            type = SocialType.Instagram,
//                            path = it
//                        )
//                    )
//                }
            },
            organizer = if (body.organizer == null) {
                null
            } else {
                UserData.Organizer(
                    id = body.organizer.id ?: -1,
                    name = body.organizer.name.orEmpty(),
                    fullName = body.organizer.fullName.orEmpty(),
                    icon = body.organizer.iconFile,
                    statistic = UserData.Organizer.Statistic(
                        projectsCount = body.organizer.statistic?.projectsCount ?: 0,
                        volunteersCount = body.organizer.statistic?.volunteersCount ?: 0,
                        organizerRatingCount = body.organizer.statistic?.organizerRatingCount ?: 0,
                        assistantsCount = body.organizer.statistic?.assistantsCount ?: 0,
                        vacanciesCount = body.organizer.statistic?.vacanciesCount ?: 0,
                        acceptedVacancyRequestsCount = body.organizer.statistic?.acceptedVacancyRequestsCount
                            ?: 0,
                        volunteersWithHoursCount = body.organizer.statistic?.volunteersWithHoursCount
                            ?: 0,
                        eventsCount = body.organizer.statistic?.eventsCount ?: 0,
                        hours = body.organizer.statistic?.hours ?: 0,
                    )
                )
            },
            friendStatus = body.friendStatus.orEmpty().lowercase().toFriendStatus(),
            evkVisible = body.evkVisible ?: false,
            statistic = UserData.Statistic(
                eventsCount = body.statistic?.eventsCount ?: 0,
                volunteerRating = body.statistic?.volunteerRating ?: 0f,
                hours = body.statistic?.hours ?: 0f,
                verifiedHours = body.statistic?.verifiedHours ?: 0f,
                requestsCount = body.statistic?.requestsCount ?: 0,
                bookmarksCount = body.statistic?.bookmarksCount ?: 0,
                friendsCount = body.statistic?.friendsCount ?: 0,
                friendRequestsCount = body.statistic?.friendRequestsCount?.takeIf { it != 0 },
                organizationsCount = body.statistic?.organizationsCount ?: 0,
                reviewsCount = body.statistic?.reviewsCount ?: 0,
            ),
            hasDonations = body.hasDonations.orFalse()
        )
    }
}

data class TrophyWrapper(
    @SerializedName("trophy")
    val trophy: TrophyBody
)

data class TrophyBody(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("name")
    val title: String?,
    @SerializedName("title")
    val titleVariant: String?,
    @SerializedName("description")
    val description: String?,
    @SerializedName("imageFile")
    val image: ImageBody?,
    @SerializedName("percent")
    val percent: Int?
) {
    data class ImageBody(
        @SerializedName("id")
        val id: String?,
        @SerializedName("name")
        val name: String?,
        @SerializedName("url")
        val url: Uri?
    )
}

private object TrophyAdapter : JsonDeserializer<Trophy> {
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): Trophy {
        val body: TrophyWrapper = context.deserializeTypedRequired(json)

        return Trophy.restore(
            id = body.trophy.id ?: -1,
            title = body.trophy.title ?: "",
            description = body.trophy.description ?: "",
            image = body.trophy.image?.url,
            percent = body.trophy.percent ?: 100
        )
    }
}

private object TrophyOrganizerAdapter : JsonDeserializer<OrganizerTrophy> {
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): OrganizerTrophy {
        val body: TrophyBody = context.deserializeTypedRequired(json)

        return OrganizerTrophy.restore(
            id = body.id ?: -1,
            title = body.title ?: "",
            description = body.description ?: "",
            image = body.image?.url,
            percent = body.percent ?: 100
        )
    }
}

data class CertificateBody(
    @SerializedName("id")
    val id: Int,
    @SerializedName("imageFile")
    val image: Uri?,
)

private object CertificateAdapter : JsonDeserializer<Certificate> {
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): Certificate {
        val body: CertificateBody = context.deserializeTypedRequired(json)

        return Certificate.restore(
            id = body.id,
            image = body.image
        )
    }
}

data class EduCertificateBody(
    @SerializedName("id")
    val id: Int?,
    @SerializedName("certificateId")
    val certificateId: Int?,
    @SerializedName("certificateUrl")
    val certificateUrl: Uri?,
    @SerializedName("certificateDate")
    val certificateDate: String?,
    @SerializedName("course")
    val course: Course?
) {
    data class Course(
        @SerializedName("id")
        val id: Int?,
        @SerializedName("name")
        val name: String?,
        @SerializedName("Any")
        val type: Any?,
        @SerializedName("annotation")
        val annotation: String?,
        @SerializedName("author")
        val author: Author?,
    ) {
        data class Author(
            @SerializedName("id")
            val id: Int?,
            @SerializedName("image")
            val url: String?,
            @SerializedName("name")
            val name: String?,
        )
    }
}

private object EduCertificateAdapter : JsonDeserializer<EduCertificate> {
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): EduCertificate {
        val body: EduCertificateBody = context.deserializeTypedRequired(json)

        return EduCertificate.restore(
            id = body.id,
            certificateId = body.certificateId,
            certificateUrl = body.certificateUrl,
            certificateDate = body.certificateDate,
            course = EduCertificateData.Course(
                id = body.course?.id,
                name = body.course?.name,
                type = body.course?.type,
                annotation = body.course?.annotation,
                author = EduCertificateData.Course.Author(
                    body.course?.author?.id,
                    body.course?.author?.url,
                    body.course?.author?.name
                )
            )
        )
    }
}

private data class ActivityReportBody(
    @SerializedName("title")
    val title: String,
    @SerializedName("vacancy")
    val vacancy: Vacancy,
    @SerializedName("dateStarted")
    val dateStarted: ZonedDateTime,
    @SerializedName("dateEnded")
    val dateEnded: ZonedDateTime?,
    @SerializedName("volunteerRating")
    val rating: Float?,
    @SerializedName("hours")
    val completedHours: Float,
    @SerializedName("organizer")
    val organizer: Organizer,
    @SerializedName("categories")
    val categories: PersistentList<Category>,
    @SerializedName("verified")
    val isVerified: Boolean,
    @SerializedName("event")
    val event: Event?,
) {
    data class Vacancy(
        @SerializedName("title")
        val title: String
    )

    data class Event(
        @SerializedName("name")
        val name: String?
    )
}

private object ActivityReportAdapter : JsonDeserializer<ActivityReport> {
    @CheckReturnValue
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): ActivityReport {
        val body: ActivityReportBody = context.deserializeTypedRequired(json)

        return ActivityReport.of(
            title = body.event?.name ?: "",
            workType = body.vacancy.title,
            dateStarted = body.dateStarted,
            dateEnded = body.dateEnded.takeIf { it != body.dateStarted },
            rating = body.rating,
            completedHours = body.completedHours,
            organizer = body.organizer,
            isVerified = body.isVerified
        )
    }
}

private data class ExperienceBody(
    @SerializedName("category")
    val category: Category?,
    @SerializedName("rows")
    val activityReports: PersistentList<ActivityReport>,
    @SerializedName("totalHours")
    val completedHours: Float,
    @SerializedName("verifiedHours")
    val verifiedHours: Float
)

private object ExperienceAdapter : JsonDeserializer<Experience> {
    @CheckReturnValue
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): Experience {
        val body: ExperienceBody = context.deserializeTypedRequired(json)

        return Experience.restore(
            activityReports = body.activityReports,
            // костыль для отображения опыта без категорий
            category = body.category ?: Category.restoreEmpty(),
            completedHours = body.completedHours,
            verifiedHours = body.verifiedHours
        )
    }
}

private data class VolunteerBookBody(
    @SerializedName("id")
    val id: UserId,
    @SerializedName("fio")
    val name: Name,
    @SerializedName("birthday")
    val birthday: ZonedDateTime?,
    @SerializedName("iconFile")
    val avatar: File?,
    @SerializedName("settlement")
    val location: Location?,
    @SerializedName("statistic")
    val statistic: Statistic,
    @SerializedName("qrCode")
    val qrCode: Uri,
    @SerializedName("url")
    val path: Uri,
    @SerializedName("experience")
    val experience: CategoryExperience?,
    @SerializedName("evkVisible")
    val evkVisible: Boolean
) {
    data class Name(
        @SerializedName("first_name")
        val firstName: String,
        @SerializedName("last_name")
        val lastName: String,
        @SerializedName("second_name")
        val secondName: String?,
        @SerializedName("second_name_not_available")
        val withoutSecondName: Boolean?
    )

    data class Statistic(
        @SerializedName("eventsCount")
        val eventsCount: Int,
        @SerializedName("hours")
        val hours: Float,
        @SerializedName("volunteerRating")
        val rating: Float?,
        @SerializedName("verifiedHours")
        val verifiedHours: Float
    )

    data class CategoryExperience(
        @SerializedName("categories")
        val categoriesExperience: PersistentList<Experience>
    )

    data class BloodDonation(
        @SerializedName("donationsCount")
        val donationsCount: Int,
        @SerializedName("awards")
        val awards: PersistentList<Awards>,
        @SerializedName("donations")
        val donations: PersistentList<Donations>
    ) {
        data class Awards(
            @SerializedName("title")
            val title: String,
            @SerializedName("date")
            val date: ZonedDateTime
        )

        data class Donations(
            @SerializedName("year")
            val year: Int,
            @SerializedName("dates")
            val dates: PersistentList<String>
        )
    }
}

private object VolunteerBookAdapter : JsonDeserializer<VolunteerBook> {
    @CheckReturnValue
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): VolunteerBook {
        val body: VolunteerBookBody = context.deserializeTypedRequired(json)

        val bloodDonation = try {
            val jsonObject = json.asJsonObject
            val bloodDonationElement = jsonObject["donationOfBlood"]
            when {
                bloodDonationElement == null || bloodDonationElement.isJsonNull -> null
                bloodDonationElement.isJsonArray -> null
                else -> context.deserialize<VolunteerBookBody.BloodDonation>(
                    bloodDonationElement, VolunteerBookBody.BloodDonation::class.java
                )
            }
        } catch (_: Exception) {
            null
        }

        return VolunteerBook.restore(
            id = body.id,
            name = PersonName.empty()
                .withName(body.name.firstName)
                .withSurname(body.name.lastName)
                .withSecondName(body.name.secondName),
            birthday = body.birthday,
            avatar = body.avatar,
            location = body.location,
            completedEventsCount = body.statistic.eventsCount,
            completedHours = body.statistic.hours,
            verifiedHours = body.statistic.verifiedHours,
            rating = body.statistic.rating,
            qrPath = body.qrCode,
            path = body.path,
            experiences = body.experience?.categoriesExperience.orEmptyList(),
            isEnabled = body.evkVisible,
            bloodDonation = bloodDonation?.let { bloodDonation ->
                VolunteerBookData.BloodDonation(
                    donationsCount = bloodDonation.donationsCount,
                    awardTitle = bloodDonation.awards.firstOrNull()?.title,
                    awardDate = bloodDonation.awards.firstOrNull()?.date,
                    donations = bloodDonation.donations.map {
                        VolunteerBookData.BloodDonation.Donation(
                            year = it.year,
                            dates = it.dates
                        )
                    }.sortedByDescending { it.year }.toPersistentList()
                )
            }
        )
    }
}

private data class OrganizationBody(
    @SerializedName("id")
    val id: OrganizationId?,
    @SerializedName("name")
    val name: String?
)

private object OrganizationAdapter : JsonAdapter<Organization> {
    @CheckReturnValue
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): Organization {
        val body: OrganizationBody = context.deserializeTypedRequired(json)

        return Organization.restore(body.id, body.name)
    }

    override fun serialize(
        src: Organization,
        typeOfSrc: Type?,
        context: JsonSerializationContext
    ): JsonElement {
        return context.serialize(
            OrganizationBody(
                id = src.identity,
                name = src.name
            )
        )
    }
}

private data class LanguageBody(
    @SerializedName("id")
    val id: LanguageId,
    @SerializedName("title")
    val title: String
)

private object ProfileLanguageAdapter : JsonDeserializer<Language> {
    @CheckReturnValue
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): Language {
        val body: LanguageBody = context.deserializeTypedRequired(json)

        return Language.restore(body.id, body.title)
    }
}

private data class ProfileLanguageBody(
    @SerializedName("language")
    val language: Language,
    @SerializedName("level")
    val level: LanguageLevel
) {
    data class Language(
        @SerializedName("id")
        val id: LanguageId,
        @SerializedName("title")
        val title: String,
    )
}

data class SerializeLanguage(
    @SerializedName("language")
    val id: LanguageId,
    @SerializedName("level")
    val levelId: LanguageLevelId
)

private object LanguageAdapter : JsonAdapter<ProfileLanguage> {
    @CheckReturnValue
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): ProfileLanguage {
        val body: ProfileLanguageBody = context.deserializeTypedRequired(json)

        return ProfileLanguage.restore(body.language.id, body.language.title, body.level)
    }

    override fun serialize(
        src: ProfileLanguage,
        typeOfSrc: Type?,
        context: JsonSerializationContext
    ): JsonElement {
        return context.serialize(
            SerializeLanguage(
                id = src.identity,
                levelId = src.level.identity
            )
        )
    }
}

private data class LanguageLevelBody(
    @SerializedName("id")
    val id: LanguageLevelId,
    @SerializedName("title")
    val title: String
)

private object LanguageLevelAdapter : JsonDeserializer<LanguageLevel> {
    @CheckReturnValue
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): LanguageLevel {
        val body: LanguageLevelBody = context.deserializeTypedRequired(json)

        return LanguageLevel.restore(body.id, body.title)
    }
}

private data class TypeOfEmploymentBody(
    @SerializedName("id")
    val id: TypeOfEmploymentId,
    @SerializedName("title")
    val title: String
)

private object TypeOfEmploymentAdapter : JsonAdapter<TypeOfEmployment> {
    @CheckReturnValue
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): TypeOfEmployment {
        val body: TypeOfEmploymentBody = context.deserializeTypedRequired(json)

        return TypeOfEmployment.restore(body.id, body.title)
    }

    override fun serialize(
        src: TypeOfEmployment,
        typeOfSrc: Type?,
        context: JsonSerializationContext
    ): JsonElement {
        return context.serialize(
            TypeOfEmploymentBody(
                id = src.identity,
                title = src.title
            )
        )
    }
}

private data class EducationLevelBody(
    @SerializedName("id")
    val id: EducationLevelId,
    @SerializedName("title")
    val title: String
)

private object EducationLevelAdapter : JsonAdapter<EducationLevel> {
    @CheckReturnValue
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): EducationLevel {
        val body: EducationLevelBody = context.deserializeTypedRequired(json)

        return EducationLevel.restore(body.id, body.title)
    }

    override fun serialize(
        src: EducationLevel,
        typeOfSrc: Type?,
        context: JsonSerializationContext
    ): JsonElement {
        return context.serialize(
            EducationLevelBody(
                id = src.identity,
                title = src.title
            )
        )
    }
}

private data class EducationBody(
    @SerializedName("level")
    val level: EducationLevelBody?,
    @SerializedName("institutionName")
    val institutionName: String?,
    @SerializedName("speciality")
    val speciality: String?,
    @SerializedName("fromYear")
    val fromYear: Int?,
    @SerializedName("toYear")
    val toYear: Int?
)

private object EducationAdapter : JsonAdapter<Education> {
    @CheckReturnValue
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): Education {
        val body: EducationBody = context.deserializeTypedRequired(json)

        return Education.restore(
            level = body.level?.title,
            institution = body.institutionName,
            speciality = body.speciality,
            fromYear = body.fromYear,
            toYear = body.toYear
        )
    }

    override fun serialize(
        src: Education,
        typeOfSrc: Type?,
        context: JsonSerializationContext
    ): JsonElement {
        return context.serialize(
            mutableMapOf<String, Any?>().also { result ->
                result["institutionName"] = src.institution ?: ""
                result["speciality"] = src.speciality ?: ""
                result["fromYear"] = src.fromYear ?: JsonNull.INSTANCE
                result["toYear"] = src.toYear ?: JsonNull.INSTANCE
            }
        )
    }
}

private data class JobBody(
    @SerializedName("organizationName")
    val organization: String?,
    @SerializedName("position")
    val post: String?,
)

private object JobAdapter : JsonAdapter<Job> {
    @CheckReturnValue
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): Job {
        val body: JobBody = context.deserializeTypedRequired(json)

        return Job.restore(
            organization = body.organization,
            post = body.post
        )
    }

    override fun serialize(
        src: Job,
        typeOfSrc: Type?,
        context: JsonSerializationContext
    ): JsonElement {
        return context.serialize(
            mutableMapOf<String, Any?>().also { result ->
                result["organizationName"] = src.organization ?: ""
                result["position"] = src.post ?: ""
            }
        )
    }
}

private data class PassportBody(
    @SerializedName("series")
    val series: String? = null,
    @SerializedName("number")
    val number: String? = null,
    @SerializedName("issueDate")
    val issueDate: ZonedDateTime? = null,
    @SerializedName("issueSubdivision")
    val issueSubdivision: String? = null,
    @SerializedName("issueSubdivisionCode")
    val issueSubdivisionCode: String? = null,
    @SerializedName("address")
    val address: Location? = null,
    @SerializedName("actualAddress")
    val actualAddress: String? = null,
    @SerializedName("expirationDate")
    val expirationDate: Any? = null,
    @SerializedName("birthplace")
    val birthplace: String? = null,
)

private object PassportAdapter : JsonAdapter<Passport> {
    @CheckReturnValue
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): Passport {
        val body: PassportBody = context.deserializeTypedRequired(json)

        val expirationDate = body.expirationDate?.let { expirationDate ->
            when (expirationDate) {
                is ZonedDateTime -> expirationDate
                is String -> try {
                    ZonedDateTime.parse(expirationDate)
                } catch (e: DateTimeParseException) {
                    try {
                        val localDate = java.time.LocalDate.parse(
                            expirationDate,
                            DateTimeFormatter.ISO_LOCAL_DATE
                        )
                        localDate.atStartOfDay(java.time.ZoneOffset.UTC)
                    } catch (e: DateTimeParseException) {
                        null
                    }
                }

                else -> null
            }
        }

        return Passport.restore(
            series = body.series ?: "",
            number = body.number ?: "",
            date = body.issueDate,
            organization = body.issueSubdivision ?: "",
            organizationCode = body.issueSubdivisionCode ?: "",
            birthPlace = body.birthplace ?: "",
            address = body.address,
            expirationDate = expirationDate,
            actualAddress = body.actualAddress ?: ""
        )
    }

    override fun serialize(
        src: Passport,
        typeOfSrc: Type?,
        context: JsonSerializationContext
    ): JsonElement {
        val jsonObject = context.serialize(
            PassportBody(
                series = src.series,
                number = src.number,
                issueDate = src.date,
                issueSubdivision = src.organization,
                issueSubdivisionCode = src.organizationCode,
                birthplace = src.birthPlace,
                address = src.address,
                expirationDate = src.expirationDate,
                actualAddress = src.actualAddress
            )
        ).asJsonObject

        try {
            jsonObject.getAsJsonObject("address")?.let { addressJson ->
                if (addressJson.get("shortName").isJsonNull) {
                    addressJson.remove("shortName")
                }
            }
        } catch (_: Exception) {
        }

        try {
            if (
                jsonObject.get("expirationDate").isJsonNull
            ) {
                jsonObject.remove("expirationDate")
            }
        } catch (_: Exception) {
        }

        try {
            val actualAddress = jsonObject.get("actualAddress")
            if (
                actualAddress.isJsonNull ||
                (actualAddress.isJsonPrimitive && actualAddress.asString.isNullOrEmpty())
            ) {
                jsonObject.remove("actualAddress")
            }
        } catch (_: Exception) {
        }

        try {
            if (
                jsonObject.get("socialMedia").isJsonNull
            ) {
                jsonObject.remove("socialMedia")
            }
        } catch (_: Exception) {
        }

        return jsonObject
    }
}

private data class ContactBody(
    @SerializedName("type")
    val type: String?,
    @SerializedName("subtype")
    val subtype: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("telephone")
    val telephone: String?,
    @SerializedName("snils")
    val snils: String?,
    @SerializedName("registration_address")
    val registrationAddress: Location?,
    @SerializedName("actual_address")
    val actualAddress: Location?
)

private object ContactAdapter : JsonAdapter<ContactPerson> {
    @CheckReturnValue
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): ContactPerson {
        val body: ContactBody = context.deserializeTypedRequired(json)

        return ContactPerson.restore(
            type = body.type,
            subtype = body.subtype,
            name = body.name,
            telephone = body.telephone,
            snils = body.snils,
            registrationAddress = body.registrationAddress,
            actualAddress = body.actualAddress
        )
    }

    override fun serialize(
        src: ContactPerson,
        typeOfSrc: Type?,
        context: JsonSerializationContext
    ): JsonElement =
        context.serialize(
            ContactBody(
                type = src.type,
                subtype = src.subtype,
                name = src.name,
                telephone = src.telephone,
                snils = src.snils,
                registrationAddress = src.registrationAddress,
                actualAddress = src.actualAddress
            )
        )
}

data class OrganizerBody(
    @SerializedName("id")
    val id: OrganizerId,
    @SerializedName("name")
    val name: String,
    @SerializedName("iconUrl")
    val icon: Uri?,
    @SerializedName("remoteId")
    val organizationId: OrganizationId
)

private object OrganizerAdapter : JsonDeserializer<Organizer> {
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): Organizer {
        val body: OrganizerBody = context.deserializeTypedRequired(json)

        return Organizer.restore(
            id = body.id,
            name = body.name,
            icon = body.icon,
            organizationId = body.organizationId
        )
    }
}

private object AwaitingRateVacanciesAdapter : JsonDeserializer<AwaitingReviewVacancy> {
    @CheckReturnValue
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): AwaitingReviewVacancy {
        val reviewVacancyBody: AwaitingReviewVacancyBody = context.deserializeTypedRequired(json)

        if (json !is JsonObject) {
            throw JsonParseException("Expected json object. Actual: `$json.`")
        }

        return AwaitingReviewVacancy.restore(
            event = reviewVacancyBody.event ?: "",
            organizer = reviewVacancyBody.organizer ?: ru.dobro.api.comments.Organizer(
                null,
                null,
                null
            ),
            title = reviewVacancyBody.title ?: "",
            vacancy = reviewVacancyBody.vacancy ?: ""
        )
    }
}

private object PostedVacancyReviewsAdapter : JsonDeserializer<PostedVacancyReview> {
    @CheckReturnValue
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): PostedVacancyReview {
        val body: PostedReviewBody = context.deserializeTypedRequired(json)

        if (json !is JsonObject) {
            throw JsonParseException("Expected json object. Actual: `$json.`")
        }

        return PostedVacancyReview.restore(
            event = body.event,
            organizer = body.organizer ?: ru.dobro.api.comments.Organizer(null, null, null),
            title = body.title,
            vacancy = body.vacancy,
            organizerRating = body.organizerRating,
            organizerComment = body.organizerComment,
            organizerRatingDate = body.organizerRatingDate ?: "",
            organizerReplyComment = body.organizerReplyComment,
            organizerReplyDate = body.organizerReplyDate
        )
    }
}

data class TrophyGroupBody(
    @SerializedName("id")
    val id: Long?,
    @SerializedName("title")
    val title: String?,
    @SerializedName("trophies")
    val trophies: PersistentList<TrophyBody>?,
)

private object TrophyGroupAdapter : JsonDeserializer<TrophyGroup> {
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): TrophyGroup {
        val body: TrophyGroupBody = context.deserializeTypedRequired(json)

        return TrophyGroup.restore(
            id = body.id ?: -1L,
            title = body.title ?: "",
            trophies = body.trophies?.map {
                Trophy.restore(
                    it.id ?: -1,
                    it.title ?: it.titleVariant ?: "",
                    it.description ?: "",
                    it.image?.url ?: Uri.EMPTY,
                    it.percent ?: 0
                )
            }?.toPersistentList() ?: persistentListOf()
        )
    }
}
