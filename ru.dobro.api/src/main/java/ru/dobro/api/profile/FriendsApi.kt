package ru.dobro.api.profile

import io.reactivex.Single
import kotlinx.collections.immutable.PersistentList
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.Path
import retrofit2.http.Query
import ru.dobro.api.AuthorizationHeader
import ru.dobro.api.MetaJsonHeader
import ru.dobro.api.global.ResponseMetaData
import ru.dobro.domain.UserId
import ru.dobro.domain.friends.FriendRequest
import ru.dobro.domain.friends.IncomingFriendRequest
import ru.dobro.domain.profile.User

/** ДЕЙСТВИЯ С ДРУЗЬЯМИ */
interface FriendsApi {
    /** Отправляет запрос на добавления волонтера с [friendId] в друзья, [id] - id текущего пользователя */
    @GET("/api/v2/volunteers/{id}/friends/request/create")
    @Headers(AuthorizationHeader.template, MetaJsonHeader.templateJson)
    suspend fun addFriendRequest(
        @Path("id") id: Long,
        @Query("friendId") friendId: Long
    )

    @GET("/api/v2/volunteers/{id}/friends/request/create")
    @Headers(AuthorizationHeader.template, MetaJsonHeader.templateJson)
    suspend fun addFriendRequest(
        @Path("id") id: UserId,
        @Query("friendId") friendId: UserId
    ): Response<Any>

    /** Отмена запроса на добавление волонтера с [friendId] в друзья или удаление его из друзей, [id] - id текущего пользователя */
    @GET("/api/v2/volunteers/{id}/friends/request/remove")
    @Headers(AuthorizationHeader.template, MetaJsonHeader.templateJson)
    suspend fun cancelFriendRequest(
        @Path("id") id: Long,
        @Query("friendId") friendId: Long
    )

    @GET("/api/v2/volunteers/{id}/friends/request/remove")
    @Headers(AuthorizationHeader.template, MetaJsonHeader.templateJson)
    suspend fun cancelFriendRequest(
        @Path("id") id: UserId,
        @Query("friendId") friendId: UserId
    ): Response<Any>

    /** Принятие запроса от волонтера с [id] на добавление в друзья */
    @GET("/api/v2/volunteers/{id}/friends/request/accept")
    @Headers(AuthorizationHeader.template, MetaJsonHeader.templateJson)
    fun acceptFriendRequestAsync(
        @Path("id") id: Long,
        @Query("requestId") requestId: Long
    ): Single<Any>

    @GET("/api/v2/volunteers/{id}/friends/request/accept")
    @Headers(AuthorizationHeader.template, MetaJsonHeader.templateJson)
    suspend fun acceptFriendRequest(
        @Path("id") id: UserId,
        @Query("requestId") requestId: UserId
    ): Response<Any>

    /** Отклонение запроса с id - [requestId] на добавление в друзья, [id] - id текущего пользователя */
    @GET("/api/v2/volunteers/{id}/friends/request/decline")
    @Headers(AuthorizationHeader.template, MetaJsonHeader.templateJson)
    fun rejectFriendRequest(
        @Path("id") id: Long,
        @Query("requestId") requestId: Long
    ): Single<Any>

    /** Получение списка друзей волонтера, [id] - идентификатор волонтера */
    @GET("/api/v2/volunteers/{id}/friends?pagination=1&limit=10")
    @Headers(
        AuthorizationHeader.template,
        MetaJsonHeader.templateMetaJson
    )
    fun getFriends(
        @Path("id") id: Long,
        @Query("page") page: Int
    ): Single<ResponseMetaData<PersistentList<FriendRequest>>>

    /** Получение списка заявок в друзья, [id] - идентификатор волонтера*/
    @GET("/api/v2/volunteers/{id}/friends/requests/incoming?pagination=1&limit=10")
    @Headers(
        AuthorizationHeader.template,
        MetaJsonHeader.templateMetaJson
    )
    fun getIncomingFriends(
        @Path("id") id: Long,
        @Query("page") page: Int
    ): Single<ResponseMetaData<PersistentList<IncomingFriendRequest>>>

    /** Получение списка отправленных заявок в друзья, [id] - идентификатор волонтера*/
    @GET("/api/v2/volunteers/{id}/friends/requests/outgoing")
    @Headers(AuthorizationHeader.template, MetaJsonHeader.templateJson)
    fun getOutgoingFriends(
        @Path("id") id: Long
    ): Single<Any>

    @GET("/api/v2/volunteers/{id}/recommended_friends")
    @Headers(AuthorizationHeader.template, MetaJsonHeader.templateJson)
    suspend fun getRecommendedFriends(
        @Path("id") userId: UserId
    ): Response<PersistentList<User>>
}
