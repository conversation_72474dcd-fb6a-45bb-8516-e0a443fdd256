package common.library.android.plugin

import android.app.Activity
import android.content.Context
import androidx.lifecycle.LifecycleOwner
import common.library.android.resource.LifecycleResourcesScopeOwner
import javax.annotation.CheckReturnValue

interface ActivityPlugin : LifecyclePlugin, LifecycleResourcesScopeOwner {
    fun <Target> plugIn(target: Target)
        where Target : Activity,
              Target : LifecycleOwner

    interface AttachBaseContext {
        @CheckReturnValue
        fun attachBaseContext(context: Context): Context
    }
}
