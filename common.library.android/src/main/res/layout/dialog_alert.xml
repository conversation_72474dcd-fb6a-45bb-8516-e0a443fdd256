<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16.dp">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/header"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
        app:layout_constraintBottom_toTopOf="@+id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Вы уверены?"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8.dp"
        android:gravity="center"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
        app:layout_constraintBottom_toTopOf="@+id/confirm"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header"
        tools:text="Удалить Андрея Викторовича из друзей"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/cancel"
        style="@style/Application.Design.Button.Primary"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@+id/confirm"
        app:layout_constraintEnd_toStartOf="@+id/confirm"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/confirm"
        tools:text="Отмена"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/confirm"
        style="@style/Application.Design.Button.Text.Gray"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32.dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/cancel"
        app:layout_constraintTop_toBottomOf="@+id/title"
        tools:text="Удалить из друзей"/>
</androidx.constraintlayout.widget.ConstraintLayout>