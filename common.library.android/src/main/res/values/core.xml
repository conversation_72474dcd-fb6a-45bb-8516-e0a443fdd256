<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="white">#FFFFFF</color>
    <color name="black">#000000</color>
    <color name="transparent">#00000000</color>
    <color name="transparent_bg">#00ffffff</color>
    <color name="translucent">#52000000</color>

    <color name="core_color_main">#0C1014</color>
    <color name="core_color_icons">#B4BBC6</color>
    <color name="core_color_primary">#540575</color>

    <dimen name="core___button__min_size">48dp</dimen>
    <dimen name="core___button__max_width">500dp</dimen>

    <string name="core___not_implemented">The functionality is not yet implemented</string>

    <!--region Color primary-->

    <attr name="core___color__primary__default"/>
    <attr name="core___color__primary__disabled"/>

    <attr name="core___color__primary__stateless"/>
    <attr name="core___color__primary__stateful"/>

    <!--endregion Color primary-->

    <!--region Color primary variant-->

    <attr name="core___color__primary_variant__default" format="color|reference"/>
    <attr name="core___color__primary_variant__disabled" format="color|reference"/>

    <attr name="core___color__primary_variant__stateless" format="color|reference"/>
    <attr name="core___color__primary_variant__stateful" format="color|reference"/>

    <!--endregion Color primary variant-->

    <!--region Color on primary-->

    <attr name="core___color__on_primary__default" format="color|reference"/>
    <attr name="core___color__on_primary__disabled" format="color|reference"/>

    <attr name="core___color__on_primary__stateless" format="color|reference"/>
    <attr name="core___color__on_primary__stateful" format="color|reference"/>

    <!--endregion Color on primary-->

    <!--region Color secondary-->

    <attr name="core___color__secondary__default" format="color|reference"/>
    <attr name="core___color__secondary__disabled" format="color|reference"/>

    <attr name="core___color__secondary__stateless" format="color|reference"/>
    <attr name="core___color__secondary__stateful" format="color|reference"/>

    <!--endregion Color secondary-->

    <!--region Color secondary variant-->

    <attr name="core___color__secondary_variant__default" format="color|reference"/>
    <attr name="core___color__secondary_variant__disabled" format="color|reference"/>

    <attr name="core___color__secondary_variant__stateless" format="color|reference"/>
    <attr name="core___color__secondary_variant__stateful" format="color|reference"/>

    <!--endregion Color secondary variant-->

    <!--region Color on secondary-->

    <attr name="core___color__on_secondary__default" format="color|reference"/>
    <attr name="core___color__on_secondary__disabled" format="color|reference"/>

    <attr name="core___color__on_secondary__stateless" format="color|reference"/>
    <attr name="core___color__on_secondary__stateful" format="color|reference"/>

    <!--endregion Color on secondary-->

    <!--region Color surface-->

    <attr name="core___color__surface__default" format="color|reference"/>
    <attr name="core___color__surface__disabled" format="color|reference"/>

    <attr name="core___color__surface__stateless" format="color|reference"/>
    <attr name="core___color__surface__stateful" format="color|reference"/>

    <!--endregion Color surface-->

    <!--region Color on surface-->

    <attr name="core___color__on_surface__default" format="color|reference"/>
    <attr name="core___color__on_surface__disabled" format="color|reference"/>

    <attr name="core___color__on_surface__stateless" format="color|reference"/>
    <attr name="core___color__on_surface__stateful" format="color|reference"/>

    <!--endregion Color on surface-->

    <!--region Color background-->

    <attr name="core___color__background__default" format="color|reference"/>
    <attr name="core___color__background__disabled" format="color|reference"/>

    <attr name="core___color__background__stateless" format="color|reference"/>
    <attr name="core___color__background__stateful" format="color|reference"/>

    <!--endregion Color background-->

    <!--region Color on background-->

    <attr name="core___color__on_background__default" format="color|reference"/>
    <attr name="core___color__on_background__disabled" format="color|reference"/>

    <attr name="core___color__on_background__stateless" format="color|reference"/>
    <attr name="core___color__on_background__stateful" format="color|reference"/>

    <!--endregion Color on background-->

    <!--region Color error-->

    <attr name="core___color__error__default" format="color|reference"/>
    <attr name="core___color__error__disabled" format="color|reference"/>

    <attr name="core___color__error__stateless" format="color|reference"/>
    <attr name="core___color__error__stateful" format="color|reference"/>

    <!--endregion Color error-->

    <!--region Color on error-->

    <attr name="core___color__on_error__default" format="color|reference"/>
    <attr name="core___color__on_error__disabled" format="color|reference"/>

    <attr name="core___color__on_error__stateless" format="color|reference"/>
    <attr name="core___color__on_error__stateful" format="color|reference"/>

    <!--endregion Color on error-->

    <style name="Core.Theme.Light.NoActionBar" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="core___color__primary__stateless">?core___color__primary__default</item>
        <item name="core___color__primary__stateful">@color/core___primary__stateful</item>

        <item name="core___color__primary_variant__stateless">
            ?core___color__primary_variant__default
        </item>
        <item name="core___color__primary_variant__stateful">
            @color/core___primary_variant__stateful
        </item>

        <item name="core___color__on_primary__stateless">?core___color__on_primary__default</item>
        <item name="core___color__on_primary__stateful">@color/core___on_primary__stateful</item>

        <item name="core___color__secondary__stateless">?core___color__secondary__default</item>
        <item name="core___color__secondary__stateful">@color/core___secondary__stateful</item>

        <item name="core___color__secondary_variant__stateless">
            ?core___color__secondary_variant__default
        </item>
        <item name="core___color__secondary_variant__stateful">
            @color/core___secondary_variant__stateful
        </item>

        <item name="core___color__on_secondary__stateless">?core___color__on_secondary__default
        </item>
        <item name="core___color__on_secondary__stateful">@color/core___on_secondary__stateful
        </item>

        <item name="core___color__surface__stateless">?core___color__surface__default</item>
        <item name="core___color__surface__stateful">@color/core___surface__stateful</item>

        <item name="core___color__on_surface__stateless">?core___color__on_surface__default</item>
        <item name="core___color__on_surface__stateful">@color/core___on_surface__stateful</item>

        <item name="core___color__background__stateless">?core___color__background__default</item>
        <item name="core___color__background__stateful">@color/core___background__stateful</item>

        <item name="core___color__on_background__stateless">?core___color__on_background__default
        </item>
        <item name="core___color__on_background__stateful">@color/core___on_background__stateful
        </item>

        <item name="core___color__error__stateless">?core___color__error__default</item>
        <item name="core___color__error__stateful">@color/core___error__stateful</item>

        <item name="core___color__on_error__stateless">?core___color__on_error__default</item>
        <item name="core___color__on_error__stateful">@color/core___on_error__stateful</item>

        <!--region Material 2.0 support-->
        <!--See [https://github.com/material-components/material-components-android/blob/master/docs/theming/Color.md] for more info.-->

        <item name="colorPrimary">?core___color__primary__default</item>
        <item name="colorPrimaryVariant">?core___color__primary_variant__default</item>
        <item name="colorOnPrimary">?core___color__on_primary__default</item>

        <item name="colorSecondary">?core___color__secondary__default</item>
        <item name="colorSecondaryVariant">?core___color__secondary_variant__default</item>

        <item name="colorOnSecondary">?core___color__on_secondary__default</item>

        <!--BUGGED!!! <item name="android:colorBackground">?core___color__background__default</item>-->
        <item name="colorOnBackground">?core___color__on_background__default</item>

        <item name="colorSurface">?core___color__surface__default</item>
        <item name="colorOnSurface">?core___color__on_surface__default</item>

        <item name="colorError">?core___color__error__default</item>
        <item name="colorOnError">?core___color__on_error__default</item>

        <!--endregion Material 2.0 support-->

        <!--region Legacy Material-->

        <!--BUGGED!!! <item name="android:windowBackground">?core___color__background__default</item>-->
        <item name="colorAccent">?core___color__primary__default</item>
        <item name="colorPrimaryDark">?core___color__primary_variant__default</item>

        <!--endregion Legacy Material-->
    </style>

    <style name="Core.Overlay.Toolbar" parent="ThemeOverlay.MaterialComponents.Toolbar.Primary">
        <item name="tint">?core___color__on_primary__stateful</item>
        <item name="drawableTint">?core___color__on_primary__stateful</item>
        <item name="colorControlNormal">?core___color__on_primary__stateful</item>
    </style>

    <style name="Core.Toolbar" parent="">
        <item name="titleTextColor">?core___color__on_primary__stateless</item>
    </style>
</resources>