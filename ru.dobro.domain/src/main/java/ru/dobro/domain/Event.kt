package ru.dobro.domain

import android.net.Uri
import common.library.core.entity.StatefulAbstractEntity
import kotlinx.collections.immutable.PersistentList
import ru.dobro.domain.overview.VacancyStatus
import java.time.ZonedDateTime
import javax.annotation.CheckReturnValue

class Event
private constructor(data: EventData) :
    StatefulAbstractEntity<EventId, EventData, Event>(data) {
    @CheckReturnValue
    override fun create(state: EventData): Event = Event(state)

    override val identity: EventId get() = internalState.eventId
    val title: String get() = internalState.title

    val image: Uri get() = internalState.image
    val status: VacancyStatus get() = internalState.status
    val address: Location? get() = internalState.address
    val categories: PersistentList<Category> = internalState.categories
    val eventDuration: ClosedRange<ZonedDateTime>? get() = internalState.eventDuration
    val eventDurationTitle: String? get() = internalState.eventDurationTitle
    val organizationPhone: String? get() = internalState.organizationPhone
    val organizationEmail: String? get() = internalState.organizationEmail
    val isVerified: Boolean get() = internalState.isVerified
    val ageCondition: String get() = internalState.ageCondition
    val isLarge: Boolean get() = internalState.isLarge

    companion object {
        @CheckReturnValue
        fun restore(data: EventData): Event = Event(data)

        @CheckReturnValue
        fun restore(
            id: EventId,
            title: String,
            image: Uri,
            status: VacancyStatus,
            address: Location?,
            categories: PersistentList<Category>,
            eventDuration: ClosedRange<ZonedDateTime>?,
            eventDurationTitle: String?,
            organizationPhone: String?,
            organizationEmail: String?,
            isVerified: Boolean,
            ageCondition: String,
            isLarge: Boolean,
        ): Event =
            restore(
                EventData(
                    eventId = id,
                    title = title,
                    image = image,
                    status = status,
                    address = address,
                    categories = categories,
                    eventDuration = eventDuration,
                    eventDurationTitle = eventDurationTitle,
                    organizationPhone = organizationPhone,
                    organizationEmail = organizationEmail,
                    isVerified = isVerified,
                    ageCondition = ageCondition,
                    isLarge = isLarge
                )
            )
    }
}
