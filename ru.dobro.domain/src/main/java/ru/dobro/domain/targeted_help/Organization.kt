package ru.dobro.domain.targeted_help

import android.net.Uri
import common.library.core.entity.StatefulAbstractEntity
import ru.dobro.domain.OrganizerId
import javax.annotation.CheckReturnValue
import ru.dobro.domain.OrganizationId as DobroOrganizationId

class Organization
private constructor(data: OrganizationData) :
    StatefulAbstractEntity<OrganizationId, OrganizationData, Organization>(data) {
    @CheckReturnValue
    override fun create(state: OrganizationData): Organization = Organization(state)

    override val identity: OrganizationId get() = internalState.id
    val shortName: String? get() = internalState.shortName
    val logo: Uri get() = internalState.logo
    val fullName: String get() = internalState.fullName
    val rating: Float? get() = internalState.rating
    val dobroOrganizationId: DobroOrganizationId get() = internalState.dobroOrganizationId
    val dobroOrganizerId: OrganizerId get() = internalState.dobroOrganizerId
    val settlement: String get() = internalState.settlement
    val volunteersCount: Int get() = internalState.volunteersCount
    val isJoined: Boolean get() = internalState.isJoined

    companion object {
        @CheckReturnValue
        fun restore(data: OrganizationData): Organization = Organization(data)

        @CheckReturnValue
        fun restore(
            id: OrganizationId,
            shortName: String?,
            logo: Uri,
            fullName: String,
            rating: Float?,
            dobroOrganizationId: DobroOrganizationId,
            dobroOrganizerId: OrganizerId,
            settlement: String,
            volunteersCount: Int,
            isJoined: Boolean
        ): Organization =
            restore(
                OrganizationData(
                    id = id,
                    shortName = shortName,
                    logo = logo,
                    fullName = fullName,
                    rating = rating,
                    dobroOrganizationId = dobroOrganizationId,
                    dobroOrganizerId = dobroOrganizerId,
                    settlement = settlement,
                    volunteersCount = volunteersCount,
                    isJoined = isJoined
                )
            )
    }
}
