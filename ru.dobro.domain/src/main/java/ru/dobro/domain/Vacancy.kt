package ru.dobro.domain

import common.library.core.entity.StatefulAbstractEntity
import kotlinx.collections.immutable.PersistentList
import java.time.LocalDateTime
import javax.annotation.CheckReturnValue

class Vacancy
private constructor(data: VacancyData) :
    StatefulAbstractEntity<VacancyId, VacancyData, Vacancy>(data) {
    @CheckReturnValue
    override fun create(state: VacancyData): Vacancy = Vacancy(state)

    override val identity: VacancyId get() = internalState.id
    val title: String get() = internalState.title

    // TODO nullable пока бэк не пофиксят
    val organizer: Organizer? get() = internalState.organizer
    val categories: PersistentList<Category> get() = internalState.categories
    val requirements: PersistentList<Requirement> get() = internalState.requirements
    val previewRequirements: PersistentList<RequirementPreview> get() = internalState.previewRequirements
    val address: Location? get() = internalState.address
    val period: ClosedRange<LocalDateTime> get() = internalState.period
    val participant: Boolean get() = internalState.participant
    val description: String get() = internalState.description

    companion object {
        @CheckReturnValue
        fun restore(data: VacancyData): Vacancy = Vacancy(data)

        @CheckReturnValue
        fun restore(
            id: VacancyId,
            title: String,
            organizer: Organizer?,
            categories: PersistentList<Category>,
            requirements: PersistentList<Requirement>,
            previewRequirements: PersistentList<RequirementPreview>,
            address: Location?,
            period: ClosedRange<LocalDateTime>,
            participant: Boolean,
            description: String
        ): Vacancy =
            restore(
                VacancyData(
                    id = id,
                    title = title,
                    organizer = organizer,
                    categories = categories,
                    requirements = requirements,
                    previewRequirements = previewRequirements,
                    address = address,
                    period = period,
                    participant = participant,
                    description = description
                )
            )
    }
}
