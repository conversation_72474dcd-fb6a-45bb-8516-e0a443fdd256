package ru.dobro.domain

import android.net.Uri
import common.library.core.entity.StatefulAbstractEntity
import javax.annotation.CheckReturnValue

class NotificationSetting
private constructor(data: NotificationSettingData) :
    StatefulAbstractEntity<NotificationType, NotificationSettingData, NotificationSetting>(data) {
    @CheckReturnValue
    override fun create(state: NotificationSettingData): NotificationSetting =
        NotificationSetting(state)

    override val identity: NotificationType get() = internalState.type
    val title: String get() = internalState.title
    val subtitle: String? get() = internalState.subtitle
    val avatar: Uri get() = internalState.avatar
    val isChecked: Boolean get() = internalState.isChecked

    companion object {
        @CheckReturnValue
        fun restore(data: NotificationSettingData): NotificationSetting = NotificationSetting(data)

        @CheckReturnValue
        fun restore(
            type: NotificationType,
            title: String,
            subtitle: String?,
            avatar: U<PERSON>,
            isChecked: Boolean
        ): NotificationSetting =
            restore(
                NotificationSettingData(
                    type = type,
                    title = title,
                    subtitle = subtitle,
                    avatar = avatar,
                    isChecked = isChecked
                )
            )
    }
}
