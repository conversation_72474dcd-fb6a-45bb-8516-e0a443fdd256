package ru.dobro.domain

import android.os.Parcelable
import common.library.core.location.GeoPoint
import common.library.core.value.core.StatefulAbstractValue
import kotlinx.parcelize.Parcelize
import javax.annotation.CheckReturnValue

@Parcelize
data class Location(val data: LocationData) : StatefulAbstractValue<LocationData, Location>(data),
    Parcelable {

    override fun create(state: LocationData): Location = Location(state)

    val title: String get() = internalState.title
    val rawTitle: String? get() = internalState.rawTitle
    val settlement: String? get() = internalState.settlement
    val settlementCode: String? get() = internalState.settlementCode
    val country: String? get() = internalState.country
    val countryISO: String? get() = internalState.countryISO
    val municipality: String? get() = internalState.municipality
    val municipalityCode: String? get() = internalState.municipalityCode
    val cityAlternative: String? get() = internalState.cityAlternative
    val flat: String? get() = internalState.flat
    val house: String? get() = internalState.house
    val street: String? get() = internalState.street
    val region: String? get() = internalState.region
    val coordinates: GeoPoint.Coordinates get() = internalState.coordinates
    val fiasId: FiasId? get() = internalState.fiasId
    val cityFiasId: FiasId? get() = internalState.cityFiasId
    val streetFiasId: FiasId? get() = internalState.streetFiasId
    val shortName: String? get() = internalState.shortName

    fun isRussia(): Boolean = (country == "Россия") ||
        (country == null && title.substringBefore(',').trim() == "Россия")

    fun isOnline(): Boolean = title == "Онлайн"

    companion object {
        @CheckReturnValue
        fun restore(data: LocationData) = Location(data)

        @CheckReturnValue
        fun restore(
            title: String,
            rawTitle: String?,
            settlement: String?,
            settlementCode: String?,
            country: String? = null,
            countryISO: String? = null,
            municipality: String? = null,
            municipalityCode: String? = null,
            cityAlternative: String? = null,
            flat: String? = null,
            house: String? = null,
            cityFiasId: FiasId? = null,
            streetFiasId: FiasId? = null,
            street: String? = null,
            region: String? = null,
            coordinates: GeoPoint.Coordinates,
            fiasId: FiasId? = null,
            shortName: String? = null,
        ) = restore(
            LocationData(
                title = title,
                rawTitle = rawTitle,
                coordinates = coordinates,
                settlement = settlement,
                settlementCode = settlementCode,
                country = country,
                countryISO = countryISO,
                municipality = municipality,
                municipalityCode = municipalityCode,
                cityAlternative = cityAlternative,
                flat = flat,
                house = house,
                street = street,
                region = region,
                fiasId = fiasId,
                cityFiasId = cityFiasId,
                streetFiasId = streetFiasId,
                shortName = shortName
            )
        )

        fun restoreRussiaCountry(): Location {
            return restore(
                title = "Россия",
                rawTitle = "Россия",
                settlement = "Россия",
                settlementCode = null,
                coordinates = GeoPoint.Coordinates(
                    latitude = 0.0,
                    longitude = 0.0
                ),
                country = "Россия",
                countryISO = "RU",
                municipality = null,
                municipalityCode = null,
                cityAlternative = null,
                flat = null,
                house = null,
                street = null,
                region = null,
                fiasId = null,
                cityFiasId = null,
                streetFiasId = null,
                shortName = null
            )
        }

        fun restorePopularCities(): List<Location> {
            return listOf(
                generateRussiaCountry(
                    title = "Москва",
                    settlement = "Москва",
                    coordinates = GeoPoint.Coordinates(
                        latitude = 55.75396,
                        longitude = 37.620393
                    ),
                    cityAlternative = "Москва",
                    region = "7700000000000",
                    fiasId = "0c5b2444-70a0-4932-980c-b4dc0d3f02b5",
                    cityFiasId = "0c5b2444-70a0-4932-980c-b4dc0d3f02b5",
                    kladrId = "7700000000000"
                ),
                generateRussiaCountry(
                    title = "Санкт-Петербург",
                    settlement = "Санкт-Петербург",
                    coordinates = GeoPoint.Coordinates(
                        latitude = 59.939084,
                        longitude = 30.315879
                    ),
                    cityAlternative = "Санкт-Петербург",
                    region = "7800000000000",
                    fiasId = "c2deb16a-0330-4f05-821f-1d09c93331e6",
                    cityFiasId = "c2deb16a-0330-4f05-821f-1d09c93331e6",
                    kladrId = "7800000000000"
                ),
                generateRussiaCountry(
                    title = "Екатеринбург",
                    settlement = "Екатеринбург",
                    coordinates = GeoPoint.Coordinates(
                        latitude = 56.838607,
                        longitude = 60.605514
                    ),
                    cityAlternative = "Екатеринбург",
                    region = "6600000000000",
                    fiasId = "2763c110-cb8b-416a-9dac-ad28a55b4402",
                    cityFiasId = "2763c110-cb8b-416a-9dac-ad28a55b4402",
                    kladrId = "6600000100000"
                ),
                generateRussiaCountry(
                    title = "Нижний Новгород",
                    settlement = "Нижний Новгород",
                    coordinates = GeoPoint.Coordinates(
                        latitude = 56.324133,
                        longitude = 44.005299
                    ),
                    cityAlternative = "Нижний Новгород",
                    region = "5200000000000",
                    fiasId = "555e7d61-d9a7-4ba6-9770-6caa8198c483",
                    cityFiasId = "555e7d61-d9a7-4ba6-9770-6caa8198c483",
                    kladrId = "5200000100000"
                ),
                generateRussiaCountry(
                    title = "Пермь",
                    settlement = "Пермь",
                    coordinates = GeoPoint.Coordinates(
                        latitude = 58.010259,
                        longitude = 56.234195
                    ),
                    cityAlternative = "Пермь",
                    region = "5900000000000",
                    fiasId = "a309e4ce-2f36-4106-b1ca-53e0f48a6d95",
                    cityFiasId = "a309e4ce-2f36-4106-b1ca-53e0f48a6d95",
                    kladrId = "5900000100000"
                ),
                generateRussiaCountry(
                    title = "Новосибирск",
                    settlement = "Новосибирск",
                    coordinates = GeoPoint.Coordinates(
                        latitude = 55.028141,
                        longitude = 82.921117
                    ),
                    cityAlternative = "Новосибирск",
                    region = "5400000000000",
                    fiasId = "8dea00e3-9aab-4d8e-887c-ef2aaa546456",
                    cityFiasId = "8dea00e3-9aab-4d8e-887c-ef2aaa546456",
                    kladrId = "5400000100000"
                ),
                generateRussiaCountry(
                    title = "Казань",
                    settlement = "Казань",
                    coordinates = GeoPoint.Coordinates(
                        latitude = 55.794438,
                        longitude = 49.111451
                    ),
                    cityAlternative = "Казань",
                    region = "1600000000000",
                    fiasId = "93b3df57-4c89-44df-ac42-96f05e9cd3b9",
                    cityFiasId = "93b3df57-4c89-44df-ac42-96f05e9cd3b9",
                    kladrId = "1600000100000"
                ),
                generateRussiaCountry(
                    title = "Челябинск",
                    settlement = "Челябинск",
                    coordinates = GeoPoint.Coordinates(
                        latitude = 55.160283,
                        longitude = 61.400856
                    ),
                    cityAlternative = "Челябинск",
                    region = "7400000000000",
                    fiasId = "a376e68d-724a-4472-be7c-891bdb09ae32",
                    cityFiasId = "a376e68d-724a-4472-be7c-891bdb09ae32",
                    kladrId = "7400000100000"
                ),
                generateRussiaCountry(
                    title = "Краснодар",
                    settlement = "Краснодар",
                    coordinates = GeoPoint.Coordinates(
                        latitude = 45.040216,
                        longitude = 38.975996
                    ),
                    cityAlternative = "Краснодар",
                    region = "2300000000000",
                    fiasId = "7dfa745e-aa19-4688-b121-b655c11e482f",
                    cityFiasId = "7dfa745e-aa19-4688-b121-b655c11e482f",
                    kladrId = "2300000100000"
                ),
            )
        }

        private fun generateRussiaCountry(
            title: String,
            settlement: String,
            coordinates: GeoPoint.Coordinates,
            cityAlternative: String,
            region: String,
            fiasId: String,
            cityFiasId: String,
            kladrId: String
        ): Location {
            return restore(
                title = title,
                rawTitle = title,
                settlement = settlement,
                settlementCode = kladrId,
                coordinates = coordinates,
                country = "Россия",
                countryISO = "RU",
                municipality = null,
                municipalityCode = null,
                cityAlternative = cityAlternative,
                flat = null,
                house = null,
                street = null,
                region = region,
                fiasId = FiasId.restore(fiasId),
                cityFiasId = FiasId.restore(cityFiasId),
                streetFiasId = null,
                shortName = null
            )
        }

        val defaultLocation: Location =
            restore(
                title = "г Москва",
                rawTitle = null,
                settlement = "г Москва",
                settlementCode = "102",
                coordinates = GeoPoint.Coordinates(55.753869, 37.620159),
                country = "Россия",
            )
    }
}
