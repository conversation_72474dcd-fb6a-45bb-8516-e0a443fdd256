package ru.dobro.domain

import common.library.core.entity.StatefulAbstractEntity
import common.library.core.value.wrapped.WrappedCompatValue
import javax.annotation.CheckReturnValue

class OrganizationType
private constructor(data: OrganizationTypeData) :
    StatefulAbstractEntity<OrganizerTypeId, OrganizationTypeData, OrganizationType>(data) {
    @CheckReturnValue
    override fun create(state: OrganizationTypeData): OrganizationType = OrganizationType(state)

    override val identity = internalState.id
    val title: String get() = internalState.title

    companion object {
        @CheckReturnValue
        fun restore(data: OrganizationTypeData): OrganizationType = OrganizationType(data)

        @CheckReturnValue
        fun restore(
            id: Long,
            title: String,
        ): OrganizationType =
            restore(
                OrganizationTypeData(
                    id = OrganizerTypeId.restore(id),
                    title = title,
                )
            )
    }
}

data class OrganizationTypeData(
    val id: OrganizerTypeId,
    val title: String,
)

class OrganizerTypeId
private constructor(value: Long) : WrappedCompatValue<Long, OrganizerTypeId>(value) {
    companion object {
        @CheckReturnValue
        fun restore(value: Long): OrganizerTypeId = OrganizerTypeId(value)
    }
}
