package ru.dobro.domain

import common.library.core.entity.StatefulAbstractEntity
import javax.annotation.CheckReturnValue

class HelpType
private constructor(data: HelpTypeData) :
    StatefulAbstractEntity<HelpTypeId, HelpTypeData, HelpType>(data) {
    @CheckReturnValue
    override fun create(state: HelpTypeData): HelpType = HelpType(state)

    override val identity: HelpTypeId get() = internalState.id
    val title: String get() = internalState.title

    companion object {
        @CheckReturnValue
        fun restore(data: HelpTypeData): HelpType = HelpType(data)

        @CheckReturnValue
        fun restore(
            id: HelpTypeId,
            title: String
        ): HelpType =
            restore(
                HelpTypeData(
                    id = id,
                    title = title
                )
            )
    }
}
