package ru.dobro.domain

import common.library.core.entity.StatefulAbstractEntity
import javax.annotation.CheckReturnValue

class TypeOfEmployment
private constructor(data: TypeOfEmploymentData) :
    StatefulAbstractEntity<TypeOfEmploymentId, TypeOfEmploymentData, TypeOfEmployment>(data) {
    @CheckReturnValue
    override fun create(state: TypeOfEmploymentData): TypeOfEmployment = TypeOfEmployment(state)

    override val identity: TypeOfEmploymentId get() = internalState.id

    val title: String get() = internalState.title

    companion object {
        @CheckReturnValue
        fun restore(data: TypeOfEmploymentData): TypeOfEmployment = TypeOfEmployment(data)

        @CheckReturnValue
        fun restore(
            id: TypeOfEmploymentId,
            title: String
        ): TypeOfEmployment =
            restore(
                TypeOfEmploymentData(
                    id = id,
                    title = title
                )
            )
    }
}
