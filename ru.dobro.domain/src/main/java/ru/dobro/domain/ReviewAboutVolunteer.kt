package ru.dobro.domain

import common.library.core.value.core.StatefulAbstractValue
import java.time.ZonedDateTime
import javax.annotation.CheckReturnValue

class ReviewAboutVolunteer
private constructor(data: ReviewAboutVolunteerData) :
    StatefulAbstractValue<ReviewAboutVolunteerData, ReviewAboutVolunteer>(data) {
    @CheckReturnValue
    override fun create(state: ReviewAboutVolunteerData): ReviewAboutVolunteer =
        ReviewAboutVolunteer(state)

    val instance: ReviewAboutVolunteerData get() = internalState

    companion object {
        @CheckReturnValue
        fun restore(data: ReviewAboutVolunteerData): ReviewAboutVolunteer =
            ReviewAboutVolunteer(data)

        @CheckReturnValue
        fun restore(
            id: Long,
            title: String,
            volunteerCanReply: Boolean,
            volunteerRating: Float,
            volunteerComment: String,
            volunteerRatingDate: ZonedDateTime?,
            volunteerReplyDate: ZonedDateTime?,
            volunteerReplyComment: String,
            event: Event,
            vacancy: String,
            organizer: ReviewAboutVolunteerData.Organizer
        ): ReviewAboutVolunteer =
            restore(
                ReviewAboutVolunteerData(
                    id = id,
                    title = title,
                    volunteerCanReply = volunteerCanReply,
                    volunteerRating = volunteerRating,
                    volunteerComment = volunteerComment,
                    volunteerRatingDate = volunteerRatingDate,
                    volunteerReplyDate = volunteerReplyDate,
                    volunteerReplyComment = volunteerReplyComment,
                    event = event,
                    vacancy = vacancy,
                    organizer = organizer
                )
            )
    }
}
