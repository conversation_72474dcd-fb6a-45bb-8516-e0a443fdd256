package ru.dobro.domain

import android.net.Uri
import common.library.core.value.core.StatefulAbstractValue
import javax.annotation.CheckReturnValue

class VideoFile
private constructor(data: VideoFileData) :
    StatefulAbstractValue<VideoFileData, VideoFile>(data) {
    @CheckReturnValue
    override fun create(state: VideoFileData): VideoFile = VideoFile(state)

    val uri: Uri get() = internalState.uri
    val previewUri: Uri get() = internalState.previewUri

    companion object {
        @CheckReturnValue
        fun restore(data: VideoFileData): VideoFile = VideoFile(data)

        @CheckReturnValue
        fun restore(
            uri: Uri,
            previewUri: Uri
        ): VideoFile =
            restore(
                VideoFileData(
                    uri = uri,
                    previewUri = previewUri
                )
            )
    }
}
