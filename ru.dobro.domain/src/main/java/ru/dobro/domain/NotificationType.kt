package ru.dobro.domain

import common.library.core.EqualityComparable
import javax.annotation.CheckReturnValue

enum class NotificationType : EqualityComparable<NotificationType> {
    VolunteerVacancyRequestApproved,
    VolunteerVacancyRequestRejected,
    VolunteerVacancyRequestReserved,
    VolunteerUpcomingEventReminder,
    VolunteerNewEvent,
    EventTimeStarted,
    EventTimeEnded,
    NonAccepted;

    @CheckReturnValue
    override fun equalTo(other: NotificationType?): Boolean = this == other
}
