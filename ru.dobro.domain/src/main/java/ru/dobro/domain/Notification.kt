package ru.dobro.domain

import android.net.Uri
import common.library.core.value.core.StatefulAbstractValue
import java.time.ZonedDateTime
import javax.annotation.CheckReturnValue

class Notification
private constructor(data: NotificationData) :
    StatefulAbstractValue<NotificationData, Notification>(data) {
    @CheckReturnValue
    override fun create(state: NotificationData): Notification = Notification(state)

    val title: String get() = internalState.title
    val heading: String? get() = internalState.heading
    val icon: Uri get() = internalState.icon
    val url: Uri get() = internalState.uri
    val avatar: Uri get() = internalState.avatar
    val createAt: ZonedDateTime get() = internalState.createAt
    val entityData: EntityData get() = internalState.entityData

    companion object {
        @CheckReturnValue
        fun restore(data: NotificationData): Notification = Notification(data)

        @CheckReturnValue
        fun restore(
            title: String,
            heading: String?,
            icon: Uri,
            avatar: Uri,
            createAt: ZonedDateTime,
            entityData: EntityData,
            url: Uri
        ): Notification =
            restore(
                NotificationData(
                    title = title,
                    heading = heading,
                    icon = icon,
                    avatar = avatar,
                    createAt = createAt,
                    entityData = entityData,
                    uri = url
                )
            )
    }
}
