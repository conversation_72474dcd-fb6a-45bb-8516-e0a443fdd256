package ru.dobro.domain

import common.library.core.value.wrapped.WrappedCompatValue
import kotlinx.collections.immutable.PersistentSet
import javax.annotation.CheckReturnValue

class TagId
private constructor(value: Long) : WrappedCompatValue<Long, TagId>(value) {
    companion object {
        @CheckReturnValue
        fun restore(value: Long): TagId = TagId(value)
    }
}

enum class SpecialTagId(val id: Long) {
    Verified(-100L),
    NotAdultOnly(-200L),
    Reserve(-300L),
    Participant(-400L),
    Standard(-500L)
}

fun getSimpleTags(tags: PersistentSet<TagId>?): List<Long> {
    val map = mutableListOf<Pair<String, Long>>()
    tags?.forEach {
        when (it.unwrap()) {
            SpecialTagId.Verified.id,
            SpecialTagId.NotAdultOnly.id,
            SpecialTagId.Reserve.id,
            SpecialTagId.Participant.id,
            SpecialTagId.Standard.id -> { /* No need */
            }

            else -> {
                map.add("e[tags]" to it.unwrap())
            }
        }
    }
    return map.map {
        it.second
    }
}

fun getSpecialTagsMap(tags: PersistentSet<TagId>?): Map<String, Int> {
    val map = mutableMapOf<String, Int>()
    tags?.forEach {
        when (it.unwrap()) {
            SpecialTagId.Verified.id -> {
                map["e[verified]"] = 1
            }

            SpecialTagId.NotAdultOnly.id -> {
                map["e[notAdultOnly]"] = 1
            }

            SpecialTagId.Reserve.id -> {
                map["e[reserve]"] = 1
            }

            SpecialTagId.Participant.id -> {
                map["e[participant]"] = 1
            }

            SpecialTagId.Standard.id -> {
                map["e[standard]"] = 1
            }

            else -> { /* No need */
            }
        }
    }
    return map
}
