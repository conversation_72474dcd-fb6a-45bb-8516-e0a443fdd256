package ru.dobro.domain

import android.net.Uri
import common.library.core.entity.StatefulAbstractEntity
import ru.dobro.domain.profile.achievements.trophy.OrganizerTrophy
import javax.annotation.CheckReturnValue

class Organizer
private constructor(data: OrganizerData) :
    StatefulAbstractEntity<OrganizerId, OrganizerData, Organizer>(data) {
    @CheckReturnValue
    override fun create(state: OrganizerData): Organizer = Organizer(state)

    override val identity: OrganizerId get() = internalState.id
    val name: String get() = internalState.name

    // TODO ждем фикс на бэке
    val icon: Uri? get() = internalState.icon
    val fullName: String get() = internalState.fullName
    val ogrn: String get() = internalState.ogrn
    val volunteersCount: Int get() = internalState.volunteersCount
    val rating: Float? get() = internalState.rating
    val acceptedVacancyRequestsCount: Int get() = internalState.acceptedVacancyRequestsCount
    val organizationId: OrganizationId get() = internalState.organizationId
    val statistic: OrganizerStatistic get() = internalState.statistic
    val isRegionalNetwork: Boolean get() = internalState.isRegionalNetwork
    val isVerified: Boolean get() = internalState.isVerified
    val trophies: List<OrganizerTrophy> get() = internalState.trophies
    val type: OrganizerType get() = internalState.type
    val alreadyAssist: Boolean get() = internalState.alreadyAssist

    companion object {
        @CheckReturnValue
        fun restore(data: OrganizerData): Organizer = Organizer(data)

        @CheckReturnValue
        fun restore(
            id: OrganizerId,
            name: String,
            icon: Uri?,
            fullName: String,
            volunteersCount: Int,
            rating: Float?,
            acceptedVacancyRequestsCount: Int,
            organizationId: OrganizationId,
            statistic: OrganizerStatistic,
            isRegionalNetwork: Boolean,
            isVerified: Boolean,
            trophies: List<OrganizerTrophy>,
            ogrn: String,
            type: OrganizerType,
            alreadyAssist: Boolean,
        ): Organizer =
            restore(
                OrganizerData(
                    id = id,
                    name = name,
                    icon = icon,
                    fullName = fullName,
                    volunteersCount = volunteersCount,
                    rating = rating,
                    acceptedVacancyRequestsCount = acceptedVacancyRequestsCount,
                    organizationId = organizationId,
                    statistic = statistic,
                    isRegionalNetwork = isRegionalNetwork,
                    trophies = trophies,
                    isVerified = isVerified,
                    ogrn = ogrn,
                    type = type,
                    alreadyAssist = alreadyAssist,
                )
            )

        @CheckReturnValue
        fun restoreEmpty(): Organizer =
            restore(
                OrganizerData(
                    id = OrganizerId.restore(0),
                    name = "Организация",
                    icon = Uri.parse(""),
                    fullName = "fullName",
                    volunteersCount = 0,
                    rating = 0f,
                    acceptedVacancyRequestsCount = 0,
                    organizationId = OrganizationId.restore(0),
                    statistic = OrganizerStatistic(
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null
                    ),
                    isRegionalNetwork = false,
                    trophies = listOf(OrganizerTrophy.restore(0, "", "", null, 0)),
                    isVerified = false,
                    ogrn = "ogrn",
                    type = OrganizerType.Volunteer,
                    alreadyAssist = false,
                )
            )
    }
}

fun Organizer.copy(
    name: String = this.name,
    icon: Uri? = this.icon,
    fullName: String = this.fullName,
    ogrn: String = this.ogrn,
    volunteersCount: Int = this.volunteersCount,
    rating: Float? = this.rating,
    acceptedVacancyRequestsCount: Int = this.acceptedVacancyRequestsCount,
    organizationId: OrganizationId = this.organizationId,
    statistic: OrganizerStatistic = this.statistic,
    isRegionalNetwork: Boolean = this.isRegionalNetwork,
    isVerified: Boolean = this.isVerified,
    trophies: List<OrganizerTrophy> = this.trophies,
    type: OrganizerType = this.type,
    alreadyAssist: Boolean = this.alreadyAssist,
): Organizer {
    return Organizer.restore(
        id = this.identity,
        name = name,
        icon = icon,
        fullName = fullName,
        ogrn = ogrn,
        volunteersCount = volunteersCount,
        rating = rating,
        acceptedVacancyRequestsCount = acceptedVacancyRequestsCount,
        organizationId = organizationId,
        statistic = statistic,
        isRegionalNetwork = isRegionalNetwork,
        isVerified = isVerified,
        trophies = trophies,
        type = type,
        alreadyAssist = alreadyAssist,
    )
}
