package ru.dobro.domain

import android.net.Uri
import common.library.core.entity.StatefulAbstractEntity
import javax.annotation.CheckReturnValue

class Organization
private constructor(data: OrganizationData) :
    StatefulAbstractEntity<OrganizationId, OrganizationData, Organization>(data) {
    @CheckReturnValue
    override fun create(state: OrganizationData): Organization = Organization(state)

    override val identity: OrganizationId get() = internalState.id
    val name: String get() = internalState.name

    // TODO ждем фикс на бэке
    val icon: Uri? get() = internalState.icon
    val settlement: String? get() = internalState.settlement
    val isJoined: <PERSON><PERSON><PERSON> get() = internalState.isJoined
    val organizer: Organizer get() = internalState.organizer

    companion object {
        @CheckReturnValue
        fun restore(data: OrganizationData): Organization = Organization(data)

        @CheckReturnValue
        fun restore(
            id: OrganizationId,
            name: String,
            icon: Uri?,
            settlement: String?,
            isJoined: <PERSON><PERSON><PERSON>,
            organizer: Organizer
        ): Organization =
            restore(
                OrganizationData(
                    id = id,
                    name = name,
                    icon = icon,
                    organizer = organizer,
                    settlement = settlement,
                    isJoined = isJoined
                )
            )
    }
}
