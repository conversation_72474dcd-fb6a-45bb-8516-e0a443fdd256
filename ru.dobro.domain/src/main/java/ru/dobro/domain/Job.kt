package ru.dobro.domain

import common.library.core.value.core.StatefulAbstractValue
import javax.annotation.CheckReturnValue

class Job
private constructor(data: JobData) :
    StatefulAbstractValue<JobData, Job>(data) {
    @CheckReturnValue
    override fun create(state: JobData): Job = Job(state)

    val organization: String? get() = internalState.organization
    val post: String? get() = internalState.post

    companion object {
        @CheckReturnValue
        fun restore(data: JobData): Job = Job(data)

        @CheckReturnValue
        fun restore(
            organization: String? = null,
            post: String? = null
        ): Job =
            restore(
                JobData(
                    organization = organization,
                    post = post
                )
            )
    }
}
