package ru.dobro.domain

import common.library.core.entity.StatefulAbstractEntity
import javax.annotation.CheckReturnValue

class LanguageLevel
private constructor(data: LanguageLevelData) :
    StatefulAbstractEntity<LanguageLevelId, LanguageLevelData, LanguageLevel>(data) {
    @CheckReturnValue
    override fun create(state: LanguageLevelData): LanguageLevel = LanguageLevel(state)

    override val identity: LanguageLevelId get() = internalState.id

    val title: String get() = internalState.title

    companion object {
        @CheckReturnValue
        fun restore(data: LanguageLevelData): LanguageLevel = LanguageLevel(data)

        @CheckReturnValue
        fun restore(
            id: LanguageLevelId,
            title: String
        ): LanguageLevel =
            restore(
                LanguageLevelData(
                    id = id,
                    title = title
                )
            )
    }
}
