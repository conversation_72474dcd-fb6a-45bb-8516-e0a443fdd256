package ru.dobro.domain

import common.library.core.value.core.StatefulAbstractValue
import javax.annotation.CheckReturnValue

class Education
private constructor(data: EducationData) :
    StatefulAbstractValue<EducationData, Education>(data) {
    @CheckReturnValue
    override fun create(state: EducationData): Education = Education(state)

    val level: String? get() = internalState.level
    val institution: String? get() = internalState.institution
    val speciality: String? get() = internalState.speciality
    val fromYear: Int? get() = internalState.fromYear
    val toYear: Int? get() = internalState.toYear

    companion object {
        @CheckReturnValue
        fun restore(data: EducationData): Education = Education(data)

        @CheckReturnValue
        fun restore(
            level: String? = null,
            institution: String? = null,
            speciality: String? = null,
            fromYear: Int? = null,
            toYear: Int? = null
        ): Education =
            restore(
                EducationData(
                    level = level,
                    institution = institution,
                    speciality = speciality,
                    fromYear = fromYear,
                    toYear = toYear
                )
            )
    }
}
