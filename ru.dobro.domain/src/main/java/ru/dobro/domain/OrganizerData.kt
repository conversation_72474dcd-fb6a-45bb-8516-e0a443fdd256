package ru.dobro.domain

import android.net.Uri
import ru.dobro.domain.profile.achievements.trophy.OrganizerTrophy

data class OrganizerData(
    val id: OrganizerId,
    val name: String,
    val icon: Uri?,
    val fullName: String,
    val rating: Float?,
    val acceptedVacancyRequestsCount: Int,
    val volunteersCount: Int,
    val organizationId: OrganizationId,
    val statistic: OrganizerStatistic,
    val isRegionalNetwork: Boolean,
    val trophies: List<OrganizerTrophy>,
    val isVerified: Boolean,
    val ogrn: String,
    val alreadyAssist: Boolean,
    val type: OrganizerType,
)

enum class OrganizerType {
    Volunteer,
    Organization
}
