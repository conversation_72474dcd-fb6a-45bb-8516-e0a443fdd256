package ru.dobro.domain

import common.library.core.entity.StatefulAbstractEntity
import javax.annotation.CheckReturnValue

class Language
private constructor(data: LanguageData) :
    StatefulAbstractEntity<LanguageId, LanguageData, Language>(data) {
    @CheckReturnValue
    override fun create(state: LanguageData): Language = Language(state)

    override val identity: LanguageId get() = internalState.id

    val title: String get() = internalState.title

    companion object {
        @CheckReturnValue
        fun restore(data: LanguageData): Language = Language(data)

        @CheckReturnValue
        fun restore(
            id: LanguageId,
            title: String
        ): Language =
            restore(
                LanguageData(
                    id = id,
                    title = title
                )
            )
    }
}
