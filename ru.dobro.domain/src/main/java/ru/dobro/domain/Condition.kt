package ru.dobro.domain

import common.library.core.entity.StatefulAbstractEntity
import javax.annotation.CheckReturnValue

class Condition
private constructor(data: ConditionData) :
    StatefulAbstractEntity<ConditionId, ConditionData, Condition>(data) {
    @CheckReturnValue
    override fun create(state: ConditionData): Condition = Condition(state)

    override val identity: ConditionId get() = internalState.id
    val title: String get() = internalState.title

    companion object {
        @CheckReturnValue
        fun restore(data: ConditionData): Condition = Condition(data)

        @CheckReturnValue
        fun restore(
            id: ConditionId,
            title: String
        ): Condition =
            restore(
                ConditionData(
                    id = id,
                    title = title
                )
            )
    }
}
