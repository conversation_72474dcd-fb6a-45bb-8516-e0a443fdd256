package ru.dobro.domain

import android.os.Parcelable
import common.library.core.value.wrapped.WrappedCompatValue
import kotlinx.parcelize.Parcelize
import javax.annotation.CheckReturnValue

@Parcelize
data class FiasId(val fias: String) : WrappedCompatValue<String, FiasId>(fias),
    Parcelable {
    companion object {
        @CheckReturnValue
        fun restore(value: String): FiasId = FiasId(value)
    }
}
