package ru.dobro.domain

import common.library.core.value.core.StatefulAbstractValue
import java.time.ZonedDateTime
import javax.annotation.CheckReturnValue

class Passport
private constructor(data: PassportData) :
    StatefulAbstractValue<PassportData, Passport>(data) {
    @CheckReturnValue
    override fun create(state: PassportData): Passport = Passport(state)

    val series: String? get() = internalState.series
    val number: String get() = internalState.number
    val organization: String? get() = internalState.organization
    val organizationCode: String? get() = internalState.organizationCode
    val date: ZonedDateTime? get() = internalState.date
    val birthPlace: String? get() = internalState.birthPlace
    val address: Location? get() = internalState.address

    // Поля только для иностранцев
    val expirationDate: ZonedDateTime? get() = internalState.expirationDate
    val actualAddress: String? get() = internalState.actualAddress

    companion object {
        @CheckReturnValue
        fun restore(data: PassportData): Passport = Passport(data)

        @CheckReturnValue
        fun restore(
            series: String?,
            number: String,
            organization: String?,
            organizationCode: String?,
            date: ZonedDateTime?,
            birthPlace: String?,
            address: Location?,
            expirationDate: ZonedDateTime?,
            actualAddress: String?,
        ): Passport =
            restore(
                PassportData(
                    series = series,
                    number = number,
                    organization = organization,
                    organizationCode = organizationCode,
                    date = date,
                    birthPlace = birthPlace,
                    address = address,
                    expirationDate = expirationDate,
                    actualAddress = actualAddress
                )
            )

        @CheckReturnValue
        fun restoreRussianPassport(
            series: String?,
            number: String,
            organization: String?,
            organizationCode: String?,
            date: ZonedDateTime?,
            birthPlace: String?,
            address: Location?,
        ): Passport =
            restore(
                PassportData(
                    series = series,
                    number = number,
                    organization = organization,
                    organizationCode = organizationCode,
                    date = date,
                    birthPlace = birthPlace,
                    address = address,
                    expirationDate = null,
                    actualAddress = null
                )
            )

        @CheckReturnValue
        fun restoreForeignPassport(
            number: String,
            date: ZonedDateTime?,
            birthPlace: String?,
            expirationDate: ZonedDateTime?,
            actualAddress: String?,
        ): Passport =
            restore(
                PassportData(
                    series = null,
                    number = number,
                    organization = null,
                    organizationCode = null,
                    date = date,
                    birthPlace = birthPlace,
                    address = null,
                    expirationDate = expirationDate,
                    actualAddress = actualAddress
                )
            )

        fun Passport?.isNull(): Boolean =
            this?.series.isNullOrEmpty() &&
                this?.organization.isNullOrEmpty() &&
                this?.organizationCode.isNullOrEmpty() &&
                this?.date == null &&
                this?.birthPlace.isNullOrEmpty() &&
                this?.address == null &&
                this?.expirationDate == null &&
                this?.actualAddress.isNullOrEmpty()
    }
}
