package ru.dobro.domain

import common.library.core.value.core.StatefulAbstractValue
import javax.annotation.CheckReturnValue

class Director
private constructor(data: DirectorData) :
    StatefulAbstractValue<DirectorData, Director>(data) {
    @CheckReturnValue
    override fun create(state: DirectorData): Director = Director(state)

    val id: Long get() = internalState.id
    val firstName: String get() = internalState.firstName
    val secondName: String get() = internalState.secondName
    val lastName: String get() = internalState.lastName

    companion object {
        @CheckReturnValue
        fun restore(data: DirectorData): Director = Director(data)

        @CheckReturnValue
        fun restore(
            id: Long,
            firstName: String,
            secondName: String,
            lastName: String
        ): Director =
            restore(
                DirectorData(
                    id = id,
                    firstName = firstName,
                    secondName = secondName,
                    lastName = lastName
                )
            )
    }
}

data class DirectorData(
    val id: Long,
    val firstName: String,
    val secondName: String,
    val lastName: String
)
