package ru.dobro.domain

import common.library.core.entity.StatefulAbstractEntity
import javax.annotation.CheckReturnValue

class RequirementPreview
private constructor(data: RequirementPreviewData) :
    StatefulAbstractEntity<RequirementPreviewId, RequirementPreviewData, RequirementPreview>(data) {
    @CheckReturnValue
    override fun create(state: RequirementPreviewData): RequirementPreview =
        RequirementPreview(state)

    override val identity: RequirementPreviewId get() = internalState.id
    val title: String get() = internalState.title

    companion object {
        @CheckReturnValue
        fun restore(data: RequirementPreviewData): RequirementPreview = RequirementPreview(data)

        @CheckReturnValue
        fun restore(
            id: RequirementPreviewId,
            title: String
        ): RequirementPreview =
            restore(
                RequirementPreviewData(
                    id = id,
                    title = title
                )
            )
    }
}
