package ru.dobro.domain

import android.net.Uri
import common.library.core.color.Color
import common.library.core.entity.StatefulAbstractEntity
import javax.annotation.CheckReturnValue

data class Category(private val data: CategoryData) :
    StatefulAbstractEntity<CategoryId, CategoryData, Category>(data) {
    @CheckReturnValue
    override fun create(state: CategoryData): Category = Category(state)

    override val identity: CategoryId get() = internalState.id
    val title: String get() = internalState.title
    val icon: Uri get() = internalState.icon
    val iconGray: Uri get() = internalState.iconGray
    val color: Color.Rgb get() = internalState.color

    @OptIn(ExperimentalUnsignedTypes::class)
    companion object {
        @CheckReturnValue
        fun restore(data: CategoryData): Category = Category(data)

        @CheckReturnValue
        fun restore(
            id: CategoryId,
            title: String,
            icon: Uri,
            iconGray: Uri,
            color: Color.Rgb,
        ): Category =
            restore(
                CategoryData(
                    id = id,
                    title = title,
                    icon = icon,
                    iconGray = iconGray,
                    color = color
                )
            )

        @CheckReturnValue
        fun restoreEmpty(): Category =
            restore(
                CategoryData(
                    id = CategoryId.restore(Long.MAX_VALUE),
                    title = "Без категории",
                    icon = Uri.EMPTY,
                    iconGray = Uri.EMPTY,
                    color = Color.Rgb.black
                )
            )

        val allCategoryId = CategoryId.restore(-1)

        @CheckReturnValue
        fun restoreAllCategory(): Category =
            restore(
                id = allCategoryId,
                title = "Все направления",
                icon = Uri.EMPTY,
                iconGray = Uri.EMPTY,
                color = Color.Rgb.black
            )
    }
}
