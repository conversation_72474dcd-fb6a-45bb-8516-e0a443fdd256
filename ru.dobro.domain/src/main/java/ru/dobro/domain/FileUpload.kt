package ru.dobro.domain

import common.library.core.entity.StatefulAbstractEntity
import common.library.core.value.wrapped.WrappedCompatValue
import javax.annotation.CheckReturnValue

class FileUpload
private constructor(data: FileUploadData) :
    StatefulAbstractEntity<FileUploadId, FileUploadData, FileUpload>(data) {
    @CheckReturnValue
    override fun create(state: FileUploadData): FileUpload = FileUpload(state)

    override val identity: FileUploadId get() = internalState.id

    companion object {
        @CheckReturnValue
        fun restore(data: FileUploadData): FileUpload = FileUpload(data)

        @CheckReturnValue
        fun restore(
            id: String
        ): FileUpload =
            restore(
                FileUploadData(
                    id = FileUploadId.restore(id)
                )
            )
    }
}

class FileUploadId
private constructor(value: String) : WrappedCompatValue<String, FileUploadId>(value) {
    companion object {
        @CheckReturnValue
        fun restore(value: String): FileUploadId = FileUploadId(value)
    }
}
