package ru.dobro.domain

import common.library.core.value.core.StatefulAbstractValue
import javax.annotation.CheckReturnValue

class CreateOrganizationAttempt
private constructor(data: CreateOrganizationAttemptData) :
    StatefulAbstractValue<CreateOrganizationAttemptData, CreateOrganizationAttempt>(data) {
    @CheckReturnValue
    override fun create(state: CreateOrganizationAttemptData): CreateOrganizationAttempt =
        CreateOrganizationAttempt(state)

    val asVolunteer: Boolean get() = internalState.asVolunteer
    val asVolunteerEsia: Boolean get() = internalState.asVolunteerEsia
    val asVolunteerText: String get() = internalState.asVolunteerText
    val asOrganization: Boolean get() = internalState.asOrganization
    val asOrganizationInfo: String get() = internalState.asOrganizationInfo

    companion object {
        @CheckReturnValue
        fun restore(data: CreateOrganizationAttemptData): CreateOrganizationAttempt =
            CreateOrganizationAttempt(data)

        @CheckReturnValue
        fun restore(
            asVolunteer: <PERSON>olean,
            asVolunteerEsia: Boolean,
            asVolunteerText: String,
            asOrganization: Boolean,
            asOrganizationInfo: String
        ): CreateOrganizationAttempt =
            restore(
                CreateOrganizationAttemptData(
                    asVolunteer = asVolunteer,
                    asVolunteerEsia = asVolunteerEsia,
                    asVolunteerText = asVolunteerText,
                    asOrganization = asOrganization,
                    asOrganizationInfo = asOrganizationInfo
                )
            )
    }
}

data class CreateOrganizationAttemptData(
    val asVolunteer: Boolean,
    val asVolunteerEsia: Boolean,
    val asVolunteerText: String,
    val asOrganization: Boolean,
    val asOrganizationInfo: String
)
