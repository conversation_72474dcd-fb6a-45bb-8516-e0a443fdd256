package ru.dobro.domain

import android.os.Parcelable
import common.library.core.location.GeoPoint
import kotlinx.parcelize.Parcelize

@Parcelize
data class LocationData(
    val title: String,
    val rawTitle: String?,
    val settlement: String?,
    val settlementCode: String?,
    val coordinates: GeoPoint.Coordinates,
    val country: String? = null,
    val countryISO: String? = null,
    val municipality: String? = null,
    val municipalityCode: String? = null,
    val cityAlternative: String? = null,
    val flat: String? = null,
    val house: String? = null,
    val street: String? = null,
    val region: String? = null,
    val fiasId: FiasId? = null,
    val cityFiasId: FiasId? = null,
    val streetFiasId: FiasId? = null,
    val shortName: String? = null,
) : Parcelable
