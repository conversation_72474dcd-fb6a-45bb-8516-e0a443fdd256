package ru.dobro.domain

import android.net.Uri
import common.library.core.entity.StatefulAbstractEntity
import javax.annotation.CheckReturnValue

class Social
private constructor(data: SocialData) :
    StatefulAbstractEntity<SocialType, SocialData, Social>(data) {
    @CheckReturnValue
    override fun create(state: SocialData): Social = Social(state)

    override val identity: SocialType get() = internalState.type
    val path: Uri get() = internalState.path

    companion object {
        @CheckReturnValue
        fun restore(data: SocialData): Social = Social(data)

        @CheckReturnValue
        fun restore(
            type: SocialType,
            path: Uri
        ): Social =
            restore(
                SocialData(
                    type = type,
                    path = path
                )
            )
    }
}
