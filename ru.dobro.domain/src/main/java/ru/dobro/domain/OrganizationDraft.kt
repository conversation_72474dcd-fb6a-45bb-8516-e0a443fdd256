package ru.dobro.domain

import common.library.core.value.core.StatefulAbstractValue
import javax.annotation.CheckReturnValue

class OrganizationDraft
private constructor(data: CreateOrganizationDraftData) :
    StatefulAbstractValue<CreateOrganizationDraftData, OrganizationDraft>(data) {
    @CheckReturnValue
    override fun create(state: CreateOrganizationDraftData): OrganizationDraft =
        OrganizationDraft(state)

    val id: Long get() = internalState.id

    companion object {
        @CheckReturnValue
        fun restore(data: CreateOrganizationDraftData): OrganizationDraft = OrganizationDraft(data)

        @CheckReturnValue
        fun restore(
            id: Long,
        ): OrganizationDraft =
            restore(
                CreateOrganizationDraftData(
                    id = id,
                )
            )
    }
}

data class CreateOrganizationDraftData(
    val id: Long
)
