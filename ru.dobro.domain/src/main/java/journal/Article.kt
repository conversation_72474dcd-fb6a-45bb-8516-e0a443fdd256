package journal

import android.net.Uri
import common.library.core.value.core.StatefulAbstractValue
import javax.annotation.CheckReturnValue

class Article
private constructor(data: ArticleData) :
    StatefulAbstractValue<ArticleData, Article>(data) {
    @CheckReturnValue
    override fun create(state: ArticleData): Article = Article(state)

    val value: ArticleData get() = internalState

    companion object {
        @CheckReturnValue
        fun restore(data: ArticleData): Article = Article(data)
    }
}

data class ArticleData(
    val id: Long,
    val title: String?,
    val link: Uri,
    val iconUrl: Uri,
    val readTime: Int?
)
