package ru.dobro

import android.Manifest
import android.app.Activity
import android.util.Log
import androidx.test.core.app.ActivityScenario
import androidx.test.espresso.Espresso
import androidx.test.espresso.action.ViewActions
import androidx.test.espresso.matcher.ViewMatchers.withId
import androidx.test.filters.LargeTest
import androidx.test.internal.runner.junit4.AndroidJUnit4ClassRunner
import androidx.test.rule.GrantPermissionRule
import com.google.android.material.textview.MaterialTextView
import common.library.core.orFalse
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import ru.dobro.EspressoHelper.clickToId
import ru.dobro.EspressoHelper.clickToIdWithParent
import ru.dobro.EspressoHelper.clickToItemWithDescription
import ru.dobro.EspressoHelper.clickToSpanText
import ru.dobro.EspressoHelper.clickToText
import ru.dobro.EspressoHelper.commonDelay
import ru.dobro.EspressoHelper.getRecyclerViewCount
import ru.dobro.EspressoHelper.pressBackButton
import ru.dobro.EspressoHelper.recycleViewClickChildItemAction
import ru.dobro.EspressoHelper.recycleViewClickItemAction
import ru.dobro.EspressoHelper.recycleViewClickTextItemAction
import ru.dobro.EspressoHelper.replaceTextOnView
import ru.dobro.EspressoHelper.scrollToId
import ru.dobro.EspressoHelper.setCheckbox
import ru.dobro.EspressoHelper.setTextEdit
import ru.dobro.EspressoHelper.typeTextOnView
import ru.dobro.EspressoHelper.waitUntilNotShown
import ru.dobro.EspressoUtils.doesViewExist
import ru.dobro.EspressoUtils.runIn
import ru.dobro.EspressoUtils.selectDropdownItem
import ru.dobro.EspressoUtils.withIndex
import ru.dobro.login.LoginActivity
import ru.dobro.main.MainActivity
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

@Suppress("MaxLineLength")
@RunWith(AndroidJUnit4ClassRunner::class)
@LargeTest
class AllTest {
    private var lastTemporaryEmail = ""
    private fun getTemporaryEmail(): String {
        lastTemporaryEmail = "${System.currentTimeMillis()}${BuildConfig.testAccountEmail}"
        return lastTemporaryEmail
    }

    @get:Rule
    var locationPermission: GrantPermissionRule =
        GrantPermissionRule.grant(Manifest.permission.ACCESS_FINE_LOCATION)

    @get:Rule
    var notificationsPermission: GrantPermissionRule =
        GrantPermissionRule.grant(Manifest.permission.POST_NOTIFICATIONS)

    private fun getMainActivity(): Activity? {
        var activity: Activity? = null
        val scenario = ActivityScenario.launch(MainActivity::class.java)
        scenario.onActivity {
            activity = it
        }
        return activity
    }

    @Test
    fun onboardingTest() = runIn<LoginActivity> {
        commonDelay()
        clickToId(R.id.login__onboarding___welcome__next)
        commonDelay()
        clickToId(R.id.login__onboarding___welcome_next__next)
        commonDelay()
        clickToId(R.id.login__onboarding___location__manual)
        recycleViewClickItemAction(viewId = R.id.address_selector___list, itemIndex = 0)
        commonDelay()
        clickToId(R.id.login__onboarding___categories__select)
        recycleViewClickItemAction(
            viewId = R.id.categories_selector___categories__list,
            itemIndex = 0
        )
        commonDelay()
        recycleViewClickItemAction(
            viewId = R.id.categories_selector___categories__list,
            itemIndex = 4
        )
        clickToId(R.id.categories_selector__medical_history__check, true)
        clickToId(R.id.categories_selector___save)
        commonDelay()
        recycleViewClickItemAction(
            viewId = R.id.login__onboarding__download__reports,
            itemIndex = 0
        )
        commonDelay()
        clickToId(R.id.login__onboarding__download__next)
        commonDelay()
        clickToId(R.id.login__onboarding___final__authorize)
    }

    @Test
    fun registrationTest() = runIn<MainActivity> {
        logout()
        toProfileGuest()
        createProfile16Year()
        createProfile()
    }

    @Test
    fun knowledgeBaseTest() = runIn<MainActivity> {
        logout()
        toProfileGuest()
        loginUser(
            email = BuildConfig.testAccountLoginExist,
            password = BuildConfig.testAccountPasswordExist
        )
        commonDelay(customDelay = 5000L)
        clickToId(R.id.main__about)
        clickToId(R.id.knowledge_base)
        commonDelay()
        recycleViewClickItemAction(viewId = R.id.categories, itemIndex = 1)
        commonDelay()
        Espresso.onView(withIndex(withId(R.id.delails), 0))
            .perform(ViewActions.click())
        commonDelay(customDelay = 5000L)
        clickToId(R.id.main__profile_knowledge_base___like_count)
        clickToId(R.id.main__about)
        clickToId(R.id.knowledge_base)
        replaceTextOnView(
            viewId = R.id.search,
            replacesText = "135",
            withScroll = false
        )
        commonDelay(customDelay = 5000L)
        Espresso.onView(withIndex(withId(R.id.delails), 0))
            .perform(ViewActions.click())
        commonDelay(customDelay = 5000L)
        clickToId(R.id.main__profile_knowledge_base___like_count)
        commonDelay(customDelay = 5000L)
    }

    @Test
    fun profileTest() = runIn<MainActivity> {
        logout()
        toProfileGuest()
        loginUser(
            email = BuildConfig.testAccountLoginExist,
            password = BuildConfig.testAccountPasswordExist
        )
        commonDelay(customDelay = 10000)
        clickToId(R.id.main__profile_me___requests)
        commonDelay(customDelay = 5000)
        clickToText(viewText = "Варианты посещения")
        commonDelay(customDelay = 5000)
        clickToId(R.id.main__profile_me)

        clickToId(R.id.main__profile_me___favorites)
        commonDelay(customDelay = 5000)
        clickToId(R.id.main__profile_me)

        clickToId(R.id.main__profile_me___volunteer_book)
        commonDelay(customDelay = 5000)
        clickToId(R.id.main__profile_me)

        clickToId(R.id.myFriendsContainer)
        commonDelay(customDelay = 5000)
        clickToText(viewText = "Заявки в друзья")
        commonDelay(customDelay = 5000)
        clickToId(R.id.main__profile_me)

        clickToId(R.id.myOrganizationsContainer)
        commonDelay(customDelay = 5000)
        Espresso.onView(withIndex(withId(R.id.leave), 0)).perform(ViewActions.click())
        commonDelay()
        clickToText(viewText = "Не помогать")
        commonDelay(customDelay = 5000)
        Espresso.onView(withIndex(withId(R.id.join), 0)).perform(ViewActions.click())
        commonDelay()
        clickToText(viewText = "Понятно")
        clickToId(R.id.main__profile_me)

        clickToId(R.id.main__profile_me___comments)
        commonDelay(customDelay = 5000)
        clickToId(R.id.main__profile_me)
    }

    @Test
    fun dobroUiAutotest() = runIn<MainActivity> {
        logout()
        toProfileGuest()
        loginUser(
            email = lastTemporaryEmail,
            password = BuildConfig.testAccountPassword
        )

        checkEditProfile()
        logout()
        toProfileGuest()
        resetPassword()
        createOrganisationProfile()
        createDobroIssueOrg()
        createDobroIssuePhys()
        createVacancyAndVariantOrg()
        createVacancyAndVariantPhys()
        requestVacancy()
        requestVisit()
        createDobroRequest()
        openVolunteersBook()
        crm()
        notificationSection()
        Thread.sleep(6000L)
    }

    // Переход во вкладку профиль для неавторизированного юзера
    private fun toProfileGuest() {
        Log.d("EspressoTest", "toProfileGuest")
        toProfile()
        if (doesViewExist(R.id.authorization___stub__perform)) {
            clickToId(R.id.authorization___stub__perform)
            if (doesViewExist(R.id.authorization___email)) {
                waitUntilNotShown(viewId = R.id.authorization___email, millis = 5000L)
            }
        }
    }

    // Переход во вкладку профиль
    private fun toProfile() {
        Log.d("EspressoTest", "toProfile")
        if (!doesViewExist(R.id.main__profile_me)) return
        clickToId(R.id.main__profile_me)
    }

    // Переход во вкладку главного экрана
    private fun toHome() {
        Log.d("EspressoTest", "toHome")
        if (!doesViewExist(R.id.main__overview)) return
        clickToId(R.id.main__overview)
    }

    // Создание профиля
    private fun createProfile(isParent: Boolean = false) {
        if (!isParent) {
            Log.d("EspressoTest", "createProfile")
        }
        toProfileGuest()
        clickToSpanText(R.id.authorization___subtitle, "Зарегистрируйтесь")
        replaceTextOnView(
            viewId = R.id.registration___username,
            replacesText = "Автотест",
            withScroll = true
        )
        replaceTextOnView(
            viewId = R.id.registration___user_surname,
            replacesText = "Автотестович",
            withScroll = true
        )
        clickToId(viewId = R.id.registration___city, withScroll = true)
        commonDelay()
        recycleViewClickItemAction(viewId = R.id.address_selector___list, itemIndex = 0)
        clickToId(viewId = R.id.registration___user_birthday, withScroll = true)
        if (isParent) {
            clickToId(viewId = com.google.android.material.R.id.mtrl_picker_header_toggle)
            commonDelay()
            val seconds: Long =
                ZonedDateTime.now().minusYears(18).toInstant().toEpochMilli() / 1000L
            val time = LocalDateTime.ofEpochSecond(seconds, 0, ZoneOffset.ofHours(0))
            val formatted = time.format(DateTimeFormatter.ofPattern("dd.MM.yyyy"))
            setTextEdit(currentViewText = formatted, newViewText = "22.01.2016")
        } else {
            clickToId(viewId = com.google.android.material.R.id.mtrl_picker_header_toggle)
            commonDelay()
            val seconds: Long =
                ZonedDateTime.now().minusYears(18).toInstant().toEpochMilli() / 1000L
            val time = LocalDateTime.ofEpochSecond(seconds, 0, ZoneOffset.ofHours(0))
            val formatted = time.format(DateTimeFormatter.ofPattern("dd.MM.yyyy"))
            setTextEdit(currentViewText = formatted, newViewText = "22.01.2005")
        }
        commonDelay()
        clickToId(viewId = com.google.android.material.R.id.confirm_button)
        typeTextOnView(
            viewId = R.id.registration___email,
            typedText = getTemporaryEmail(),
            withScroll = true
        )
        typeTextOnView(
            viewId = R.id.password,
            typedText = BuildConfig.testAccountPassword,
            withScroll = true
        )
        typeTextOnView(
            viewId = R.id.passwordSecond,
            typedText = BuildConfig.testAccountPassword,
            withScroll = true
        )
        clickToId(viewId = R.id.registration___perform, withScroll = true)
        if (!isParent) {
            commonDelay(customDelay = 5000L)
            clickToId(viewId = R.id.check_email__close)
            Log.d("EspressoTest", "createProfile passed")
        }
    }

    private fun checkEditProfile() {
        Log.d("EspressoTest", "checkEditProfile")
        commonDelay()
        clickToId(viewId = R.id.main__profile_me___edit)
        commonDelay()
        clickToId(
            viewId = R.id.main__profile_edit___contacts__actual_address__container,
            withScroll = true
        )
        clickToId(viewId = R.id.address_selector___search)
        replaceTextOnView(
            viewId = R.id.address_selector___search,
            replacesText = "Росс",
            withScroll = false
        )
        commonDelay(customDelay = 4000L)
        recycleViewClickItemAction(viewId = R.id.address_selector___list, itemIndex = 0)
        commonDelay()
        clickToId(
            viewId = R.id.main__profile_edit___additional__passport__add,
            withScroll = true
        )
        commonDelay()
        replaceTextOnView(
            viewId = R.id.main__profile_edit___passport_edit__series,
            replacesText = "1111",
            withScroll = true
        )
        replaceTextOnView(
            viewId = R.id.main__profile_edit___passport_edit__number,
            replacesText = "111111",
            withScroll = true
        )
        replaceTextOnView(
            viewId = R.id.main__profile_edit___passport_edit__organization,
            replacesText = "бубубу",
            withScroll = true
        )
        replaceTextOnView(
            viewId = R.id.main__profile_edit___passport_edit__organization_code,
            replacesText = "111-111",
            withScroll = true
        )
        clickToId(viewId = R.id.main__profile_edit___passport_edit__date)
        commonDelay()
        clickToId(viewId = com.google.android.material.R.id.mtrl_picker_header_toggle)
        commonDelay(customDelay = 4000L)
        clickToId(viewId = com.google.android.material.R.id.confirm_button)
        replaceTextOnView(
            viewId = R.id.main__profile_edit___passport_edit__birth_place,
            replacesText = "бубубу",
            withScroll = true
        )
        clickToId(viewId = R.id.main__profile_edit___passport_edit__address__container)
        clickToId(viewId = R.id.country)
        clickToId(viewId = R.id.address_selector___search)
        replaceTextOnView(
            viewId = R.id.address_selector___search,
            replacesText = "Росс",
            withScroll = false
        )
        commonDelay(customDelay = 4000L)
        recycleViewClickItemAction(viewId = R.id.address_selector___list, itemIndex = 0)
        clickToId(viewId = R.id.city)
        commonDelay()
        recycleViewClickItemAction(viewId = R.id.address_selector___list, itemIndex = 0)
        commonDelay()
        clickToId(viewId = R.id.street)
        clickToId(viewId = R.id.address_selector___search)
        replaceTextOnView(
            viewId = R.id.address_selector___search,
            replacesText = "Пер",
            withScroll = false
        )
        commonDelay(customDelay = 4000L)
        recycleViewClickItemAction(viewId = R.id.address_selector___list, itemIndex = 0)
        commonDelay()
        clickToId(viewId = R.id.house)
        clickToId(viewId = R.id.address_selector___search)
        replaceTextOnView(
            viewId = R.id.address_selector___search,
            replacesText = "1",
            withScroll = false
        )
        commonDelay(customDelay = 4000L)
        recycleViewClickItemAction(viewId = R.id.address_selector___list, itemIndex = 0)
        commonDelay()
        clickToId(viewId = R.id.save)
        commonDelay()

        Log.d("EspressoTest", "checkEditProfile passed")
    }

    // Создание профиля до 16 лет
    private fun createProfile16Year() {
        Log.d("EspressoTest", "createProfile16Year")
        createProfile(isParent = true)
        replaceTextOnView(
            viewId = R.id.name,
            replacesText = "Автотест",
            withScroll = true
        )
        replaceTextOnView(
            viewId = R.id.surname,
            replacesText = "Автотестыч",
            withScroll = true
        )
        replaceTextOnView(
            viewId = R.id.passportSeries,
            replacesText = "1111",
            withScroll = true
        )
        replaceTextOnView(
            viewId = R.id.passportNumber,
            replacesText = "111111",
            withScroll = true
        )
        replaceTextOnView(
            viewId = R.id.birthCertificateNumber,
            replacesText = "11111111",
            withScroll = true
        )
        clickToId(viewId = R.id.perform, withScroll = true)
        clickToId(viewId = R.id.check_email__close)
        Log.d("EspressoTest", "createProfile16Year passed")
    }

    // Восстановление пароля
    private fun resetPassword() {
        Log.d("EspressoTest", "resetPassword")
        clickToId(viewId = R.id.authorization___reset_password, withScroll = true)
        typeTextOnView(
            viewId = R.id.main__reset_password___email,
            typedText = lastTemporaryEmail,
            withScroll = true
        )
        clickToId(viewId = R.id.main__reset_password___perform)
        clickToId(viewId = R.id.check_email__close)
        Log.d("EspressoTest", "resetPassword passed")
    }

    private fun createOrganisationProfile() {
        Log.d("EspressoTest", "createOrganisationProfile")
        toProfileGuest()
        loginUser(
            email = lastTemporaryEmail,
            password = BuildConfig.testAccountPassword
        )
        clickToId(R.id.main__profile_me___become_organizer)
        commonDelay()
        clickToText(viewText = "Как юридическое лицо")
        replaceTextOnView(
            viewId = R.id.organizationName,
            replacesText = System.currentTimeMillis().toString(),
            withScroll = true
        )
        replaceTextOnView(
            viewId = R.id.email,
            replacesText = lastTemporaryEmail,
            withScroll = true
        )
        clickToId(viewId = R.id.type, withScroll = true)
        commonDelay()
        selectDropdownItem("ВУЗ")
        clickToText(viewText = "Добавить адрес", withScroll = true)
        clickToId(viewId = R.id.country)
        clickToId(viewId = R.id.address_selector___search)
        replaceTextOnView(
            viewId = R.id.address_selector___search,
            replacesText = "Росс",
            withScroll = false
        )
        commonDelay(customDelay = 4000L)
        recycleViewClickItemAction(viewId = R.id.address_selector___list, itemIndex = 0)
        clickToId(viewId = R.id.city)
        commonDelay()
        recycleViewClickItemAction(viewId = R.id.address_selector___list, itemIndex = 0)
        commonDelay()
        clickToId(viewId = R.id.street)
        clickToId(viewId = R.id.address_selector___search)
        replaceTextOnView(
            viewId = R.id.address_selector___search,
            replacesText = "Пер",
            withScroll = false
        )
        commonDelay(customDelay = 4000L)
        recycleViewClickItemAction(viewId = R.id.address_selector___list, itemIndex = 0)
        commonDelay()
        clickToId(viewId = R.id.house)
        clickToId(viewId = R.id.address_selector___search)
        replaceTextOnView(
            viewId = R.id.address_selector___search,
            replacesText = "1",
            withScroll = false
        )
        commonDelay(customDelay = 4000L)
        recycleViewClickItemAction(viewId = R.id.address_selector___list, itemIndex = 0)
        commonDelay()
        clickToId(viewId = R.id.save)
        commonDelay()
        clickToId(viewId = R.id.personalData, withScroll = true)
        clickToId(viewId = R.id.perform, withScroll = true)
        commonDelay(customDelay = 5000L)
        clickToText("Понятно")
        Log.d("EspressoTest", "createOrganisationProfile passed")
    }

    // Логин пользовователя
    private fun loginUser(email: String, password: String) {
        Log.d("EspressoTest", "loginUser")
        toProfileGuest()
        typeTextOnView(
            viewId = R.id.authorization___email,
            typedText = email,
            withScroll = true
        )
        typeTextOnView(
            viewId = R.id.authorization___password,
            typedText = password,
            withScroll = true
        )
        clickToId(viewId = R.id.authorization___perform)
    }

    // Создание заявки
    private fun createDobroRequest() {
        Log.d("EspressoTest", "createDobroRequest")
        logout()
        loginUser(
            email = BuildConfig.testAccountLoginExist,
            password = BuildConfig.testAccountPasswordExist
        )
        commonDelay(customDelay = 5000L)
        clickToId(viewId = R.id.main__dobro_center)
        commonDelay(customDelay = 5000L)
        if (doesViewExist(viewId = R.id.requestsServicesList)) {
            for (i in 0 until getRecyclerViewCount(R.id.requestsServicesList)) {
                recycleViewClickChildItemAction(
                    viewId = R.id.requestsServicesList,
                    childId = R.id.removeRequest,
                    itemIndex = i
                )
                clickToText(viewText = "Удалить")
            }
        }
        scrollToId(viewId = R.id.availableServicesList)
        recycleViewClickItemAction(viewId = R.id.availableServicesList, itemIndex = 0)
        clickToId(viewId = R.id.submit)
        for (i in 0 until getRecyclerViewCount(R.id.listIssue)) {
            recycleViewClickChildItemAction(
                viewId = R.id.listIssue,
                childId = R.id.custom_fields__recycler_view__first_element,
                itemIndex = i
            )
        }
        clickToIdWithParent(
            viewId = R.id.checkBox,
            parentViewId = R.id.termsOfUse,
            withScroll = true
        )
        clickToIdWithParent(
            viewId = R.id.checkBox,
            parentViewId = R.id.publicProcessing,
            withScroll = true
        )
        clickToIdWithParent(
            viewId = R.id.checkBox,
            parentViewId = R.id.portalRules,
            withScroll = true
        )
        clickToIdWithParent(
            viewId = R.id.checkBox,
            parentViewId = R.id.processing,
            withScroll = true
        )
        clickToIdWithParent(
            viewId = R.id.checkBox,
            parentViewId = R.id.sendingServiceRequest,
            withScroll = true
        )
        clickToId(viewId = R.id.next, withScroll = true)

        clickToId(viewId = R.id.settlement)
        commonDelay()
        clickToId(viewId = R.id.address_selector___search)
        replaceTextOnView(
            viewId = R.id.address_selector___search,
            replacesText = "Моск",
            withScroll = false
        )
        commonDelay()
        recycleViewClickItemAction(viewId = R.id.address_selector___list, itemIndex = 0)
        commonDelay(customDelay = 5000L)
        recycleViewClickItemAction(viewId = R.id.mapItems, itemIndex = 0)
        clickToId(viewId = R.id.select)
        clickToId(viewId = R.id.confirm)
        clickToId(viewId = R.id.done)
        Log.d("EspressoTest", "createDobroRequest passed")
    }

    // Смена профиля
    private fun clickChangeProfile() {
        Log.d("EspressoTest", "clickChangeProfile")
        commonDelay(customDelay = 5000L)
        clickToId(R.id.main__profile_me___change_profile)
    }

    // Создание доброго дела организации
    private fun createDobroIssueOrg() {
        Log.d("EspressoTest", "createDobroIssueOrg")
        logout()
        commonDelay(customDelay = 3000L)
        loginUser(
            email = "<EMAIL>",
            password = "19871987"
        )
        commonDelay(customDelay = 3000L)
        clickChangeProfile()
        clickToText(viewText = "тест птичка !")
        createDobroIssueCommon()
        Log.d("EspressoTest", "createDobroIssueOrg passed")
    }

    // Создание доброго дела физика
    private fun createDobroIssuePhys() {
        Log.d("EspressoTest", "createDobroIssuePhys")
        toProfile()
        clickChangeProfile()
        commonDelay(customDelay = 3000)
        clickToItemWithDescription(viewDescription = "1-menu")
        createDobroIssueCommon()
        Log.d("EspressoTest", "createDobroIssuePhys passed")
    }

    // Создание доброго дела
    private fun createDobroIssueCommon() {
        Log.d("EspressoTest", "createDobroIssueCommon")
        commonDelay(customDelay = 5000L)
        clickToId(viewId = R.id.addEvent)
        commonDelay(customDelay = 3000L)
        recycleViewClickItemAction(viewId = R.id.categoriesList, itemIndex = 0)
        replaceTextOnView(
            viewId = R.id.eventName,
            replacesText = "Доброе дело для автотеста",
            withScroll = true
        )
        replaceTextOnView(
            viewId = R.id.description,
            replacesText = "Тест тест тест тест тест тест тестТест тест тест тест тест тест тестТест тест тест тест тест тест тестТест тест тест тест тест тест тестТест тест тест тест тест тест тестТест тест тест тест тест тест тестТест тест тест тест тест тест тестТест тест тест тест тест тест тестТест тест тест тест тест тест тестТест тест тест тест тест тест тест",
            withScroll = true
        )
        clickToId(viewId = R.id.setAddress, withScroll = true)
        clickToId(viewId = R.id.country)
        clickToId(viewId = R.id.address_selector___search)
        replaceTextOnView(
            viewId = R.id.address_selector___search,
            replacesText = "Росс",
            withScroll = false
        )
        commonDelay()
        recycleViewClickItemAction(viewId = R.id.address_selector___list, itemIndex = 0)
        clickToId(viewId = R.id.city)
        commonDelay()
        recycleViewClickItemAction(viewId = R.id.address_selector___list, itemIndex = 0)
        commonDelay()
        clickToId(viewId = R.id.save)
        clickToId(viewId = R.id.periodInput, withScroll = true)
        clickToText(viewText = "СОХРАНИТЬ")
        clickToId(viewId = R.id.startInput, withScroll = true)
        clickToText(viewText = "ОК")
        clickToId(viewId = R.id.endInput, withScroll = true)
        clickToText(viewText = "ОК")
        setCheckbox(viewId = R.id.isAcceptRules, checked = true, withScroll = true)
        clickToId(viewId = R.id.save, withScroll = true)
    }

    // Создание вакансии и варианта посещения оргинизации
    private fun createVacancyAndVariantOrg() {
        Log.d("EspressoTest", "createVacancyAndVariantOrg")
        toProfile()
        clickChangeProfile()
        clickToText(viewText = "тест птичка !")
        commonDelay(5000L)
        recycleViewClickItemAction(viewId = R.id.main__organization___events__list, itemIndex = 0)
        createVacancyAndVariantCommon()
        Log.d("EspressoTest", "createVacancyAndVariantOrg passed")
    }

    // Создание вакансии и варианта посещения физика
    private fun createVacancyAndVariantPhys() {
        Log.d("EspressoTest", "createVacancyAndVariantPhys")
        toProfile()
        clickChangeProfile()
        clickToItemWithDescription(viewDescription = "1-menu")
        commonDelay(customDelay = 3000L)
        recycleViewClickItemAction(viewId = R.id.events, itemIndex = 0)
        createVacancyAndVariantCommon()
        Log.d("EspressoTest", "createVacancyAndVariantPhys passed")
    }

    // Создание вакансии и варианта посещения
    private fun createVacancyAndVariantCommon() {
        Log.d("EspressoTest", "createVacancyAndVariantCommon")
        commonDelay(customDelay = 5000L)
        clickToText(viewText = "Создать вакансию")
        commonDelay(customDelay = 5000L)
        clickToId(viewId = R.id.main__profile_me_organizator___func_input_text)
        commonDelay()
        if (!doesViewExist((R.id.common___single_select__menu__title))) {
            clickToId(viewId = R.id.main__profile_me_organizator___func_input_text)
        }
        commonDelay(customDelay = 5000L)
        recycleViewClickItemAction(viewId = R.id.common___single_select__menu__list, itemIndex = 0)
        clickToId(viewId = R.id.main__profile_me_organizator___vacancy_input_text)
        recycleViewClickItemAction(viewId = R.id.common___single_select__menu__list, itemIndex = 0)
        recycleViewClickItemAction(
            viewId = R.id.main__profile_me_organizator___task_list,
            itemIndex = 0
        )
        commonCreateVacancyAndVariant()
        createVariantOrg()
        commonDelay()
        pressBackButton()
    }

    private fun requestVisit() {
        Log.d("EspressoTest", "requestVisit")
        toHome()
        commonDelay(customDelay = 3000L)
        clickToId(viewId = R.id.searchContainer)
        commonDelay(customDelay = 3000L)
        clickToId(viewId = R.id.application___filter)
        commonDelay(customDelay = 5000L)
        recycleViewClickItemAction(viewId = R.id.common___filter__list, itemIndex = 1)
        commonDelay()
        recycleViewClickItemAction(viewId = R.id.address_selector___list, itemIndex = 0)
        commonDelay(customDelay = 5000L)

        recycleViewClickTextItemAction(
            viewId = R.id.common___filter__list,
            viewText = "Можно как посетитель"
        )

        commonDelay()
        clickToId(viewId = R.id.common___filter__submit)
        commonDelay(customDelay = 5000L)
        recycleViewClickItemAction(viewId = R.id.main__events___recycler, itemIndex = 2)
        commonDelay()
        clickToText(viewText = "Подать заявку")
        recycleViewClickItemAction(viewId = R.id.functions, itemIndex = 0)
        commonDelay(customDelay = 5000L)
        val isVisitRequest = doesViewExist(viewText = "Посетить мероприятие")
            || doesViewExist(viewText = "Подать заявку")
            || doesViewExist(viewText = "Записаться в резерв")

        if (!isVisitRequest) {
            clickToText(viewText = "Отменить заявку")
            clickToId(viewId = R.id.main__vacancy__request_rejecting___reason)
            replaceTextOnView(
                viewId = R.id.main__vacancy__request_rejecting___reason,
                replacesText = "Тест",
                withScroll = false
            )
            clickToId(viewId = R.id.main__vacancy__request_rejecting___perform)
            commonDelay(customDelay = 5000L)
        }
        val isVisit = doesViewExist(viewText = "Посетить мероприятие")
        val isVisitReserve = doesViewExist(viewText = "Записаться в резерв")
        clickToText(viewText = if (isVisit) "Посетить мероприятие" else if (isVisitReserve) "Записаться в резерв" else "Подать заявку")
        commonDelay()
        clickToId(viewId = R.id.main__vacancy_request___select_all, withScroll = true)
        clickToId(viewId = R.id.main__vacancy_request___offer__checkbox, withScroll = true)
        clickToText(viewText = "Подать заявку")
        commonDelay()
        pressBackButton()
        pressBackButton()
        Log.d("EspressoTest", "requestVisit passed")
    }

    // Создание варианта посещения от организации
    private fun createVariantOrg() {
        Log.d("EspressoTest", "createVariantOrg")
        commonDelay(customDelay = 5000L)
        clickToText(viewText = "Создать вариант посещения")
        commonDelay(customDelay = 3000L)
        clickToId(viewId = R.id.main__profile_me_organizator___variant_input)
        commonDelay(customDelay = 3000L)
        recycleViewClickItemAction(
            viewId = R.id.common___single_select__menu__list,
            itemIndex = 0
        )
        commonCreateVacancyAndVariant()
        pressBackButton()
    }

    // Общий метод создания вакансии и варианта посещения
    private fun commonCreateVacancyAndVariant() {
        Log.d("EspressoTest", "commonCreateVacancyAndVariant")
        scrollToId(viewId = R.id.main__profile_me_organizator___volunteer_count_input_text)
        clickToId(viewId = R.id.main__profile_me_organizator___volunteer_count_input_text)
        replaceTextOnView(
            viewId = R.id.main__profile_me_organizator___volunteer_count_input_text,
            replacesText = "1",
            withScroll = false
        )
        clickToId(viewId = R.id.main__profile_edit___info__birth, withScroll = true)
        clickToText(viewText = "СОХРАНИТЬ")
        clickToId(viewId = R.id.main__profile_me_organizator___info__data_start, withScroll = true)
        clickToText(viewText = "ОК")
        clickToId(viewId = R.id.main__profile_me_organizator___info__data_end, withScroll = true)
        clickToText(viewText = "ОК")
        clickToId(viewId = R.id.main__profile_me_organizator___info__save, withScroll = true)
    }

    private fun requestVacancy() {
        Log.d("EspressoTest", "requestVacancy")
        toProfile()
        commonDelay(customDelay = 3000L)
        logout()
        commonDelay(customDelay = 3000L)
        loginUser(
            email = "<EMAIL>",
            password = "19871987"
        )
        commonDelay(customDelay = 3000L)
        clickChangeProfile()
        clickToItemWithDescription(viewDescription = "0-menu")
        toHome()
        commonDelay(5000L)
        recycleViewClickItemAction(
            viewId = R.id.listRecommendedEvents,
            itemIndex = 3,
            withScroll = true,
            multiView = false
        )
        commonDelay()
        clickToText(viewText = "Подать заявку")
        recycleViewClickItemAction(viewId = R.id.functions, itemIndex = 0)
        commonDelay()
        val isRequestCreated = doesViewExist(viewText = "Отменить заявку")
        if (isRequestCreated) {
            clickToText(viewText = "Отменить заявку")
            clickToId(viewId = R.id.main__vacancy__request_rejecting___reason)
            replaceTextOnView(
                viewId = R.id.main__vacancy__request_rejecting___reason,
                replacesText = "Тест",
                withScroll = false
            )
            commonDelay()
            clickToId(viewId = R.id.main__vacancy__request_rejecting___perform)
        }
        clickToText(viewText = "Подать заявку")
        commonDelay()
        clickToId(viewId = R.id.main__vacancy_request___select_all, withScroll = true)
        clickToId(viewId = R.id.main__vacancy_request___offer__checkbox, withScroll = true)
        clickToText(viewText = "Подать заявку")
        commonDelay(customDelay = 5000L)
        pressBackButton()
        Log.d("EspressoTest", "requestVacancy passed")
    }

    // Переход в раздел оповещения
    private fun notificationSection() {
        Log.d("EspressoTest", "notificationSection")
        commonDelay()
        clickToId(viewId = R.id.main__notifications)
        waitUntilNotShown(R.id.main__notification___logged_container, 5000L)
        clickToId(viewId = R.id.application___settings)
        commonDelay(customDelay = 3000L)
        val fromText = getMainActivity()?.findViewById<MaterialTextView>(R.id.from)
        val toText = getMainActivity()?.findViewById<MaterialTextView>(R.id.to)
        if (fromText?.equals("09:00").orFalse() && toText?.equals("19:00").orFalse()) {
            pressBackButton()
        }
        Log.d("EspressoTest", "notificationSection passed")
    }

    // Открытие книжки волонтера
    private fun openVolunteersBook() {
        Log.d("EspressoTest", "openVolunteersBook")
        logout()
        loginUser("<EMAIL>", "Nata.1987")
        commonDelay(customDelay = 3000L)
        clickToText(viewText = "Книжка волонтёра", withScroll = true)
        pressBackButton()
        Log.d("EspressoTest", "openVolunteersBook passed")
    }

    // Выход из профиля
    private fun logout() {
        Log.d("EspressoTest", "logout")
        commonDelay()
        toProfile()
        if (!doesViewExist(R.id.authorization___stub__perform)) {
            if (doesViewExist(R.id.main__profile_me___log_out)) {
                clickToId(R.id.main__profile_me___log_out)
                commonDelay()
                clickToText("Выйти")
            } else {
                clickChangeProfile()
                clickToText("Выйти")
                commonDelay()
                clickToText("Выйти")
            }
        }
    }

    private fun crm() {
        Log.d("EspressoTest", "crm")
        logout()
        toProfileGuest()
        loginUser(
            email = "<EMAIL>",
            password = "Yandex.000"
        )
        commonDelay(customDelay = 3000L)
        createRequest(eventName = "экология тест")
        createRequest(eventName = "Уборка во дворе")
        logout()
        toProfileGuest()
        loginUser(
            email = "<EMAIL>",
            password = "19871987"
        )
        commonDelay(customDelay = 3000L)
        clickChangeProfile()
        commonDelay()
        clickToItemWithDescription(viewDescription = "1-menu")
        commonDelay(customDelay = 3000L)
        clickToId(viewId = R.id.main__overview)
        commonDelay(customDelay = 5000L)
        recycleViewClickItemAction(
            viewId = R.id.main__crm___event_vacancies,
            itemIndex = 0,
            repeatCheckCount = false
        )
        commonDelay(customDelay = 3000L)
        /*
        recycleViewClickChildItemAction(
            viewId = R.id.main__crm__accept_reject_search_fragment_events,
            childId = R.id.main__crm__accept__item_accept,
            itemIndex = 0
        )
         */
        pressBackButton()
        Log.d("EspressoTest", "crm passed")
    }

    private fun createRequest(eventName: String) {
        toHome()
        commonDelay(customDelay = 3000L)
        clickToId(viewId = R.id.searchContainer)
        commonDelay(customDelay = 3000L)

        clickToId(viewId = R.id.main__search___query)
        replaceTextOnView(
            viewId = R.id.main__search___query,
            replacesText = eventName,
            withScroll = false
        )
        clickToId(viewId = R.id.application___filter)
        commonDelay(customDelay = 5000L)
        recycleViewClickItemAction(viewId = R.id.common___filter__list, itemIndex = 1)
        commonDelay()
        recycleViewClickItemAction(viewId = R.id.address_selector___list, itemIndex = 0)
        commonDelay(customDelay = 5000L)
        clickToId(viewId = R.id.common___filter__submit)
        commonDelay(customDelay = 5000L)
        recycleViewClickItemAction(viewId = R.id.main__events___recycler, itemIndex = 1)
        commonDelay(customDelay = 5000L)
        clickToText(viewText = "Подать заявку")
        recycleViewClickItemAction(viewId = R.id.functions, itemIndex = 0)
        commonDelay()
        if (doesViewExist(viewText = "Отменить заявку")) {
            clickToText(viewText = "Отменить заявку")
            clickToId(viewId = R.id.main__vacancy__request_rejecting___reason)
            replaceTextOnView(
                viewId = R.id.main__vacancy__request_rejecting___reason,
                replacesText = "Тест",
                withScroll = false
            )
            commonDelay()
            clickToId(viewId = R.id.main__vacancy__request_rejecting___perform)
        }
        commonDelay(customDelay = 5000L)
        if (doesViewExist(viewText = "Подать заявку")) {
            clickToText(viewText = "Подать заявку")
        } else if (doesViewExist(viewText = "Посетить мероприятие")) {
            clickToText(viewText = "Посетить мероприятие")
        } else {
            clickToText(viewText = "Записаться в резерв")
        }
        commonDelay()
        clickToId(viewId = R.id.main__vacancy_request___select_all, withScroll = true)
        clickToId(viewId = R.id.main__vacancy_request___offer__checkbox, withScroll = true)
        clickToText(viewText = "Подать заявку")
        commonDelay()
        pressBackButton()
        pressBackButton()
        commonDelay()
    }
}
