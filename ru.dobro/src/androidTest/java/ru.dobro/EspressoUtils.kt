package ru.dobro

import android.app.Activity
import android.text.SpannableString
import android.text.style.ClickableSpan
import android.view.View
import android.widget.Checkable
import android.widget.TextView
import androidx.annotation.IdRes
import androidx.recyclerview.widget.RecyclerView
import androidx.test.core.app.ActivityScenario
import androidx.test.espresso.Espresso
import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.NoMatchingViewException
import androidx.test.espresso.PerformException
import androidx.test.espresso.UiController
import androidx.test.espresso.ViewAction
import androidx.test.espresso.action.ViewActions
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.matcher.BoundedMatcher
import androidx.test.espresso.matcher.RootMatchers
import androidx.test.espresso.matcher.ViewMatchers.isDisplayed
import androidx.test.espresso.matcher.ViewMatchers.isRoot
import androidx.test.espresso.matcher.ViewMatchers.withContentDescription
import androidx.test.espresso.matcher.ViewMatchers.withId
import androidx.test.espresso.matcher.ViewMatchers.withText
import androidx.test.espresso.util.HumanReadables
import androidx.test.espresso.util.TreeIterables
import org.hamcrest.BaseMatcher
import org.hamcrest.CoreMatchers.isA
import org.hamcrest.Description
import org.hamcrest.Matcher
import org.hamcrest.Matchers
import org.hamcrest.Matchers.allOf
import org.hamcrest.TypeSafeMatcher
import org.hamcrest.core.AllOf
import java.util.concurrent.TimeoutException

object EspressoUtils {

    fun waitUntilNotShown(
        viewId: Int? = null,
        viewText: String? = null,
        viewDescription: String? = null,
        millis: Long,
        repeatAction: (() -> Unit)? = null
    ): ViewAction {
        return object : ViewAction {
            override fun getConstraints(): Matcher<View> {
                return isRoot()
            }

            @Suppress("MaxLineLength")
            override fun getDescription(): String {
                return "wait for a specific view with id <$viewId> or viewText <$viewText> or viewDescription <$viewDescription> is hidden during $millis millis."
            }

            override fun perform(uiController: UiController, view: View) {
                uiController.loopMainThreadUntilIdle()
                val startTime = System.currentTimeMillis()
                val endTime = startTime + millis
                val viewMatcher: Matcher<View?> = if (viewId != null) {
                    withId(viewId)
                } else {
                    if (viewDescription != null) {
                        withContentDescription(
                            viewDescription
                        )
                    } else {
                        getElementFromMatchAtPosition(AllOf.allOf(withText(viewText)), 0)
                    }
                }

                do {
                    for (child in TreeIterables.breadthFirstViewTraversal(view)) {
                        if (viewMatcher.matches(child) && child.isShown) {
                            return
                        }
                    }
                    uiController.loopMainThreadForAtLeast(50)
                } while (System.currentTimeMillis() < endTime)

                /*
                if (repeatAction != null) {
                    repeatAction()
                    return
                }

                 */

                throw PerformException.Builder()
                    .withActionDescription(this.description)
                    .withViewDescription(HumanReadables.describe(view))
                    .withCause(TimeoutException())
                    .build()
            }
        }
    }

    fun clickClickableSpan(textToClick: CharSequence): ViewAction {
        return object : ViewAction {
            override fun getConstraints(): Matcher<View> {
                return Matchers.instanceOf(TextView::class.java)
            }

            override fun getDescription(): String {
                return "clicking on a ClickableSpan"
            }

            override fun perform(uiController: UiController, view: View) {
                val textView = view as TextView
                val spannableString: SpannableString = textView.text as SpannableString
                if (spannableString.isEmpty()) {
                    throw NoMatchingViewException.Builder()
                        .includeViewHierarchy(true)
                        .withRootView(textView)
                        .build()
                }

                val spans: Array<ClickableSpan> = spannableString.getSpans(
                    0, spannableString.length,
                    ClickableSpan::class.java
                )
                if (spans.isNotEmpty()) {
                    var spanCandidate: ClickableSpan?
                    for (span in spans) {
                        spanCandidate = span
                        val start: Int = spannableString.getSpanStart(spanCandidate)
                        val end: Int = spannableString.getSpanEnd(spanCandidate)
                        val sequence: CharSequence = spannableString.subSequence(start, end)
                        if (textToClick.toString() == sequence.toString()) {
                            span.onClick(textView)
                            return
                        }
                    }
                }

                throw NoMatchingViewException.Builder()
                    .includeViewHierarchy(true)
                    .withRootView(textView)
                    .build()
            }
        }
    }

    fun clickChildViewWithId(id: Int): ViewAction {
        return object : ViewAction {
            override fun getConstraints(): Matcher<View>? {
                return null
            }

            override fun getDescription(): String {
                return "Click on a child view with specified id."
            }

            override fun perform(uiController: UiController, view: View) {
                val v = view.findViewById<View?>(id)
                v?.performClick()
            }
        }
    }

    fun getCountFromRecyclerView(@IdRes recyclerViewId: Int): Int {
        try {
            val count = intArrayOf(0)
            val matcher: Matcher<View> = object : TypeSafeMatcher<View>() {
                protected override fun matchesSafely(item: View): Boolean {
                    count[0] = (item as RecyclerView).adapter!!.itemCount
                    return true
                }

                override fun describeTo(description: org.hamcrest.Description?) {
                }
            }
            onView(allOf(withId(recyclerViewId), isDisplayed())).check(matches(matcher))
            return count[0]
        } catch (e: Exception) {
            return 0
        }
    }

    fun setChecked(checked: Boolean): ViewAction {
        return object : ViewAction {
            override fun getConstraints(): BaseMatcher<View> {
                return object : BaseMatcher<View>() {
                    override fun describeTo(description: Description?) {
                    }

                    override fun matches(item: Any): Boolean {
                        return isA(Checkable::class.java).matches(item)
                    }
                }
            }

            override fun getDescription(): String? {
                return null
            }

            override fun perform(uiController: UiController, view: View) {
                val checkableView = view as Checkable
                checkableView.isChecked = checked
            }
        }
    }

    fun getElementFromMatchAtPosition(
        matcher: Matcher<View>,
        position: Int
    ): Matcher<View?> {
        return object : BaseMatcher<View?>() {
            var counter = 0
            override fun describeTo(description: org.hamcrest.Description?) {
                description?.appendText("Element at hierarchy position $position")
            }

            override fun matches(item: Any): Boolean {
                if (matcher.matches(item)) {
                    if (counter == position) {
                        counter++
                        return true
                    }
                    counter++
                }
                return false
            }
        }
    }

    fun doesViewExist(
        viewId: Int? = null,
        viewText: String? = null,
        viewDescription: String? = null
    ): Boolean {
        val isViewExist = runCatching {
            if (viewId != null) {
                onView(
                    getElementFromMatchAtPosition(
                        allOf(
                            withId(viewId)
                        ), 0
                    )
                ).check(matches(isDisplayed()))
            } else if (viewText != null) {
                onView(
                    getElementFromMatchAtPosition(
                        allOf(
                            withText(
                                viewText
                            )
                        ), 0
                    )
                ).check(matches(isDisplayed()))
            } else if (viewDescription != null) {
                onView(
                    getElementFromMatchAtPosition(
                        allOf(
                            withContentDescription(viewDescription)
                        ), 0
                    )
                ).check(matches(isDisplayed()))
            } else {
                false
            }
        }
        return isViewExist.isSuccess
    }

    fun withText(title: String) = object : BoundedMatcher<View, View>(View::class.java) {
        override fun describeTo(description: Description?) {
            description?.appendText("Searching for title with: $title")
        }

        override fun matchesSafely(item: View?): Boolean {
            val views = ArrayList<View>()
            item?.findViewsWithText(views, title, View.FIND_VIEWS_WITH_TEXT)

            return when (views.size) {
                1 -> true
                else -> false
            }
        }
    }

    fun withIndex(matcher: Matcher<View?>, index: Int): Matcher<View?> {
        return object : TypeSafeMatcher<View?>() {
            var currentIndex = 0
            override fun describeTo(description: Description) {
                description.appendText("with index: ")
                description.appendValue(index)
                matcher.describeTo(description)
            }

            override fun matchesSafely(view: View?): Boolean {
                return matcher.matches(view) && currentIndex++ == index
            }
        }
    }

    fun selectDropdownItem(viewText: String) {
        Espresso.onData(Matchers.equalTo(viewText))
            .inRoot(RootMatchers.isPlatformPopup()).perform(ViewActions.click())
    }

    inline fun <reified activity : Activity> runIn(body: () -> Unit) {
        val activityScenario = ActivityScenario.launch(activity::class.java)
        body.invoke()
        activityScenario.close()
    }
}
