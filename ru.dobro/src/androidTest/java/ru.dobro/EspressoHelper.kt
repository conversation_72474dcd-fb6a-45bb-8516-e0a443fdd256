package ru.dobro

import android.os.SystemClock
import android.util.Log
import androidx.recyclerview.widget.RecyclerView
import androidx.test.espresso.Espresso
import androidx.test.espresso.action.ViewActions
import androidx.test.espresso.assertion.ViewAssertions
import androidx.test.espresso.contrib.RecyclerViewActions
import androidx.test.espresso.matcher.ViewMatchers
import androidx.test.platform.app.InstrumentationRegistry
import junit.framework.AssertionFailedError
import org.hamcrest.core.AllOf.allOf

@Suppress("MaxLineLength")
object EspressoHelper {

    private const val COMMON_DELAY = 1500L
    private const val COMMON_DELAY_WAITING = 5000L

    fun commonDelay(customDelay: Long? = null) {
        SystemClock.sleep(customDelay ?: COMMON_DELAY)
    }

    private fun getNameClassFromResource(idResource: Int): String =
        InstrumentationRegistry.getInstrumentation().targetContext.resources.getResourceEntryName(
            idResource
        )

    private fun waitExistViewWithId(viewId: Int, repeatAction: (() -> Unit)? = null) {
        val isViewExist = EspressoUtils.doesViewExist(viewId = viewId)
        if (!isViewExist) {
            Espresso.onView(ViewMatchers.isRoot())
                .perform(
                    EspressoUtils.waitUntilNotShown(
                        viewId = viewId,
                        millis = COMMON_DELAY_WAITING,
                        repeatAction = repeatAction
                    )
                )
        }
    }

    private fun waitExistViewWithText(viewText: String, repeatAction: (() -> Unit)? = null) {
        val isViewExist = EspressoUtils.doesViewExist(viewText = viewText)
        if (!isViewExist) {
            Espresso.onView(ViewMatchers.isRoot())
                .perform(
                    EspressoUtils.waitUntilNotShown(
                        viewText = viewText,
                        millis = COMMON_DELAY_WAITING,
                        repeatAction = repeatAction
                    )
                )
        }
    }

    private fun waitExistViewWithDescription(
        viewDescription: String,
        repeatAction: (() -> Unit)? = null
    ) {
        val isViewExist = EspressoUtils.doesViewExist(viewDescription = viewDescription)
        if (!isViewExist) {
            Espresso.onView(ViewMatchers.isRoot())
                .perform(
                    EspressoUtils.waitUntilNotShown(
                        viewDescription = viewDescription,
                        millis = COMMON_DELAY_WAITING,
                        repeatAction = repeatAction
                    )
                )
        }
    }

    fun waitUntilNotShown(viewId: Int, millis: Long, repeatAction: (() -> Unit)? = null) {
        Espresso.onView(ViewMatchers.isRoot())
            .perform(
                EspressoUtils.waitUntilNotShown(
                    viewId = viewId,
                    millis = millis,
                    repeatAction = repeatAction
                )
            )
    }

    fun clickToText(viewText: String, withScroll: Boolean = false, repeatAction: Boolean = true) {
        Log.d("EspressoTest", "clickToText viewText = $viewText repeatAction = $repeatAction")
        if (repeatAction) {
            waitExistViewWithText(
                viewText = viewText,
                repeatAction = {
                    clickToText(viewText, withScroll, false)
                }
            )
        } else {
            waitExistViewWithText(
                viewText = viewText,
                repeatAction = null
            )
        }
        if (withScroll) {
            Espresso.onView(ViewMatchers.withText(viewText))
                .perform(ViewActions.scrollTo(), ViewActions.click())
        } else {
            Espresso.onView(ViewMatchers.withText(viewText))
                .perform(ViewActions.click())
        }
    }

    fun clickToItemWithDescription(
        viewDescription: String,
        withScroll: Boolean = false,
        repeatAction: Boolean = true
    ) {
        Log.d(
            "EspressoTest",
            "clickToItemWithDescription viewDescription = $viewDescription repeatAction = $repeatAction"
        )
        if (repeatAction) {
            waitExistViewWithDescription(
                viewDescription = viewDescription,
                repeatAction = {
                    clickToItemWithDescription(viewDescription, withScroll, false)
                }
            )
        } else {
            waitExistViewWithDescription(
                viewDescription = viewDescription,
                repeatAction = null
            )
        }
        if (withScroll) {
            Espresso.onView(ViewMatchers.withContentDescription(viewDescription))
                .perform(ViewActions.scrollTo(), ViewActions.click())
        } else {
            Espresso.onView(ViewMatchers.withContentDescription(viewDescription))
                .perform(ViewActions.click())
        }
    }

    fun clickToId(viewId: Int, withScroll: Boolean = false, repeatAction: Boolean = true) {
        Log.d(
            "EspressoTest",
            "clickToId viewId = ${getNameClassFromResource(viewId)} repeatAction = $repeatAction"
        )
        if (repeatAction) {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = {
                    clickToId(viewId, withScroll, false)
                }
            )
        } else {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = null
            )
        }
        if (withScroll) {
            Espresso.onView(ViewMatchers.withId(viewId))
                .perform(ViewActions.scrollTo(), ViewActions.click())
        } else {
            Espresso.onView(ViewMatchers.withId(viewId))
                .perform(ViewActions.click())
        }
    }

    fun clickToIdWithParent(
        viewId: Int,
        parentViewId: Int,
        withScroll: Boolean = false,
        repeatAction: Boolean = true
    ) {
        Log.d(
            "EspressoTest",
            "clickToIdWithParent viewId = ${getNameClassFromResource(viewId)} parentViewId = ${
                getNameClassFromResource(
                    parentViewId
                )
            } repeatAction = $repeatAction"
        )
        if (repeatAction) {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = {
                    clickToIdWithParent(viewId, parentViewId, withScroll, false)
                }
            )
        } else {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = null
            )
        }
        if (withScroll) {
            Espresso.onView(
                allOf(
                    ViewMatchers.withId(viewId),
                    ViewMatchers.withParent(ViewMatchers.withId(parentViewId))
                )
            )
                .perform(ViewActions.scrollTo(), ViewActions.click())
        } else {
            Espresso.onView(
                allOf(
                    ViewMatchers.withId(viewId),
                    ViewMatchers.withParent(ViewMatchers.withId(parentViewId))
                )
            )
                .perform(ViewActions.click())
        }
    }

    fun clickToSpanText(viewId: Int, spanText: String, withScroll: Boolean = false) {
        Log.d(
            "EspressoTest",
            "clickToSpanText viewId = ${getNameClassFromResource(viewId)} "
        )
        val isViewExist = EspressoUtils.doesViewExist(viewId = viewId)
        if (!isViewExist) {
            Espresso.onView(ViewMatchers.isRoot())
                .perform(
                    EspressoUtils.waitUntilNotShown(
                        viewId = viewId,
                        millis = COMMON_DELAY_WAITING
                    )
                )
        }
        if (withScroll) {
            Espresso.onView(ViewMatchers.withId(viewId))
                .perform(ViewActions.scrollTo(), EspressoUtils.clickClickableSpan(spanText))
        } else {
            Espresso.onView(ViewMatchers.withId(viewId))
                .perform(EspressoUtils.clickClickableSpan(spanText))
        }
    }

    fun clickTextFromMatchAtPosition(
        viewText: String,
        itemIndex: Int,
        repeatAction: Boolean = true
    ) {
        if (repeatAction) {
            waitExistViewWithText(
                viewText = viewText,
                repeatAction = {
                    clickTextFromMatchAtPosition(viewText, itemIndex, false)
                }
            )
        } else {
            waitExistViewWithText(
                viewText = viewText,
                repeatAction = null
            )
        }
        Espresso.onView(
            EspressoUtils.getElementFromMatchAtPosition(
                allOf(
                    ViewMatchers.withText(
                        viewText
                    )
                ), itemIndex
            )
        )
            .perform(ViewActions.click())
    }

    fun recycleViewClickItemAction(
        viewId: Int,
        itemIndex: Int,
        withScroll: Boolean = false,
        repeatAction: Boolean = false,
        repeatCheckCount: Boolean = true,
        multiView: Boolean = true,
    ) {
        Log.d(
            "EspressoTest",
            "recycleViewClickItemAction viewId = ${getNameClassFromResource(viewId)} itemIndex = $itemIndex repeatAction = $repeatAction repeatCheckCount = $repeatCheckCount"
        )
        if (repeatAction) {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = {
                    recycleViewClickItemAction(viewId, itemIndex, withScroll, false)
                }
            )
        } else {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = null
            )
        }

        val rvCount = getRecyclerViewCount(viewId = viewId)
        if (repeatCheckCount && rvCount <= itemIndex) {
            commonDelay(customDelay = 5000L)
            recycleViewClickItemAction(viewId, itemIndex, withScroll, false, false)
        }

        if (withScroll) {
            Espresso.onView(
                if (multiView) {
                    EspressoUtils.getElementFromMatchAtPosition(
                        allOf(
                            ViewMatchers.withId(
                                viewId
                            )
                        ), 0
                    )
                } else {
                    ViewMatchers.withId(
                        viewId
                    )
                }
            ).perform(
                RecyclerViewActions.actionOnItemAtPosition<RecyclerView.ViewHolder>(
                    itemIndex,
                    ViewActions.scrollTo()
                ),
                RecyclerViewActions.actionOnItemAtPosition<RecyclerView.ViewHolder>(
                    itemIndex,
                    ViewActions.click()
                )
            )
        } else {
            Espresso.onView(
                if (multiView) {
                    EspressoUtils.getElementFromMatchAtPosition(
                        allOf(
                            ViewMatchers.withId(
                                viewId
                            )
                        ), 0
                    )
                } else {
                    ViewMatchers.withId(
                        viewId
                    )
                }
            ).perform(
                RecyclerViewActions.actionOnItemAtPosition<RecyclerView.ViewHolder>(
                    itemIndex,
                    ViewActions.click()
                )
            )
        }
    }

    fun recycleViewClickChildItemAction(
        viewId: Int,
        childId: Int,
        itemIndex: Int,
        repeatAction: Boolean = true
    ) {

        Log.d(
            "EspressoTest",
            "recycleViewClickChildItemAction viewId = ${getNameClassFromResource(viewId)}  childId = ${
                getNameClassFromResource(
                    childId
                )
            } itemIndex = $itemIndex repeatAction = $repeatAction"
        )

        if (repeatAction) {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = {
                    recycleViewClickChildItemAction(viewId, childId, itemIndex, false)
                }
            )
        } else {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = null
            )
        }
        Espresso.onView(ViewMatchers.withId(viewId)).perform(
            RecyclerViewActions.actionOnItemAtPosition<RecyclerView.ViewHolder>(
                itemIndex,
                EspressoUtils.clickChildViewWithId(childId)
            )
        )
    }

    fun recycleViewClickTextItemAction(
        viewId: Int,
        viewText: String,
        repeatAction: Boolean = true
    ) {
        Log.d(
            "EspressoTest",
            "recycleViewClickTextItemAction viewId = ${getNameClassFromResource(viewId)} viewText = $viewText repeatAction = $repeatAction"
        )

        if (repeatAction) {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = {
                    recycleViewClickTextItemAction(viewId, viewText, false)
                }
            )
        } else {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = null
            )
        }

        Espresso.onView(ViewMatchers.withId(viewId))
            .perform(
                RecyclerViewActions.actionOnItem<RecyclerView.ViewHolder>(
                    ViewMatchers.hasDescendant(
                        ViewMatchers.withText(viewText)
                    ), ViewActions.click()
                )
            )
    }

    fun replaceTextOnView(
        viewId: Int,
        replacesText: String,
        withScroll: Boolean,
        repeatAction: Boolean = true
    ) {
        Log.d(
            "EspressoTest",
            "replaceTextOnView viewId = ${getNameClassFromResource(viewId)}  replacesText = $replacesText repeatAction = $repeatAction"
        )
        if (repeatAction) {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = {
                    replaceTextOnView(viewId, replacesText, withScroll, false)
                }
            )
        } else {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = null
            )
        }
        if (withScroll) {
            Espresso.onView(ViewMatchers.withId(viewId))
                .perform(ViewActions.scrollTo(), ViewActions.replaceText(replacesText))
        } else {
            Espresso.onView(ViewMatchers.withId(viewId))
                .perform(ViewActions.replaceText(replacesText))
        }
    }

    fun typeTextOnView(
        viewId: Int,
        typedText: String,
        withScroll: Boolean,
        repeatAction: Boolean = true
    ) {
        Log.d(
            "EspressoTest",
            "typeTextOnView viewId = ${getNameClassFromResource(viewId)}  typedText = $typedText repeatAction = $repeatAction"
        )
        if (repeatAction) {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = {
                    typeTextOnView(viewId, typedText, withScroll, false)
                }
            )
        } else {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = null
            )
        }
        if (withScroll) {
            Espresso.onView(ViewMatchers.withId(viewId))
                .perform(ViewActions.scrollTo(), ViewActions.typeText(typedText))
        } else {
            Espresso.onView(ViewMatchers.withId(viewId))
                .perform(ViewActions.typeText(typedText))
        }
    }

    fun getRecyclerViewCount(viewId: Int, repeatAction: Boolean = true): Int {
        Log.d(
            "EspressoTest",
            "getRecyclerViewCount viewId = ${getNameClassFromResource(viewId)} repeatAction = $repeatAction"
        )
        if (repeatAction) {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = {
                    getRecyclerViewCount(viewId, false)
                }
            )
        } else {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = null
            )
        }
        return try {
            Espresso.onView(
                EspressoUtils.getElementFromMatchAtPosition(
                    allOf(
                        ViewMatchers.withId(
                            viewId
                        )
                    ), 0
                )
            )
                .check(ViewAssertions.matches(ViewMatchers.isDisplayed()))

            EspressoUtils.getCountFromRecyclerView(viewId)
        } catch (_: AssertionFailedError) {
            0
        }
    }

    fun scrollToId(viewId: Int, repeatAction: Boolean = true) {
        Log.d(
            "EspressoTest",
            "scrollToId viewId = ${getNameClassFromResource(viewId)} repeatAction = $repeatAction"
        )
        if (repeatAction) {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = {
                    scrollToId(viewId, false)
                }
            )
        } else {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = null
            )
        }
        Espresso.onView(ViewMatchers.withId(viewId))
            .perform(ViewActions.scrollTo())
    }

    fun setCheckbox(
        viewId: Int,
        checked: Boolean,
        withScroll: Boolean,
        repeatAction: Boolean = true
    ) {
        Log.d(
            "EspressoTest",
            "setCheckbox viewId = ${getNameClassFromResource(viewId)} checked = $checked repeatAction = $repeatAction"
        )
        if (repeatAction) {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = {
                    setCheckbox(viewId, checked, withScroll, false)
                }
            )
        } else {
            waitExistViewWithId(
                viewId = viewId,
                repeatAction = null
            )
        }
        if (withScroll) {
            Espresso.onView(ViewMatchers.withId(viewId))
                .perform(
                    ViewActions.scrollTo(),
                    EspressoUtils.setChecked(checked)
                )
        } else {
            Espresso.onView(ViewMatchers.withId(viewId))
                .perform(
                    EspressoUtils.setChecked(checked)
                )
        }
    }

    fun setTextEdit(currentViewText: String, newViewText: String, repeatAction: Boolean = true) {
        Log.d(
            "EspressoTest",
            "setTextEdit currentViewText = $currentViewText newViewText = $newViewText repeatAction = $repeatAction"
        )
        if (repeatAction) {
            waitExistViewWithText(
                viewText = currentViewText,
                repeatAction = {
                    setTextEdit(currentViewText, newViewText, false)
                }
            )
        } else {
            waitExistViewWithText(
                viewText = currentViewText,
                repeatAction = null
            )
        }
        Espresso.onView(ViewMatchers.withText(currentViewText))
            .perform(ViewActions.replaceText(newViewText))
    }

    fun pressBackButton() {
        Thread.sleep(300L)
        Espresso.onView(ViewMatchers.isRoot()).perform(ViewActions.pressBack())
    }
}
