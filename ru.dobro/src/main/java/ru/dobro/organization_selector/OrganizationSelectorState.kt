package ru.dobro.organization_selector

import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import ru.dobro.domain.profile.Organization
import javax.annotation.CheckReturnValue

sealed class OrganizationSelectorState {
    object Idle : OrganizationSelectorState() {
        @CheckReturnValue
        fun requestChoice(): Choice = Choice(
            items = persistentListOf(),
            searchQuery = ""
        )
    }

    data class Choice(
        val items: PersistentList<Organization>,
        val searchQuery: String
    ) : OrganizationSelectorState() {
        @CheckReturnValue
        fun setItems(items: PersistentList<Organization>, query: String) =
            copy(
                items = items,
                searchQuery = query
            )

        @CheckReturnValue
        fun reset() =
            copy(
                items = persistentListOf(),
                searchQuery = ""
            )
    }
}
