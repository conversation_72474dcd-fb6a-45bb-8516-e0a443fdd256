package ru.dobro.organization_selector

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import common.library.android.widget.recycler_view.getRxAsyncDiffer
import common.library.core.EqualityComparators
import common.library.core.rx.invoke
import io.reactivex.Flowable
import io.reactivex.processors.PublishProcessor
import org.reactivestreams.Subscriber
import ru.dobro.databinding.OrganizationSelectorItemBinding
import ru.dobro.domain.profile.Organization

class OrganizationSelectorAdapter :
    ListAdapter<Organization, OrganizationSelectorAdapter.ViewHolder>(
        getRxAsyncDiffer(
            EqualityComparators.value(),
            EqualityComparators.value()
        )
    ) {
    private val _onItemClick: PublishProcessor<Organization> = PublishProcessor.create()

    val onItemClick: Flowable<Organization> get() = _onItemClick.hide()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            OrganizationSelectorItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            ), _onItemClick
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class ViewHolder(
        private val binding: OrganizationSelectorItemBinding,
        private val _onClick: Subscriber<Organization>
    ) : RecyclerView.ViewHolder(
        binding.root
    ) {
        fun bind(item: Organization) {
            binding.apply {
                organizationSelectorItemTitle.text = item.name
                root.setOnClickListener { _onClick(item) }
            }
        }
    }
}
