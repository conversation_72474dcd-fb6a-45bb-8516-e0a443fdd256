package ru.dobro.organization_selector

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.Fragment
import androidx.fragment.app.clearFragmentResult
import androidx.fragment.app.setFragmentResult
import arrow.core.Some
import arrow.core.toOption
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.message.MessageDisplay
import common.library.android.onFragmentResult
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleErrorMessages
import common.library.android.rx.throttleUserInput
import common.library.android.soft_input.Ime
import common.library.android.string.RString
import common.library.android.string.r
import common.library.android.widget.isGoneVisible
import common.library.android.widget.onClick
import common.library.android.widget.onTextChanged
import common.library.core.lazyGet
import common.library.core.logging.logWarning
import common.library.core.rx.RxSchedulers
import common.library.core.rx.ofSubtype
import common.library.core.state.load.LoadDataBySelectorState
import common.library.core.state.load.loadSingleBySelector
import common.library.core.variable.Agent
import common.library.core.variable.agent
import common.library.core.variable.sendWhen
import io.reactivex.Observable
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.collections.immutable.toPersistentList
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.R
import ru.dobro.api.DobroApi
import ru.dobro.core.BaseBottomSheetDialogFragment
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.error.ErrorHandler
import ru.dobro.core.error.ErrorMessages
import ru.dobro.databinding.OrganizationSelectorBinding
import ru.dobro.domain.profile.Organization
import java.util.concurrent.TimeUnit
import javax.annotation.CheckReturnValue

class OrganizationSelectorFragment : BaseBottomSheetDialogFragment({
    bind<Agent<OrganizationSelectorState>>() with singleton { agent(OrganizationSelectorState.Idle) }
    bind<Agent<OrganizationsDataState>>() with singleton { agent(LoadDataBySelectorState.Idle.empty()) }
}) {
    private lateinit var binding: OrganizationSelectorBinding

    private val _state: Agent<OrganizationSelectorState> by instance()

    private val _organizationsDataState: Agent<OrganizationsDataState> by instance()

    private val _bundler: Bundler by instance()

    private val _api: DobroApi by instance()

    private val _messageDisplay: MessageDisplay by instance()

    private val _errorHandler: ErrorHandler by instance()

    private val _adapter: OrganizationSelectorAdapter by lazyGet { OrganizationSelectorAdapter() }

    companion object {
        @CheckReturnValue
        fun create(
            requestKey: String,
            bundler: Bundler
        ): OrganizationSelectorFragment = OrganizationSelectorFragment().apply {
            arguments = requestKey.intoBundle(bundler)
        }

        @CheckReturnValue
        fun onResult(
            receiver: Fragment,
            requestKey: String,
            bundler: Bundler
        ): Observable<Organization> =
            receiver.onFragmentResult(requestKey, bundler)
    }

    init {
        setStyle(STYLE_NORMAL, R.style.Application_BottomSheet)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        dialog.setOnShowListener {
            (it as BottomSheetDialog).findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
                ?.let { sheetView ->
                    sheetView.minimumHeight = resources.displayMetrics.heightPixels
                    sheetView.layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
                    it.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                }
        }
        dialog.behavior.skipCollapsed = true
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = OrganizationSelectorBinding.inflate(inflater)
        return binding.root
    }

    override val screenName: String = AnalyticsConstants.Screen.Common.organizationSelector

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.organizationSelectorList.adapter = _adapter

        binding.organizationSelectorSearch.requestFocus()
        Ime.show().withDelay().to(binding.organizationSelectorSearch)
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        val requestCode: String? = arguments?.getParams(_bundler)
        if (requestCode === null) {
            logger.warning { "Illegal params." }

            dismiss()
            return
        }

        clearFragmentResult(requestCode)

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<OrganizationSelectorState.Idle>()
            .subscribeBy {
                _state.sendWhen<OrganizationSelectorState, OrganizationSelectorState.Idle> {
                    requestChoice()
                }
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<OrganizationSelectorState.Choice>()
            .distinctUntilChanged()
            .observeOn(RxSchedulers.main())
            .subscribeBy { state ->
                if (!binding.organizationSelectorSearch.isFocused) {
                    binding.organizationSelectorSearch.setText(state.searchQuery)
                }
                _adapter.submitList(state.items)
            }
            .scoped()

        binding.organizationSelectorApply.setOnClickListener {
            val organization: Organization = Organization.Companion.restore(
                null,
                binding.organizationSelectorSearch.text.toString()
            )
            setFragmentResult(
                requestCode,
                organization.intoBundle(_bundler)
            )
            dismiss()
        }

        binding.organizationSelectorSearchContainer.setEndIconOnClickListener {
            binding.organizationSelectorSearch.setText("")

            _organizationsDataState.send {
                reset()
            }
        }

        binding.organizationSelectorSearch
            .onTextChanged
            .observeOn(RxSchedulers.computation())
            .debounce(300, TimeUnit.MILLISECONDS)
            .distinctUntilChanged()
            .observeOn(RxSchedulers.main())
            .subscribeBy { input ->
                binding.organizationSelectorApply.isGoneVisible = input.text.isNotBlank()
                if (input.count > 0) {
                    _organizationsDataState.send {
                        reset().load(input.text.toString())
                    }
                } else {
                    _state.sendWhen<OrganizationSelectorState, OrganizationSelectorState.Choice> {
                        reset()
                    }
                }
            }
            .scoped()

        _organizationsDataState
            .loadSingleBySelector {
                _api.profile().getOrganizationsAsync(it)
                    .doOnError { _messageDisplay.showMessage("Не удалось загрузить данные".r) }
                    .logWarning(logger) { "Ошибка поиска организации." }
            }
            .scoped()

        _organizationsDataState
            .observeOn(RxSchedulers.computation())
            .ofSubtype<OrganizationsDataStateLoaded>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state.sendWhen<OrganizationSelectorState, OrganizationSelectorState.Choice> {
                    setItems(
                        items = it.data
                            .filterNot { it.name.isNullOrEmpty() }
                            .toPersistentList(),
                        query = it.selector
                    )
                }
                _organizationsDataState.send {
                    reset()
                }
            }
            .scoped()

        _organizationsDataState
            .observeOn(RxSchedulers.computation())
            .ofSubtype<OrganizationsDataStateFailed>()
            .map { ErrorMessages.ofException(_errorHandler.ofException(it.error)).toOption() }
            .doOnNext { _organizationsDataState.sendWhen<OrganizationsDataState, OrganizationsDataStateFailed> { handle() } }
            .ofSubtype<Some<RString>>()
            .throttleErrorMessages()
            .observeOn(RxSchedulers.main())
            .subscribeBy { _messageDisplay.showMessage(it.t) }
            .scoped()

        binding.organizationSelectorClose
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                dismiss()
            }
            .scoped()

        _adapter
            .onItemClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Common.organizationSelectorOrganizationClick)
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Common.organizationSelectorOrganizationClick)

                setFragmentResult(
                    requestCode,
                    it.intoBundle(_bundler)
                )
                dismiss()
            }
            .scoped()
    }
}
