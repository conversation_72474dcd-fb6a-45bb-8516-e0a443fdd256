package ru.dobro.account_disabled

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.DialogFragment
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import common.library.android.getSupportColor
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.intent.newTask
import common.library.android.intent.sendTo
import common.library.android.intent.tryStartActivityBy
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.string.span.spannedString
import common.library.android.widget.onClick
import common.library.core.Characters
import common.library.core.email.Email
import common.library.core.rx.RxSchedulers
import io.reactivex.rxkotlin.subscribeBy
import org.kodein.di.generic.instance
import ru.dobro.R
import ru.dobro.core.BaseDialogFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.databinding.FragmentAccountDisabledBinding
import ru.dobro.main.MainActivity
import javax.annotation.CheckReturnValue

class AccountDisabledFragment : BaseDialogFragment(), HasCustomToolbar {
    override val screenName: String = AnalyticsConstants.Screen.Login.accountDisabled

    private lateinit var binding: FragmentAccountDisabledBinding

    private val _bundler: Bundler by instance()

    companion object {
        class NavigationContext(
            private val _navigation: NavController,
            private val _bundler: Bundler
        ) {
            fun toAccountDisabledWithRecreate(
                recreateActivity: Boolean
            ) {
                _navigation.navigate(
                    R.id.account_disabled,
                    recreateActivity.intoBundle(_bundler),
                    NavOptions.Builder()
                        .setPopUpTo(R.id.main_navigation, false)
                        .setLaunchSingleTop(true)
                        .build()
                )
            }
        }

        @CheckReturnValue
        fun navigate(
            navigation: NavController,
            bundler: Bundler
        ): NavigationContext = NavigationContext(navigation, bundler)
    }

    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        HasCustomToolbar.CustomToolbar.None

    init {
        setStyle(DialogFragment.STYLE_NORMAL, R.style.Application_Theme)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)

        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        isCancelable = false
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = FragmentAccountDisabledBinding.inflate(inflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        (requireActivity() as MainActivity).apply {
            updateBottomNav()
            scheduleBottomNavigationAllTabsRefresh()
        }

        requireActivity().onBackPressedDispatcher.addCallback(
            viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    navigateBack()
                }
            })

        binding.accountDisabledSubtitle.movementMethod = LinkMovementMethod.getInstance()
        binding.accountDisabledSubtitle.text = requireContext().spannedString {
            it
                .append(getString(R.string.account_disabled___subtitle_part1))
                .append(Characters.nonBreakingSpace)
                .append("<EMAIL>")
                .spans {
                    object : ClickableSpan() {
                        override fun onClick(p0: View) {
                            tryStartActivityBy {
                                it
                                    .sendTo(Email("info", "dobro.ru", Email.CaseSensitivity.None))
                                    .newTask()
                            }
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            ds.isUnderlineText = false
                            ds.color =
                                requireContext().getSupportColor(R.color.application___color__primary)
                        }
                    }
                }
                .append(Characters.nonBreakingSpace)
                .append(getString(R.string.account_disabled___subtitle_part2))
        }
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        binding.accountDisabledPerform
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                navigateBack()
            }
            .scoped()
    }

    private fun navigateBack() {
        val shouldRecreateActivity: Boolean = arguments?.getParams(_bundler) ?: false

        if (shouldRecreateActivity) {
            (requireActivity() as MainActivity).dropCurrentTabBackstack()
        } else {
            findNavController().navigateUp()
        }
    }
}
