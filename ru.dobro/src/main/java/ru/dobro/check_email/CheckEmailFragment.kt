package ru.dobro.check_email

import android.os.Bundle
import android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import androidx.annotation.IdRes
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import common.library.android.coroutines.repeatOnCreate
import common.library.android.getSupportColor
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.intent.tryRunEmailApp
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.string.span.spannedString
import common.library.android.widget.isGoneVisible
import common.library.android.widget.onClick
import common.library.core.Characters
import common.library.core.email.Email
import common.library.core.ifAllNotNull
import common.library.core.rx.RxSchedulers
import common.library.core.rx.invoke
import io.reactivex.processors.PublishProcessor
import io.reactivex.rxkotlin.subscribeBy
import org.kodein.di.generic.instance
import ru.dobro.R
import ru.dobro.api.DobroApi
import ru.dobro.authorization.authorizationCompletedSuccessfully
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.NavigateBackHandler
import ru.dobro.core.account.AccountManager
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.settings.Settings
import ru.dobro.core.settings.UserSettings
import ru.dobro.databinding.CheckEmailBinding
import ru.dobro.main.MainActivity
import ru.dobro.registration.RegistrationFragment
import ru.dobro.registration.RegistrationViewModel
import javax.annotation.CheckReturnValue

class CheckEmailFragment : BaseFragment(), HasCustomToolbar, NavigateBackHandler {
    private lateinit var binding: CheckEmailBinding

    companion object {
        class NavigationContext(
            private val _navigation: NavController,
            private val _bundler: Bundler
        ) {
            @CheckReturnValue
            fun toCheckEmail(
                @IdRes actionId: Int,
            ) {
                _navigation.navigate(
                    actionId,
                    CheckEmailRequest(
                        openedByDeeplink = false,
                        emailRegistrationData = null
                    ).intoBundle(_bundler)
                )
            }

            @CheckReturnValue
            fun toCheckEmailFromEmailRegistration(
                @IdRes actionId: Int,
                emailRegistrationData: RegistrationViewModel.State
            ) {
                _navigation.navigate(
                    actionId,
                    CheckEmailRequest(
                        openedByDeeplink = false,
                        emailRegistrationData = emailRegistrationData
                    ).intoBundle(_bundler)
                )
            }
        }

        @CheckReturnValue
        fun navigate(
            navigation: NavController,
            bundler: Bundler
        ): NavigationContext = NavigationContext(navigation, bundler)
    }

    private val _bundler: Bundler by instance()

    private val _api: DobroApi by instance()

    private val _accountManager: AccountManager by instance()

    val userSettings: UserSettings by instance()

    private val _settings: Settings by instance()

    override val screenName: String = AnalyticsConstants.Screen.Login.checkEmail

    private val _onChooseAnotherEmailClick: PublishProcessor<Unit> = PublishProcessor.create()

    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        HasCustomToolbar.CustomToolbar.None

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = CheckEmailBinding.inflate(inflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        repeatOnCreate {
            authorizeIfApplicable()
        }
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        binding.checkEmailOpenMail
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                tryRunEmailApp(getString(R.string.application___choose__title))
            }
            .scoped()

        binding.checkEmailClose
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                findNavController().navigateUp()
                findNavController().navigateUp()
            }
            .scoped()

        _onChooseAnotherEmailClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                val request: CheckEmailRequest? = arguments?.getParams(_bundler)
                if (request?.openedByDeeplink == true) {
                    findNavController().navigate(R.id.check_email__to__registration)
                } else if (request?.emailRegistrationData != null) {
                    _accountManager.removeAccount()
                    _accountManager.setAccountType(ru.dobro.core.account.AccountType.Physic)

                    (requireActivity() as MainActivity).apply {
                        updateBottomNav()
                        scheduleBottomNavigationAllTabsRefresh()
                        hideNotificationsCountBadge()
                    }

                    RegistrationFragment
                        .navigate(findNavController(), _bundler)
                        .toRegistrationWithData(
                            R.id.check_email__to__registration,
                            request.emailRegistrationData
                        )
                } else {
                    findNavController().navigateUp()
                }
            }
            .scoped()

        ifAllNotNull(
            context?.getString(R.string.check_email___email_didnt_received__not_clickable_part),
            context?.getString(R.string.check_email___email_didnt_received__clickable_part)
        ) { notClickablePart, clickablePart ->
            val spannedString = requireContext().spannedString {
                it
                    .append(notClickablePart)
                    .append(Characters.nonBreakingSpace)
                    .append(clickablePart)
            }

            val myClickableSpan: ClickableSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    _onChooseAnotherEmailClick.invoke()
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.isUnderlineText = false
                    ds.color =
                        requireContext().getSupportColor(R.color.application___color__primary)
                }
            }

            spannedString.setSpan(
                myClickableSpan,
                notClickablePart.length + 1,
                spannedString.length,
                SPAN_EXCLUSIVE_EXCLUSIVE
            )

            binding.checkEmailNoEmailReceived.apply {
                movementMethod = LinkMovementMethod.getInstance()
                text = spannedString
            }
        }
    }

    override fun onNavigateBack(): NavigateBackHandler.Result {
        findNavController().popBackStack()
        findNavController().popBackStack()
        return NavigateBackHandler.Result.Consumed
    }

    private suspend fun authorizeIfApplicable() {
        val request: CheckEmailRequest? = arguments?.getParams(_bundler)
        if (request?.emailRegistrationData != null) {
            binding.progress.isVisible(true)

            try {
                val result = _api.overview().authorize(
                    email = Email(
                        localPart = request.emailRegistrationData.email.substringBefore("@"),
                        domain = request.emailRegistrationData.email.substringAfter("@"),
                        caseSensitivity = Email.CaseSensitivity.None
                    ),
                    password = request.emailRegistrationData.password,
                    clientId = getString(R.string.client_id),
                    clientSecret = getString(R.string.client_secret),
                )

                authorizationCompletedSuccessfully(
                    authResponse = result,
                    onCompleteEvent = {
                        binding.progress.isVisible(false)
                        (activity as? MainActivity)?.scheduleBottomNavigationAllTabsRefresh()
                    },
                    mainActivity = (activity as? MainActivity)
                )
            } catch (e: Exception) {
                binding.progress.isVisible(false)
            }
        }
    }

    private fun ProgressBar.isVisible(isVisible: Boolean) {
        listOf(
            binding.checkEmailOpenMail,
            binding.checkEmailClose,
            binding.checkEmailNoEmailReceived,
        ).forEach { it.isEnabled = !isVisible }
        binding.progress.isGoneVisible = isVisible
    }
}
