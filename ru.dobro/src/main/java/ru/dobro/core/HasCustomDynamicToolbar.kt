package ru.dobro.core

import androidx.appcompat.widget.Toolbar
import com.google.android.material.appbar.AppBarLayout
import kotlinx.coroutines.flow.Flow
import javax.annotation.CheckReturnValue

interface HasCustomDynamicToolbar<Event> : HasCustomToolbar {
    fun onUpdateCustomToolbar(event: Event, appBar: AppBarLayout?, toolbar: Toolbar)

    @CheckReturnValue
    fun onCustomToolbarChanged(): Flow<Event>
}
