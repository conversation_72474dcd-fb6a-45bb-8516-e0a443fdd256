package ru.dobro.core

import android.text.Html
import android.text.SpannableString
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ForegroundColorSpan
import android.text.style.URLSpan
import android.widget.TextView
import androidx.annotation.StringRes
import com.google.android.material.textfield.TextInputLayout
import common.library.android.string.span.spannedString
import ru.dobro.R
import javax.annotation.CheckReturnValue

@CheckReturnValue
fun TextView.setTextWithHtml(
    text: String?,
) {
    text?.let {
        this.text = Html.fromHtml(
            text,
            Html.FROM_HTML_MODE_LEGACY,
            HtmlImageGetter(textView = this, matchParentWidth = false, densityAware = true),
            null
        )
    }
}

fun TextView.setHtmlText(
    htmlText: String,
    linkColor: Int? = resources.getColor(R.color.application___color__primary, null),
    shouldUnderlineLinks: Boolean = false
) {
    val modifiedHtmlText = htmlText
        .replace("\n", "<br/>") // заменяем чтобы отображать /n
        .replace(
            "<li>",
            "<li>&nbsp;&nbsp;&nbsp;"
        ) // добавляет расстояние между ⬤ и текстом при перечислении

    val spannableString =
        SpannableString(Html.fromHtml(modifiedHtmlText, Html.FROM_HTML_MODE_COMPACT))
    for (link in spannableString.getSpans(0, spannableString.length, URLSpan::class.java)) {
        spannableString.setSpan(object : URLSpan(link.url) {
            override fun updateDrawState(textPaint: TextPaint) {
                linkColor?.let { textPaint.color = it }
                textPaint.isUnderlineText = shouldUnderlineLinks
            }
        }, spannableString.getSpanStart(link), spannableString.getSpanEnd(link), 0)
    }
    this.text = spannableString
    this.movementMethod = LinkMovementMethod.getInstance()
}

fun TextInputLayout.setFieldAsRequired(@StringRes title: Int) {
    hint = context.spannedString {
        it.append(title)
            .append(common.library.core.Characters.asterisk)
            .spans { ForegroundColorSpan(context.getColor(R.color.application___color__accent)) }
    }
}
