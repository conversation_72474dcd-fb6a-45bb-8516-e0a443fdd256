package ru.dobro.core

import android.content.Context
import android.os.Bundle
import android.os.Parcelable
import android.text.Editable
import android.text.SpannableStringBuilder
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatEditText
import ru.dobro.R

/**
 * Класс для ввода номера телефона при авторизации с маской
 *
 */
class MaskedEditText : AppCompatEditText, TextWatcher {

    companion object {
        private const val SPACE = " "
    }

    private var mask: String? = null
    private var charRepresentation = 0.toChar()
    private var keepHint = false
    private lateinit var rawToMask: IntArray
    private var rawText: RawText? = null
    private var editingBefore = false
    private var editingOnChanged = false
    private var editingAfter = false
    private lateinit var maskToRaw: IntArray
    private var selectionNumber = 0
    private var initialized = false
    private var ignore = false
    private var maxRawLength = 0
    private var lastValidMaskPosition = 0
    private var selectionChanged = false
    private var focusChangeListener: OnFocusChangeListener? = null
    private var allowedChars: String? = null
    private var deniedChars: String? = null

    constructor(context: Context?) : super(context!!) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
        val attributes = context.obtainStyledAttributes(attrs, R.styleable.MaskedEditText)
        mask = attributes.getString(R.styleable.MaskedEditText_mask)
        allowedChars = attributes.getString(R.styleable.MaskedEditText_allowed_chars)
        deniedChars = attributes.getString(R.styleable.MaskedEditText_denied_chars)
        val representation = attributes.getString(R.styleable.MaskedEditText_char_representation)
        charRepresentation = if (representation == null) {
            '#'
        } else {
            representation[0]
        }
        keepHint = attributes.getBoolean(R.styleable.MaskedEditText_keep_hint, false)
        cleanUp()

        // Ignoring enter key presses
        /*setOnEditorActionListener { _, actionId, _ ->
            when (actionId) {
                else -> true
            }
        }*/
        attributes.recycle()
    }

    override fun onSaveInstanceState(): Parcelable? {
        val superParcellable = super.onSaveInstanceState()
        val state = Bundle()
        state.putParcelable("super", superParcellable)
        state.putString("text", getRawText())
        state.putBoolean("keepHint", isKeepHint())
        return state
    }

    override fun onRestoreInstanceState(state: Parcelable) {
        val bundle = state as Bundle
        keepHint = bundle.getBoolean("keepHint", false)
        super.onRestoreInstanceState(state.getParcelable("super"))
        val text = bundle.getString("text")
        setText(text)
    }

    /** @param listener - its onFocusChange() method will be called before performing MaskedEditText operations,
     * related to this event.
     */
    override fun setOnFocusChangeListener(listener: OnFocusChangeListener) {
        focusChangeListener = listener
    }

    private fun cleanUp() {
        initialized = false
        generatePositionArrays()
        rawText = RawText()
        selectionNumber = rawToMask[0]
        editingBefore = true
        editingOnChanged = true
        editingAfter = true
        if (hasHint() && rawText!!.length() == 0) {
            this.setText(makeMaskedTextWithHint())
        } else {
            this.setText(makeMaskedText())
        }
        editingBefore = false
        editingOnChanged = false
        editingAfter = false
        maxRawLength = maskToRaw[previousValidPosition(mask!!.length - 1)] + 1
        lastValidMaskPosition = findLastValidMaskPosition()
        initialized = true
        super.setOnFocusChangeListener { v, hasFocus ->
            if (focusChangeListener != null) {
                focusChangeListener!!.onFocusChange(v, hasFocus)
            }
            if (hasFocus()) {
                selectionChanged = false
                <EMAIL>(lastValidPosition())
            }
        }
    }

    private fun findLastValidMaskPosition(): Int {
        for (i in maskToRaw.indices.reversed()) {
            if (maskToRaw[i] != -1) return i
        }
        throw IndexOutOfBoundsException("Mask must contain at least one representation char")
    }

    private fun hasHint(): Boolean {
        return hint != null
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyle: Int) : super(
        context!!, attrs, defStyle
    ) {
        init()
    }

    fun setMask(mask: String?) {
        this.mask = mask
        cleanUp()
    }

    fun getMask(): String? {
        return mask
    }

    fun getRawText(): String {
        return rawText!!.text
    }

    fun setCharRepresentation(charRepresentation: Char) {
        this.charRepresentation = charRepresentation
        cleanUp()
    }

    fun getCharRepresentation(): Char {
        return charRepresentation
    }

    /**
     * Generates positions for values characters. For instance:
     * Input data: mask = "+7(###)###-##-##
     * After method execution:
     * rawToMask = [3, 4, 5, 6, 8, 9, 11, 12, 14, 15]
     * maskToRaw = [-1, -1, -1, 0, 1, 2, -1, 3, 4, 5, -1, 6, 7, -1, 8, 9]
     * charsInMask = "+7()- " (and space, yes)
     */
    private fun generatePositionArrays() {
        val aux = IntArray(mask!!.length)
        maskToRaw = IntArray(mask!!.length)
        var charsInMaskAux = ""
        var charIndex = 0
        for (i in mask!!.indices) {
            val currentChar = mask!![i]
            if (currentChar == charRepresentation) {
                aux[charIndex] = i
                maskToRaw[i] = charIndex++
            } else {
                val charAsString = currentChar.toString()
                if (!charsInMaskAux.contains(charAsString)) {
                    charsInMaskAux += charAsString
                }
                maskToRaw[i] = -1
            }
        }
        if (charsInMaskAux.indexOf(' ') < 0) {
            charsInMaskAux += SPACE
        }
        rawToMask = IntArray(charIndex)
        for (i in 0 until charIndex) {
            rawToMask[i] = aux[i]
        }
    }

    private fun init() {
        addTextChangedListener(this)
    }

    override fun beforeTextChanged(
        s: CharSequence, start: Int, count: Int,
        after: Int
    ) {
        if (!editingBefore) {
            editingBefore = true
            if (start > lastValidMaskPosition) {
                ignore = true
            }
            var rangeStart = start
            if (after == 0) {
                rangeStart = erasingStart(start)
            }
            val range = calculateRange(rangeStart, start + count)
            if (range.start != -1) {
                rawText!!.subtractFromString(range)
            }
            if (count > 0) {
                selectionNumber = previousValidPosition(start)
            }
        }
    }

    private fun erasingStart(start: Int): Int {
        var startParam = start
        while (startParam > 0 && maskToRaw[startParam] == -1) {
            startParam--
        }
        return startParam
    }

    override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
        var countParam = count
        if (!editingOnChanged && editingBefore) {
            editingOnChanged = true
            if (ignore) {
                return
            }
            if (countParam > 0) {
                val startingPosition = maskToRaw[nextValidPosition(start)]
                val addedString = s.subSequence(start, start + countParam).toString()
                countParam =
                    rawText!!.addToString(clear(addedString), startingPosition, maxRawLength)
                if (initialized) {
                    val currentPosition: Int =
                        if (startingPosition + countParam < rawToMask.size) rawToMask[startingPosition + countParam] else lastValidMaskPosition + 1
                    selectionNumber = nextValidPosition(currentPosition)
                }
            }
        }
    }

    override fun afterTextChanged(s: Editable) {
        if (!editingAfter && editingBefore && editingOnChanged) {
            editingAfter = true
            if (hasHint() && (keepHint || rawText!!.length() == 0)) {
                setText(makeMaskedTextWithHint())
            } else {
                setText(makeMaskedText())
            }
            selectionChanged = false
            setSelection(selectionNumber)
            editingBefore = false
            editingOnChanged = false
            editingAfter = false
            ignore = false
        }
    }

    fun isKeepHint(): Boolean {
        return keepHint
    }

    fun setKeepHint(keepHint: Boolean) {
        this.keepHint = keepHint
        setText(getRawText())
    }

    override fun onSelectionChanged(selStart: Int, selEnd: Int) {
        // On Android 4+ this method is being called more than 1 time if there is a hint in the EditText, what moves the cursor to left
        // Using the boolean var selectionChanged to limit to one execution
        var selStartParam = selStart
        var selEndParam = selEnd
        if (initialized) {
            if (!selectionChanged) {
                selStartParam = fixSelection(selStartParam)
                selEndParam = fixSelection(selEndParam)

                // exactly in this order. If getText.length() == 0 then selStart will be -1
                if (selStartParam > text!!.length) selStartParam = text!!.length
                if (selStartParam < 0) selStartParam = 0

                // exactly in this order. If getText.length() == 0 then selEnd will be -1
                if (selEndParam > text!!.length) selEndParam = text!!.length
                if (selEndParam < 0) selEndParam = 0
                setSelection(selStartParam, selEndParam)
                selectionChanged = true
            } else {
                // check to see if the current selection is outside the already entered text
                if (selStartParam > rawText!!.length() - 1) {
                    val start = fixSelection(selStartParam)
                    val end = fixSelection(selEndParam)
                    if (start >= 0 && end < text!!.length) {
                        setSelection(start, end)
                    }
                }
            }
        }
        super.onSelectionChanged(selStartParam, selEndParam)
    }

    private fun fixSelection(selection: Int): Int {
        return if (selection > lastValidPosition()) {
            lastValidPosition()
        } else {
            nextValidPosition(selection)
        }
    }

    private fun nextValidPosition(currentPosition: Int): Int {
        var currentPositionParam = currentPosition
        while (currentPositionParam < lastValidMaskPosition && maskToRaw[currentPositionParam] == -1) {
            currentPositionParam++
        }
        return if (currentPositionParam > lastValidMaskPosition) lastValidMaskPosition + 1 else currentPositionParam
    }

    private fun previousValidPosition(currentPosition: Int): Int {
        var currentPositionParam = currentPosition
        while (currentPositionParam >= 0 && maskToRaw[currentPositionParam] == -1) {
            currentPositionParam--
            if (currentPositionParam < 0) {
                return nextValidPosition(0)
            }
        }
        return currentPositionParam
    }

    private fun lastValidPosition(): Int {
        return if (rawText!!.length() == maxRawLength) {
            rawToMask[rawText!!.length() - 1] + 1
        } else {
            nextValidPosition(rawToMask[rawText!!.length()])
        }
    }

    private fun makeMaskedText(): String {
        val maskedTextLength: Int = if (rawText!!.length() < rawToMask.size) {
            rawToMask[rawText!!.length()]
        } else {
            mask!!.length
        }

        val maskedText =
            CharArray(maskedTextLength) // mask.replace(charRepresentation, ' ').toCharArray();
        for (i in maskedText.indices) {
            val rawIndex = maskToRaw[i]
            if (rawIndex == -1) {
                maskedText[i] = mask!![i]
            } else {
                maskedText[i] = rawText!!.charAt(rawIndex)
            }
        }
        return String(maskedText)
    }

    private fun makeMaskedTextWithHint(): CharSequence {
        val ssb = SpannableStringBuilder()
        var mtrv: Int
        val maskFirstChunkEnd = rawToMask[0]
        for (i in mask!!.indices) {
            mtrv = maskToRaw[i]
            if (mtrv != -1) {
                if (mtrv < rawText!!.length()) {
                    ssb.append(rawText!!.charAt(mtrv))
                } else {
                    ssb.append(hint[maskToRaw[i]])
                }
            } else {
                ssb.append(mask!![i])
            }
            if (keepHint && rawText!!.length() < rawToMask.size && i >= rawToMask[rawText!!.length()]
                || !keepHint && i >= maskFirstChunkEnd
            ) {
                ssb.setSpan(ForegroundColorSpan(currentHintTextColor), i, i + 1, 0)
            }
        }
        return ssb
    }

    private fun calculateRange(start: Int, end: Int): Range {
        val range = Range()
        var i = start
        while (i <= end && i < mask!!.length) {
            if (maskToRaw[i] != -1) {
                if (range.start == -1) {
                    range.start = maskToRaw[i]
                }
                range.end = maskToRaw[i]
            }
            i++
        }
        if (end == mask!!.length) {
            range.end = rawText!!.length()
        }
        if (range.start == range.end && start < end) {
            val newStart = previousValidPosition(range.start - 1)
            if (newStart < range.start) {
                range.start = newStart
            }
        }
        return range
    }

    private fun clear(string: String): String {
        var stringParam = string
        if (deniedChars != null) {
            for (c in deniedChars!!.toCharArray()) {
                stringParam = stringParam.replace(c.toString(), "")
            }
        }
        if (allowedChars != null) {
            val builder = StringBuilder(stringParam.length)
            for (c in stringParam.toCharArray()) {
                if (allowedChars!!.contains(c.toString())) {
                    builder.append(c)
                }
            }
            stringParam = builder.toString()
        }
        return stringParam
    }

    class Range internal constructor() {
        var start: Int = -1
        var end: Int = -1
    }

    inner class RawText {
        var text = ""
            private set

        /**
         * text = 012345678, range = 123 =&gt; text = 0456789
         * @param range given range
         */
        fun subtractFromString(range: Range) {
            var firstPart = ""
            var lastPart = ""
            if (range.start > 0 && range.start <= text.length) {
                firstPart = text.substring(0, range.start)
            }
            if (range.end >= 0 && range.end < text.length) {
                lastPart = text.substring(range.end, text.length)
            }
            text = firstPart + lastPart
        }

        /**
         *
         * @param newString New String to be added
         * @param start Position to insert newString
         * @param maxLength Maximum raw text length
         * @return Number of added characters
         */
        fun addToString(newString: String?, start: Int, maxLength: Int): Int {
            var newStringParam = newString
            var firstPart = ""
            var lastPart = ""
            if (newStringParam == null || newStringParam == "") {
                return 0
            } else {
                require(start >= 0) { "Start position must be non-negative" }
            }
            require(start <= text.length) { "Start position must be less than the actual text length" }
            var count = newStringParam.length
            if (start > 0) {
                firstPart = text.substring(0, start)
            }
            if (start >= 0 && start < text.length) {
                lastPart = text.substring(start, text.length)
            }
            if (text.length + newStringParam.length > maxLength) {
                count = maxLength - text.length
                newStringParam = newStringParam.substring(0, count)
            }
            text = firstPart + newStringParam + lastPart
            return count
        }

        fun length(): Int {
            return text.length
        }

        fun charAt(position: Int): Char {
            return text[position]
        }
    }
}
