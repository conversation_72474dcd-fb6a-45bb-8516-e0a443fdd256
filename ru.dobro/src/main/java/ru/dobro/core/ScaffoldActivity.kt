package ru.dobro.core

import android.os.Bundle
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.activity.enableEdgeToEdge
import androidx.annotation.IdRes
import androidx.annotation.LayoutRes
import androidx.annotation.NavigationRes
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.NavigationUI
import androidx.navigation.ui.setupWithNavController
import common.library.android.BasicActivity
import common.library.android.FragmentLifecycleEvent
import common.library.android.coroutines.repeatOnStart
import common.library.android.coroutines.subscribeToFlow
import common.library.android.coroutines.throttleUserInput
import common.library.android.di.DependencyBuilder
import common.library.android.di.bindNavigationFromContainer
import common.library.android.inflateInto
import common.library.android.message.MessageDisplay
import common.library.android.message.SnackbarMessageDisplay
import common.library.android.message.ToastMessageDisplay
import common.library.android.widget.gone
import common.library.android.widget.isGoneVisible
import common.library.android.widget.isVisible
import common.library.android.widget.visible
import common.library.core.location.UserLocationProvider
import common.library.core.network.NetworkMonitor
import common.library.mobile.services.InAppUpdatesProviderImpl
import common.library.mobile.services.di.mobileServicesModule
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.PersistentSet
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentSetOf
import kotlinx.collections.immutable.toPersistentSet
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.reactive.asFlow
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.R
import ru.dobro.core.HasCustomToolbar.CustomToolbar
import ru.dobro.core.HasCustomToolbar.CustomToolbarStyle
import ru.dobro.core.account.AccountManager
import ru.dobro.core.error.ErrorHandler
import ru.dobro.core.settings.Settings
import ru.dobro.core.window.restoreStatusbarColorToDefaults
import ru.dobro.core.window.setStatusbarColor
import ru.dobro.databinding.CommonScaffoldBinding
import javax.annotation.CheckReturnValue

abstract class ScaffoldActivity(
    @IdRes val navigationHostContainerId: Int,
    @LayoutRes val contentId: Int? = null,
    private val bottomNavigationConfig: BottomNavigationConfig? = null,
    dependency: DependencyBuilder<AppCompatActivity> = {}
) : BasicActivity({
    bind<MessageDisplay>(overrides = true) with singleton {
        SnackbarMessageDisplay(instance(), instance())
    }
    bind<MessageDisplay>(MessageDisplayScope.Integrated) with singleton {
        SnackbarMessageDisplay(instance(), instance())
    }
    bind<MessageDisplay>(MessageDisplayScope.Overlay) with singleton {
        ToastMessageDisplay(instance())
    }

    bindNavigationFromContainer(it, navigationHostContainerId)

    bind<ErrorHandler>() with singleton {
        ErrorHandler(instance(), instance(), instance())
    }

    bind<UserLocationProvider>() with singleton { UserLocationProvider(it) }

    import(mobileServicesModule { InAppUpdatesProviderImpl(it) })

    dependency(it)
}) {
    private lateinit var binding: CommonScaffoldBinding

    protected val navigation: NavController by instance()

    private val _accountManager: AccountManager by instance()

    private val _networkMonitor: NetworkMonitor by instance()

    private val _settings: Settings by instance()

    private var _navigationConfiguration: AppBarConfiguration? = null

    private val _navigateBack = MutableSharedFlow<Unit>()
    private var _navigateBackHandlers = persistentSetOf<NavigateBackHandler>()

    protected abstract val defaultCustomToolbar: HasCustomToolbar
    protected abstract val topLevelDestinationIds: PersistentSet<Int>

    private val _innerTopLevelDestinationIds: MutableSet<Int> = mutableSetOf()

    open val bottomNavigationTabDestinationIds: List<Int> = emptyList()

    var currentNavController: NavController? = null

    private val _onToolbarContainersChanged = MutableSharedFlow<ToolbarContainers>(
        replay = 1, extraBufferCapacity = 1
    )

    private var selectedFragment: NavHostFragment? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        onBackPressedDispatcher.addCallback(this) {
            lifecycleScope.launch {
                _navigateBack.emit(Unit)
            }
        }

        _innerTopLevelDestinationIds.apply {
            clear()
            addAll(topLevelDestinationIds)
        }

        _navigationConfiguration = AppBarConfiguration(_innerTopLevelDestinationIds)

        enableEdgeToEdge()

        //region Setup content.
        binding = CommonScaffoldBinding.inflate(layoutInflater)

        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val bars = insets.getInsets(
                WindowInsetsCompat.Type.systemBars()
                    or WindowInsetsCompat.Type.displayCutout()
            )
            val ime = insets.getInsets(WindowInsetsCompat.Type.ime())

            v.updatePadding(
                left = bars.left,
                top = bars.top,
                right = bars.right,
                bottom = maxOf(bars.bottom, ime.bottom),
            )
            WindowInsetsCompat.CONSUMED
        }

        setContentView(binding.root)

        _onToolbarContainersChanged.tryEmit(
            ToolbarContainers(
                shift = binding.commonScaffoldToolbarShiftContainer,
                overlap = binding.commonScaffoldToolbarOverlapContainer
            )
        )

        if (contentId !== null) {
            layoutInflater.inflateInto(contentId, binding.commonScaffoldContainer)
        }
        if (bottomNavigationConfig?.bottomNavigationId !== null) {
            layoutInflater.inflateInto(
                bottomNavigationConfig.bottomNavigationId,
                binding.commonScaffoldBottomContent
            )
        }
        // Reset navigator state.
        if (savedInstanceState !== null) {
            requireNavController().popBackStack(
                requireNavController().graph.startDestinationId,
                false
            )
        }

        setupBackNavigationHandling()
        setupFragmentLifecycleToolbarSupport()
        setupFragmentLifecycleBottomVisibility()
        setupNetworkMonitor()

        //endregion Setup content.
    }

    final override fun onSupportNavigateUp(): Boolean {
        lifecycleScope.launch {
            _navigateBack.emit(Unit)
        }
        return true
    }

    private fun setupBackNavigationHandling() {
        repeatOnStart {
            _navigateBack
                .throttleUserInput()
                .subscribeToFlow {
                    val results = _navigateBackHandlers
                        .map { it.onNavigateBack() }
                        .toPersistentSet()
                    when {
                        NavigateBackHandler.Result.Exit in results -> finishAfterTransition()
                        NavigateBackHandler.Result.Consumed in results -> { /* noop */
                        }

                        else -> {
                            if (!NavigationUI.navigateUp(
                                    requireNavController(),
                                    _navigationConfiguration!!
                                )
                            ) {
                                finishAfterTransition()
                            }
                        }
                    }
                }
        }
    }

    private fun setupFragmentLifecycleBottomVisibility() {
        fragmentLifecycleEvents()
            .filterIsInstance<FragmentLifecycleEvent.Resumed>()
            .map { it.fragment }
            .map { frag ->
                frag.let {
                    frag is NavigateBackHandler &&
                        frag.id in listOf(
                        R.id.create_post, R.id.SimpleWebViewFragment,
                        R.id.image_picker, R.id.donorOnboardingFragment
                    )
                }
            }
            .onEach { hidden ->
                binding.commonScaffoldBottomContent.isVisible = !hidden
            }
            .launchIn(lifecycleScope)
    }

    private fun setupNetworkMonitor() {
        repeatOnStart {
            _networkMonitor.observeInternetConnection()
                .asFlow()
                .collect { isConnected ->
                    if (isConnected) {
                        binding.commonScaffoldToolbarNoInternetContainer.gone()
                        window.restoreStatusbarColorToDefaults(requireNavController())
                    } else {
                        binding.commonScaffoldToolbarNoInternetContainer.visible()
                        window.setStatusbarColor(false, R.color.application___color__accent_variant)
                    }
                }
        }
    }

    private fun setupFragmentLifecycleToolbarSupport() {
        val fragmentsWithToolbar = mutableListOf<HasCustomToolbar>()
        var updateActionBarJob: Job? = null

        lifecycleScope.launchWhenStarted {
            // поток событий Started/Stopped
            val toolbarHostFlow = fragmentLifecycleEvents()
                .filter {
                    (it is FragmentLifecycleEvent.Started && it.fragment is HasCustomToolbar) ||
                        (it is FragmentLifecycleEvent.Stopped && it.fragment is HasCustomToolbar)
                }
                .map { event ->
                    when (event) {
                        is FragmentLifecycleEvent.Started ->
                            fragmentsWithToolbar.add(event.fragment as HasCustomToolbar)

                        is FragmentLifecycleEvent.Stopped ->
                            fragmentsWithToolbar.remove(event.fragment as HasCustomToolbar)

                        else -> {
                        }
                    }
                    fragmentsWithToolbar.lastOrNull() ?: defaultCustomToolbar
                }
                //  .onStart { emit(defaultCustomToolbar) }
                .distinctUntilChanged()

            // withLatestFrom → flatMapLatest
            toolbarHostFlow
                .flatMapLatest { host ->
                    _onToolbarContainersChanged
                        .map { containers -> host to containers }
                }
                .collectLatest { (host, containers) ->
                    updateActionBarJob?.cancel()
                    setSupportActionBar(null)

                    val customToolbar = host.onCreateCustomToolbar()
                    val selected = (customToolbar as? CustomToolbar.Layout)
                        ?.let { containers.getByStyle(it.style) }

                    containers.all.forEach { c ->
                        c.isGoneVisible = (c == selected)
                        c.removeAllViews()
                    }

                    when (customToolbar) {
                        CustomToolbar.None -> Unit
                        is CustomToolbar.Layout -> {
                            val container = checkNotNull(selected)
                            val view = customToolbar.onCreateView(container)
                            val toolbar = customToolbar.getToolbar(view)
                            val appBar = customToolbar.getAppBarLayout(view)
                            container.addView(appBar ?: toolbar)
                            setSupportActionBar(toolbar)
                            toolbar.setupWithNavController(
                                requireNavController(),
                                _navigationConfiguration!!
                            )
                            toolbar.setNavigationOnClickListener {
                                lifecycleScope.launch {
                                    _navigateBack.emit(Unit)
                                }
                            }

                            if (host is HasCustomDynamicToolbar<*>) {
                                updateActionBarJob = lifecycleScope.launch {
                                    host.onCustomToolbarChanged()
                                        .collectLatest { ev ->
                                            @Suppress("UNCHECKED_CAST")
                                            (host as HasCustomDynamicToolbar<Any?>)
                                                .onUpdateCustomToolbar(ev, appBar, toolbar)
                                        }
                                }
                            }
                        }
                    }
                }
        }
    }

    // Универсальный FragmentLifecycleEvent → callbackFlow
    private fun fragmentLifecycleEvents(): Flow<FragmentLifecycleEvent> = callbackFlow {
        val cb = object : FragmentManager.FragmentLifecycleCallbacks() {
            override fun onFragmentStarted(fm: FragmentManager, f: Fragment) {
                trySend(FragmentLifecycleEvent.Started(fm, f))
            }

            override fun onFragmentResumed(fm: FragmentManager, f: Fragment) {
                trySend(FragmentLifecycleEvent.Resumed(fm, f))
            }

            override fun onFragmentPaused(fm: FragmentManager, f: Fragment) {
                trySend(FragmentLifecycleEvent.Paused(fm, f))
            }

            override fun onFragmentStopped(fm: FragmentManager, f: Fragment) {
                trySend(FragmentLifecycleEvent.Stopped(fm, f))
            }
        }
        supportFragmentManager.registerFragmentLifecycleCallbacks(cb, true)
        awaitClose { supportFragmentManager.unregisterFragmentLifecycleCallbacks(cb) }
    }

    open fun onBottomNavigationTabSelected(tab: Int) {}

    fun requireNavController(): NavController {
        return currentNavController ?: navigation
    }

    class BottomNavigationConfig(
        @LayoutRes val bottomNavigationId: Int,
        @NavigationRes val tabsGraphId: Int,
    )

    private class ToolbarContainers(
        val shift: ViewGroup,
        val overlap: ViewGroup
    ) {
        val all: PersistentList<ViewGroup> = persistentListOf(
            shift,
            overlap
        )

        @CheckReturnValue
        fun getByStyle(style: CustomToolbarStyle): ViewGroup =
            when (style) {
                CustomToolbarStyle.ShiftContent -> shift
                CustomToolbarStyle.OverlapContent -> overlap
                CustomToolbarStyle.OverlapContentWithTransparentSystemWindow -> overlap
            }
    }

    protected fun selectBottomNavigationTab(tabId: Int) {
        val newFragment = obtainNavHostFragment(
            supportFragmentManager,
            getFragmentTag(bottomNavigationTabDestinationIds.indexOf(tabId)),
            bottomNavigationConfig!!.tabsGraphId,
            tabId,
            navigationHostContainerId
        )

        val fTrans = supportFragmentManager.beginTransaction()
        with(fTrans) {
            if (selectedFragment != null) detach(selectedFragment!!)
            attach(newFragment)
            commitNow()
        }
        selectedFragment = newFragment
        currentNavController = selectedFragment!!.navController

        onBottomNavigationTabSelected(tabId)
    }

    private fun obtainNavHostFragment(
        fragmentManager: FragmentManager,
        fragmentTag: String,
        navGraphId: Int,
        startDestination: Int,
        containerId: Int
    ): NavHostFragment {
        // If the Nav Host fragment exists, return it
        val existingFragment = fragmentManager.findFragmentByTag(fragmentTag) as? NavHostFragment
        existingFragment?.let { return it }

        // Otherwise, create it and return it.
        val navHostFragment = NavHostFragment.create(navGraphId)

        fragmentManager.beginTransaction()
            .add(containerId, navHostFragment, fragmentTag)
            .commitNow()

        val navGraph = navHostFragment.navController.navInflater.inflate(navGraphId)
        navGraph.setStartDestination(startDestination)
        navHostFragment.navController.setGraph(navGraph, null)

        return navHostFragment
    }

    private fun getFragmentTag(index: Int) = "bottomNavigation#$index"

    fun showBottomMenu() {
        binding.commonScaffoldBottomContent.visible()
    }

    fun hideBottomMenu() {
        binding.commonScaffoldBottomContent.gone()
    }
}
