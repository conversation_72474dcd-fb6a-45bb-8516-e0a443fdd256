package ru.dobro.core

import common.library.android.BasicDialogFragment
import common.library.android.di.DependencyBuilder
import org.kodein.di.generic.instance
import ru.dobro.core.analytic.AnalyticManager
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.metric.MetricManager

abstract class BaseDialogFragment(dependency: DependencyBuilder<BasicDialogFragment> = {}) :
    BasicDialogFragment({
        dependency(it)
    }) {
    abstract val screenName: String

    protected val firebaseAnalyticManager: AnalyticManager by instance()

    protected val metricManager: MetricManager by instance()

    override fun onResume() {
        super.onResume()

        metricManager.sendSimpleEvent(AnalyticsConstants.Screen.showScreen + screenName)
        firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Screen.showScreen + screenName)
    }

    override fun onPause() {
        super.onPause()

        metricManager.sendSimpleEvent(AnalyticsConstants.Screen.showScreen + screenName)
        firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Screen.hideScreen + screenName)
    }
}
