package ru.dobro.core

import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.Toolbar
import com.google.android.material.appbar.AppBarLayout
import javax.annotation.CheckReturnValue

interface HasCustomToolbar {
    @CheckReturnValue
    fun onCreateCustomToolbar(): CustomToolbar

    enum class CustomToolbarStyle {
        ShiftContent,
        OverlapContent,
        OverlapContentWithTransparentSystemWindow
    }

    sealed class CustomToolbar {
        object None : CustomToolbar()

        abstract class Layout(
            val style: CustomToolbarStyle = CustomToolbarStyle.ShiftContent
        ) : CustomToolbar() {
            @CheckReturnValue
            abstract fun onCreateView(container: ViewGroup): View

            @CheckReturnValue
            abstract fun getAppBarLayout(view: View): AppBarLayout?

            @CheckReturnValue
            abstract fun getToolbar(view: View): Toolbar
        }
    }
}
