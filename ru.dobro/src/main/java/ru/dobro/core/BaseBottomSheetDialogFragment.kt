package ru.dobro.core

import common.library.android.BasicBottomSheetDialogFragment
import common.library.android.di.DependencyBuilder
import org.kodein.di.generic.instance
import ru.dobro.core.analytic.AnalyticManager
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.metric.MetricManager

abstract class BaseBottomSheetDialogFragment(
    dependency: DependencyBuilder<BasicBottomSheetDialogFragment> = {}
) : BasicBottomSheetDialogFragment({
    dependency(it)
}) {
    protected val firebaseAnalyticManager: AnalyticManager by instance()
    protected val metricManager: MetricManager by instance()

    abstract val screenName: String

    override fun onResume() {
        super.onResume()

        metricManager.sendSimpleEvent(AnalyticsConstants.Screen.showScreen + screenName)
        firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Screen.showScreen + screenName)
    }

    override fun onPause() {
        super.onPause()

        metricManager.sendSimpleEvent(AnalyticsConstants.Screen.showScreen + screenName)
        firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Screen.hideScreen + screenName)
    }
}
