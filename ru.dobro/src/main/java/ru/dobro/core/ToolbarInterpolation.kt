package ru.dobro.core

import android.view.animation.LinearInterpolator

class ToolbarInterpolation private constructor(
    val offsetTopBeforeAnimationStartPx: Float,
    val velocity: Float
) : LinearInterpolator() {
    fun ofScrollPx(scrollPx: Int): Float {
        val maxProgress = 256f / velocity
        return getInterpolation(
            (scrollPx - offsetTopBeforeAnimationStartPx).coerceIn(
                0f,
                maxProgress
            ) / maxProgress
        )
    }

    class Builder {
        private var offsetTopBeforeAnimationStartPx = 0f
        private var velocity = 4.0f

        fun setOffsetTopBeforeAnimationStartPx(value: Float) =
            apply { this.offsetTopBeforeAnimationStartPx = value }

        fun setVelocity(value: Float) = apply { this.velocity = value }

        fun build(): ToolbarInterpolation {
            return ToolbarInterpolation(offsetTopBeforeAnimationStartPx, velocity)
        }
    }
}
