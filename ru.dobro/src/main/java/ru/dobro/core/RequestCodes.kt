package ru.dobro.core

import java.util.concurrent.atomic.AtomicInteger
import javax.annotation.CheckReturnValue

object RequestCodes {
    @CheckReturnValue
    inline fun <reified Target : Any> scopedBy(key: String): String =
        "${Target::class.qualifiedName}:$key"

    private val _lastPermissionCode: AtomicInteger = AtomicInteger()

    @CheckReturnValue
    fun permission(): Int = _lastPermissionCode.incrementAndGet()

    private val _lastResultCode: AtomicInteger = AtomicInteger()

    @CheckReturnValue
    fun result(): Int = _lastResultCode.incrementAndGet()
}
