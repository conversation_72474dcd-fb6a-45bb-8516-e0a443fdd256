package ru.dobro.core

import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.view.View
import android.widget.ImageButton
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.appcompat.view.menu.ActionMenuItemView
import androidx.appcompat.widget.ActionMenuView
import androidx.core.content.ContextCompat
import androidx.core.view.children
import com.google.android.material.appbar.MaterialToolbar
import common.library.core.color.ColorUtils
import ru.dobro.R
import javax.annotation.CheckReturnValue

@CheckReturnValue
fun MaterialToolbar.setBackgroundAndIconsColorByScroll(
    scrollFactor: Int,
    @ColorInt initBackgroundColor: Int = ContextCompat.getColor(
        context,
        common.library.android.R.color.transparent_bg
    ),
    @ColorInt targetBackgroundColor: Int = ContextCompat.getColor(context, R.color.white),
    @ColorInt initControlsColor: Int = ContextCompat.getColor(context, R.color.white),
    @ColorInt targetControlsColor: Int = ContextCompat.getColor(
        context,
        R.color.application___color__primary
    ),
    interpolation: ToolbarInterpolation? = null,
    l: ((color: Int) -> Unit)? = null
) {
    val toolbarInterpolation = interpolation ?: ToolbarInterpolation.Builder()
        .build()
    val ratio = toolbarInterpolation.ofScrollPx(scrollFactor)

    val backgroundColor = ColorUtils.blendARGB(initBackgroundColor, targetBackgroundColor, ratio)
    val iconsColor = ColorUtils.blendARGB(initControlsColor, targetControlsColor, ratio)
    val textColor = ColorUtils.blendARGB(
        ContextCompat.getColor(
            context,
            common.library.android.R.color.transparent
        ), ContextCompat.getColor(context, R.color.black), ratio
    )

    if (l != null) {
        l.invoke(iconsColor)
    } else {
        setTitleTextColor(textColor)
        setSubtitleTextColor(textColor)
    }

    setBackgroundColor(backgroundColor)

    val colorFilter =
        PorterDuffColorFilter(iconsColor, PorterDuff.Mode.MULTIPLY)

    children.forEach { view ->
        when (view) {
            is ImageButton -> view.drawable.colorFilter = colorFilter
            is ActionMenuView -> view.children.filterIsInstance<ActionMenuItemView>()
                .forEach { item ->
                    item.compoundDrawables.filterNotNull().forEach { drawable ->
                        item.post { drawable.colorFilter = colorFilter }
                    }
                }
        }
    }

    // Changing the color of the Overflow Menu icon.
    overflowIcon?.colorFilter = colorFilter
}

fun MaterialToolbar.setOverflowIconColor(color: Int) {
    overflowIcon?.colorFilter = PorterDuffColorFilter(color, PorterDuff.Mode.SRC_ATOP)
}

fun MaterialToolbar.setHomeButtonToCircleStyle(@DrawableRes iconDrawable: Int) {
    for (i in 0 until childCount) {
        val v: View = getChildAt(i)

        if (v is ImageButton) {
            v.foreground =
                ContextCompat.getDrawable(context, R.drawable.application___ripple__circle)
            v.setImageResource(iconDrawable)
            v.setBackgroundResource(R.drawable.application___background__circle)
        }
    }
}
