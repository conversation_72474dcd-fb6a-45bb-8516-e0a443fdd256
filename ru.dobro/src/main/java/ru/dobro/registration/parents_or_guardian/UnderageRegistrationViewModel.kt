package ru.dobro.registration.parents_or_guardian

import androidx.lifecycle.viewModelScope
import common.library.core.collection.orEmptySet
import common.library.core.location.GeoPoint
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import ru.dobro.core.analytic.AnalyticManager
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.metric.MetricManager
import ru.dobro.core.settings.UserSettings
import ru.dobro.domain.SettlementExtended
import ru.dobro.domain.repository.AuthRepository
import ru.dobro.domain.resultWrapper.HttpException
import ru.dobro.domain.resultWrapper.ResultWrapper
import ru.dobro.domain.resultWrapper.ResultWrapperUI
import ru.dobro.domain.resultWrapper.toResultWrapperUI
import ru.dobro.registration.RegistrationRequestParams
import ru.dobro.registration_social_network.SocialNetworkRegistrationRequestParams

class UnderageRegistrationViewModel(
    private val authRepository: AuthRepository,
    private val userSettings: UserSettings,
    private val metricManager: MetricManager,
    private val firebaseAnalyticManager: AnalyticManager
) : androidx.lifecycle.ViewModel() {
    private val _state = MutableStateFlow(UnderageRegistrationState())
    val state = _state.asStateFlow()

    fun setName(name: String) {
        _state.update { it.copy(name = name) }
    }

    fun setSurname(surname: String) {
        _state.update { it.copy(surname = surname) }
    }

    fun setPassportSeries(passportSeries: String) {
        _state.update { it.copy(passportSeries = passportSeries) }
    }

    fun setPassportNumber(passportNumber: String) {
        _state.update { it.copy(passportNumber = passportNumber) }
    }

    fun setBirthCertificateNumber(birthCertificateNumber: String) {
        _state.update { it.copy(birthCertificateNumber = birthCertificateNumber) }
    }

    fun createUser(parent: RegistrationRequestParams) {
        if (listOf(
                state.value.name,
                state.value.surname,
                state.value.passportNumber,
                state.value.birthCertificateNumber
            ).all { !it.isNullOrBlank() }
        ) {
            viewModelScope.launch {
                _state.update { it.copy(createUserAttempt = ResultWrapperUI.Loading) }
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.underagedRegistrationOnPerformClick)
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.underagedRegistrationOnPerformClick)
                _state.update {
                    val userRegistration = authRepository.createUnderAgedUser(
                        name = parent.name,
                        surname = parent.surname,
                        password = parent.password,
                        email = parent.email,
                        birthdate = parent.birthdate,
                        settlement = parent.settlement ?: GeoPoint.Coordinates(0.0, 0.0),
                        settlementExtended = SettlementExtended.restore(
                            title = parent.title,
                            country = parent.countryISO,
                            municipality = parent.municipality,
                            municipalityCode = parent.municipalityCode,
                            settlement = parent.cityAlternative,
                            flat = parent.flat,
                            house = parent.house,
                            street = parent.street,
                            region = parent.region?.take(2),
                            x = parent.settlement?.longitude,
                            y = parent.settlement?.latitude
                        ),
                        parentOrGuardianName = state.value.name.orEmpty(),
                        parentOrGuardianSurname = state.value.surname.orEmpty(),
                        parentOrGuardianPassportSeries = state.value.passportSeries.orEmpty(),
                        parentOrGuardianPassportNumber = state.value.passportNumber.orEmpty(),
                        parentOrGuardianBirthCertificate = state.value.birthCertificateNumber.orEmpty(),
                        categories = userSettings.getCategories().orEmptySet(),
                        receiveNewsletters = parent.isReceiveNewsletters
                    )
                    when (userRegistration) {
                        is ResultWrapper.Success -> it.copy(createUserAttempt = userRegistration.toResultWrapperUI())
                        is ResultWrapper.Error -> {
                            when (userRegistration.error) {
                                is HttpException.AccountDisabled -> {
                                    it.copy(isAccountDisabled = true)
                                }

                                else -> it.copy(createUserAttempt = userRegistration.toResultWrapperUI())
                            }
                        }
                    }
                }
            }
        }
    }

    fun createUserFromSocialNetwork(params: SocialNetworkRegistrationRequestParams) {
        if (listOf(
                state.value.name,
                state.value.surname,
                state.value.passportNumber,
                state.value.birthCertificateNumber
            ).all { !it.isNullOrBlank() }
        ) {
            viewModelScope.launch {
                _state.update { it.copy(createUserAttempt = ResultWrapperUI.Loading) }
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.underagedRegistrationOnPerformClick)
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.underagedRegistrationOnPerformClick)
                _state.update {
                    val userRegistration = authRepository.createUnderAgedUserFromSocialNetwork(
                        name = params.name,
                        surname = params.surname,
                        email = params.email.toString(),
                        birthdate = params.birthdate,
                        settlement = params.settlement ?: GeoPoint.Coordinates(0.0, 0.0),
                        country = params.country,
                        city = params.city,
                        remoteId = params.remoteAuth.remoteId,
                        remoteToken = params.remoteAuth.remoteToken,
                        socialType = params.remoteAuth.type,
                        emailConfirmed = params.remoteAuth.emailConfirmed,
                        isReceiveNewsletters = params.isReceiveNewsletters,
                        categories = userSettings.getCategories().orEmptySet(),
                        hasMedicalHistory = userSettings.getHasMedicalHistory(),
                        hasDriverLicense = userSettings.getHasDriverLicence(),
                        parentOrGuardianName = state.value.name.orEmpty(),
                        parentOrGuardianSurname = state.value.surname.orEmpty(),
                        parentOrGuardianPassportSeries = state.value.passportSeries.orEmpty(),
                        parentOrGuardianPassportNumber = state.value.passportNumber.orEmpty(),
                        parentOrGuardianBirthCertificate = state.value.birthCertificateNumber.orEmpty(),
                    )
                    when (userRegistration) {
                        is ResultWrapper.Success -> it.copy(createUserAttempt = userRegistration.toResultWrapperUI())
                        is ResultWrapper.Error -> {
                            when (userRegistration.error) {
                                is HttpException.AccountDisabled -> {
                                    it.copy(isAccountDisabled = true)
                                }

                                else -> it.copy(createUserAttempt = userRegistration.toResultWrapperUI())
                            }
                        }
                    }
                }
            }
        }
    }

    data class UnderageRegistrationState(
        val name: String? = null,
        val surname: String? = null,
        val passportSeries: String? = null,
        val passportNumber: String? = null,
        val birthCertificateNumber: String? = null,
        val createUserAttempt: ResultWrapperUI<Any?> = ResultWrapperUI.Idle,
        val isAccountDisabled: Boolean = false,
    )
}
