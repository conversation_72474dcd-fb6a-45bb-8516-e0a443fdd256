package ru.dobro.registration

import common.library.core.location.GeoPoint
import common.library.core.state.task.TaskActionState
import java.time.ZonedDateTime

data class RegistrationRequestParams(
    val name: String,
    val surname: String,
    val birthdate: ZonedDateTime,
    val email: String,
    val password: String,
    val settlement: GeoPoint.Coordinates?,
    val settlementCode: String?,
    val title: String?,
    val country: String?,
    val countryISO: String?,
    val municipality: String?,
    val municipalityCode: String?,
    val cityAlternative: String?,
    val flat: String?,
    val house: String?,
    val street: String?,
    val region: String?,
    val isReceiveNewsletters: Boolean,
    val isRussian: Boolean
)

typealias RegistrationTaskParams = RegistrationRequestParams
typealias RegistrationTaskState = TaskActionState<RegistrationTaskParams>
