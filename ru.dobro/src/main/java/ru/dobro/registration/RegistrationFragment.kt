package ru.dobro.registration

import android.net.Uri
import android.os.Bundle
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import androidx.annotation.IdRes
import androidx.core.content.res.ResourcesCompat
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.MaterialDatePicker
import com.google.android.material.textfield.TextInputLayout
import com.vk.id.AccessToken
import com.vk.id.VKID
import com.vk.id.VKIDAuthFail
import common.library.android.getSupportColor
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.intent.newTask
import common.library.android.intent.tryStartActivityBy
import common.library.android.intent.view
import common.library.android.message.MessageDisplay
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.resource.withOnStartResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.soft_input.SoftInputMode
import common.library.android.string.r
import common.library.android.string.span.spannedString
import common.library.android.widget.enableSoftInputMode
import common.library.android.widget.gone
import common.library.android.widget.onClick
import common.library.android.widget.visible
import common.library.core.Characters
import common.library.core.lazyGet
import common.library.core.location.GeoPoint
import common.library.core.orTrue
import common.library.core.rx.RxSchedulers
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.collect
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.App
import ru.dobro.R
import ru.dobro.address_selector.AddressSelectorFragment
import ru.dobro.address_selector.Country
import ru.dobro.api.DobroApi
import ru.dobro.authorization.SocialNetworksAdapter
import ru.dobro.authorization.authorizationVKCompleted
import ru.dobro.check_email.CheckEmailFragment
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.RequestCodes
import ru.dobro.core.account.AccountManager
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.error.ErrorHandler
import ru.dobro.core.format.DisplayFormat
import ru.dobro.core.settings.Environment
import ru.dobro.core.settings.Settings
import ru.dobro.core.validation.DatePickerRangeValidator
import ru.dobro.databinding.RegistrationBinding
import ru.dobro.domain.Location
import ru.dobro.domain.error.Field
import ru.dobro.registration.parents_or_guardian.UnderageRegistrationFragment
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.ZonedDateTime
import javax.annotation.CheckReturnValue

class RegistrationFragment(override val screenName: String = AnalyticsConstants.Screen.Login.registration) :
    BaseFragment({
        bind<RegistrationViewModel>() with singleton {
            RegistrationViewModel(
                instance(),
                instance()
            )
        }
        bind<SocialNetworksAdapter>() with singleton { SocialNetworksAdapter() }
    }), HasCustomToolbar {
    private lateinit var binding: RegistrationBinding

    companion object {
        class NavigationContext(
            private val _navigation: NavController,
            private val _bundler: Bundler
        ) {
            @CheckReturnValue
            fun toRegistrationWithData(
                @IdRes actionId: Int,
                registrationData: RegistrationViewModel.State
            ) {
                _navigation.navigate(
                    actionId,
                    registrationData.intoBundle(_bundler)
                )
            }
        }

        @CheckReturnValue
        fun navigate(
            navigation: NavController,
            bundler: Bundler
        ): NavigationContext = NavigationContext(navigation, bundler)
    }

    private val viewModel: RegistrationViewModel by instance()

    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        HasCustomToolbar.CustomToolbar.None

    private val _requestCodeSelectCity: String =
        RequestCodes.scopedBy<RegistrationFragment>("select_city")

    private val _requestCodeSelectCountry: String =
        RequestCodes.scopedBy<RegistrationFragment>("select_country")

    private val _bundler: Bundler by instance()
    private val _settings: Settings by instance()
    private val _messageDisplay: MessageDisplay by instance()
    private val _api: DobroApi by instance()
    private val _displayFormatter: DisplayFormat by instance()
    private val _accountManager: AccountManager by instance()
    private val _errorHandler: ErrorHandler by instance()

    private val _socialNetworksAdapter: SocialNetworksAdapter by lazyGet { SocialNetworksAdapter() }

    private val _agreement: Uri get() = Uri.parse(getString(R.string.application___agreement))
    private val _consentOfPersonalDataProcessing: Uri get() = Uri.parse(getString(R.string.application___consent_of_personal_data_processing))

    // Google не должен быть при регистрации, но должен быть при авторизации
    private val _socialNetworkList = SocialNetworksAdapter._socialNetworkExpandedList.filter {
        (it as? SocialNetworksAdapter.Item.SocialNetwork)?.path != "google"
    }.toPersistentList()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = RegistrationBinding.inflate(inflater)
        return binding.root
    }

    override fun onStart() = withOnStartResourcesScope {
        super.onStart()
        activity?.window?.enableSoftInputMode(SoftInputMode.AdjustResize)?.scoped()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        metricManager.sendSimpleEvent(AnalyticsConstants.Screen.showScreen + screenName)

        _socialNetworksAdapter.setItems(_socialNetworkList)
        binding.registrationSocialNetworkContainer.apply {
            adapter = _socialNetworksAdapter
            (layoutManager as GridLayoutManager).spanCount =
                App.authorizationExpandedSocialsSpanCount
        }

        setTopSpan()
        setBottomSpan()

        lifecycleScope.launch {
            viewModel.state.apply {
                launch { observeName() }
                launch { observeSurname() }
                launch { observeCountry() }
                launch { observeCity() }
                launch { observeBirthday() }
                launch { observeEmail() }
                launch { observePassword() }
                launch { observeRepeatPassword() }
                launch { observeIsUnderAged() }
                launch { observeRegistrationSubscribe() }
                launch { observeErrorField() }
                launch { observeIsCreating() }
                launch { observeIsCreated() }
                launch { observeIsAccountDisabled() }
            }
            launch { handleFragmentResults() }
        }

        binding.apply {
            registrationUsername.addTextChangedListener { name ->
                registrationUsernameContainer.makeDefault(registrationUsername)
                viewModel.setName(name?.toString().orEmpty())
            }
            registrationUserSurname.addTextChangedListener { surname ->
                registrationUserSurnameContainer.makeDefault(registrationUserSurname)
                viewModel.setSurname(surname?.toString().orEmpty())
            }
            registrationCountry.addTextChangedListener {
                registrationCountryContainer.makeDefault(registrationCountry)
            }
            registrationCity.addTextChangedListener {
                registrationCityContainer.makeDefault(registrationCity)
            }
            registrationUserBirthday.addTextChangedListener {
                if (viewModel.state.value.isUnderAged) {
                    registrationUserBirthdayContainer.makeWarning(registrationUserBirthday)
                } else {
                    registrationUserBirthdayContainer.makeDefault(registrationUserBirthday)
                }
            }
            registrationEmail.addTextChangedListener { email ->
                registrationEmailContainer.makeDefault(registrationEmail)
                viewModel.setEmail(email?.toString().orEmpty())
            }
            password.addTextChangedListener { password ->
                passwordContainer.makeDefault(binding.password)
                viewModel.setPassword(password?.toString().orEmpty())
            }

            passwordSecond.addTextChangedListener { password ->
                passwordSecondContainer.makeDefault(binding.passwordSecond)
                viewModel.setRepeatPassword(password?.toString().orEmpty())
            }

            registrationClose.setOnClickListener { onClose() }
            registrationCountry.setOnClickListener {
                onCountrySelect()
            }
            registrationCity.setOnClickListener {
                onCitySelect()
            }
            registrationUserBirthday.setOnClickListener { onBirthday() }
            registrationSubscribeContainer.setOnClickListener { viewModel.switchIsReceiveNewsletters() }
        }

        setupDefaults()

        val registrationData: RegistrationViewModel.State? = arguments?.getParams(_bundler)
        registrationData?.let { data ->
            viewModel.apply {
                setName(data.name)
                setSurname(data.surname)
                setCountry(data.country)
                data.city?.let {
                    setCity(it)
                }
                data.birthday?.let {
                    setBirthdate(it)
                }
                setIsUnderAged(data.isUnderAged)
            }
        }
    }

    private fun setupDefaults() {
        if (viewModel.state.value.city == null) {
            _accountManager.getUserLocation()?.let { location ->
                viewModel.setCity(location)
            }
        }
    }

    private suspend fun Flow<RegistrationViewModel.State>.observeName() {
        distinctUntilChangedBy { it.name }.map { it.name }.collectLatest { name ->
            if (binding.registrationUsername.text.toString() != name) {
                binding.registrationUsername.setText(name)
            }
        }
    }

    private suspend fun Flow<RegistrationViewModel.State>.observeSurname() {
        distinctUntilChangedBy { it.surname }.map { it.surname }.collectLatest { surname ->
            if (binding.registrationUserSurname.text.toString() != surname) {
                binding.registrationUserSurname.setText(surname)
            }
        }
    }

    private suspend fun Flow<RegistrationViewModel.State>.observeCountry() {
        distinctUntilChangedBy { it.country }.map { it.country }.collectLatest { country ->
            binding.registrationCountry.setText(country.country ?: "")
        }
    }

    private suspend fun Flow<RegistrationViewModel.State>.observeCity() {
        distinctUntilChangedBy { it.city }.map { it.city }.collectLatest { city ->
            binding.registrationCity.setText(city?.settlement ?: city?.cityAlternative ?: "")
        }
    }

    private suspend fun Flow<RegistrationViewModel.State>.observeBirthday() {
        distinctUntilChangedBy { it.birthday }.map { it.birthday }.collectLatest { birthday ->
            birthday?.let {
                binding.registrationUserBirthday.setText(
                    _displayFormatter.dateZonedFormatter().format(it)
                )
            } ?: binding.registrationUserBirthday.setText("")
            val legalCapacityAge = ZonedDateTime.now().minusYears(App.majorityAge.toLong())
            val shouldRegisterParent = birthday?.isAfter(legalCapacityAge) == true
            viewModel.setIsUnderAged(shouldRegisterParent)
        }
    }

    private suspend fun Flow<RegistrationViewModel.State>.observeEmail() {
        distinctUntilChangedBy { it.email }.map { it.email }.collectLatest { email ->
            if (binding.registrationEmail.text.toString() != email) {
                binding.registrationEmail.setText(email)
            }
        }
    }

    private suspend fun Flow<RegistrationViewModel.State>.observePassword() {
        distinctUntilChangedBy { it.password }.map { it.password to it.passwordRepeat }
            .collectLatest { passwords ->
                if (binding.password.text.toString() != passwords.first) {
                    binding.password.setText(passwords.first)
                }
                if (passwords.second.isNotBlank()) {
                    checkRepeatPasswordValid(
                        passwords.first,
                        passwords.second
                    )
                }
            }
    }

    private suspend fun Flow<RegistrationViewModel.State>.observeRepeatPassword() {
        distinctUntilChangedBy { it.passwordRepeat }.map { it.password to it.passwordRepeat }
            .collectLatest { passwords ->
                if (binding.passwordSecond.text.toString() != passwords.second) {
                    binding.passwordSecond.setText(passwords.second)
                }
                checkRepeatPasswordValid(passwords.first, passwords.second)
            }
    }

    private suspend fun Flow<RegistrationViewModel.State>.observeIsUnderAged() {
        distinctUntilChangedBy { it.isUnderAged }.map { it.isUnderAged }
            .collectLatest { isUnderAged ->
                if (isUnderAged) {
                    binding.apply {
                        registrationUserBirthdayContainer.makeWarning(registrationUserBirthday)
                        registrationPerform.text = getString(R.string.registration___next)
                        //
                        ageWarning.visible()
                    }
                } else {
                    binding.apply {
                        registrationUserBirthdayContainer.makeDefault(registrationUserBirthday)
                        registrationPerform.text = getString(R.string.registration___create_account)
                        ageWarning.gone()
                    }
                }
            }
    }

    private suspend fun Flow<RegistrationViewModel.State>.observeRegistrationSubscribe() {
        distinctUntilChangedBy { it.isReceiveNewsletters }.map { it.isReceiveNewsletters }
            .collectLatest { isReceiveNewsletters ->
                binding.registrationSubscribeCheckBox.isChecked = isReceiveNewsletters
            }
    }

    private suspend fun Flow<RegistrationViewModel.State>.observeErrorField() {
        distinctUntilChangedBy { it.errorField }.map { it.errorField }.collectLatest { errorField ->
            when (errorField?.first) {
                Field.Name -> {
                    binding.scroll.smoothScrollTo(0, binding.root.top)
                    binding.registrationUsernameContainer.makeAccent(
                        binding.registrationUsername,
                        errorField.second
                    )
                }

                Field.Surname -> {
                    binding.scroll.smoothScrollTo(0, binding.root.top)
                    binding.registrationUserSurnameContainer.makeAccent(
                        binding.registrationUserSurname,
                        errorField.second
                    )
                }

                Field.Email -> {
                    binding.scroll.smoothScrollTo(0, binding.root.bottom)
                    binding.registrationEmailContainer.makeAccent(
                        binding.registrationEmail,
                        errorField.second
                    )
                }

                Field.Password -> {
                    binding.scroll.smoothScrollTo(0, binding.root.bottom)
                    binding.passwordContainer.makeAccent(
                        binding.password,
                        errorField.second
                    )
                }

                Field.Unknown -> {
                    _messageDisplay.showMessage(errorField.second.r)
                }

                null -> { /* No need */
                }
            }
            viewModel.resetErrorField()
        }
    }

    private suspend fun Flow<RegistrationViewModel.State>.observeIsCreating() {
        distinctUntilChangedBy { it.isCreating }.map { it.isCreating }.collectLatest { isCreating ->
            if (isCreating) {
                listOf(
                    binding.registrationUsernameContainer,
                    binding.registrationUserSurnameContainer,
                    binding.registrationCountryContainer,
                    binding.registrationCityContainer,
                    binding.registrationUserBirthdayContainer,
                    binding.registrationEmailContainer,
                    binding.passwordContainer,
                    binding.passwordSecondContainer,
                    binding.registrationSubscribeContainer,
                    binding.registrationPerform
                ).forEach { it.isEnabled = false }
                binding.progress.visible()
            } else {
                listOf(
                    binding.registrationUsernameContainer,
                    binding.registrationUserSurnameContainer,
                    binding.registrationCountryContainer,
                    binding.registrationCityContainer,
                    binding.registrationUserBirthdayContainer,
                    binding.registrationEmailContainer,
                    binding.passwordContainer,
                    binding.passwordSecondContainer,
                    binding.registrationSubscribeContainer,
                    binding.registrationPerform
                ).forEach { it.isEnabled = true }
                binding.progress.gone()
            }
        }
    }

    private suspend fun Flow<RegistrationViewModel.State>.observeIsCreated() {
        distinctUntilChangedBy { it.isCreated }.map { it.isCreated }.collectLatest { isCreated ->
            if (isCreated) {
                try {
                    firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.registrationSuccess)
                    metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.registrationSuccess)
                    CheckEmailFragment
                        .navigate(findNavController(), _bundler)
                        .toCheckEmailFromEmailRegistration(
                            R.id.registration__to__check_email,
                            viewModel.state.value
                        )
                } catch (_: IllegalArgumentException) {
                    navigate(findNavController(), _bundler)
                        .toRegistrationWithData(
                            R.id.registration,
                            viewModel.state.value
                        )
                }
            }
        }
    }

    private suspend fun Flow<RegistrationViewModel.State>.observeIsAccountDisabled() {
        distinctUntilChangedBy { it.isAccountDisabled }.map { it.isAccountDisabled }
            .collectLatest { isAccountDisabled ->
                if (isAccountDisabled) {
                    findNavController().navigate(R.id.registration__to__account_disabled)
                }
            }
    }

    private fun onClose() {
        metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.registrationOnCloseClick)
        firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.registrationOnCloseClick)
        findNavController().navigateUp()
        findNavController().navigateUp()
    }

    private fun onCountrySelect() {
        AddressSelectorFragment.createCountry(
            requestKey = _requestCodeSelectCountry
        )
            .show(parentFragmentManager, null)
    }

    private fun onCitySelect() {
        AddressSelectorFragment.createSettlement(
            requestKey = _requestCodeSelectCity,
            country = viewModel.state.value.country.country?.let { Country.Special(it) }
                ?: Country.All
        ).show(parentFragmentManager, null)
    }

    private fun onRegistration() {
        val state = viewModel.state.value
        if (listOf(
                checkNameValid(state.name),
                checkSurnameValid(state.surname),
                checkCountryValid(state.country),
                checkCityValid(state.city),
                checkBirthdayValid(state.birthday),
                checkEmailValid(state.email),
                checkPasswordValid(state.password),
                checkRepeatPasswordValid(state.password, state.passwordRepeat)
            ).all { it }
        ) {
            if (state.isUnderAged) {
                nextStep()
            } else {
                viewModel.createAccount()
            }
            metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.registrationOnCloseClick)
            firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.registrationOnPerformClick)
        }
    }

    private fun onBirthday() {
        val constraintsBuilder = CalendarConstraints.Builder()
        constraintsBuilder.setStart(ZonedDateTime.now().minusYears(90).toInstant().toEpochMilli())
        constraintsBuilder.setEnd(ZonedDateTime.now().minusYears(8).toInstant().toEpochMilli())
        constraintsBuilder.setValidator(
            DatePickerRangeValidator(
                startAt = LocalDate.now().minusYears(90),
                endAt = LocalDate.now().minusYears(8)
            )
        )
        val builder: MaterialDatePicker.Builder<Long> =
            MaterialDatePicker.Builder.datePicker()
        builder.setCalendarConstraints(constraintsBuilder.build())
        builder.setSelection(ZonedDateTime.now().minusYears(18).toInstant().toEpochMilli())
        viewModel.state.value.birthday?.let {
            builder.setSelection(
                it.toLocalDate().atStartOfDay(ZoneOffset.UTC).toInstant().toEpochMilli()
            )
        }
        val picker: MaterialDatePicker<Long> = builder.build()
        picker.addOnPositiveButtonClickListener {
            val instant: Instant = Instant.ofEpochMilli(it)
            val date = instant.atZone(ZoneOffset.UTC)
            viewModel.setBirthdate(date)
        }
        picker.show(requireActivity().supportFragmentManager, picker.toString())
    }

    private fun setTopSpan() {
        context?.let { context ->
            val clickablePart =
                context.getString(R.string.registration___authorize__clickable_part)
            val notClickablePart =
                context.getString(R.string.registration___authorize__not_clickable_part)

            val subtitleSpannedString = requireContext().spannedString {
                it
                    .append(notClickablePart)
                    .append(Characters.nonBreakingSpace)
                    .append(clickablePart)
            }

            val subtitleClickableSpan: ClickableSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    findNavController().navigateUp()

                    metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.registrationOnAgreementClick)
                    firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.registrationOnLoginClick)
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.isUnderlineText = false
                    ds.color =
                        context.getSupportColor(R.color.application___color__primary)
                }
            }

            subtitleSpannedString.setSpan(
                subtitleClickableSpan,
                notClickablePart.length + 1,
                subtitleSpannedString.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            binding.registrationAuthorize.apply {
                movementMethod = LinkMovementMethod.getInstance()
                text = subtitleSpannedString
            }
        }
    }

    private fun setBottomSpan() {
        val subtitleSpannedString = requireContext().spannedString {
            it
                .append(R.string.registration__bottom_span_1_part)
                .append(Characters.nonBreakingSpace)
                .append(R.string.registration__bottom_span_2_part)
                .spans {
                    object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.registrationOnAgreementClick)
                            firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.registrationOnAgreementClick)
                            tryStartActivityBy {
                                it
                                    .view(_agreement)
                                    .newTask()
                            }
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            ds.isUnderlineText = false
                            ds.color =
                                requireContext().getSupportColor(R.color.application___color__primary)
                        }
                    }
                }
                .append(Characters.nonBreakingSpace)
                .append(R.string.registration__bottom_span_3_part)
                .append(Characters.nonBreakingSpace)
                .append(R.string.registration__bottom_span_4_part)
                .spans {
                    object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.deleteAccountSuccessfulClick)
                            firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.registrationOnConditionsClick)
                            tryStartActivityBy {
                                it
                                    .view(_consentOfPersonalDataProcessing)
                                    .newTask()
                            }
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            ds.isUnderlineText = false
                            ds.color =
                                requireContext().getSupportColor(R.color.application___color__primary)
                        }
                    }
                }
        }

        binding.registrationSpan.apply {
            movementMethod = LinkMovementMethod.getInstance()
            text = subtitleSpannedString
        }
    }

    private fun checkNameValid(name: String?): Boolean {
        return if (name.isNullOrBlank()) {
            binding.scroll.smoothScrollTo(0, binding.root.top)
            binding.registrationUsernameContainer.makeAccent(
                binding.registrationUsername,
                "Укажите имя"
            )
            false
        } else {
            true
        }
    }

    private fun checkSurnameValid(surname: String?): Boolean {
        return if (surname.isNullOrBlank()) {
            binding.scroll.smoothScrollTo(0, binding.root.top)
            binding.registrationUserSurnameContainer.makeAccent(
                binding.registrationUserSurname,
                "Укажите фамилию"
            )
            false
        } else {
            true
        }
    }

    private fun checkCountryValid(country: Location?): Boolean {
        return if (country == null) {
            binding.scroll.smoothScrollTo(0, binding.root.bottom)
            binding.registrationCountryContainer.makeAccent(
                binding.registrationCountry,
                "Укажите страну"
            )
            false
        } else {
            true
        }
    }

    private fun checkCityValid(city: Location?): Boolean {
        return if (city == null) {
            binding.scroll.smoothScrollTo(0, binding.root.bottom)
            binding.registrationCityContainer.makeAccent(
                binding.registrationCity,
                "Укажите город"
            )
            false
        } else {
            true
        }
    }

    private fun checkBirthdayValid(birthday: ZonedDateTime?): Boolean {
        return if (birthday == null) {
            binding.scroll.smoothScrollTo(0, binding.root.bottom)
            binding.registrationUserBirthdayContainer.makeAccent(
                binding.registrationUserBirthday,
                "Укажите дату рождения"
            )
            false
        } else {
            true
        }
    }

    private fun checkEmailValid(email: String?): Boolean {
        return if (email.isNullOrBlank()) {
            binding.scroll.smoothScrollTo(0, binding.root.bottom)
            binding.registrationEmailContainer.makeAccent(
                binding.registrationEmail,
                "Укажите почту"
            )
            false
        } else {
            true
        }
    }

    private fun checkPasswordValid(password: String?): Boolean {
        return if (password.isNullOrBlank()) {
            binding.scroll.smoothScrollTo(0, binding.root.bottom)
            binding.passwordContainer.makeAccent(
                binding.password,
                "Укажите пароль"
            )
            false
        } else {
            true
        }
    }

    private fun checkRepeatPasswordValid(password: String?, repeatPassword: String?): Boolean {
        return if (password != repeatPassword) {
            binding.passwordSecondContainer.makeAccent(
                binding.passwordSecond,
                "Пароли не совавдают"
            )
            false
        } else {
            binding.passwordSecondContainer.makeDefault(binding.passwordSecond)
            true
        }
    }

    private fun nextStep() {
        UnderageRegistrationFragment
            .navigate(
                findNavController(),
                _bundler
            )
            .toUnderageRegistration(
                R.id.registration__to__registration__underage_registration,
                RegistrationRequestParams(
                    name = viewModel.state.value.name,
                    surname = viewModel.state.value.surname,
                    password = viewModel.state.value.password,
                    email = viewModel.state.value.email,
                    birthdate = viewModel.state.value.birthday!!,
                    settlement = viewModel.state.value.city?.coordinates
                        ?: GeoPoint.Coordinates(0.0, 0.0),
                    settlementCode = viewModel.state.value.city?.settlementCode,
                    title = viewModel.state.value.city?.title,
                    country = viewModel.state.value.city?.country,
                    countryISO = viewModel.state.value.city?.countryISO,
                    municipality = viewModel.state.value.city?.municipality,
                    municipalityCode = viewModel.state.value.city?.municipalityCode,
                    cityAlternative = viewModel.state.value.city?.cityAlternative,
                    flat = viewModel.state.value.city?.flat,
                    house = viewModel.state.value.city?.house,
                    street = viewModel.state.value.city?.street,
                    region = viewModel.state.value.city?.region?.take(2),
                    isReceiveNewsletters = viewModel.state.value.isReceiveNewsletters,
                    isRussian = viewModel.state.value.city?.isRussia().orTrue()
                )
            )
    }

    private fun CoroutineScope.handleFragmentResults() {
        launch {
            AddressSelectorFragment
                .onResult(
                    this@RegistrationFragment,
                    _requestCodeSelectCity,
                    _bundler
                ).collect {
                    viewModel.setCity(it)
                }
        }

        launch {
            AddressSelectorFragment
                .onResult(
                    this@RegistrationFragment,
                    _requestCodeSelectCountry,
                    _bundler
                ).collect {
                    viewModel.setCountry(it)
                }
        }
    }

    private fun TextInputLayout.makeAccent(
        edittext: EditText,
        errorText: String? = null
    ) {
        edittext.background.setTint(
            ResourcesCompat.getColor(
                resources,
                R.color.application___color__accent_background,
                null
            )
        )
        if (errorText != null) error = errorText
    }

    private fun TextInputLayout.makeDefault(edittext: EditText) {
        edittext.background.setTint(
            ResourcesCompat.getColor(
                resources,
                R.color.white,
                null
            )
        )
        error = null
    }

    private fun TextInputLayout.makeWarning(edittext: EditText) {
        edittext.background.setTint(
            ResourcesCompat.getColor(
                resources,
                R.color.application___color__orange_44,
                null
            )
        )
        error = null
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()
        _socialNetworksAdapter
            .onItemClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(AndroidSchedulers.mainThread())
            .subscribeBy { item ->
                when (item) {
                    is SocialNetworksAdapter.Item.SocialNetwork -> {
                        metricManager.sendSimpleEvent("${AnalyticsConstants.Event.Login.registrationSocialNetworkClick}_${item.path}_click")
                        firebaseAnalyticManager.sendSimpleEvent("${AnalyticsConstants.Event.Login.registrationSocialNetworkClick}_${item.path}_click")

                        if (item.path == "vkontakte") {
                            binding.registrationPerform.gone()
                            binding.progress.visible()
                            VKID(requireContext()).authorize(
                                this@RegistrationFragment,
                                object : VKID.AuthCallback {
                                    override fun onFail(fail: VKIDAuthFail) {
                                        binding.registrationPerform.visible()
                                        binding.progress.gone()
                                    }

                                    override fun onSuccess(accessToken: AccessToken) {
                                        binding.progress.gone()
                                        authorizationVKCompleted(
                                            context = requireContext(),
                                            api = _api,
                                            accessToken = accessToken,
                                            messageDisplay = _messageDisplay,
                                            errorHandler = _errorHandler
                                        )
                                    }
                                })
                        } else {
                            val api = _settings.environment.getOrDefault().api
                            if (api is Environment.Api.Real) {
                                val url = api.server.buildSocialNetworkAuthorizationUri(item.path)

                                findNavController().navigate(
                                    R.id.authorizationWebView,
                                    Bundle().apply {
                                        putString("URL", url.toString())
                                        putString("title", "Регистрация")
                                    }
                                )
                            }
                        }
                    }

                    is SocialNetworksAdapter.Item.ExpandButton -> {
                        val listWithoutExpandButton = _socialNetworkList

                        _socialNetworksAdapter.setItems(listWithoutExpandButton)
                        (binding.registrationSocialNetworkContainer.layoutManager as GridLayoutManager).spanCount =
                            App.authorizationExpandedSocialsSpanCount
                    }
                }
            }
            .scoped()

        binding.registrationPerform
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                onRegistration()
            }
            .scoped()
    }
}
