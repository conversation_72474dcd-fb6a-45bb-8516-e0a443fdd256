package ru.dobro.registration

import androidx.lifecycle.viewModelScope
import common.library.core.collection.orEmptySet
import common.library.core.location.GeoPoint
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import ru.dobro.core.settings.UserSettings
import ru.dobro.domain.Location
import ru.dobro.domain.SettlementExtended
import ru.dobro.domain.error.Field
import ru.dobro.domain.error.HintError
import ru.dobro.domain.repository.AuthRepository
import ru.dobro.domain.resultWrapper.HttpException
import ru.dobro.domain.resultWrapper.ResultWrapper
import java.time.ZonedDateTime

class RegistrationViewModel(
    private val repository: AuthRepository,
    private val userSettings: UserSettings
) : androidx.lifecycle.ViewModel() {

    private val _state: MutableStateFlow<State> = MutableStateFlow(State())
    val state: StateFlow<State> = _state.asStateFlow()

    fun setName(name: String) {
        _state.update { it.copy(name = name) }
    }

    fun setSurname(surname: String) {
        _state.update { it.copy(surname = surname) }
    }

    fun setCountry(country: Location) {
        if (country != state.value.country) {
            _state.update { it.copy(country = country, city = null) }
        }
    }

    fun setCity(city: Location) {
        _state.update { it.copy(city = city) }
    }

    fun setBirthdate(birthdate: ZonedDateTime) {
        _state.update { it.copy(birthday = birthdate) }
    }

    fun setEmail(email: String) {
        _state.update { it.copy(email = email) }
    }

    fun setPassword(password: String) {
        _state.update { it.copy(password = password) }
    }

    fun setRepeatPassword(password: String) {
        _state.update { it.copy(passwordRepeat = password) }
    }

    fun switchIsReceiveNewsletters() {
        _state.update { it.copy(isReceiveNewsletters = !it.isReceiveNewsletters) }
    }

    fun setIsUnderAged(isUnderAged: Boolean) {
        _state.update { it.copy(isUnderAged = isUnderAged) }
    }

    fun createAccount() {
        // TODO перенести логику по созданию в Репозиторий (но сначала его нужно создать)
        viewModelScope.launch(IO) {
            if (state.value.city != null && state.value.birthday != null) {
                _state.update { it.copy(isCreating = true) }
                val result = repository.createUser(
                    name = state.value.name,
                    surname = state.value.surname,
                    password = state.value.password,
                    email = state.value.email,
                    birthdate = state.value.birthday!!,
                    settlement = state.value.city?.coordinates ?: GeoPoint.Coordinates(0.0, 0.0),
                    settlementExtended = SettlementExtended.restore(
                        title = state.value.city?.title,
                        country = state.value.city?.countryISO,
                        municipality = state.value.city?.municipality,
                        municipalityCode = state.value.city?.municipalityCode,
                        settlement = state.value.city?.cityAlternative,
                        flat = state.value.city?.flat,
                        house = state.value.city?.house,
                        street = state.value.city?.street,
                        region = state.value.city?.region?.take(2),
                        x = state.value.city?.coordinates?.longitude,
                        y = state.value.city?.coordinates?.latitude
                    ),
                    categories = userSettings.getCategories().orEmptySet(),
                    receiveNewsletters = state.value.isReceiveNewsletters
                )

                when (result) {
                    is ResultWrapper.Success -> {
                        _state.update { it.copy(isCreating = false, isCreated = true) }
                    }

                    is ResultWrapper.Error -> {
                        when (val error = result.error) {
                            is HttpException.WithBody -> {
                                when (val body = error.errorBody) {
                                    is HintError -> {
                                        body.violations.firstOrNull()?.let { violation ->
                                            _state.update {
                                                it.copy(
                                                    isCreating = false,
                                                    errorField = violation.propertyPath to violation.message
                                                )
                                            }
                                        } ?: also {
                                            _state.update {
                                                it.copy(
                                                    isCreating = false,
                                                    errorField = Field.Unknown to (body.message
                                                        ?: "Произошла неизвестная ошибка, попробуйте позже")
                                                )
                                            }
                                        }
                                    }
                                }
                            }

                            is HttpException.AccountDisabled -> {
                                _state.update {
                                    it.copy(
                                        isCreating = false,
                                        isAccountDisabled = true
                                    )
                                }
                            }

                            else -> {
                                _state.update {
                                    it.copy(
                                        isCreating = false,
                                        errorField = Field.Unknown to ("Произошла неизвестная ошибка, попробуйте позже")
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    fun resetErrorField() {
        _state.update { it.copy(errorField = null) }
    }

    /**
     *  [isUnderAged] - необходимо заполнить данные родителем
     *  [isCreating] - true, когда отправляется запрос
     *  [errorField] - используется для "подсветки" поля с ошибкой
     *  [isCreated] - если true, успешно создано
     **/
    data class State(
        val name: String = "",
        val surname: String = "",
        val country: Location = Location.restoreRussiaCountry(),
        val city: Location? = null,
        val birthday: ZonedDateTime? = null,
        val email: String = "",
        val password: String = "",
        val passwordRepeat: String = "",
        val isReceiveNewsletters: Boolean = true,
        val isUnderAged: Boolean = false,
        val isCreating: Boolean = false,
        val errorField: Pair<Field, String>? = null,
        val isCreated: Boolean = false,
        val isAccountDisabled: Boolean = false,
    )
}
