package ru.dobro.change_email

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.Toolbar
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import com.google.android.material.appbar.AppBarLayout
import common.library.android.inflateBy
import common.library.android.input.InputFieldDescriptor
import common.library.android.input.setupInputFields
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.message.MessageDisplay
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.string.r
import common.library.core.rx.RxSchedulers
import common.library.core.rx.ofSubtype
import common.library.core.state.task.handleActionExecution
import common.library.core.variable.Agent
import common.library.core.variable.agent
import common.library.core.variable.sendWhen
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.collections.immutable.persistentListOf
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.R
import ru.dobro.api.DobroApi
import ru.dobro.api.profile.ProfileApi
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.error.ErrorMessages
import ru.dobro.databinding.FragmentChangeEmailBinding
import ru.dobro.domain.UserId
import javax.annotation.CheckReturnValue

class ChangeEmailFragment : BaseFragment({
    bind<Agent<ChangeEmailState>>() with singleton { agent(ChangeEmailState.Idle) }
}), HasCustomToolbar {
    override val screenName: String = AnalyticsConstants.Screen.Main.changeEmail
    private lateinit var binding: FragmentChangeEmailBinding

    companion object {
        class NavigationContext(
            private val _navigation: NavController,
            private val _bundler: Bundler
        ) {
            fun toChangeEmailFragment(
                userId: UserId,
                resultKey: String
            ) {
                _navigation.navigate(
                    R.id.main__profile_edit__to__change_email,
                    RequestToChangeEmailFragment(
                        userId = userId,
                        resultKey = resultKey
                    ).intoBundle(_bundler)
                )
            }
        }

        @CheckReturnValue
        fun navigate(
            navigation: NavController,
            bundler: Bundler
        ): NavigationContext = NavigationContext(navigation, bundler)
    }

    @CheckReturnValue
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar = object :
        HasCustomToolbar.CustomToolbar.Layout(HasCustomToolbar.CustomToolbarStyle.OverlapContent) {
        @CheckReturnValue
        override fun onCreateView(container: ViewGroup): View =
            container.context.inflateBy(
                R.layout.application___toolbar__transparent_accent,
                container
            )

        @CheckReturnValue
        override fun getAppBarLayout(view: View): AppBarLayout? = null

        @CheckReturnValue
        override fun getToolbar(view: View): Toolbar = view.findViewById(R.id.application___toolbar)
    }

    private val _state: Agent<ChangeEmailState> by instance()

    private val _bundler: Bundler by instance()

    private val _api: DobroApi by instance()

    private val _messageDisplay: MessageDisplay by instance()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentChangeEmailBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        val request: RequestToChangeEmailFragment? = arguments?.getParams(_bundler)

        if (request === null) {
            logger.warning { "Invalid request to ChangeEmailFragment" }

            findNavController().navigateUp()

            return
        }

        _state
            .ofSubtype<ChangeEmailState.Idle>()
            .subscribeBy {
                _state.sendWhen<ChangeEmailState, ChangeEmailState.Idle> {
                    requestInput(request.userId)
                }
            }
            .scoped()

        _state
            .handleActionExecution<
                ChangeEmailState,
                ChangeEmailState.Processing,
                ProfileApi.ChangeEmailRequest
                >(
                task = {
                    _api
                        .profile()
                        .changeEmailAsync(it)
                        .doOnError { _messageDisplay.showMessage("Не удалось загрузить данные".r) }
                },
                toTaskState = { it.changeEmailTask },
                updateState = { updateTask(it) }
            )
            .scoped()

        _state
            .setupInputFields(
                persistentListOf(
                    InputFieldDescriptor(
                        ChangeEmailField.Email,
                        binding.newEmail,
                        binding.newEmailContainer,
                        false
                    )
                ),
                binding.performChangeEmail
            )
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<ChangeEmailState.Completed>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                when (it) {
                    ChangeEmailState.Completed.Success -> {
                        findNavController().navigate(
                            R.id.change_email__to__check_new_email
                        )
                    }

                    is ChangeEmailState.Completed.Fail -> {
                        ErrorMessages.ofException(it.error)?.let {
                            _messageDisplay.showMessage(it)
                        }

                        _state
                            .sendWhen<ChangeEmailState, ChangeEmailState.Completed.Fail> {
                                backToInput()
                            }
                    }
                }
            }
            .scoped()
    }
}
