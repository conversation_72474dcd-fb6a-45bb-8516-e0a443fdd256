package ru.dobro.change_email

import common.library.android.input.InputField
import common.library.android.input.InputLine
import common.library.android.input.InputRequestState
import common.library.core.contract.ContractException
import common.library.core.email.Email
import common.library.core.state.task.TaskActionState
import ru.dobro.api.profile.ProfileApi
import ru.dobro.core.validation.EmailInputValidator
import ru.dobro.domain.UserId
import ru.dobro.main.profile_edit.ChangeEmailDataState
import ru.dobro.main.profile_edit.ChangeEmailDataStateIdleCompletedFail
import ru.dobro.main.profile_edit.ChangeEmailDataStateIdleCompletedSuccess
import javax.annotation.CheckReturnValue

sealed class ChangeEmailState :
    InputRequestState<ChangeEmailField, ChangeEmailState> {
    data object Idle : ChangeEmailState() {
        @CheckReturnValue
        fun requestInput(userId: UserId) = Input(
            email = InputField.empty(),
            _userId = userId
        )
    }

    data class Input(
        val email: InputField<Email>,
        private val _userId: UserId,
    ) : ChangeEmailState(),
        InputRequestState.Input<ChangeEmailField, ChangeEmailState> {
        override val isValid: Boolean
            get() = email is InputField.NonEmpty.Valid

        override fun focus(id: ChangeEmailField): ChangeEmailState =
            when (id) {
                ChangeEmailField.Email -> copy(email = email.markFocused())
            }

        override fun get(id: ChangeEmailField): InputLine<*> =
            when (id) {
                ChangeEmailField.Email -> email
            }

        override fun input(id: ChangeEmailField, value: String): ChangeEmailState =
            when (id) {
                ChangeEmailField.Email -> inputEmail(value)
            }

        @CheckReturnValue
        private fun inputEmail(value: String): ChangeEmailState = copy(
            email = EmailInputValidator.validateInput(email, value)
        )

        override fun processing(): ChangeEmailState {
            if (email !is InputField.NonEmpty.Valid) {
                throw ContractException("Invalid input.")
            }

            return Processing(
                this,
                TaskActionState
                    .Idle
                    .awaiting<ProfileApi.ChangeEmailRequest>()
                    .execute(
                        ProfileApi.ChangeEmailRequest(
                            email = email.getDataChange()
                        )
                    )
            )
        }
    }

    data class Processing(
        private val _input: Input,
        val changeEmailTask: ChangeEmailDataState
    ) : ChangeEmailState() {
        @CheckReturnValue
        fun updateTask(action: ChangeEmailDataState.() -> ChangeEmailDataState): ChangeEmailState {
            return when (val state = changeEmailTask.action()) {
                is ChangeEmailDataStateIdleCompletedFail -> Completed.Fail(state.error, _input)
                is ChangeEmailDataStateIdleCompletedSuccess -> Completed.Success
                else -> copy(changeEmailTask = state)
            }
        }
    }

    sealed class Completed : ChangeEmailState() {
        data object Success : Completed()

        data class Fail(
            val error: Throwable?,
            val _input: Input
        ) : Completed() {
            @CheckReturnValue
            fun backToInput(): ChangeEmailState = _input
        }
    }
}
