package ru.dobro.main.task_report

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.navigation.fragment.findNavController
import common.library.android.dialog.dialog
import common.library.android.dialog.okButton
import common.library.android.image.internalization.ImageInternalizer
import common.library.android.input.InputFieldDescriptor
import common.library.android.input.setupInputFields
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.pick
import common.library.android.intent.startActivityForResultBy
import common.library.android.message.MessageDisplay
import common.library.android.permissions.PermissionsState
import common.library.android.permissions.checkPermissionManager
import common.library.android.resource.withOnCreateResourcesScope
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.string.rString
import common.library.android.string.rText
import common.library.android.widget.isGoneVisible
import common.library.android.widget.onClick
import common.library.core.data.FileExtension
import common.library.core.data.MimeType
import common.library.core.lazyGet
import common.library.core.logging.logWarning
import common.library.core.rx.RxSchedulers
import common.library.core.rx.ofSubtype
import common.library.core.state.task.TaskActionState
import common.library.core.state.task.TaskState
import common.library.core.state.task.handleActionExecution
import common.library.core.variable.Agent
import common.library.core.variable.agent
import common.library.core.variable.sendWhen
import io.reactivex.Single
import io.reactivex.disposables.Disposable
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.collections.immutable.persistentListOf
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.App
import ru.dobro.R
import ru.dobro.api.TargetedHelpApi
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.RequestCodes
import ru.dobro.core.account.AccountManager
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.data.TempFiles
import ru.dobro.core.error.UserVisibleException
import ru.dobro.databinding.MainTaskReportBinding
import ru.dobro.domain.targeted_help.Task
import ru.dobro.image_picker.GalleryState
import ru.dobro.image_picker.GalleryState.Idle.checkPermissionsAndOpenIfGranted
import ru.dobro.image_picker.GalleryState.Ready.open
import ru.dobro.image_picker.GalleryState.Selection.cancel
import ru.dobro.image_picker.GalleryState.Selection.select
import javax.annotation.CheckReturnValue

/**
 * Required param [Task]
 */
class TaskReportFragment : BaseFragment({
    bind<Agent<TaskReportState>>() with singleton { agent(TaskReportState.Idle) }
}), HasCustomToolbar {
    private lateinit var binding: MainTaskReportBinding

    private val _taskReportAdapter: TaskReportAdapter by lazyGet { TaskReportAdapter() }

    private val _accountManager: AccountManager by instance()

    private val _messageDisplay: MessageDisplay by instance()

    private val _state: Agent<TaskReportState> by instance()

    private val _targetedHelpApi: TargetedHelpApi by instance()

    private val _imageInternalizer: ImageInternalizer by instance()

    private val _bundler: Bundler by instance()

    override val screenName: String
        get() = AnalyticsConstants.Screen.Main.taskReport

    companion object {
        private val _galleryRequestCode: Int = RequestCodes.result()
        private val _filePickerPermissionsRequestCode: Int = RequestCodes.permission()
        val tempFiles: TempFiles = TempFiles.forType<TaskReportFragment>()

        @CheckReturnValue
        private fun _createResultFile(context: Context, extension: FileExtension): java.io.File {
            return tempFiles.create(context, "result__", extension)
        }
    }

    @CheckReturnValue
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        HasCustomToolbar.CustomToolbar.None

    override fun onCreate(savedInstanceState: Bundle?) = withOnCreateResourcesScope {
        super.onCreate(savedInstanceState)

        tempFiles
            .deleteOldAsync(requireContext(), App.tempFilesTimeToLive)
            .subscribe()
            .scoped()

        setupUploadImage().scoped()
    }

    @CheckReturnValue
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = MainTaskReportBinding.inflate(inflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.mainTaskReportList.adapter = _taskReportAdapter
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        val accountInfo = _accountManager.getInfo()

        if (accountInfo === null) {
            logger.debug { "No account info." }

            findNavController().navigateUp()

            return
        }

        val task: Task? = arguments?.getParams(_bundler)
        if (task === null) {
            logger.debug { "Illegal param task: $task" }

            findNavController().navigateUp()

            return
        }

        _state.setupInputFields(
            persistentListOf(
                InputFieldDescriptor(
                    TaskReportField.HelpType,
                    binding.mainTaskReportHelpType,
                    binding.mainTaskReportHelpTypeContainer,
                    false
                ),
                InputFieldDescriptor(
                    TaskReportField.Difficulties,
                    binding.mainTaskReportDifficulties,
                    binding.mainTaskReportDifficultiesContainer,
                    false
                ),
                InputFieldDescriptor(
                    TaskReportField.Comment,
                    binding.mainTaskReportComment,
                    binding.mainTaskReportCommentContainer,
                    false
                )
            )
        ) { }.scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<TaskReportState.Idle>()
            .subscribeBy {
                _state.sendWhen<TaskReportState, TaskReportState.Idle> {
                    requestInput(task)
                }
            }
            .scoped()

        _state
            .map {
                (it is TaskReportState.Processing && it.taskState is TaskActionState.InProgress)
                    || (it is TaskReportState.UploadImageProcessing && it.state is TaskState.InProgress)
            }
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.mainTaskReportProgress.isGoneVisible = it
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<TaskReportState.Input>()
            .distinctUntilChanged { displaying -> displaying::class }
            .observeOn(RxSchedulers.main())
            .subscribeBy { state ->
                if (!binding.mainTaskReportComment.isFocused) {
                    binding.mainTaskReportComment.setText(
                        state.comment.tryGetData().orNull()
                    )
                }
                if (!binding.mainTaskReportHelpType.isFocused) {
                    binding.mainTaskReportHelpType.setText(
                        state.helpType.tryGetData().orNull()
                    )
                }
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<TaskReportState.Input>()
            .distinctUntilChanged()
            .observeOn(RxSchedulers.main())
            .subscribeBy { state ->
                _taskReportAdapter.submitList(state.buildItems())
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<TaskReportState.Input>()
            .map { it.isValid }
            .distinctUntilChanged()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.mainTaskReportSave.isEnabled = it
            }
            .scoped()

        _state
            .handleActionExecution<
                TaskReportState,
                TaskReportState.Processing,
                SaveReportRequest>(
                toTaskState = { it.taskState },
                task = {
                    _targetedHelpApi
                        .overview()
                        .addTaskReportAsync(it.takId, it.body)
                },
                updateState = { updateTaskState(it) }
            )
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<TaskReportState.Completed>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                when (it) {
                    is TaskReportState.Completed.Success -> {
                        _messageDisplay.showMessage(R.string.main__task_report___added.rString)

                        findNavController().navigateUp()
                    }

                    is TaskReportState.Completed.Failed -> {
                        if (it.error !== null) {
                            _messageDisplay.showMessage(it.error)
                        }

                        _state.sendWhen<TaskReportState, TaskReportState.Completed.Failed> {
                            backToInput()
                        }
                    }

                    is TaskReportState.Completed.SuccessUploadImage -> {
                        _state.sendWhen<TaskReportState, TaskReportState.Completed.SuccessUploadImage> {
                            backToInput()
                        }
                    }
                }
            }
            .scoped()

        binding.mainTaskReportSave
            .onClick
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state.sendWhen<TaskReportState, TaskReportState.Input> {
                    processing()
                }
            }
            .scoped()

        binding.mainTaskReportClose
            .onClick
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                findNavController().navigateUp()
            }
            .scoped()

        _taskReportAdapter
            .onPlusClick
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state.sendWhen<TaskReportState, TaskReportState.Input> {
                    addHourToUser(it)
                }
            }
            .scoped()

        _taskReportAdapter
            .onMinusClick
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state.sendWhen<TaskReportState, TaskReportState.Input> {
                    removeHourFromUser(it)
                }
            }
            .scoped()

        _taskReportAdapter
            .onDeleteImageClick
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state.sendWhen<TaskReportState, TaskReportState.Input> {
                    removeImage(it)
                }
            }
            .scoped()

        _taskReportAdapter
            .onAddImageClick
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _openGallery()
            }
            .scoped()

        _state
            .ofSubtype<TaskReportState.Input>()
            .observeOn(RxSchedulers.computation())
            .map { it.galleryState }
            .ofSubtype<GalleryState.Selection>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                startActivityForResultBy(_galleryRequestCode) {
                    it.pick().withData(MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
                }
            }
            .scoped()

        _state
            .ofSubtype<TaskReportState.Input>()
            .observeOn(RxSchedulers.computation())
            .map { it.galleryState }
            .ofSubtype<GalleryState.Internalization>()
            .concatMapCompletable {
                val file: java.io.File =
                    _createResultFile(requireContext(), FileExtension.jpeg)

                Single
                    .fromCallable {
                        when (
                            _imageInternalizer.internalize(
                                it.selectedImage,
                                file,
                                MimeType.Image.jpeg
                            )
                        ) {
                            ImageInternalizer.Error.InvalidInputSource,
                            ImageInternalizer.Error.UnsupportedInputFormat,
                            ImageInternalizer.Error.UnsupportedOutputFormat -> throw UserVisibleException(
                                R.string.application___error__unsupported_file_format.rString
                            )

                            null -> file
                        }
                    }
                    .subscribeOn(RxSchedulers.io())
                    .observeOn(RxSchedulers.computation())
                    .logWarning(logger) { "Failed to save selected file into internal storage." }
                    .doOnSuccess {
                        _state.sendWhen<TaskReportState, TaskReportState.Input> {
                            updateGalleryState {
                                if (this is GalleryState.Internalization) {
                                    success(it)
                                } else {
                                    this
                                }
                            }
                        }
                    }
                    .doOnError {
                        _state.sendWhen<TaskReportState, TaskReportState.Input> {
                            updateGalleryState {
                                if (this is GalleryState.Internalization) {
                                    fail(it)
                                } else {
                                    this
                                }
                            }
                        }

                        file.delete()
                    }
                    .ignoreElement()
                    .onErrorComplete()
            }
            .subscribe()
            .scoped()

        _handleGalleryPermissions().scoped()
    }

    private fun _openGallery() {
        _state.sendWhen<TaskReportState, TaskReportState.Input> {
            updateGalleryState {
                when (this) {
                    is GalleryState.Idle -> checkPermissionsAndOpenIfGranted(
                        checkPermissionManager
                    )

                    is GalleryState.PermissionsCheck -> updatePermissionsState { resetExplained() }
                    is GalleryState.Ready -> open()
                    else -> this
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            _galleryRequestCode -> {
                val selectedFile: Uri? = if (resultCode == Activity.RESULT_OK) {
                    data?.data
                } else {
                    null
                }
                _state.sendWhen<TaskReportState, TaskReportState.Input> {
                    updateGalleryState {
                        if (this is GalleryState.Selection) {
                            if (selectedFile !== null) {
                                select(selectedFile)
                            } else {
                                cancel()
                            }
                        } else {
                            this
                        }
                    }
                }
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            _filePickerPermissionsRequestCode ->
                _state.sendWhen<TaskReportState, TaskReportState.Input> {
                    updateGalleryState {
                        if (this is GalleryState.PermissionsCheck) {
                            updatePermissionsState {
                                handleResponse(
                                    checkPermissionManager,
                                    permissions,
                                    grantResults
                                )
                            }
                        } else {
                            this
                        }
                    }
                }
        }
    }

    @CheckReturnValue
    private fun _handleGalleryPermissions(): Disposable {
        var permissionDialog: Dialog? = null

        fun _dismissPermissionDialog() {
            val dialog: Dialog? = permissionDialog
            if (dialog !== null) {
                dialog.dismiss()
            }
            permissionDialog = null
        }

        return _state
            .subscribeBy {
                if (it is TaskReportState.Input && it.galleryState is GalleryState.PermissionsCheck) {
                    val galleryState = it.galleryState
                    val permissionsState: PermissionsState.NotAllGranted =
                        galleryState.permissionsState

                    if (permissionsState.isAllDeniedExplained()) {
                        permissionsState.requestDeniedOnce(
                            checkPermissionManager,
                            _filePickerPermissionsRequestCode
                        )

                        _dismissPermissionDialog()
                    } else {
                        if (permissionDialog == null) {
                            val dialog: AlertDialog = requireContext().dialog {
                                it
                                    .message(R.string.image_picker__permission_explain.rText)
                                    .okButton { _dismissPermissionDialog() }
                                    .cancellable()
                            }

                            dialog.setOnDismissListener {
                                galleryState.updatePermissionsState { markAllExplained() }
                            }

                            dialog.show()

                            permissionDialog = dialog
                        }
                    }
                } else {
                    _dismissPermissionDialog()
                }
            }
    }
}
