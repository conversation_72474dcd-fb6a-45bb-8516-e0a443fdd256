package ru.dobro.main.profile.organization.addEvent

import android.net.Uri
import android.os.Bundle
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ScrollView
import android.widget.Toast
import androidx.annotation.IdRes
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import com.google.android.flexbox.AlignItems
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.MaterialDatePicker
import com.google.android.material.timepicker.MaterialTimePicker
import com.google.android.material.timepicker.TimeFormat
import common.library.android.coroutines.repeatOnResume
import common.library.android.coroutines.safeLaunch
import common.library.android.coroutines.subscribeToFlow
import common.library.android.coroutines.throttleUserInput
import common.library.android.getSupportColor
import common.library.android.inflateBy
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.fromBundle
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.intent.newTask
import common.library.android.intent.tryStartActivityBy
import common.library.android.intent.view
import common.library.android.message.MessageDisplay
import common.library.android.onFragmentResult
import common.library.android.string.r
import common.library.android.string.span.spannedString
import common.library.android.widget.gone
import common.library.android.widget.isGoneVisible
import common.library.android.widget.isVisible
import common.library.android.widget.onThrottleClick
import common.library.android.widget.visible
import common.library.core.Characters
import common.library.core.lazyGet
import common.library.core.variable.ScreenState
import io.reactivex.Observable
import kotlinx.collections.immutable.PersistentSet
import kotlinx.collections.immutable.persistentSetOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.collections.immutable.toPersistentSet
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.R
import ru.dobro.api.DobroApi
import ru.dobro.api.vacancy.VacancyApi
import ru.dobro.categories_selector.CategoriesAdapter
import ru.dobro.categories_selector.CategoriesSelectorRequest
import ru.dobro.common.socials.SocialsAdapter
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.RequestCodes
import ru.dobro.core.account.AccountManager
import ru.dobro.core.account.OrganizationStatus
import ru.dobro.core.account.getOrganizationId
import ru.dobro.core.account.getOrganizationStatus
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.setHtmlText
import ru.dobro.core.validation.DatePickerRangeValidator
import ru.dobro.core.validation.Ui.makeAccent
import ru.dobro.core.validation.Ui.makeDefault
import ru.dobro.databinding.MainProfileMeOrganizatorAddEventBinding
import ru.dobro.domain.Category
import ru.dobro.domain.Location
import ru.dobro.domain.event.Event
import ru.dobro.domain.navigation.Fields
import ru.dobro.domain.navigation.PickAddress
import ru.dobro.image_picker.CropMode
import ru.dobro.image_picker.ImagePickerFragment
import ru.dobro.image_picker.ImagePickerResult
import ru.dobro.images
import ru.dobro.main.event.EventFragment
import ru.dobro.main.organization.ReviewAdapter
import ru.dobro.main.profile.ProfileMeFragment
import ru.dobro.main.profile.becomeOrganizator.CreateOrganizationFragment
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.Calendar
import javax.annotation.CheckReturnValue
import kotlin.math.abs

private const val MAX_DAYS_TO_SHOW = 7

class AddEventFragment : BaseFragment({
    bind<ScreenState<AddEventState>>() with singleton { ScreenState(AddEventState.Idle) }
    bind<SocialsAdapter>() with singleton { SocialsAdapter(instance()) }
    bind<ReviewAdapter>() with singleton { ReviewAdapter(instance()) }
}), HasCustomToolbar, DaysAdapterDelegate {

    private val state: ScreenState<AddEventState> by instance()

    private val messageDisplay: MessageDisplay by instance()

    private val dobroApi: DobroApi by instance()

    private val accountManager: AccountManager by instance()

    private val bundler: Bundler by instance()

    private val categoriesAdapter: CategoriesAdapter by lazyGet { CategoriesAdapter() }

    private val daysAdapter: DaysAdapter by lazyGet { DaysAdapter() }

    private val requestCodeSelectImage: String =
        RequestCodes.scopedBy<ProfileMeFragment>("select_image")

    private val requestCodeSelectAddress: String =
        RequestCodes.scopedBy<CreateOrganizationFragment>("select_address")

    private lateinit var binding: MainProfileMeOrganizatorAddEventBinding

    private var currentIndex = 0

    private var initialSet = false

    override val screenName: String
        get() = AnalyticsConstants.Screen.Main.profileMe

    companion object {
        @CheckReturnValue
        fun navigate(
            navigator: NavController,
            bundler: Bundler
        ): NavigationContext = NavigationContext(navigator, bundler)

        class NavigationContext(
            private val navigator: NavController,
            private val bundler: Bundler
        ) {
            @CheckReturnValue
            fun toAddEventDefault(
                @IdRes actionId: Int,
            ) {
                navigator.navigate(
                    actionId
                )
            }

            @CheckReturnValue
            fun toAddEventEdit(
                @IdRes actionId: Int,
                event: Event
            ) {
                navigator.navigate(
                    actionId,
                    event.intoBundle(bundler)
                )
            }
        }

        @CheckReturnValue
        fun onResult(
            fragment: Fragment,
            resultKey: String,
            bundler: Bundler
        ): Observable<Event> = fragment.onFragmentResult(resultKey, bundler)
    }

    @CheckReturnValue
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        if (accountManager.hasUserAccount()) {
            object : HasCustomToolbar.CustomToolbar.Layout() {
                @CheckReturnValue
                override fun onCreateView(container: ViewGroup): View =
                    container.context.inflateBy(R.layout.application___toolbar__white, container)

                @CheckReturnValue
                override fun getAppBarLayout(view: View): AppBarLayout? = null

                @CheckReturnValue
                override fun getToolbar(view: View): Toolbar {
                    return view.findViewById(R.id.application___toolbar)
                }
            }
        } else {
            HasCustomToolbar.CustomToolbar.None
        }

    @CheckReturnValue
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = MainProfileMeOrganizatorAddEventBinding.inflate(inflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val layoutManager = FlexboxLayoutManager(requireContext())
        layoutManager.flexWrap = FlexWrap.WRAP
        layoutManager.flexDirection = FlexDirection.ROW
        layoutManager.justifyContent = JustifyContent.FLEX_START
        layoutManager.alignItems = AlignItems.FLEX_START

        binding.categoriesList.layoutManager = layoutManager
        binding.categoriesList.adapter = categoriesAdapter

        daysAdapter.attachDelegate(this)
        binding.days.adapter = daysAdapter

        binding.categoriesTitle.text =
            view.context.spannedString {
                it.append(resources.getText(R.string.main_profile_organization__profile))
                    .append(Characters.asterisk)
                    .spans { ForegroundColorSpan(view.context.getColor(R.color.application___color__accent)) }
            }

        binding.eventNameTitle.text =
            view.context.spannedString {
                it.append("Название Доброго Дела")
                    .append(Characters.asterisk)
                    .spans { ForegroundColorSpan(view.context.getColor(R.color.application___color__accent)) }
            }

        binding.descriptionTitle.text =
            view.context.spannedString {
                it.append("Описание Доброго Дела")
                    .append(Characters.asterisk)
                    .spans { ForegroundColorSpan(view.context.getColor(R.color.application___color__accent)) }
            }

        binding.mainProfileMeOrganizatorInfoLocationPlace.hint =
            view.context.spannedString {
                it.append("Место проведения")
                    .append(Characters.asterisk)
                    .spans { ForegroundColorSpan(view.context.getColor(R.color.application___color__accent)) }
            }

        binding.periodInput.hint =
            view.context.spannedString {
                it.append("Дата проведения")
                    .append(Characters.asterisk)
                    .spans { ForegroundColorSpan(view.context.getColor(R.color.application___color__accent)) }
            }

        binding.startInput.hint =
            view.context.spannedString {
                it.append("Начало")
                    .append(Characters.asterisk)
                    .spans { ForegroundColorSpan(view.context.getColor(R.color.application___color__accent)) }
            }

        binding.endInput.hint =
            view.context.spannedString {
                it.append("Окончание")
                    .append(Characters.asterisk)
                    .spans { ForegroundColorSpan(view.context.getColor(R.color.application___color__accent)) }
            }

        val totalSymbols = 200
        binding.description.addTextChangedListener {
            binding.descriptionInput.makeDefault(binding.description)
            if (it?.trim()?.isEmpty() == true) {
                binding.descriptionInput.helperText =
                    "Минимальное количество символов 200"
            } else if ((it?.count() ?: 0) < totalSymbols) {
                val left = (totalSymbols - (it?.count() ?: 0))
                binding.descriptionInput.helperText =
                    requireContext().resources.getQuantityString(
                        R.plurals.main_profile_organization__symbols_left,
                        left,
                        left
                    )
            } else {
                binding.descriptionInput.helperText = ""
            }
            state.sendWhen<AddEventState.Choice> {
                inputEventDescription(it.toString())
            }
        }

        binding.isOnline.setOnCheckedChangeListener { buttonView, isChecked ->
            state.sendWhen<AddEventState.Choice> {
                inputIsEventOnline(isChecked)
            }
        }

        binding.setTimeForEveryDay.setOnClickListener {
            if (binding.setTimeForEveryDay.text.toString() == "Указать время для каждого дня") {
                val list = mutableListOf<Day>()
                state.sendWhen<AddEventState.Choice> {
                    if (this.eventPeriod.first != null && this.eventPeriod.second != null) {
                        val days = ChronoUnit.DAYS.between(eventPeriod.first, eventPeriod.second)
                        val format = DateTimeFormatter.ofPattern("dd.MM.yyyy")
                        for (day in 0..days) {
                            list.add(
                                Day(
                                    "${eventPeriod.first?.plusDays(day)?.format(format)}",
                                    eventPeriod.first?.plusDays(day)?.toLocalDate(), null, null
                                )
                            )
                        }
                        inputEventDaysTime(list, list.take(MAX_DAYS_TO_SHOW))
                    } else {
                        inputEventDaysTime(listOf(), listOf())
                    }
                }
                binding.setTimeForEveryDay.text =
                    "Указать одинаковое для всех дней"
            } else {
                binding.setTimeForEveryDay.text =
                    "Указать время для каждого дня"
                state.sendWhen<AddEventState.Choice> {
                    inputEventDaysTime(listOf(), listOf())
                }
            }
        }

        val subtitleSpannedString = requireContext().spannedString {
            it
                .append("Принимаю все условия")
                .append(Characters.nonBreakingSpace)
                .append("Оферты")
                .spans {
                    object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            tryStartActivityBy {
                                it
                                    .view(Uri.parse("https://dobro.ru/offerorganizer/"))
                                    .newTask()
                            }
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            ds.isUnderlineText = false
                            ds.color =
                                requireContext().getSupportColor(R.color.application___color__primary)
                        }
                    }
                }
                .append(Characters.nonBreakingSpace)
                .append("без каких либо исключений и/или ограничений")
                .append(Characters.asterisk)
                .spans { ForegroundColorSpan(view.context.getColor(R.color.application___color__accent)) }
        }

        binding.isAcceptRules.apply {
            movementMethod = LinkMovementMethod.getInstance()
            text = subtitleSpannedString
        }

        binding.isAcceptRules.setOnCheckedChangeListener { buttonView, isChecked ->
            state.sendWhen<AddEventState.Choice> {
                inputIsConfirmed(isChecked)
            }
        }

        binding.eventName.addTextChangedListener {
            binding.eventNameInput.makeDefault(binding.eventName)
            state.sendWhen<AddEventState.Choice> {
                inputEventName(it.toString())
            }
        }

        binding.isPrivate.setOnCheckedChangeListener { buttonView, isChecked ->
            state.sendWhen<AddEventState.Choice> {
                inputIsEventPrivate(isChecked)
            }
        }

        setFragmentResultListener(requestCodeSelectAddress) { _, bundle ->
            val result = bundle.getParcelable<Location>(requestCodeSelectAddress)
            state.sendWhen<AddEventState.Choice> {
                inputEventPlace(result)
            }
        }

        binding.daysMore.setOnClickListener {
            currentIndex += MAX_DAYS_TO_SHOW
            state.sendWhen<AddEventState.Choice> {
                inputEventDaysTime(
                    this.eventDaysTime,
                    this.eventDaysTime.take(currentIndex + MAX_DAYS_TO_SHOW)
                )
            }
        }

        val event: Event? = arguments?.getParams(bundler)

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<AddEventState.Idle>()
                .subscribeToFlow {
                    state.sendWhen<AddEventState.Idle> {
                        requestDataLoading(
                            CategoriesSelectorRequest.Default(
                                selectedCategories = persistentSetOf(),
                                hasDriverLicence = false,
                                hasMedicalHistory = false,
                                requestKey = "Key"
                            )
                        )
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .handlePlainLoadingFlow<
                    AddEventState.DataLoading,
                    PersistentSet<Category>>(
                    scope = this,
                    toTaskState = { it.categoriesDataState },
                    task = {
                        dobroApi.overview().getCategories()
                    },
                    onError = {
                        logger.warning { "Failed to load categories." }
                    },
                    onUpdateState = { updateAddEventDataState(it, event) }
                )
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<AddEventState.Choice>()
                .distinctUntilChangedBy { choice -> choice.chosenCategories }
                .subscribeToFlow { state ->
                    categoriesAdapter.submitList(state.allCategories.map {
                        CategoriesAdapter.CategoryItem(
                            category = it,
                            isChecked = state.chosenCategories.contains(it)
                        )
                    }.toPersistentList())
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<AddEventState.Choice>()
                .distinctUntilChangedBy { choice -> choice.eventPlace }
                .subscribeToFlow { state ->
                    if (state.eventPlace == null) {
                        binding.setAddress.visible()
                        binding.address.gone()
                        binding.changeAddress.gone()
                    } else {
                        binding.address.text = if (!state.eventPlace.flat.isNullOrBlank()) {
                            getString(
                                R.string.address_with_office,
                                state.eventPlace.title,
                                state.eventPlace.flat
                            )
                        } else {
                            state.eventPlace.title
                        }
                        binding.address.visible()
                        binding.changeAddress.visible()
                        binding.setAddress.gone()
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<AddEventState.Choice>()
                .distinctUntilChangedBy { choice -> choice.isEventOnline }
                .subscribeToFlow { isOnline ->
                    binding.addressContainer.isGoneVisible = !isOnline.isEventOnline
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<AddEventState.Choice>()
                .distinctUntilChangedBy { choice -> choice.eventDaysTime }
                .subscribeToFlow { state ->
                    if (state.eventDaysTime.isNotEmpty()) {
                        binding.timeForEveryDayExpand.visible()
                        binding.days.visible()
                    } else {
                        binding.timeForEveryDayExpand.gone()
                        binding.days.gone()
                    }
                    // _daysAdapter.submitList(state.eventDaysTime)
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<AddEventState.Choice>()
                .distinctUntilChangedBy { choice -> choice.eventDaysTimeToShow }
                .subscribeToFlow { state ->
                    binding.daysMore.isGoneVisible =
                        state.eventDaysTime.size - state.eventDaysTimeToShow.size > 0
                    daysAdapter.submitList(state.eventDaysTimeToShow)
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<AddEventState.Choice>()
                .distinctUntilChangedBy { choice -> choice.eventPeriod }
                .subscribeToFlow { state ->
                    val a = state.eventPeriod.second?.dayOfYear
                    val b = state.eventPeriod.first?.dayOfYear
                    if ((a?.minus(b ?: 0) ?: 0) > 0) {
                        binding.setTimeForEveryDay.visible()
                    } else {
                        binding.setTimeForEveryDay.gone()
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<AddEventState.Choice>()
                .distinctUntilChangedBy { choice -> choice.eventImage }
                .subscribeToFlow { state ->
                    state.eventImage?.let {
                        binding.image.visible()
                        binding.addImage.gone()
                        binding.changeImage.visible()
                        images.load(state.eventImage.uri)
                            .into(binding.image)
                    } ?: also {
                        binding.image.gone()
                        binding.addImage.visible()
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<AddEventState.Choice>()
                .distinctUntilChangedBy { choice -> choice.isPrivate }
                .map { it.isPrivate }
                .subscribeToFlow {
                    binding.isPrivate.isChecked = it
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<AddEventState.Choice>()
                .filter { !initialSet }
                .distinctUntilChanged()
                .subscribeToFlow { state ->
                    val format = DateTimeFormatter.ofPattern("dd.MM.yyyy")
                    val formatter = DateTimeFormatter.ofPattern("HH:mm")

                    binding.eventName.setText(state.eventName)
                    binding.description.setHtmlText(state.eventDescription)
                    binding.isOnline.isChecked = state.isEventOnline
                    if (state.eventPeriod.first != null) {
                        binding.period.setText(
                            "${
                                state.eventPeriod.first?.format(
                                    format
                                )
                            } - ${state.eventPeriod.second?.format(format)}"
                        )
                    }
                    binding.start.setText(
                        state.eventStart?.format(
                            formatter
                        )
                    )
                    binding.end.setText(
                        state.eventEnd?.format(
                            formatter
                        )
                    )
                    binding.startInput.isEnabled = true
                    binding.endInput.isEnabled = true
                    initialSet = true
                }
        }

        binding.isPrivate.isGoneVisible =
            event?.organizer?.isVerified ?: (accountManager.getAccountType()
                .getOrganizationStatus() == OrganizationStatus.VALID)

        viewLifecycleOwner.repeatOnResume {
            categoriesAdapter
                .onItemClick
                .throttleUserInput()
                .subscribeToFlow {
                    state.sendWhen<AddEventState.Choice> {
                        if (it.isChecked) {
                            removeCategory(it.category)
                        } else {
                            addCategory(it.category)
                        }
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.setAddress
                .onThrottleClick {
                    findNavController().navigate(
                        AddEventFragmentDirections.actionCreateOrganizationFragmentToPickAddressFragment(
                            PickAddress(
                                observingResultCode = requestCodeSelectAddress,
                                defaultLocation = (state.value as? AddEventState.Choice)?.eventPlace,
                                requiredFields = listOf(Fields.Country, Fields.City)
                            )
                        )
                    )
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.changeAddress
                .onThrottleClick {
                    findNavController().navigate(
                        AddEventFragmentDirections.actionCreateOrganizationFragmentToPickAddressFragment(
                            PickAddress(
                                observingResultCode = requestCodeSelectAddress,
                                defaultLocation = (state.value as? AddEventState.Choice)?.eventPlace,
                                requiredFields = listOf(Fields.Country, Fields.City)
                            )
                        )
                    )
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.timeForEveryDayExpand
                .onThrottleClick {
                    if (binding.days.isVisible) {
                        binding.days.gone()
                        binding.daysMore.gone()
                        binding.timeForEveryDayExpand.setCompoundDrawablesWithIntrinsicBounds(
                            null, null,
                            ContextCompat.getDrawable(
                                requireContext(),
                                R.drawable.application___arrow_down
                            ), null
                        )
                    } else {
                        binding.days.visible()
                        (state.value as? AddEventState.Choice)?.let { state ->
                            binding.daysMore.isGoneVisible =
                                state.eventDaysTime.size - state.eventDaysTimeToShow.size > 0
                        }
                        binding.timeForEveryDayExpand.setCompoundDrawablesWithIntrinsicBounds(
                            null, null,
                            ContextCompat.getDrawable(
                                requireContext(),
                                R.drawable.application___arrow_up
                            ), null
                        )
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.period
                .onThrottleClick {
                    val builder = MaterialDatePicker.Builder.dateRangePicker()
                    val now = Calendar.getInstance()
                    builder.setSelection(
                        androidx.core.util.Pair(
                            now.timeInMillis,
                            now.timeInMillis
                        )
                    )
                    val constraintsBuilder = CalendarConstraints.Builder()
                    constraintsBuilder.setValidator(
                        DatePickerRangeValidator(
                            LocalDate.now().minusDays(1),
                            LocalDate.MAX
                        )
                    )
                    builder.setCalendarConstraints(constraintsBuilder.build())
                    val picker = builder.build()

                    picker.addOnNegativeButtonClickListener { picker.dismiss() }
                    picker.addOnPositiveButtonClickListener {
                        val format = DateTimeFormatter.ofPattern("dd.MM.yyyy")

                        val first = it.first?.run {
                            LocalDateTime.ofInstant(
                                Instant.ofEpochMilli(this),
                                ZoneOffset.UTC
                            )
                        }
                        val second = it.second?.run {
                            LocalDateTime.ofInstant(
                                Instant.ofEpochMilli(this),
                                ZoneOffset.UTC
                            )
                        }
                        binding.periodInput.makeDefault(binding.period)
                        state.sendWhen<AddEventState.Choice> {
                            inputEventPeriod(first to second)
                        }
                        state.sendWhen<AddEventState.Choice> {
                            inputEventDaysTime(listOf(), listOf())
                        }

                        binding.startInput.isEnabled = true
                        binding.endInput.isEnabled = true

                        binding.period.setText(
                            "${first?.format(format)} - ${
                                second?.format(
                                    format
                                )
                            }"
                        )
                    }

                    picker.show(requireActivity().supportFragmentManager, picker.toString())
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.addImage
                .onThrottleClick {
                    ImagePickerFragment.navigate(findNavController(), bundler)
                        .toGalleryPickerForResult(
                            requestCodeSelectImage,
                            R.id.action_addEventFragment_to_image_picker,
                            cropMode = CropMode.Other
                        )
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.start
                .onThrottleClick {
                    val state = state.value as AddEventState.Choice
                    val picker =
                        MaterialTimePicker.Builder()
                            .setTimeFormat(TimeFormat.CLOCK_24H)
                            .setHour(state.eventStart?.hour ?: 10)
                            .setMinute(state.eventStart?.minute ?: 0)
                            .setTitleText("Начало")
                            .build()

                    picker.addOnPositiveButtonClickListener {
                        val startSeconds = ((picker.hour * 60 + picker.minute) * 60).toLong()
                        val start = startSeconds.run {
                            LocalDateTime.of(1970, 1, 1, picker.hour, picker.minute)
                        }
                        val state = <EMAIL> as? AddEventState.Choice

                        val end = state?.eventEnd
                        val totalStart = (start?.hour ?: 0) * 60 + (start?.minute ?: 0)
                        val totalEnd = (end?.hour ?: 0) * 60 + (end?.minute ?: 0)
                        val totalSeconds = abs(totalEnd - totalStart)
                        end?.let {
                            if (totalSeconds <= 8 * 60 && (totalStart < totalEnd)) {
                                val formatter = DateTimeFormatter.ofPattern("HH:mm")
                                binding.start.setText(
                                    start.format(
                                        formatter
                                    )
                                )
                                binding.startInput.makeDefault(binding.start)
                                <EMAIL><AddEventState.Choice> {
                                    inputEventStart(
                                        LocalDateTime.of(
                                            this.eventPeriod.first?.toLocalDate(),
                                            start.toLocalTime()
                                        )
                                    )
                                }
                            } else {
                                Toast.makeText(
                                    requireActivity(),
                                    "Максимальная длительность одного дня доброго дела – 8 часов",
                                    Toast.LENGTH_LONG
                                ).show()
                            }
                        } ?: also {
                            val formatter = DateTimeFormatter.ofPattern("HH:mm")
                            binding.start.setText(
                                start.format(
                                    formatter
                                )
                            )
                            binding.startInput.makeDefault(binding.start)
                            <EMAIL><AddEventState.Choice> {
                                inputEventStart(
                                    LocalDateTime.of(
                                        this.eventPeriod.first?.toLocalDate(),
                                        start.toLocalTime()
                                    )
                                )
                            }
                        }
                    }
                    picker.addOnNegativeButtonClickListener {
                        picker.dismiss()
                    }
                    picker.addOnCancelListener {
                        picker.dismiss()
                    }
                    picker.addOnDismissListener {
                        picker.dismiss()
                    }
                    picker.show(requireActivity().supportFragmentManager, "begin")
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.end
                .onThrottleClick {
                    val state = state.value as AddEventState.Choice
                    val picker =
                        MaterialTimePicker.Builder()
                            .setTimeFormat(TimeFormat.CLOCK_24H)
                            .setHour(state.eventEnd?.hour ?: 18)
                            .setMinute(state.eventEnd?.minute ?: 0)
                            .setTitleText("Окончание")
                            .build()

                    picker.addOnPositiveButtonClickListener {
                        val endSeconds = ((picker.hour * 60 + picker.minute) * 60).toLong()
                        val end = endSeconds.run {
                            LocalDateTime.of(1970, 1, 1, picker.hour, picker.minute)
                        }
                        val state = <EMAIL> as? AddEventState.Choice

                        val start = state?.eventStart
                        val totalStart = (start?.hour ?: 0) * 60 + (start?.minute ?: 0)
                        val totalEnd = (end?.hour ?: 0) * 60 + (end?.minute ?: 0)
                        val totalSeconds = abs(totalEnd - totalStart)
                        start?.let {
                            if (totalSeconds <= 8 * 60 && (totalStart < totalEnd)) {
                                val formatter = DateTimeFormatter.ofPattern("HH:mm")
                                binding.end.setText(
                                    end.format(
                                        formatter
                                    )
                                )
                                binding.endInput.makeDefault(binding.end)
                                <EMAIL><AddEventState.Choice> {
                                    inputEventEnd(
                                        LocalDateTime.of(
                                            this.eventPeriod.second?.toLocalDate(),
                                            end.toLocalTime()
                                        )
                                    )
                                }
                            } else {
                                Toast.makeText(
                                    requireActivity(),
                                    "Максимальная длительность одного дня доброго дела – 8 часов",
                                    Toast.LENGTH_LONG
                                ).show()
                            }
                        } ?: also {
                            val formatter = DateTimeFormatter.ofPattern("HH:mm")
                            binding.end.setText(end.format(formatter))
                            binding.endInput.makeDefault(binding.end)
                            <EMAIL><AddEventState.Choice> {
                                inputEventEnd(
                                    LocalDateTime.of(
                                        this.eventPeriod.second?.toLocalDate(),
                                        end.toLocalTime()
                                    )
                                )
                            }
                        }
                    }
                    picker.addOnNegativeButtonClickListener {
                        picker.dismiss()
                    }
                    picker.addOnCancelListener {
                        picker.dismiss()
                    }
                    picker.addOnDismissListener {
                        picker.dismiss()
                    }
                    picker.show(requireActivity().supportFragmentManager, "begin")
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.save
                .onThrottleClick {
                    (state.value as? AddEventState.Choice)?.let { state ->
                        if (checkChosenCategories(state.chosenCategories)) {
                            if (checkEventName(state.eventName)) {
                                if (checkEventDescription(state.eventDescription)) {
                                    if (checkIsOnline(state.isEventOnline, state.eventPlace)) {
                                        if (checkPeriod(state.eventPeriod)) {
                                            if (checkStart(state.eventStart)) {
                                                if (checkEnd(state.eventEnd)) {
                                                    if (checkExpandedDays(state.eventDaysTime)) {
                                                        if (checkRules(state.isConfirmed)) {
                                                            createEvent(event)
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
        }

        parentFragmentManager.setFragmentResultListener(
            requestCodeSelectImage,
            viewLifecycleOwner
        ) { _, bundle ->
            bundler.fromBundle<ImagePickerResult>(bundle)?.let { result ->
                state.sendWhen<AddEventState.Choice> {
                    inputEventImage(result.file)
                }
            }
        }
    }

    private fun checkChosenCategories(categories: PersistentSet<Category>): Boolean {
        return if (categories.isEmpty()) {
            messageDisplay.showMessage("Укажите хотя бы одну категорию".r)
            binding.scroll.smoothScrollTo(0, binding.root.top)
            false
        } else {
            true
        }
    }

    private fun checkEventName(eventName: String): Boolean {
        return if (eventName.isBlank()) {
            binding.eventNameInput.makeAccent(binding.eventName, "Укажите название", binding.scroll)
            false
        } else {
            binding.eventNameInput.makeDefault(binding.eventName)
            true
        }
    }

    private fun checkEventDescription(description: String): Boolean {
        return if (description.isBlank()) {
            binding.descriptionInput.makeAccent(
                binding.description,
                "Укажите описание",
                binding.scroll
            )
            false
        } else if (description.count() < 200) {
            binding.descriptionInput.makeAccent(
                binding.description,
                "Описание должно быть не менее 200 символов",
                binding.scroll
            )
            false
        } else if (description.count() > 3000) {
            binding.descriptionInput.makeAccent(
                binding.description,
                "Описание должно быть не более 3000 символов",
                binding.scroll
            )
            false
        } else {
            true
        }
    }

    private fun checkIsOnline(isOnline: Boolean, eventPlace: Location?): Boolean {
        return if (!isOnline) {
            if (eventPlace?.title == null) {
                messageDisplay.showMessage("Укажите место проведения".r)
                binding.scroll.smoothScrollTo(0, binding.isOnline.top)
                false
            } else {
                true
            }
        } else {
            true
        }
    }

    private fun checkPeriod(eventPeriod: Pair<LocalDateTime?, LocalDateTime?>): Boolean {
        return if (eventPeriod.first == null || eventPeriod.second == null) {
            binding.periodInput.makeAccent(
                binding.period,
                "Укажите дату проведения",
                binding.scroll
            )
            false
        } else {
            binding.periodInput.makeDefault(binding.period)
            true
        }
    }

    private fun checkStart(eventStart: LocalDateTime?): Boolean {
        return if ((eventStart?.hour ?: (0 + (eventStart?.minute ?: 0))) <= 0) {
            binding.startInput.makeAccent(binding.start, "Укажите начало", binding.scroll)
            false
        } else {
            binding.startInput.makeDefault(binding.start)
            true
        }
    }

    private fun checkEnd(eventEnd: LocalDateTime?): Boolean {
        return if ((eventEnd?.hour ?: (0 + (eventEnd?.minute ?: 0))) <= 0) {
            binding.endInput.makeAccent(binding.end, "Укажите окончание", binding.scroll)
            false
        } else {
            binding.endInput.makeDefault(binding.end)
            true
        }
    }

    private fun checkExpandedDays(days: List<Day>): Boolean {
        return if (days.isNotEmpty()) {
            var isValidDays = true
            days.forEach {
                isValidDays = isValidDays && ((it.start?.hour ?: (0 + (it.start?.minute
                    ?: 0))) > 0
                    && (it.end?.hour ?: (0 + (it.end?.minute ?: 0))) > 0)
            }
            if (!isValidDays) {
                binding.scroll.smoothScrollTo(0, binding.timeForEveryDayExpand.top.minus(300))
                messageDisplay.showMessage("Укажите правильный диапазон времени".r)
            }
            isValidDays
        } else {
            true
        }
    }

    private fun checkRules(isAcceptRules: Boolean): Boolean {
        return if (!isAcceptRules) {
            messageDisplay.showMessage("Примите условия оферты".r)
            binding.scroll.fullScroll(ScrollView.FOCUS_DOWN)
            false
        } else {
            true
        }
    }

    private fun CoroutineScope.createEvent(event: Event?) {
        val state = state.value as AddEventState.Choice

        val request = VacancyApi.CreateEventRequestBody(
            status = 100,
            name = state.eventName,
            description = state.eventDescription,
            categories = state.chosenCategories.map { it.identity }.toPersistentSet(),
            eventPeriod = VacancyApi.CreateEventRequestBody.EventPeriod(
                ZonedDateTime.of(state.eventStart, ZoneId.systemDefault()),
                ZonedDateTime.of(state.eventEnd, ZoneId.systemDefault())
            ),
            eventPeriodPerDay = state.eventDaysTime.map { day ->
                val start = LocalDateTime.of(day.day, day.start?.toLocalTime())
                val end = LocalDateTime.of(day.day, day.end?.toLocalTime())
                VacancyApi.CreateEventRequestBody.EventPeriod(
                    start.atZone(ZoneOffset.UTC), end.atZone(ZoneOffset.UTC)
                )
            }.toPersistentList(),
            imageFile = state.eventImage?.identity,
            organizer = if (event != null) {
                null
            } else {
                accountManager.getAccountType()
                    .getOrganizationId()
            },
            location = if (!state.isEventOnline) {
                VacancyApi.CreateEventRequestBody.Settlement(
                    title = state.eventPlace?.title,
                    country = state.eventPlace?.countryISO,
                    municipality = state.eventPlace?.municipality,
                    municipalityCode = state.eventPlace?.municipalityCode,
                    settlement = state.eventPlace?.cityAlternative,
                    flat = state.eventPlace?.flat,
                    house = state.eventPlace?.house,
                    street = state.eventPlace?.street,
                    region = state.eventPlace?.region?.take(2),
                    settlementCode = state.eventPlace?.region?.take(2),
                    x = state.eventPlace?.coordinates?.longitude,
                    y = state.eventPlace?.coordinates?.latitude
                )
            } else {
                VacancyApi.CreateEventRequestBody.Settlement()
            },
            isPrivate = state.isPrivate,
            online = state.isEventOnline,
            photos = persistentSetOf()
        )

        event?.let { event ->
            safeLaunch(
                onError = {
                    Toast.makeText(
                        requireContext(),
                        "Не удалось изменить Доброе дело",
                        Toast.LENGTH_LONG
                    ).show()
                }
            ) {
                dobroApi.vacancy().editEventRequest(event.identity, request)

                Toast.makeText(
                    requireContext(),
                    "Доброе дело изменено",
                    Toast.LENGTH_LONG
                ).show()
                findNavController().navigateUp()
            }
        } ?: also {
            safeLaunch(
                onError = {
                    Toast.makeText(
                        requireContext(),
                        "Не удалось создать Доброе дело",
                        Toast.LENGTH_LONG
                    ).show()
                }
            ) {
                val result = dobroApi.vacancy().createEventRequest(request)

                EventFragment.navigate(findNavController(), bundler)
                    .toEvent(R.id.main_addEventFragment_to_main_profile_me, result.id)
            }
        }
    }

    override fun setStartTime(position: Int, currentHours: Int, currentMinutes: Int) {
        val picker =
            MaterialTimePicker.Builder()
                .setTimeFormat(TimeFormat.CLOCK_24H)
                .setHour(currentHours)
                .setMinute(currentMinutes)
                .setTitleText("Начало")
                .build()

        picker.addOnPositiveButtonClickListener {
            val startSeconds = ((picker.hour * 60 + picker.minute) * 60).toLong()
            val start = startSeconds.run {
                LocalDateTime.of(1970, 1, 1, picker.hour, picker.minute)
            }
            val state = state.value as? AddEventState.Choice

            val end = state?.eventDaysTime?.get(position)?.end
            val totalStart = (start?.hour ?: 0) * 60 + (start?.minute ?: 0)
            val totalEnd = (end?.hour ?: 0) * 60 + (end?.minute ?: 0)
            val totalSeconds = totalEnd - totalStart
            end?.let {
                if (totalSeconds <= 8 * 60 && (totalStart < totalEnd)) {
                    this.state.sendWhen<AddEventState.Choice> {
                        inputEventDaysTime(this.eventDaysTime.toMutableList().apply {
                            this[position] = Day(
                                this[position].title,
                                this[position].day,
                                start,
                                this[position].end
                            )
                        }, this.eventDaysTimeToShow.toMutableList().apply {
                            this[position] = Day(
                                this[position].title,
                                this[position].day,
                                start,
                                this[position].end
                            )
                        })
                    }
                } else {
                    Toast.makeText(
                        requireActivity(),
                        "Максимальная длительность одного дня доброго дела – 8 часов",
                        Toast.LENGTH_LONG
                    ).show()
                    this.state.sendWhen<AddEventState.Choice> {
                        inputEventDaysTime(this.eventDaysTime, this.eventDaysTimeToShow)
                    }
                }
            } ?: also {
                this.state.sendWhen<AddEventState.Choice> {
                    inputEventDaysTime(this.eventDaysTime.toMutableList().apply {
                        this[position] =
                            Day(this[position].title, this[position].day, start, this[position].end)
                    }, this.eventDaysTimeToShow.toMutableList().apply {
                        this[position] =
                            Day(this[position].title, this[position].day, start, this[position].end)
                    })
                }
            }
        }
        picker.addOnNegativeButtonClickListener {
            picker.dismiss()
        }
        picker.addOnCancelListener {
            picker.dismiss()
        }
        picker.addOnDismissListener {
            picker.dismiss()
        }
        picker.show(requireActivity().supportFragmentManager, "begin")
    }

    override fun setEndTime(position: Int, currentHours: Int, currentMinutes: Int) {
        val picker =
            MaterialTimePicker.Builder()
                .setTimeFormat(TimeFormat.CLOCK_24H)
                .setHour(currentHours)
                .setMinute(currentMinutes)
                .setTitleText("Окончание")
                .build()

        picker.addOnPositiveButtonClickListener {
            val endSeconds = ((picker.hour * 60 + picker.minute) * 60).toLong()
            val end = endSeconds.run {
                LocalDateTime.of(1970, 1, 1, picker.hour, picker.minute)
            }
            val state = state.value as? AddEventState.Choice

            val start = state?.eventDaysTime?.get(position)?.start
            val totalStart = (start?.hour ?: 0) * 60 + (start?.minute ?: 0)
            val totalEnd = (end?.hour ?: 0) * 60 + (end?.minute ?: 0)
            val totalSeconds = totalEnd - totalStart
            start?.let {
                if (totalSeconds <= 8 * 60 && (totalStart < totalEnd)) {
                    this.state.sendWhen<AddEventState.Choice> {
                        inputEventDaysTime(this.eventDaysTime.toMutableList().apply {
                            this[position] = Day(
                                this[position].title,
                                this[position].day,
                                this[position].start,
                                end
                            )
                        }, this.eventDaysTimeToShow.toMutableList().apply {
                            this[position] = Day(
                                this[position].title,
                                this[position].day,
                                this[position].start,
                                end
                            )
                        })
                    }
                } else {
                    Toast.makeText(
                        requireActivity(),
                        "Максимальная длительность одного дня доброго дела – 8 часов",
                        Toast.LENGTH_LONG
                    ).show()
                    this.state.sendWhen<AddEventState.Choice> {
                        inputEventDaysTime(this.eventDaysTime, this.eventDaysTimeToShow)
                    }
                }
            } ?: also {
                this.state.sendWhen<AddEventState.Choice> {
                    inputEventDaysTime(this.eventDaysTime.toMutableList().apply {
                        this[position] =
                            Day(this[position].title, this[position].day, this[position].start, end)
                    }, this.eventDaysTimeToShow.toMutableList().apply {
                        this[position] =
                            Day(this[position].title, this[position].day, this[position].start, end)
                    })
                }
            }
        }
        picker.addOnNegativeButtonClickListener {
            picker.dismiss()
        }
        picker.addOnCancelListener {
            picker.dismiss()
        }
        picker.addOnDismissListener {
            picker.dismiss()
        }
        picker.show(requireActivity().supportFragmentManager, "begin")
    }
}
