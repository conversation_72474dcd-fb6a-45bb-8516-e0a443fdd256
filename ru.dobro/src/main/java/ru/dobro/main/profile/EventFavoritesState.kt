package ru.dobro.main.profile

import common.library.android.string.RString
import common.library.core.state.load.LoadDataPlainState
import kotlinx.collections.immutable.PersistentList
import ru.dobro.core.error.ErrorMessages
import ru.dobro.domain.Event
import ru.dobro.domain.EventId
import javax.annotation.CheckReturnValue

sealed class EventFavoritesState {

    data object Idle : EventFavoritesState() {
        @CheckReturnValue
        fun loadData(): EventFavoritesState {
            return Processing(
                favoritesDataState = EventFavoritesDataStateIdle
                    .empty<PersistentList<Event>>()
                    .load()
            )
        }
    }

    data class Processing(
        val favoritesDataState: EventFavoritesDataState
    ) : EventFavoritesState() {
        @CheckReturnValue
        inline fun updateFavoritesDataState(
            crossinline action: EventFavoritesDataState.() -> EventFavoritesDataState
        ): EventFavoritesState = copy(favoritesDataState = favoritesDataState.action())

        @CheckReturnValue
        fun refresh(): Processing = copy(
            favoritesDataState = when (favoritesDataState) {
                is LoadDataPlainState.Idle.Empty -> favoritesDataState.load()
                is LoadDataPlainState.Idle.Loaded -> favoritesDataState.refresh()
                is LoadDataPlainState.Idle.Failed -> favoritesDataState.retry()
                else -> favoritesDataState
            }
        )

        @CheckReturnValue
        fun handleAnyFailed(): Processing = copy(
            favoritesDataState = when (favoritesDataState) {
                is LoadDataPlainState.Idle.Failed -> favoritesDataState.handle()
                else -> favoritesDataState
            }
        )

        @CheckReturnValue
        fun removeEventFromFavorites(eventId: EventId): EventFavoritesState =
            RemoveFavoriteProcessing(
                processing = this,
                taskState = AddOrRemoveEventFavoriteDataStateIdle
                    .awaiting<AddOrRemoveEventFavoriteRequest>()
                    .execute(
                        AddOrRemoveEventFavoriteRequest(
                            eventId = eventId,
                            favoriteMode = FavoriteMode.Remove
                        )
                    )
            )
    }

    data class RemoveFavoriteProcessing(
        private val processing: Processing,
        val taskState: AddOrRemoveEventFavoriteDataState
    ) : EventFavoritesState() {
        @CheckReturnValue
        fun updateTaskState(action: AddOrRemoveEventFavoriteDataState.() -> AddOrRemoveEventFavoriteDataState): EventFavoritesState =
            when (val state: AddOrRemoveEventFavoriteDataState = taskState.action()) {
                is AddOrRemoveEventFavoriteDataStateIdleCompletedSuccess -> Completed.Success(
                    processing = processing
                )

                is AddOrRemoveEventFavoriteDataStateIdleCompletedFail -> Completed.Failed(
                    processing = processing,
                    error = ErrorMessages.ofException(state.error)
                )

                else -> copy(taskState = state)
            }
    }

    sealed class Completed : EventFavoritesState() {
        data class Success(
            private val processing: Processing
        ) : Completed() {
            @CheckReturnValue
            fun backToProcessing(): Processing = processing.refresh()
        }

        data class Failed(
            private val processing: Processing,
            val error: RString?
        ) : Completed() {
            @CheckReturnValue
            fun backToProcessing(): Processing = processing
        }
    }
}
