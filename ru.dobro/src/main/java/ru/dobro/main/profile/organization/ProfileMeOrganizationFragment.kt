package ru.dobro.main.profile.organization

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.text.style.ForegroundColorSpan
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.Menu
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.Toolbar
import androidx.core.os.bundleOf
import androidx.core.view.children
import androidx.core.view.isGone
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import arrow.core.Some
import arrow.core.toOption
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.google.android.flexbox.AlignItems
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.tbuonomo.viewpagerdotsindicator.setPaddingHorizontal
import common.library.android.clipboardManager
import common.library.android.coroutines.repeatOnResume
import common.library.android.coroutines.subscribeToFlow
import common.library.android.coroutines.throttleErrorMessages
import common.library.android.coroutines.throttleUserInput
import common.library.android.coroutines.throttleViewUpdates
import common.library.android.coroutines.withLatestFrom
import common.library.android.dimension.dp
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.intoBundle
import common.library.android.intent.newTask
import common.library.android.intent.startActivityBy
import common.library.android.intent.tryStartActivityBy
import common.library.android.intent.view
import common.library.android.message.MessageDisplay
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.string.RString
import common.library.android.string.r
import common.library.android.string.span.spannedString
import common.library.android.widget.gone
import common.library.android.widget.invisible
import common.library.android.widget.isGoneVisible
import common.library.android.widget.isVisible
import common.library.android.widget.onClickAsFlow
import common.library.android.widget.onRefreshAsFlow
import common.library.android.widget.onThrottleClick
import common.library.android.widget.recycler_view.addSpacingItemDecoration
import common.library.android.widget.setMargins
import common.library.android.widget.visible
import common.library.android.widget.wrapIntoSwipeRefresh
import common.library.core.Characters
import common.library.core.collection.mapPersistent
import common.library.core.contract.unreachable
import common.library.core.email.formatDefault
import common.library.core.formatFullEastern
import common.library.core.isNullOrEmpty
import common.library.core.lazyGet
import common.library.core.orFalse
import common.library.core.phone_number.formatE164
import common.library.core.state.load.LoadDataPlainState
import common.library.core.string.nullIfBlank
import common.library.core.variable.ScreenState
import kotlinx.collections.immutable.mutate
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentSetOf
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.merge
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import org.kodein.di.KodeinAware
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.App.Companion.nonExpandedSocialsCount
import ru.dobro.R
import ru.dobro.api.DobroApi
import ru.dobro.api.OrganizationDashboard
import ru.dobro.authorization.AuthorizationFragment
import ru.dobro.categories_selector.CategoriesSelectorFragment
import ru.dobro.common.categories.CategoriesAdapter
import ru.dobro.common.socials.SocialsAdapter
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.RequestCodes
import ru.dobro.core.account.AccountManager
import ru.dobro.core.account.AccountType
import ru.dobro.core.account.OrganizationStatus
import ru.dobro.core.account.getOrganizationId
import ru.dobro.core.account.getOrganizationStatus
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.error.ErrorHandler
import ru.dobro.core.error.ErrorMessages
import ru.dobro.core.format.DisplayFormat
import ru.dobro.core.isShimmerAnimated
import ru.dobro.core.setTextWithHtml
import ru.dobro.core.settings.Settings
import ru.dobro.databinding.MainProfileMeOrganizatorBinding
import ru.dobro.domain.Category
import ru.dobro.domain.CategoryId
import ru.dobro.domain.EventId
import ru.dobro.domain.OrganizationId
import ru.dobro.domain.OrganizerId
import ru.dobro.domain.SocialType
import ru.dobro.domain.UserId
import ru.dobro.domain.VacancyId
import ru.dobro.domain.organization.Organization
import ru.dobro.domain.organization.OrganizationStatusClass
import ru.dobro.domain.profile.User
import ru.dobro.domain.profile.achievements.trophy.OrganizerTrophy
import ru.dobro.images
import ru.dobro.main.MainActivity
import ru.dobro.main.event.EventFragment
import ru.dobro.main.events.EventsFragment
import ru.dobro.main.organization.DocumentsAdapter
import ru.dobro.main.organization.EventsAdapter
import ru.dobro.main.organization.ReviewAdapter
import ru.dobro.main.organization.ReviewAdapterDelegate
import ru.dobro.main.profile.ProfileMeDataStateIdleLoaded
import ru.dobro.main.profile.ProfileMeDataStateReloading
import ru.dobro.main.profile.ProfileMeFragment
import ru.dobro.main.profile.ProfileMeOrganizationData
import ru.dobro.main.profile.ProfileMeOrganizationDataStateIdleLoaded
import ru.dobro.main.profile.ProfileMeOrganizationDataStateReloading
import ru.dobro.main.profile.achievements.adapter.CertificateAdapter
import ru.dobro.main.profile.achievements.adapter.EduCertificateAdapter
import ru.dobro.main.profile.achievements.adapter.TrophiesAdapter
import ru.dobro.main.profile.achievements.info.AchievementInfoFragment
import ru.dobro.main.profile.editOrganization.EditOrganizationRequest
import ru.dobro.main.profile.organization.ProfileMeOrganizationState.AuthLess
import ru.dobro.main.profile.organization.ProfileMeOrganizationState.Displaying
import ru.dobro.main.profile.organization.ProfileMeOrganizationState.Idle
import ru.dobro.main.search.SearchFragment
import ru.dobro.main.vacancy.VacancyFragment
import ru.dobro.media_viewer.MediaViewerListFragment
import javax.annotation.CheckReturnValue

class ProfileMeOrganizationFragment : BaseFragment({
    bind<ScreenState<ProfileMeOrganizationState>>() with singleton { ScreenState(Idle) }
    bind<EventsAdapter>() with singleton { EventsAdapter(instance()) }
    bind<SocialsAdapter>() with singleton { SocialsAdapter(instance()) }
}), HasCustomToolbar {

    private val _state: ScreenState<ProfileMeOrganizationState> by instance()

    private val _messageDisplay: MessageDisplay by instance()

    private val _errorHandler: ErrorHandler by instance()

    private val _dobroApi: DobroApi by instance()

    private val _displayFormat: DisplayFormat by instance()

    private val _accountManager: AccountManager by instance()

    private val _settings: Settings by instance()

    private val _bundler: Bundler by instance()

    private val _socialsAdapter: SocialsAdapter by instance()

    private val _eventsAdapter: EventsAdapter by instance()

    private val _categoriesAdapter: CategoriesAdapter by lazyGet { CategoriesAdapter() }

    private val _trophiesAdapter: TrophiesAdapter by lazyGet { TrophiesAdapter() }

    private val _documentsAdapter: DocumentsAdapter by lazyGet { DocumentsAdapter() }

    private var _reviewAdapter: ReviewAdapter? = null

    private val _eduCertificateAdapter: EduCertificateAdapter by lazyGet { EduCertificateAdapter() }

    private val _certificateAdapter: CertificateAdapter by lazyGet { CertificateAdapter() }

    private val _onEditClick: MutableSharedFlow<ClickType> = MutableSharedFlow()

    private var wasMenuAdded = false

    private val _requestCodeSelectCategories: String =
        RequestCodes.scopedBy<ProfileMeOrganizationFragment>("select_categories")

    private val _requestCodeTrophiesList: String = "trophiesList"
    private val _requestCodeCertificateList: String = "certificateList"
    private val _requestCodeEduCertificateList: String = "eduCertificateList"

    val accounts = mutableSetOf<ProfileMeFragment.AccountType>()
    private var status: OrganizationStatus? = null

    private var typeDraft: TypeDraft = TypeDraft.Default

    private val _requestCodeAuthorization: String =
        RequestCodes.scopedBy<AuthorizationFragment>("authorization")

    private lateinit var binding: MainProfileMeOrganizatorBinding

    private lateinit var _swipeRefreshLayout: SwipeRefreshLayout

    private lateinit var popupMenu: android.widget.PopupMenu

    // TODO Хранить в бандле не получилось. Неплохо было бы переделать
    object OrganizationStatusHelpRequestData {
        var organizationStatusHelpRequest: OrganizationStatusHelpRequest =
            OrganizationStatusHelpRequest(
                shouldHelpScreenShow = true,
                shouldClickOnEdit = false,
                shouldOpenVolunteerProfile = false,
                shouldOpenDeeplinkFromVolunteerProfile = false
            )
    }

    override val screenName: String
        get() = AnalyticsConstants.Screen.Main.profileMe

    init {
        setHasOptionsMenu(true)
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        when (_accountManager.getAccountType()) {
            is AccountType.PhysicOrganization,
            is AccountType.Physic -> {
                findNavController().navigate(R.id.action_profileMeOrganizationFragment_to_main__profile_me)
            }

            else -> {
                // Do nothing
            }
        }
    }

    @CheckReturnValue
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        HasCustomToolbar.CustomToolbar.None

    @CheckReturnValue
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = MainProfileMeOrganizatorBinding.inflate(inflater)
        _swipeRefreshLayout = binding.root.wrapIntoSwipeRefresh()
        return _swipeRefreshLayout
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.mainProfileMeOrganizatorContactsSocials.adapter = _socialsAdapter

        val layoutManager = FlexboxLayoutManager(requireContext())
        layoutManager.flexWrap = FlexWrap.WRAP
        layoutManager.flexDirection = FlexDirection.ROW
        layoutManager.justifyContent = JustifyContent.FLEX_START
        layoutManager.alignItems = AlignItems.FLEX_START

        binding.mainOrganizationEventsList.adapter = _eventsAdapter
        binding.mainOrganizationEventsList.addSpacingItemDecoration {
            it.horizontalSpacing(4.dp)
        }
        popupMenu = android.widget.PopupMenu(
            binding.root.context,
            binding.toolbar.mainProfileMeChangeProfile
        )
        popupMenu.inflate(R.menu.main__profile_me_accounts)

        binding.mainProfileMeOrganizatorCategoriesList.layoutManager = layoutManager
        binding.mainProfileMeOrganizatorCategoriesList.adapter = _categoriesAdapter

        binding.mainProfileMeOrganizatorDocumentsList.adapter = _documentsAdapter

        _reviewAdapter = ReviewAdapter(object : ReviewAdapterDelegate {
            override fun onShowEventClicked(eventId: EventId) {
                VacancyFragment.navigate(findNavController(), _bundler)
                    .toVacancy(
                        R.id.action_profileMeOrganizationFragment__to_main__vacancy,
                        VacancyId.restore(eventId.unwrap())
                    )
            }

            override fun onPersonClicked(userId: UserId) {
                findNavController().navigate(
                    R.id.action__to__public_profile,
                    userId.intoBundle(_bundler)
                )
            }
        })
        binding.mainProfileMeOrganizatorReviewList.adapter = _reviewAdapter

        binding.mainProfileMeOrganizatorReviewList.addSpacingItemDecoration {
            it.verticalSpacing(4.dp)
        }

        binding.mainProfileMeOrganizatorAboutTrophiesList.adapter = _trophiesAdapter
        binding.mainProfileMeOrganizatorAboutCertificatesList.adapter = _certificateAdapter
        binding.mainProfileMeOrganizatorAboutEduCertificatesList.adapter = _eduCertificateAdapter

        binding.mainProfileMeOrganizatorAboutEduCertificatesList.addItemDecoration(
            DividerItemDecoration(requireContext(), LinearLayoutManager.VERTICAL)
        )

        binding.skeleton.mainProfileMeOrganizatorSkeletonShimmer.isGoneVisible =
            _accountManager.hasUserAccount()
        binding.skeleton.mainProfileMeOrganizatorSkeletonShimmer.isShimmerAnimated =
            _accountManager.hasUserAccount()

        viewLifecycleOwner.repeatOnResume {
            _documentsAdapter
                .onItemClick
                .throttleUserInput()
                .subscribeToFlow { link ->
                    tryStartActivityBy {
                        it
                            .view(link)
                            .newTask()
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _state
                .observeWhen<Idle>()
                .subscribeToFlow {
                    _state.sendWhen<Idle> {
                        loadData(_accountManager)
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _state
                .observeWhen<Displaying>()
                .take(1)
                .onEach {
                    delay(2_000L)
                }
                .subscribeToFlow {
                    val isAfterEditing = arguments?.getBoolean("is_after_editing").orFalse()
                    if (isAfterEditing) {
                        _state.sendWhen<Displaying> { refreshAll() }
                        arguments?.putBoolean("is_after_editing", false)
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _state
                .observeWhen<Displaying>()
                .distinctUntilChangedBy { displaying -> displaying.isEditable }
                .map { it.isEditable }
                .subscribeToFlow {
                    binding.toolbar.mainProfileMeEdit.isVisible = it
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _state
                .observeWhen<Displaying>()
                .distinctUntilChangedBy { displaying -> displaying.profileMeDataState }
                .map { it.profileMeDataState }
                .filterIsInstance<ProfileMeDataStateIdleLoaded>()
                .map { it.data }
                .subscribeToFlow {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        popupMenu.setForceShowIcon(true)
                    }
                    popupMenu.handleMenuItemClick()
                    if (it.userMenuItem?.size in setOf(0, 1, null)) {
                        binding.toolbar.mainProfileMeChangeProfile.invisible()
                        binding.toolbar.mainProfileMeChangeProfile.isClickable = false
                        binding.toolbar.mainProfileMeBecomeOrganizer.visible()
                        binding.toolbar.mainProfileMeBecomeOrganizer.isClickable = true
                        binding.toolbar.mainProfileMeLogOut.visible()
                    } else {
                        binding.toolbar.mainProfileMeChangeProfile.visible()
                        binding.toolbar.mainProfileMeChangeProfile.isClickable = true
                        binding.toolbar.mainProfileMeBecomeOrganizer.invisible()
                        binding.toolbar.mainProfileMeBecomeOrganizer.isClickable = false
                        binding.toolbar.mainProfileMeLogOut.gone()
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.toolbar.mainProfileMeChangeProfile
                .onThrottleClick {
                    metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.profileOrganizerChangeProfileClick)
                    firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Main.profileOrganizerChangeProfileClick)

                    popupMenu.show()
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.toolbar.mainProfileMeEdit
                .onThrottleClick {
                    launch {
                        _onEditClick.emit(ClickType.Edit)
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.toolbar.mainProfileMeLogOut
                .onThrottleClick {
                    metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.profileMeOnLogoutClick)
                    firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Main.profileMeOnLogoutClick)

                    _logoutDialog().show()
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.toolbar.mainProfileMeBecomeOrganizer
                .onThrottleClick {
                    findNavController().navigate(
                        R.id.action_main__profile_me_to_becomeOrganizerFragment
                    )
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.mainOrganizationEventsAll
                .onClickAsFlow
                .withLatestFrom(
                    _state
                        .observeWhen<Displaying>()
                )
                .map { it.second }
                .filter {
                    it.profileMeOrganizationDataState is ProfileMeOrganizationDataStateIdleLoaded ||
                        it.profileMeOrganizationDataState is ProfileMeOrganizationDataStateReloading
                }
                .map {
                    when (val state = it.profileMeOrganizationDataState) {
                        is ProfileMeOrganizationDataStateIdleLoaded -> state.data
                        is ProfileMeOrganizationDataStateReloading -> state.previousData
                        else -> unreachable()
                    }
                }
                .subscribeToFlow { organization ->
                    accounts.firstOrNull { it is ProfileMeFragment.AccountType.PhysicOrganization }
                        ?.let { accountType ->
                            val account =
                                accountType as ProfileMeFragment.AccountType.PhysicOrganization
                            firebaseAnalyticManager.sendEventWithId(
                                AnalyticsConstants.Event.Main.organizationOnAllEventsClick,
                                OrganizerId.restore(account.organizationId)
                            )

                            EventsFragment
                                .navigate(findNavController(), _bundler)
                                .toFullOrganizationEvents(
                                    actionId = R.id.action_profileMeOrganizationFragment_to__main__events,
                                    organizerId = organization.data.organizer.identity
                                )
                        }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.authorization.authorizationStubPerform
                .onThrottleClick {
                    AuthorizationFragment
                        .navigate(findNavController(), _bundler)
                        .toAuthorizationForResult(
                            R.id.action_profileMeOrganizationFragment_to_authorization,
                            _requestCodeAuthorization
                        )
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.addEvent
                .onThrottleClick {
                    when (typeDraft) {
                        TypeDraft.Preparing, TypeDraft.Moderation, TypeDraft.Rejected -> _showOrganizationHelpFragment()
                        TypeDraft.NotDraft -> findNavController().navigate(R.id.action_profileMeOrganizationFragment_to_addEventFragment)
                        TypeDraft.Banned -> showAlertDialog(
                            getString(R.string.profile_banned),
                            getString(
                                R.string.profile_banned_description
                            )
                        )

                        TypeDraft.Removed -> {}
                        TypeDraft.Default -> {}
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.mainProfileMeOrganizatorAboutTrophiesShowAll
                .onThrottleClick {
                    val bundle = bundleOf(_requestCodeTrophiesList to _trophiesAdapter.currentList)
                    findNavController().navigate(
                        R.id.action_profileMeOrganizationFragment_to_achievementInfoFragment,
                        bundle
                    )
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.mainProfileMeOrganizatorAboutCertificatesShowAll
                .onThrottleClick {
                    val bundle =
                        bundleOf(_requestCodeCertificateList to _certificateAdapter.currentList)
                    findNavController().navigate(
                        R.id.action_main__profile_me_to_achievementInfoFragment,
                        bundle
                    )
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.mainProfileMeOrganizatorAboutEduCertificatesShowAll
                .onThrottleClick {
                    val bundle =
                        bundleOf(_requestCodeEduCertificateList to _eduCertificateAdapter.currentList)
                    findNavController().navigate(
                        R.id.action_main__profile_me_to_achievementInfoFragment,
                        bundle
                    )
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _state
                .observeWhen<AuthLess>()
                .subscribeToFlow {
                    binding.skeleton.mainProfileMeOrganizatorSkeletonShimmer.gone()
                    binding.skeleton.mainProfileMeOrganizatorSkeletonShimmer.isShimmerAnimated =
                        false
                    binding.mainProfileMeOrganizatorContent.gone()
                    binding.authorization.authorizationContainer.visible()
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _state
                .observe()
                .map {
                    it is Displaying
                        && (it.profileMeDataState is ProfileMeDataStateReloading && !it.profileMeDataState.isBackground)
                        && (it.profileMeOrganizationDataState is ProfileMeOrganizationDataStateReloading && !it.profileMeOrganizationDataState.isBackground)
                }
                .distinctUntilChanged()
                .subscribeToFlow {
                    _swipeRefreshLayout.isRefreshing = it
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _swipeRefreshLayout
                .onRefreshAsFlow()
                .throttleUserInput()
                .subscribeToFlow {
                    _state.sendWhen<Displaying> { refreshAll() }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _state
                .observeWhen<Displaying>()
                .filter {
                    (it.profileMeOrganizationDataState is ProfileMeOrganizationDataStateIdleLoaded ||
                        it.profileMeOrganizationDataState is ProfileMeOrganizationDataStateReloading)
                }
                .map {
                    when (it.profileMeOrganizationDataState) {
                        is ProfileMeOrganizationDataStateIdleLoaded -> it.profileMeOrganizationDataState.data
                        is ProfileMeOrganizationDataStateReloading -> it.profileMeOrganizationDataState.previousData
                        is LoadDataPlainState.Idle.Empty,
                        is LoadDataPlainState.Idle.Failed,
                        is LoadDataPlainState.InProgress.Loading -> unreachable()

                        else -> {
                            unreachable()
                        }
                    }
                }
                .filterIsInstance<ProfileMeOrganizationData.LegalOrganizationData>()
                .throttleViewUpdates()
                .subscribeToFlow { organization ->
                    when (organization.data.statusClass) {
                        OrganizationStatusClass.Rejected,
                        OrganizationStatusClass.Created -> {
                            _accountManager.setAccountType(
                                AccountType.LegalOrganization(
                                    organizerId = OrganizerId.restore(organization.dashboard.organizer.remoteId.unwrap()),
                                    organizationStatus = OrganizationStatus.DRAFT
                                )
                            )
                            (requireActivity() as? MainActivity)?.updateBottomNav()
                            return@subscribeToFlow
                        }

                        else -> { /* No need. */
                        }
                    }

                    _checkOrganizationType(organization.data.identity)

                    typeDraft = mapStatusToTypeDraft(
                        organization.data.status,
                        TypeDraft.NotDraft,
                        binding,
                        requireContext()
                    )

                    organization.data.status.nullIfBlank()?.let { status ->
                        binding.mainOrganizationStatusIcon.visible()
                        binding.mainOrganizationStatus.visible()
                        binding.mainOrganizationStatus.text = status
                        val metrics = resources.displayMetrics
                        val pxValue =
                            TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 8F, metrics)
                        binding.mainOrganizationStatusIcon.setMargins(left = pxValue.toInt())
                    } ?: also {
                        binding.mainOrganizationStatusIcon.gone()
                        binding.mainOrganizationStatus.gone()
                    }
                    organization.data.organizer.trophies.loadTrophies()

                    binding.mainProfileMeOrganizatorReviewList.isGone =
                        organization.reviews.isEmpty()
                    binding.mainProfileMeOrganizatorReviewTitle.isGone =
                        organization.reviews.isEmpty()
                    binding.mainProfileMeOrganizatorReviewAll.isGone =
                        organization.reviews.isEmpty()
                    _reviewAdapter?.submitList(organization.reviews)

                    _bindContentView(organization.data, organization.dashboard)

                    binding.mainOrganizationEventsAll.isGoneVisible =
                        organization.events.isNotEmpty()
                    binding.mainOrganizationEventsList.isGoneVisible =
                        organization.events.isNotEmpty()

                    binding.mainOrganizationEventsEmptyMessage.isGoneVisible =
                        organization.events.isEmpty()

                    binding.mainOrganizationEventsTitle.text = requireContext().spannedString {
                        it
                            .append(getString(R.string.main__organization___events))
                            .append(Characters.space)
                            .append(organization.events.size.digitToChar())
                            .spans { ForegroundColorSpan(requireContext().getColor(R.color.application___color__icons)) }
                    }

                    _eventsAdapter.submitList(
                        persistentListOf<EventsAdapter.Item>().mutate { list ->
                            list.addAll(organization.events.mapPersistent {
                                EventsAdapter.Item.EventItem(
                                    it
                                )
                            })
                        }
                    )
                }
        }

        viewLifecycleOwner.repeatOnResume {
            merge(
                binding.mainProfileMeOrganizatorId.onClickAsFlow.map { ClickType.Id },
                binding.mainProfileMeOrganizatorContactsEmail.onClickAsFlow.map { ClickType.Email },
                binding.mainProfileMeOrganizatorContactsPhone.onClickAsFlow.map { ClickType.Phone },
                binding.mainProfileMeOrganizatorContactsSite.onClickAsFlow.map { ClickType.Site },
                binding.mainProfileMeOrganizatorInfoImages.onClickAsFlow.map { ClickType.Images },
                binding.mainProfileMeOrganizatorInfoVideos.onClickAsFlow.map { ClickType.Videos },
                binding.mainProfileMeOrganizatorReviewAll.onClickAsFlow.map { ClickType.Reviews },
                _socialsAdapter.onExpandClick.map { ClickType.SocialsExpand },
                _onEditClick
            )
                .throttleUserInput()
                .withLatestFrom(
                    _state
                        .observeWhen<Displaying>()
                        .map { it.profileMeOrganizationDataState }
                        .filterIsInstance<ProfileMeOrganizationDataStateIdleLoaded>()
                        .map { it.data }
                )
                .subscribeToFlow { (clickType: ClickType, organizationData: ProfileMeOrganizationData) ->
                    clickType.onClick(organizationData.data)
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _state
                .observeWhen<Displaying>()
                .filter {
                    (it.profileMeOrganizationDataState is ProfileMeOrganizationDataStateIdleLoaded ||
                        it.profileMeOrganizationDataState is ProfileMeOrganizationDataStateReloading)
                }
                .map {
                    when (it.profileMeOrganizationDataState) {
                        is ProfileMeOrganizationDataStateIdleLoaded -> it.profileMeOrganizationDataState.data
                        is ProfileMeOrganizationDataStateReloading -> it.profileMeOrganizationDataState.previousData
                        is LoadDataPlainState.Idle.Empty,
                        is LoadDataPlainState.Idle.Failed,
                        is LoadDataPlainState.InProgress.Loading -> unreachable()

                        else -> {
                            unreachable()
                        }
                    }
                }
                .filterIsInstance<ProfileMeOrganizationData.DraftOrganizationData>()
                .throttleViewUpdates()
                .subscribeToFlow { organization ->
                    when (organization.data.statusClass) {
                        OrganizationStatusClass.Active -> {
                            _accountManager.setAccountType(
                                AccountType.LegalOrganization(
                                    organizerId = organization.data.organizer.identity,
                                    organizationStatus = OrganizationStatus.VALID
                                )
                            )
                            (requireActivity() as? MainActivity)?.updateBottomNav()
                            return@subscribeToFlow
                        }

                        else -> { /* No need. */
                        }
                    }

                    _checkOrganizationType(organization.data.identity)

                    typeDraft = mapStatusToTypeDraft(
                        organization.data.status,
                        TypeDraft.Default,
                        binding,
                        requireContext()
                    )

                    binding.mainOrganizationEventsAll.isGoneVisible =
                        organization.events.isNotEmpty()
                    binding.mainOrganizationEventsList.isGoneVisible =
                        organization.events.isNotEmpty()
                    binding.mainOrganizationEventsTitle.isGoneVisible =
                        organization.events.isNotEmpty()

                    binding.mainOrganizationEventsEmptyMessage.isGoneVisible =
                        organization.events.isEmpty()

                    binding.mainOrganizationEventsTitle.text = requireContext().spannedString {
                        it
                            .append(getString(R.string.main__organization___events))
                            .append(Characters.space)
                            .append(Characters.space)
                            .append(organization.events.size.digitToChar())
                            .spans { ForegroundColorSpan(requireContext().getColor(R.color.application___color__icons)) }
                    }

                    _eventsAdapter.submitList(
                        persistentListOf<EventsAdapter.Item>().mutate { list ->
                            list.addAll(organization.events.mapPersistent {
                                EventsAdapter.Item.EventItem(
                                    it
                                )
                            })
                        }
                    )

                    _bindContentView(organization.data, null)
                    organization.data.status.nullIfBlank()?.let { status ->
                        binding.mainOrganizationStatusIcon.visible()
                        binding.mainOrganizationStatus.visible()
                        binding.mainOrganizationStatus.text = status
                        val metrics = resources.displayMetrics
                        val pxValue =
                            TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 8F, metrics)
                        binding.mainOrganizationStatusIcon.setMargins(left = pxValue.toInt())
                    } ?: also {
                        binding.mainOrganizationStatusIcon.gone()
                        binding.mainOrganizationStatus.gone()
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _eventsAdapter
                .onItemClick
                .throttleUserInput()
                .subscribeToFlow {
                    firebaseAnalyticManager.sendEventWithId(
                        AnalyticsConstants.Event.Main.organizationOnEventClick,
                        it.identity
                    )

                    EventFragment.navigate(findNavController(), _bundler)
                        .toEvent(
                            R.id.action_profileMeOrganizationFragment_to__main__event,
                            it.identity
                        )
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _state
                .observeWhen<Displaying>()
                .map {
                    ErrorMessages.ofException(
                        when {
                            it.profileMeDataState is LoadDataPlainState.Idle.Failed -> _errorHandler.ofException(
                                it.profileMeDataState.error
                            )

                            it.profileMeOrganizationDataState is LoadDataPlainState.Idle.Failed -> _errorHandler.ofException(
                                it.profileMeOrganizationDataState.error
                            )

                            else -> null
                        }
                    ).toOption()
                }
                .onEach {
                    _state.sendWhen<Displaying> { handleAnyFailed() }
                }
                .filterIsInstance<Some<RString>>()
                .throttleErrorMessages()
                .subscribeToFlow {
                    _messageDisplay.showMessage(it.t)
                }
        }

        viewLifecycleOwner.repeatOnResume {
            binding.mainProfileMeOrganizatorRequests
                .onThrottleClick {
                    val bundle = bundleOf("OrganizerId" to id)
                    findNavController().navigate(
                        R.id.action_profileMeOrganizationFragment_to_organizationRequestsFragment,
                        bundle
                    )
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _socialsAdapter
                .onSocialClick
                .throttleUserInput()
                .subscribeToFlow { link ->
                    try {
                        startActivityBy {
                            it.view(link).newTask()
                        }
                    } catch (e: Exception) {
                        val clipData =
                            ClipData.newPlainText(
                                "text",
                                binding.mainProfileMeOrganizatorContactsSite.text
                            )

                        val clipboardManager = context?.clipboardManager
                        if (clipboardManager !== null) {
                            clipboardManager.setPrimaryClip(clipData)
                            _messageDisplay.showMessage(
                                RString.string(
                                    "Адрес соцсети скопирован",
                                    null
                                )
                            )
                        }
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _trophiesAdapter
                .onCardClick
                .throttleUserInput()
                .subscribeToFlow {
                    val bundle = bundleOf(
                        AchievementInfoFragment.name to it.item.title,
                        AchievementInfoFragment.description to it.item.description,
                        AchievementInfoFragment.imageUrl to it.item.image.toString(),
                        AchievementInfoFragment.progress to 100
                    )
                    findNavController().navigate(
                        R.id.action_profileMeOrganizationFragment_to_trophyInfoFragment,
                        bundle
                    )
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _certificateAdapter
                .onCardClick
                .throttleUserInput()
                .subscribeToFlow {
                    _certificateAdapter.currentList.mapNotNull { it.item.image }.let {
//                    findNavController().navigate(R.id.action_main__profile_me_organizator_to_media_viewer___image_pager,
//                        MediaViewerPagerRequest(
//                            position = pair.second,
//                            files = it.toPersistentList(),
//                            type = ContentType.CERTIFICATES
//                        ).intoBundle(_bundler))
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _eduCertificateAdapter
                .onCardClick
                .throttleUserInput()
                .subscribeToFlow { eduCertificate ->
                    eduCertificate.item.certificateUrl?.let { url ->
                        tryStartActivityBy {
                            it.view(url).newTask()
                        }
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _categoriesAdapter
                .onItemClick
                .throttleUserInput()
                .subscribeToFlow { category ->
                    SearchFragment
                        .navigate(findNavController(), _bundler)
                        .toSearch(
                            R.id.main__search,
                            persistentSetOf<Category>(
                                Category.restore(
                                    id = CategoryId.restore(category.item.id),
                                    title = category.item.title,
                                    icon = category.item.icon,
                                    iconGray = category.item.icon,
                                    color = category.item.color
                                )
                            )
                        )
                }
        }

        viewLifecycleOwner.repeatOnResume {
            _state
                .handlePlainLoadingFlow<Displaying, User>(
                    scope = this,
                    toTaskState = { it.profileMeDataState },
                    task = {
                        _dobroApi.profile().getUserMe()
                    },
                    onError = {
                        _messageDisplay.showMessage("Не удалось загрузить данные".r)
                        logger.warning { "Failed to load \"my\" profile." }
                    },
                    onUpdateState = { updateProfileMeDataState(it) }
                )
        }

        viewLifecycleOwner.repeatOnResume {
            _state
                .handlePlainLoadingFlow<Displaying, ProfileMeOrganizationData>(
                    scope = this,
                    toTaskState = { it.profileMeOrganizationDataState },
                    task = {
                        when (val accountType = _accountManager.getAccountType()) {
                            is AccountType.LegalOrganization -> {
                                when (accountType.organizationStatus) {
                                    OrganizationStatus.VALID -> {
                                        val dashboard = _dobroApi.organization()
                                            .getOrganizationDashboard(
                                                _accountManager.getAccountType().getOrganizationId()
                                                    ?: OrganizerId.restore(-1L)
                                            )

                                        val organization = _dobroApi.organization()
                                            .getOrganization(dashboard.organizer.remoteId)

                                        val reviews = _dobroApi.organization()
                                            .getOrganizerReviews(
                                                organization.organizer.identity.unwrap(),
                                                page = 1,
                                                limit = 3
                                            )

                                        val events = _dobroApi.organization().getActualEvents(
                                            organization.organizer.identity,
                                            page = 1
                                        )

                                        ProfileMeOrganizationData.LegalOrganizationData(
                                            data = organization,
                                            dashboard = dashboard,
                                            events = events.data,
                                            reviews = reviews
                                        )
                                    }

                                    OrganizationStatus.DRAFT -> {
                                        val organizerId =
                                            when (val type = _accountManager.getAccountType()) {
                                                is AccountType.LegalOrganization -> {
                                                    type.organizerId
                                                }

                                                is AccountType.PhysicOrganization -> {
                                                    type.organizerId
                                                }

                                                AccountType.Physic -> {
                                                    null
                                                }
                                            }
                                        if (organizerId != null) {
                                            val organization = _dobroApi.organization()
                                                .getOrganization(
                                                    OrganizationId.restore(
                                                        organizerId.unwrap()
                                                    )
                                                )

                                            val events = _dobroApi.organization()
                                                .getActualEvents(
                                                    organization.organizer.identity,
                                                    page = 1
                                                )

                                            ProfileMeOrganizationData.DraftOrganizationData(
                                                data = organization,
                                                events = events.data
                                            )
                                        } else {
                                            throw IllegalStateException("Can`t load organization draft")
                                        }
                                    }
                                }
                            }

                            else -> throw IllegalStateException("Can`t load organization draft")
                        }
                    },
                    onError = {
                        logger.warning { "Failed to load organization." }
                        _messageDisplay.showMessage("Не удалось загрузить данные".r)
                    },
                    onUpdateState = { updateProfileMeOrganizationDataState(it) }
                )
        }

        viewLifecycleOwner.repeatOnResume {
            _checkOrganizationType()
        }

        parentFragmentManager.setFragmentResultListener(
            _requestCodeAuthorization,
            viewLifecycleOwner
        ) { _, _ ->
            _state.sendWhen<AuthLess> {
                reset()
            }
        }

        parentFragmentManager.setFragmentResultListener(
            _requestCodeSelectCategories,
            viewLifecycleOwner
        ) { _, _ ->
            _state.sendWhen<Displaying> { reloadAll() }
        }
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        _state.sendWhen<Displaying> { reloadAll() }

        status = when (val type = _accountManager.getAccountType()) {
            is AccountType.LegalOrganization -> {
                type.organizationStatus
            }

            is AccountType.PhysicOrganization -> {
                type.organizationStatus
            }

            AccountType.Physic -> null
        }
    }

    override fun onPause() {
        super.onPause()
        OrganizationStatusHelpRequestData.organizationStatusHelpRequest =
            OrganizationStatusHelpRequest(
                shouldHelpScreenShow = true,
                shouldClickOnEdit = false,
                shouldOpenVolunteerProfile = false,
                shouldOpenDeeplinkFromVolunteerProfile = false
            )
    }

    private suspend fun _checkOrganizationType(id: OrganizationId? = null) {
        val checkOrganizationTypeWithNavigation = checkOrganizationTypeWithNavigation(
            navController = findNavController(),
            typesFilter = setOf(
                HelpMessageType.OrganizationOnModeration,
                HelpMessageType.OrganizationIsRejected,
                HelpMessageType.OrganizationIsPreparing
            )
        )

        OrganizationStatusHelpRequestData.organizationStatusHelpRequest.let {
            when {
                it.shouldHelpScreenShow -> checkOrganizationTypeWithNavigation
                it.shouldClickOnEdit -> {
                    findNavController().navigate(
                        R.id.action_profileMeOrganizationFragment_to_editOrganizationFragment,
                        EditOrganizationRequest.DeepLink(
                            organizationId = id ?: OrganizationId.restore(
                                (_accountManager.getAccountType() as AccountType.LegalOrganization).organizerId.unwrap()
                            )
                        ).intoBundle(_bundler)
                    )
                }

                it.shouldOpenVolunteerProfile -> {
                    _accountManager.setAccountType(AccountType.Physic)
                    findNavController().navigate(R.id.action_profileMeOrganizationFragment_to_main__profile_me)
                    (requireActivity() as? MainActivity)?.updateBottomNav()
                }

                it.shouldOpenDeeplinkFromVolunteerProfile -> {
                    _accountManager.setAccountType(AccountType.Physic)
                    findNavController().navigate(R.id.action_profileMeOrganizationFragment_to_main__profile_me)
                    (requireActivity() as? MainActivity)?.updateBottomNav()
                    (requireActivity() as? MainActivity)?.triggerLastOpenedDeeplink()
                }

                else -> {}
            }
        }
    }

    private fun _showOrganizationHelpFragment() {
        when (typeDraft) {
            TypeDraft.Moderation -> HelpMessageType.OrganizationOnModeration
            TypeDraft.Rejected -> HelpMessageType.OrganizationIsRejected
            TypeDraft.Preparing -> HelpMessageType.OrganizationIsPreparing
            else -> null
        }?.let { type ->
            HelpMessageFragment
                .navigate(findNavController(), _bundler)
                .to(
                    actionId = R.id.organizationStatusHelpFragment,
                    type = type
                )
        }
    }

    fun ClickType.onClick(organization: Organization) {
        when (this) {
            ClickType.VolunteerBook -> {
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.profileMeOnVolunteerBookClick)
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Main.profileMeOnVolunteerBookClick)

//                        findNavController().navigate(
//                            R.id.main__profile_me_organizator__to__main__profile___volunteer_book,
//                            user.identity.intoBundle(_bundler)
//                        )
            }

            ClickType.Id -> {
                val clipData =
                    ClipData.newPlainText("text", organization.identity.unwrap().toString())

                val clipboardManager = context?.clipboardManager
                if (clipboardManager !== null) {
                    clipboardManager.setPrimaryClip(clipData)
                    _messageDisplay.showMessage(
                        RString.string(
                            "Id организации скопирован",
                            null
                        )
                    )
                }
            }

            ClickType.ChangeCategories -> {
                CategoriesSelectorFragment.createAuthorized(
                    requestKey = _requestCodeSelectCategories,
                    bundler = _bundler,
                    selectedCategories = organization.categories,
                    organizationId = organization.identity
                )
                    .show(parentFragmentManager, null)
            }

            ClickType.Comments -> {
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Main.profileReviewsClick)
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.profileReviewsClick)

//                        findNavController().navigate(
//                            R.id.action_main__profile_to__main__profile___comments,
//                            user.identity.intoBundle(_bundler)
//                        )
            }

            ClickType.SocialsExpand -> {
                _socialsAdapter.submitList(organization.socials.filter { it.identity != SocialType.Site }
                    .map {
                        SocialsAdapter.Item.SocialItem(
                            it
                        )
                    })
            }

            ClickType.Reviews -> {
                val bundle =
                    bundleOf("OrganizationId" to organization.organizer.identity.unwrap())
                findNavController().navigate(
                    R.id.action_profileMeOrganizationFragment_to_organizationsAllReviews,
                    bundle
                )
            }

            ClickType.Videos -> {
//                        firebaseAnalyticManager.sendEventWithId(
//                            AnalyticsConstants.Event.Main.organizationOnAllVideosClick,
//                            organization.identity
//                        )

                MediaViewerListFragment.navigate(findNavController(), _bundler)
                    .toVideos(
                        R.id.action_profileMeOrganizationFragment_to_media_viewer___list,
                        organization.videos
                    )
            }

            ClickType.Events -> {
//                firebaseAnalyticManager.sendEventWithId(
//                    AnalyticsConstants.Event.Main.organizationOnAllEventsClick,
//                    organization.identity
//                )
//
//                EventsFragment
//                    .navigate(findNavController(), _bundler)
//                    .toFullOrganizationEvents(
//                        actionId = R.id.action_profileMeOrganizationFragment_to__main__events,
//                        organizerId = organization.organizer.identity
//                    )
            }

            ClickType.Images -> {
//                        firebaseAnalyticManager.sendEventWithId(
//                            AnalyticsConstants.Event.Main.organizationOnAllImagesClick,
//                            organization.identity
//                        )

                MediaViewerListFragment.navigate(findNavController(), _bundler)
                    .toImages(
                        R.id.action_profileMeOrganizationFragment_to_media_viewer___list,
                        organization.images
                    )
            }

            ClickType.Edit -> {
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.profileOrganizerEditClick)
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Main.profileOrganizerEditClick)
                when (val type = _accountManager.getAccountType()) {
                    is AccountType.LegalOrganization -> {
                        (_state.value as? Displaying)?.let { displaying ->
                            metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.organizationLegalSelected)

                            var identity: OrganizationId? = null
                            (displaying.profileMeOrganizationDataState as? ProfileMeOrganizationDataStateIdleLoaded)?.let {
                                identity =
                                    (it.data as? ProfileMeOrganizationData.LegalOrganizationData)?.dashboard?.identity
                            }
                            (displaying.profileMeOrganizationDataState as? ProfileMeOrganizationDataStateReloading)?.let {
                                identity =
                                    (it.previousData as? ProfileMeOrganizationData.LegalOrganizationData)?.dashboard?.identity
                            }
                            when (typeDraft) {
                                TypeDraft.Banned -> showAlertDialog(
                                    getString(R.string.profile_banned),
                                    getString(
                                        R.string.profile_banned_description,
                                    ),
                                    legalOrganization = type,
                                    identity,
                                )

                                else -> {
                                    findNavController().navigate(
                                        R.id.action_profileMeOrganizationFragment_to_editOrganizationFragment,
                                        EditOrganizationRequest.DeepLink(
                                            organizationId = identity ?: OrganizationId.restore(
                                                type.organizerId.unwrap()
                                            )
                                        ).intoBundle(_bundler)
                                    )
                                }
                            }
                        }
                    }

                    is AccountType.PhysicOrganization -> {
                        metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.organizationIndividualSelected)

                        when (typeDraft) {
                            TypeDraft.Banned -> showAlertDialog(
                                getString(R.string.profile_banned),
                                getString(
                                    R.string.profile_banned_description,
                                ),
                                physicOrganization = type
                            )

                            else -> {
                                findNavController().navigate(
                                    R.id.action_profileMeOrganizationFragment_to_editOrganizationFragment,
                                    EditOrganizationRequest.DeepLink(
                                        organizationId = OrganizationId.restore(
                                            type.organizerId.unwrap()
                                        )
                                    ).intoBundle(_bundler)
                                )
                            }
                        }
                    }

                    AccountType.Physic -> {
                        // Do nothing
                    }
                }
            }

            ClickType.Email -> {
                val clipData =
                    ClipData.newPlainText(
                        "text",
                        binding.mainProfileMeOrganizatorContactsEmail.text
                    )

                val clipboardManager = context?.clipboardManager
                if (clipboardManager !== null) {
                    clipboardManager.setPrimaryClip(clipData)
                    _messageDisplay.showMessage(RString.string("Почта скопирована", null))
                }
            }

            ClickType.Phone -> {
                val clipData =
                    ClipData.newPlainText(
                        "text",
                        binding.mainProfileMeOrganizatorContactsPhone.text
                    )

                val clipboardManager = context?.clipboardManager
                if (clipboardManager !== null) {
                    clipboardManager.setPrimaryClip(clipData)
                    _messageDisplay.showMessage(RString.string("Телефон скопирован", null))
                }
            }

            ClickType.Leader -> {
            }

            ClickType.Site -> {
                try {
                    startActivityBy {
                        it.view(organization.site ?: Uri.EMPTY)
                    }
                } catch (e: Exception) {
                    val clipData =
                        ClipData.newPlainText(
                            "text",
                            binding.mainProfileMeOrganizatorContactsSite.text
                        )

                    val clipboardManager = context?.clipboardManager
                    if (clipboardManager !== null) {
                        clipboardManager.setPrimaryClip(clipData)
                        _messageDisplay.showMessage(RString.string("Сайт скопирован", null))
                    }
                }
            }
        }
    }

    override fun onStop() {
        super.onStop()
        wasMenuAdded = false
    }

    enum class ClickType {
        Id,
        VolunteerBook,
        ChangeCategories,
        SocialsExpand,
        Comments,
        Images,
        Videos,
        Events,
        Reviews,
        Edit,
        Email,
        Phone,
        Leader,
        Site
    }

    @SuppressLint("InflateParams")
    private fun _bindContentView(
        organization: Organization,
        organizationDashboard: OrganizationDashboard?
    ) {
        _state.sendWhen<Displaying> {
            setIsEditable(organizationDashboard?.canEdit ?: organization.isEditor)
        }
        binding.skeleton.mainProfileMeOrganizatorSkeletonShimmer.gone()
        binding.skeleton.mainProfileMeOrganizatorSkeletonShimmer.isShimmerAnimated = false
        binding.mainProfileMeOrganizatorContent.visible()

        when (status) {
            OrganizationStatus.DRAFT -> {
                binding.mainOrganizationEventsTitle.gone()
                binding.mainProfileMeOrganizatorAboutTrophies.gone()
                binding.mainOrganizationReview.gone()
            }

            else -> {
                binding.mainOrganizationEventsTitle.visible()
                binding.mainProfileMeOrganizatorAboutTrophies.visible()
                binding.mainOrganizationReview.visible()
            }
        }

        binding.mainProfileMeOrganizatorName.text =
            if (organization.organizer.isVerified) {
                requireContext().spannedString {
                    it
                        .append(organization.name)
                        .append(Characters.space)
                        .append()
                        .image(R.drawable.main__profile_me_organization___verified)
                }
            } else {
                organization.name
            }

        binding.mainProfileMeOrganizatorInfoFullName.text = organization.organizer.fullName
        binding.mainProfileMeOrganizatorInfoFullName.isGoneVisible =
            organization.organizer.fullName.isNotEmpty()

        binding.mainProfileMeOrganizatorInfoVolunteers.text = getString(
            R.string.main__organization___volunteers,
            organization.organizer.statistic.volunteersCount
        )

        binding.mainProfileMeOrganizatorContactsLeader.isGoneVisible = organization.leader != null
        organization.leader?.let {
            binding.mainProfileMeOrganizatorContactsLeader.text =
                getString(R.string.main__organization___leader, it.name.formatFullEastern())
        }

        binding.mainProfileMeOrganizatorContactsSite.isGoneVisible =
            !organization.site.isNullOrEmpty
        organization.site?.let { uri ->
            binding.mainProfileMeOrganizatorContactsSite.text = requireContext().spannedString {
                it.append(R.string.main__organization___site)
                    .append(Characters.space)
                    .append(uri)
                    .spans { ForegroundColorSpan(requireContext().getColor(R.color.application___color__primary)) }
            }
        }

        images.load(organization.organizer.icon)
            .circleCrop()
            .transition(DrawableTransitionOptions.withCrossFade())
            .into(binding.mainProfileMeOrganizatorAvatar)

        if (organizationDashboard?.organizer?.statistic?.organizerRating != 0f && organizationDashboard?.organizer?.statistic?.organizerRating != null) {
            binding.mainProfileMeRatingContainer.visible()
            val metrics = resources.displayMetrics
            val pxValue = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 4F, metrics)
            binding.mainProfileMeOrganizatorId.setMargins(left = pxValue.toInt())
        } else {
            binding.mainProfileMeRatingContainer.gone()
            val metrics = resources.displayMetrics
            val pxValue = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 8F, metrics)
            binding.mainProfileMeOrganizatorId.setMargins(left = pxValue.toInt())
            binding.mainProfileMeOrganizatorName.setPaddingHorizontal(pxValue.toInt())
            binding.mainOrganizationStatusIcon.setMargins(left = 0)
        }

        binding.mainProfileMeOrganizatorRating.text =
            organizationDashboard?.organizer?.statistic?.organizerRating?.let {
                _displayFormat.ratingFormatter().format(it)
            }

        binding.mainProfileMeOrganizatorId.text = requireContext().spannedString {
            it
                .append(R.string.main__profile___id)
                .append(Characters.colon)
                .append(Characters.space)
                .append(organization.organizer.organizationId.unwrap().toString())
        }

        val mapContent = organization.categories.map {
            CategoriesAdapter.Category(
                CategoriesAdapter.Item(
                    it.identity.unwrap(),
                    it.title,
                    it.icon,
                    it.color
                )
            )
        }
        _categoriesAdapter.submitList(mapContent)

        binding.mainProfileMeOrganizatorContactsCity.isGoneVisible =
            organization.settlement != null
        organization.settlement
        organization.settlement?.let { binding.mainProfileMeOrganizatorContactsCity.text = it }

        binding.mainProfileMeOrganizatorAnalyticsOrganizationsCount.text = 1.toString()
        binding.mainProfileMeOrganizatorAnalyticsVolounersCount.text =
            organization.organizer.volunteersCount.toString()
        binding.mainProfileMeOrganizatorAnalyticsGoodActionsCount.text =
            organization.organizer.statistic.eventsCount.toString()
        binding.mainProfileMeOrganizatorAnalyticsHoursCount.text =
            organization.organizer.statistic.hours.toString()

        binding.mainProfileMeOrganizatorContacts.isGoneVisible =
            organization.phone != null || organization.email != null || organization.settlement != null

        binding.mainProfileMeOrganizatorContactsPhone.isGoneVisible = organization.phone != null
        organization.phone?.let { phone ->
            binding.mainProfileMeOrganizatorContactsPhone.text = requireContext().spannedString {
                it.append(R.string.main__organization___phone_variant)
                    .append(Characters.space)
                    .append(phone.formatE164())
                    .spans { ForegroundColorSpan(requireContext().getColor(R.color.application___color__primary)) }
            }
        }

        binding.mainProfileMeOrganizatorContactsEmail.isGoneVisible = organization.email != null
        organization.email?.let { email ->
            binding.mainProfileMeOrganizatorContactsEmail.text = requireContext().spannedString {
                it.append(R.string.main__organization___email)
                    .append(Characters.space)
                    .append(email.formatDefault())
                    .spans { ForegroundColorSpan(requireContext().getColor(R.color.application___color__primary)) }
            }
        }

        binding.mainProfileMeOrganizatorDescriptionTitle.isGoneVisible =
            !organization.description.isNullOrEmpty()
        binding.mainProfileMeOrganizatorDescription.isGoneVisible =
            !organization.description.isNullOrBlank()
        organization.description?.let {
            binding.mainProfileMeOrganizatorDescription.setTextWithHtml(it)
            binding.mainProfileMeOrganizatorDescription.movementMethod =
                LinkMovementMethod.getInstance()
        }

        binding.mainProfileMeOrganizatorResultsTitle.isGoneVisible =
            !organization.results.isNullOrBlank()
        binding.mainProfileMeOrganizatorResults.isGoneVisible = organization.results != null
        organization.results?.let {
            binding.mainProfileMeOrganizatorResults.setTextWithHtml(it)
            binding.mainProfileMeOrganizatorResults.movementMethod =
                LinkMovementMethod.getInstance()
        }

        _documentsAdapter.submitList(organization.documents)
        binding.mainProfileMeOrganizatorDocumentsList.isGoneVisible =
            organization.documents.isNotEmpty()
        binding.mainProfileMeOrganizatorDocumentsTitle.isGoneVisible =
            organization.documents.isNotEmpty()

        _bindTaskView(organizationDashboard?.sections?.get(0)?.counter ?: 0)
        binding.mainProfileMeOrganizatorContactsSocialsTitle.isGoneVisible =
            organization.socials.isNotEmpty()
        binding.mainProfileMeOrganizatorContactsSocials.isGoneVisible =
            organization.socials.isNotEmpty()
        if (organization.socials.size > nonExpandedSocialsCount) {
            _socialsAdapter.submitList(persistentListOf<SocialsAdapter.Item>().mutate { list ->
                list.addAll(
                    organization.socials.filter { it.identity != SocialType.Site }
                        .take(nonExpandedSocialsCount - 1)
                        .map { SocialsAdapter.Item.SocialItem(it) })
                list.add(SocialsAdapter.Item.ExpandItem(organization.socials.size - (nonExpandedSocialsCount - 1)))
            })
        } else {
            _socialsAdapter.submitList(organization.socials.filter { it.identity != SocialType.Site }
                .map {
                    SocialsAdapter.Item.SocialItem(
                        it
                    )
                })
        }
    }

    private fun _bindTaskView(count: Int) {
        binding.mainProfileMeOrganizatorRequestsCount.text = count.toString()
        binding.mainProfileMeOrganizatorRequestsCount.isGoneVisible = count != 0
    }

    private fun _logoutDialog(): AlertDialog.Builder {
        return MaterialAlertDialogBuilder(requireContext())
            .setTitle(R.string.main__profile___logout__title)
            .setMessage(R.string.main__profile___logout__message)
            .setPositiveButton(getString(R.string.main__profile___logout)) { _, _ ->
                _accountManager.removeAccount()
                _settings.flags.setDonorOnboardingShown(false)
                _accountManager.setAccountType(AccountType.Physic)
                _state.sendWhen<Displaying> {
                    reset()
                }
                view?.findViewById<Toolbar>(R.id.application___toolbar)?.menu?.clear()
                findNavController().navigate(R.id.action_profileMeOrganizationFragment_to_main__profile_me)
                (requireActivity() as MainActivity).apply {
                    updateBottomNav()
                    scheduleBottomNavigationAllTabsRefresh()
                    hideNotificationsCountBadge()
                }
            }
            .setNegativeButton(R.string.application___cancel) { dialog, _ ->
                dialog.dismiss()
            }
    }

    private fun showAlertDialog(
        title: String,
        description: String,
        legalOrganization: AccountType.LegalOrganization? = null,
        identity: OrganizationId? = null,
        physicOrganization: AccountType.PhysicOrganization? = null
    ) {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle(title)
            .setMessage(description)
            .setPositiveButton("Понятно") { _, _ ->
                if (legalOrganization != null && typeDraft != TypeDraft.Rejected && typeDraft != TypeDraft.Banned) {
                    findNavController().navigate(
                        R.id.action_profileMeOrganizationFragment_to_editOrganizationFragment,
                        EditOrganizationRequest.DeepLink(
                            organizationId = identity ?: OrganizationId.restore(
                                legalOrganization.organizerId.unwrap()
                            )
                        ).intoBundle(_bundler)
                    )
                } else if (physicOrganization != null && typeDraft != TypeDraft.Rejected && typeDraft != TypeDraft.Banned) {
                    findNavController().navigate(
                        R.id.action_profileMeOrganizationFragment_to_editOrganizationFragment,
                        EditOrganizationRequest.DeepLink(
                            organizationId = OrganizationId.restore(
                                physicOrganization.organizerId.unwrap()
                            )
                        ).intoBundle(_bundler)
                    )
                }
            }.show()
    }

    private fun List<OrganizerTrophy>.loadTrophies() {
        if (_trophiesAdapter.currentList.isEmpty()) {
            val volunteerTrophiesList =
                java.util.Collections.synchronizedList(this.toMutableList())
            binding.mainProfileMeOrganizatorAboutTrophies.visibility =
                if (this.isEmpty()) View.GONE else View.VISIBLE
            this.forEach { volunteerTrophy ->
                val mapContent = volunteerTrophiesList.map {
                    TrophiesAdapter.Trophy(
                        TrophiesAdapter.Item(
                            it.identity,
                            it.title,
                            it.description,
                            it.percent,
                            it.image
                        )
                    )
                }
                _trophiesAdapter.submitList(mapContent)
                binding.mainProfileMeOrganizatorAboutTrophiesCount.text =
                    volunteerTrophiesList.count().toString()
            }
        } else {
            binding.mainProfileMeOrganizatorAboutTrophiesCount.text =
                _trophiesAdapter.currentList.count().toString()
        }
    }

    private fun android.widget.PopupMenu.handleMenuItemClick() {
        _state.value.let { event ->
            if (event is Displaying && event.profileMeDataState is LoadDataPlainState.Idle.Loaded) {
                val subMenu =
                    this.menu

                if ((subMenu?.size()?.minus(1)
                        ?: 0) <= (event.profileMeDataState.data.userMenuItem?.count()
                        ?: 0) && !wasMenuAdded
                ) {
                    event.profileMeDataState.data.userMenuItem?.filter { it.type == "dashboard" }
                        ?.forEachIndexed { index, account ->
                            if (subMenu?.children?.firstOrNull { it.title == account.name } == null) {
                                Glide.with(requireActivity())
                                    .asBitmap()
                                    .load(account.icon)
                                    .circleCrop()
                                    .into(object : CustomTarget<Bitmap>(80, 80) {
                                        override fun onResourceReady(
                                            resource: Bitmap,
                                            transition: Transition<in Bitmap>?
                                        ) {
                                            if (account.name.contains("(организатор)")) {
                                                /** Это организатор */
                                                accounts.add(
                                                    ProfileMeFragment.AccountType.PhysicOrganization(
                                                        account.name,
                                                        account.url.lastPathSegment?.toLongOrNull()
                                                            ?: -1
                                                    )
                                                )
                                            } else if (account.url.lastPathSegment.toString() == "dashboard") {
                                                /** Это просто пользователь */
                                                accounts.add(
                                                    ProfileMeFragment.AccountType.User(
                                                        account.name
                                                    )
                                                )
                                            } else if (account.url.lastPathSegment.toString() == "info") {
                                                /** Это организация требует редактирования */
                                                accounts.add(
                                                    ProfileMeFragment.AccountType.ToBeEditOrganization(
                                                        account.name,
                                                        account.url.pathSegments.last { it.toLongOrNull() != null }
                                                            .toLongOrNull() ?: -1
                                                    )
                                                )
                                            } else if (account.url.lastPathSegment?.toLongOrNull() != null) {
                                                /** Это организатор юрид */
                                                accounts.add(
                                                    ProfileMeFragment.AccountType.LegalOrganization(
                                                        account.name,
                                                        account.url.lastPathSegment?.toLongOrNull()
                                                            ?: -1
                                                    )
                                                )
                                            }
                                            val subMenuItem =
                                                subMenu?.add(0, index, Menu.NONE, account.name)
                                            subMenuItem?.icon =
                                                BitmapDrawable(resources, resource)
                                            subMenuItem?.setContentDescription("$index-menu")
                                        }

                                        override fun onLoadCleared(placeholder: Drawable?) {}
                                    })
                                wasMenuAdded = true
                            }
                            wasMenuAdded = true
                        }
                }
            }
        }
        this.setOnMenuItemClickListener { item ->
            when (item.itemId) {
                R.id.main__profile_me___logout -> {
                    metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.profileMeOnLogoutClick)
                    firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Main.profileMeOnLogoutClick)
                    _logoutDialog().show()
                    true
                }

                R.id.main__profile_me___become_organizer, R.id.main__profile_me___add_organization -> {
                    findNavController().navigate(R.id.action_profileMeOrganizationFragment_to_becomeOrganizerFragment)
                    true
                }

                else -> {
                    if (item.itemId != R.id.main__profile_me___change_profile) {
                        val account = accounts.firstOrNull { it.title == item.title }
                        account?.let { accountType ->
                            when (accountType) {
                                is ProfileMeFragment.AccountType.User -> {
                                    _accountManager.setAccountType(AccountType.Physic)
                                    findNavController().navigate(R.id.action_profileMeOrganizationFragment_to_main__profile_me)
                                    (requireActivity() as? MainActivity)?.updateBottomNav()
                                }

                                is ProfileMeFragment.AccountType.LegalOrganization -> {
                                    _accountManager.setAccountType(
                                        AccountType.LegalOrganization(
                                            organizerId = OrganizerId.restore(
                                                accountType.organizationId
                                            ),
                                            organizationStatus = OrganizationStatus.VALID
                                        )
                                    )
                                    (requireActivity() as? MainActivity)?.updateBottomNav()
                                    findNavController().navigate(R.id.action_profileMeOrganizationFragment_to_main__profileMeOrganizationFragment)
                                }

                                is ProfileMeFragment.AccountType.PhysicOrganization -> {
                                    _accountManager.setAccountType(
                                        AccountType.PhysicOrganization(
                                            organizerId = OrganizerId.restore(
                                                accountType.organizationId
                                            ),
                                            organizationStatus = OrganizationStatus.VALID
                                        )
                                    )
                                    findNavController().navigate(R.id.action_profileMeOrganizationFragment_to_main__profile_me)
                                    (requireActivity() as? MainActivity)?.updateBottomNav()
                                }

                                is ProfileMeFragment.AccountType.ToBeEditOrganization -> {
                                    _accountManager.setAccountType(
                                        AccountType.LegalOrganization(
                                            organizerId = OrganizerId.restore(
                                                accountType.organizationId
                                            ),
                                            organizationStatus = OrganizationStatus.DRAFT
                                        )
                                    )
                                    (requireActivity() as? MainActivity)?.updateBottomNav()
                                    findNavController().navigate(R.id.action_profileMeOrganizationFragment_to_main__profileMeOrganizationFragment)
                                }
                            }
                        }
                    }
                    true
                }
            }
        }
    }
}

private fun mapStatusToTypeDraft(
    status: String,
    default: TypeDraft,
    binding: MainProfileMeOrganizatorBinding?,
    context: Context?
): TypeDraft {
    return when (status) {
        "Отклонена" -> {
            context?.let {
                binding?.apply {
                    mainOrganizationStatusIcon.imageTintList =
                        ColorStateList.valueOf(context.getColor(R.color.main__requests___status__rejected))
                    mainOrganizationStatus.setTextColor(context.getColor(R.color.main__requests___status__rejected))
                }
            }

            TypeDraft.Rejected
        }

        "Удаленая" -> {
            context?.let {
                binding?.apply {
                    mainOrganizationStatusIcon.imageTintList =
                        ColorStateList.valueOf(context.getColor(R.color.main__requests___status__rejected))
                    mainOrganizationStatus.setTextColor(context.getColor(R.color.main__requests___status__rejected))
                }
            }
            TypeDraft.Removed
        }

        "Заблокирована" -> {
            context?.let {
                binding?.apply {
                    mainOrganizationStatusIcon.imageTintList =
                        ColorStateList.valueOf(context.getColor(R.color.main__requests___status__rejected))
                    mainOrganizationStatus.setTextColor(context.getColor(R.color.main__requests___status__rejected))
                }
            }
            TypeDraft.Banned
        }

        "Профиль не заполнен" -> {
            context?.let {
                binding?.apply {
                    mainOrganizationStatusIcon.imageTintList =
                        ColorStateList.valueOf(context.getColor(R.color.main__requests___status__new))
                    mainOrganizationStatus.setTextColor(context.getColor(R.color.main__requests___status__new))
                }
            }
            TypeDraft.Preparing
        }

        "На модерации" -> {
            context?.let {
                binding?.apply {
                    mainOrganizationStatusIcon.imageTintList =
                        ColorStateList.valueOf(context.getColor(R.color.main__requests___status__reserved))
                    mainOrganizationStatus.setTextColor(context.getColor(R.color.main__requests___status__reserved))
                }
            }
            TypeDraft.Moderation
        }

        "Активная" -> {
            context?.let {
                binding?.apply {
                    mainOrganizationStatusIcon.imageTintList =
                        ColorStateList.valueOf(context.getColor(R.color.main__requests___status__accepted))
                    mainOrganizationStatus.setTextColor(context.getColor(R.color.main__requests___status__accepted))
                }
            }
            TypeDraft.NotDraft
        }

        else -> default
    }
}

enum class TypeDraft {
    Preparing,
    Moderation,
    NotDraft,
    Rejected,
    Removed,
    Banned,
    Default,
}

suspend fun KodeinAware.checkOrganizationTypeWithNavigation(
    navController: NavController,
    type: HelpMessageType? = null,
    typesFilter: Set<HelpMessageType>
) {
    val accountManager: AccountManager by instance()
    val dobroApi: DobroApi by instance()
    val bundler: Bundler by instance()

    val organizerId = when (val type = accountManager.getAccountType()) {
        is AccountType.LegalOrganization -> type.organizerId

        is AccountType.PhysicOrganization -> type.organizerId

        AccountType.Physic -> null
    }
    if (organizerId != null && accountManager.getAccountType()
            .getOrganizationStatus() == OrganizationStatus.DRAFT
    ) {
        val organization = dobroApi.organization()
            .getOrganization(
                OrganizationId.restore(
                    organizerId.unwrap()
                )
            )

        val organizationType = when (mapStatusToTypeDraft(
            organization.status,
            TypeDraft.Default,
            null,
            null
        )) {
            TypeDraft.Moderation -> HelpMessageType.OrganizationOnModeration
            TypeDraft.Rejected -> HelpMessageType.OrganizationIsRejected
            TypeDraft.Preparing -> HelpMessageType.OrganizationIsPreparing
            else -> null
        }

        (organizationType ?: type)?.let {
            if (it in typesFilter) {
                if (it == HelpMessageType.OrganizationIsRejected) {
                    HelpMessageFragment
                        .navigate(navController, bundler)
                        .toWithRejectReason(
                            actionId = R.id.organizationStatusHelpFragment,
                            type = it,
                            rejectReason = organization.rejectComment
                        )
                } else {
                    HelpMessageFragment
                        .navigate(navController, bundler)
                        .to(
                            actionId = R.id.organizationStatusHelpFragment,
                            type = it
                        )
                }
            }
        }
    }
}
