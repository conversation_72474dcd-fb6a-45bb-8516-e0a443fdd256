package ru.dobro.main.profile.comments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.Toolbar
import androidx.navigation.fragment.findNavController
import arrow.core.toOption
import com.google.android.material.appbar.AppBarLayout
import common.library.android.dimension.dp
import common.library.android.inflateBy
import common.library.android.intent.bundler.Bundler
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleErrorMessages
import common.library.android.rx.throttleUserInput
import common.library.android.widget.gone
import common.library.android.widget.isGoneVisible
import common.library.android.widget.recycler_view.addSpacingItemDecoration
import common.library.android.widget.visible
import common.library.core.logging.logWarning
import common.library.core.resource.ResourcesScope
import common.library.core.rx.RxSchedulers
import common.library.core.rx.ofSubtype
import common.library.core.state.load.LoadDataPlainState
import common.library.core.state.load.handlePlainLoading
import common.library.core.variable.Agent
import common.library.core.variable.agent
import common.library.core.variable.sendWhen
import io.reactivex.Observable
import io.reactivex.ObservableSource
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.toPersistentList
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.R
import ru.dobro.api.DobroApi
import ru.dobro.api.comments.AwaitingReviewVacancy
import ru.dobro.api.comments.PostedVacancyReview
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.RequestCodes
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.error.ErrorMessages
import ru.dobro.databinding.MainProfileCommentsBinding
import ru.dobro.domain.VacancyId
import ru.dobro.domain.vacancy.Vacancy
import ru.dobro.main.profile.comments.adapters.AwaitingReviewVacanciesAdapter
import ru.dobro.main.profile.comments.adapters.PostedVacanciesReviewAdapter
import ru.dobro.main.profile.comments.dialog.AddVacancyReview
import ru.dobro.main.vacancy.VacancyFragment
import javax.annotation.CheckReturnValue

class CommentsFragment : BaseFragment({
    bind<AwaitingReviewVacanciesAdapter>() with singleton { AwaitingReviewVacanciesAdapter(instance()) }
    bind<PostedVacanciesReviewAdapter>() with singleton { PostedVacanciesReviewAdapter(instance()) }
    bind<Agent<CommentsState>>() with singleton { agent(CommentsState.Idle) }
}), HasCustomToolbar {
    override val screenName: String = AnalyticsConstants.Screen.Main.profileComments
    private lateinit var binding: MainProfileCommentsBinding
    private val _state: Agent<CommentsState> by instance()
    private val _api: DobroApi by instance()

    private val itemsVerticalSpacing = 8.dp

    private val _requestCodeSendRate: String =
        RequestCodes.scopedBy<CommentsFragment>("send_comment")
    private val _bundler: Bundler by instance()

    /**
     * Adapters
     */
    private val _awaitingReviewVacanciesAdapter: AwaitingReviewVacanciesAdapter by instance()
    private val _postedVacanciesReviewAdapter: PostedVacanciesReviewAdapter by instance()

    init {
        setHasOptionsMenu(true)
    }

    /**
     * LIFECYCLE
     */
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar {
        return object : HasCustomToolbar.CustomToolbar.Layout() {
            @CheckReturnValue
            override fun onCreateView(container: ViewGroup): View =
                container.context.inflateBy(R.layout.application___toolbar__white, container)

            @CheckReturnValue
            override fun getAppBarLayout(view: View): AppBarLayout? = null

            @CheckReturnValue
            override fun getToolbar(view: View): Toolbar {
                return view.findViewById(R.id.application___toolbar)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = MainProfileCommentsBinding.inflate(inflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        bindLayoutViews()
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        /**
         * Начальная загрузка данных
         */
        initData()

        /**
         * ЗАГРУЗКА ДАННЫХ
         */
        /**
         * Отоброжение индикатора загрузки
         */
        showIfLoading()

        /**
         * Загрузка готовых к комментированию событий
         */
        loadReadyToRateEvents()

        /**
         * Загрузка опубликованных комментариев
         */
        loadPostedComments()

        /**
         * Обрабатываем ошибку загрузки
         */
        onLoadFailed()

        /**
         * ОБНОВЛЕНИЕ СПИСКА
         */
        /**
         * Обновляем список readyToRate
         */
        updateReadyToRateEventsList()

        /**
         * Обновляем список postedComments
         */
        updatePostedCommentsList()

        /**
         * RECYCLER_VIEW ОБРАБОТКА НАЖАТИЯ
         */
        onReadyToRateEventsClicked()
        onPostedCommentClicked()

        /**
         * Ответ от bottomSheet и обновление списка
         */
        observeBottomSheetReturn()
    }

    private fun bindLayoutViews() {
        binding.eventsToRateList.apply {
            adapter = _awaitingReviewVacanciesAdapter
            addSpacingItemDecoration { it.verticalSpacing(itemsVerticalSpacing) }
        }
        binding.categoriesSelectorHelpTypesList.apply {
            adapter = _postedVacanciesReviewAdapter
            addSpacingItemDecoration { it.verticalSpacing(itemsVerticalSpacing) }
        }
    }

    /**
     * Initial
     */
    private fun ResourcesScope.initData() {
        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<CommentsState.Idle>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state
                    .sendWhen<CommentsState, CommentsState.Idle> {
                        requestDataLoading()
                    }
            }
            .scoped()
    }

    private fun ResourcesScope.showIfLoading() {
        _state
            .map {
                (it is CommentsState.DataLoading &&
                    (it.awaitingReviewVacanciesDataState is LoadDataPlainState.InProgress ||
                        it.postedVacanciesReviewDataState is LoadDataPlainState.InProgress))
            }
            .observeOn(RxSchedulers.main())
            .subscribeBy { binding.commentsSelectorProgress.isGoneVisible = it }
            .scoped()
    }

    /**
     * Loading
     */
    private fun ResourcesScope.loadReadyToRateEvents() {
        _state
            .handlePlainLoading<CommentsState, CommentsState.DataLoading,
                PersistentList<AwaitingReviewVacancy>>(
                toTaskState = { it.awaitingReviewVacanciesDataState },
                task = {
                    _api.profile()
                        .getAwaitingRateVacancy()
                        .logWarning(logger) { "Failed to load ready to rate events." }
                }, updateState = { updateReadyToRateEventsDataState(it) })
            .scoped()
    }

    private fun ResourcesScope.loadPostedComments() {
        _state
            .handlePlainLoading<CommentsState, CommentsState.DataLoading,
                PersistentList<PostedVacancyReview>>(
                toTaskState = { it.postedVacanciesReviewDataState },
                task = {
                    _api.profile()
                        .getPostedComments()
                        .logWarning(logger) { "Failed to load posted comments." }
                }, updateState = { updatePostedCommentsDataState(it) })
            .scoped()
    }

    /**
     * Loading failed
     */
    private fun ResourcesScope.onLoadFailed() {
        _state
            .observeOn(RxSchedulers.computation())
            .distinctUntilChanged { state -> state::class }
            .ofSubtype<CommentsState.DataLoading>()
            .map {
                ErrorMessages.ofException(
                    (it.awaitingReviewVacanciesDataState as? LoadDataPlainState.Idle.Failed)?.error
                        ?: (it.postedVacanciesReviewDataState as? LoadDataPlainState.Idle.Failed)?.error
                ).toOption()
            }
            .doOnNext { _state.sendWhen<CommentsState, CommentsState.DataLoading> { handleAll() } }
            .throttleErrorMessages()
            .observeOn(RxSchedulers.main())
            .subscribeBy {}
            .scoped()
    }

    /**
     * Update list
     */
    private fun ResourcesScope.updateReadyToRateEventsList() {
        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<CommentsState.State>()
            .distinctUntilChanged { state -> state.readyToReview }
            .observeOn(RxSchedulers.main())
            .subscribeBy { state ->
                if (state.readyToReview.size == 0) {
                    binding.mainProfileMeRequestsCount.gone()
                    binding.mainCommentsEventsToRateEmpty.visible()
                    _awaitingReviewVacanciesAdapter.submitList(listOf())
                } else {
                    binding.mainCommentsEventsToRateEmpty.gone()
                    binding.mainProfileMeRequestsCount.visible()
                    binding.mainProfileMeRequestsCount.text = state.readyToReview.size.toString()

                    val awaitingReviewVacancyAndVacancy =
                        mutableListOf<Pair<AwaitingReviewVacancy, Vacancy>>()
                    state.readyToReview.getAwaitingReviewVacancyAndVacancyObservable()
                        ?.observeOn(RxSchedulers.main())
                        ?.doOnComplete {
                            _awaitingReviewVacanciesAdapter.submitList(
                                awaitingReviewVacancyAndVacancy
                                    .sortedBy { it.second.identity.unwrap() }
                                    .map { pair ->
                                        AwaitingReviewVacanciesAdapter.VacancyItem(
                                            AwaitingReviewVacanciesAdapter.Item(
                                                pair.second,
                                                pair.first
                                            )
                                        )
                                    }.toPersistentList()
                            )
                        }
                        ?.subscribe {
                            awaitingReviewVacancyAndVacancy.add(it)
                        }?.scoped()
                }
            }
            .scoped()
    }

    private fun ResourcesScope.updatePostedCommentsList() {
        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<CommentsState.State>()
            .distinctUntilChanged { state -> state.postedComments }
            .observeOn(RxSchedulers.main())
            .subscribeBy { state ->
                if (state.postedComments.size == 0) {
                    binding.mainCommentsPostedEventsEmpty.visible()
                    _postedVacanciesReviewAdapter.submitList(listOf())
                } else {
                    binding.mainCommentsPostedEventsEmpty.gone()
                    val postedVacancyReviewAndVacancy =
                        mutableListOf<Pair<PostedVacancyReview, Vacancy>>()
                    state.postedComments.getVacancyReviewAndVacancyObservable()
                        ?.observeOn(RxSchedulers.main())
                        ?.doOnComplete {
                            _postedVacanciesReviewAdapter.submitList(
                                postedVacancyReviewAndVacancy
                                    .sortedBy { it.second.identity.unwrap() }
                                    .map { pair ->
                                        PostedVacanciesReviewAdapter.CommentItem(
                                            PostedVacanciesReviewAdapter.Item(
                                                pair.second,
                                                pair.first
                                            )
                                        )
                                    }.toPersistentList()
                            )
                        }
                        ?.subscribe {
                            postedVacancyReviewAndVacancy.add(it)
                        }?.scoped()
                }
            }
            .scoped()
    }

    /**
     * Load vacancy and event
     */
    private fun PersistentList<AwaitingReviewVacancy>.getAwaitingReviewVacancyAndVacancyObservable(): Observable<Pair<AwaitingReviewVacancy, Vacancy>>? {
        return Observable.just(this)
            .flatMap { Observable.fromIterable(it) }
            .flatMap {
                getAwaitingReviewVacancyAndVacancy(it)
            }
    }

    private fun getAwaitingReviewVacancyAndVacancy(awaitingReviewVacancy: AwaitingReviewVacancy): ObservableSource<out Pair<AwaitingReviewVacancy, Vacancy>> {
        return _api.vacancy()
            .getVacancyAsync(VacancyId.restore(awaitingReviewVacancy.vacancy.parseVacancyId()))
            .map { awaitingReviewVacancy to it }
            .toObservable()
    }

    private fun PersistentList<PostedVacancyReview>.getVacancyReviewAndVacancyObservable(): Observable<Pair<PostedVacancyReview, Vacancy>>? {
        return Observable.just(this)
            .flatMap { Observable.fromIterable(it) }
            .flatMap { getVacancyReviewAndVacancy(it) }
    }

    private fun getVacancyReviewAndVacancy(postedVacancyReview: PostedVacancyReview): ObservableSource<out Pair<PostedVacancyReview, Vacancy>> {
        return _api.vacancy()
            .getVacancyAsync(VacancyId.restore(postedVacancyReview.vacancy.parseVacancyId()))
            .map { postedVacancyReview to it }
            .toObservable()
    }

    /**
     * List click events
     */
    private fun ResourcesScope.onReadyToRateEventsClicked() {
        _awaitingReviewVacanciesAdapter
            .onItemClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                AddVacancyReview.createDialog(
                    _requestCodeSendRate,
                    _bundler,
                    it.item.vacancy.identity
                ).show(parentFragmentManager, null)
            }
            .scoped()

        _awaitingReviewVacanciesAdapter
            .onCardClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                VacancyFragment
                    .navigate(findNavController(), _bundler)
                    .toVacancy(
                        R.id.action_commentsFragment_to_main__vacancy,
                        it.item.vacancy.identity
                    )
            }
            .scoped()
    }

    private fun ResourcesScope.onPostedCommentClicked() {
        _postedVacanciesReviewAdapter
            .onCardClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                VacancyFragment
                    .navigate(findNavController(), _bundler)
                    .toVacancy(
                        R.id.action_commentsFragment_to_main__vacancy,
                        it.item.vacancy.identity
                    )
            }
            .scoped()
    }

    /**
     * Send review return
     */
    private fun ResourcesScope.observeBottomSheetReturn() {
        AddVacancyReview
            .onResult(this@CommentsFragment, _requestCodeSendRate, _bundler)
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state.send { CommentsState.Idle.requestDataLoading() }
                loadReadyToRateEvents()
                loadPostedComments()
            }
            .scoped()
    }

    private fun String.parseVacancyId(): Long {
        return this.split('/').takeLast(1)[0].toLong()
    }
}
