package ru.dobro.main.dobro_centers.map

import android.Manifest
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.style.ForegroundColorSpan
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import android.widget.FrameLayout
import androidx.appcompat.app.AlertDialog
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSnapHelper
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SnapHelper
import com.yandex.mapkit.Animation
import com.yandex.mapkit.MapKitFactory
import com.yandex.mapkit.geometry.Point
import com.yandex.mapkit.map.CameraPosition
import com.yandex.mapkit.map.Cluster
import com.yandex.mapkit.map.ClusterListener
import com.yandex.mapkit.map.ClusterTapListener
import com.yandex.mapkit.map.ClusterizedPlacemarkCollection
import com.yandex.mapkit.map.IconStyle
import com.yandex.mapkit.map.MapObject
import com.yandex.mapkit.map.MapObjectCollection
import com.yandex.mapkit.map.MapObjectTapListener
import com.yandex.mapkit.map.PlacemarkMapObject
import com.yandex.runtime.image.ImageProvider
import common.library.android.coroutines.repeatOnCreate
import common.library.android.coroutines.repeatOnResume
import common.library.android.coroutines.safeLaunch
import common.library.android.coroutines.subscribeToFlow
import common.library.android.dialog.cancelButton
import common.library.android.dialog.dialog
import common.library.android.dialog.okButton
import common.library.android.dimension.dp
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.fromBundle
import common.library.android.intent.bundler.getParams
import common.library.android.message.MessageDisplay
import common.library.android.permissions.CheckPermissionFragmentManager
import common.library.android.permissions.PermissionsState
import common.library.android.permissions.checkPermissionManager
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.string.r
import common.library.android.string.rString
import common.library.android.string.rText
import common.library.android.string.span.spannedString
import common.library.android.widget.gone
import common.library.android.widget.isGoneVisible
import common.library.android.widget.recycler_view.addSpacingItemDecoration
import common.library.android.widget.visible
import common.library.core.Characters
import common.library.core.location.GeoPoint
import common.library.core.location.UserLocationProvider
import common.library.core.variable.ScreenState
import kotlinx.collections.immutable.PersistentList
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.flatMapMerge
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.sample
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.withContext
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.R
import ru.dobro.address_selector.AddressSelectorFragment
import ru.dobro.address_selector.Country
import ru.dobro.address_selector.CountryName
import ru.dobro.address_selector.UserGeoCoordinates
import ru.dobro.api.DobroApi
import ru.dobro.api.dobro_center.requests.PostServiceRequestBody
import ru.dobro.api.global.AddressResponse
import ru.dobro.api.global.ResponseItemsFeatures
import ru.dobro.api.overview.getAddressSuggestionLocationData
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.RequestCodes
import ru.dobro.core.account.AccountManager
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.isShimmerAnimated
import ru.dobro.databinding.FragmentServiceMapBinding
import ru.dobro.domain.Location
import ru.dobro.domain.MapLocation
import ru.dobro.domain.map.MapItem
import ru.dobro.domain.navigation.Service
import ru.dobro.login.onboarding.LocationPermissionState
import ru.dobro.login.onboarding.LocationPermissionState.Idle.checkPermissionsAndOpenIfGranted
import ru.dobro.login.onboarding.LocationPermissionState.Process.error
import ru.dobro.login.onboarding.LocationPermissionState.Process.get
import ru.dobro.login.onboarding.LocationPermissionState.Ready.start
import ru.dobro.main.dobro_centers.map.adapter.MapItemAdapter
import ru.dobro.main.dobro_centers.map.adapter.MapItemDelegate
import ru.dobro.main.dobro_centers.map.dialog.ServiceMapConfirmDialog
import ru.dobro.main.events.events_map.MapClusterImageProvider
import java.util.Locale
import java.util.Random
import java.util.concurrent.TimeoutException
import javax.annotation.CheckReturnValue
import kotlin.time.Duration.Companion.milliseconds

class ServiceMapFragment(
    override val screenName: String = AnalyticsConstants.Screen.Main.eventsMap
) : BaseFragment({
    bind<ScreenState<ServiceMapState>>() with singleton { ScreenState(ServiceMapState.Idle) }
}), HasCustomToolbar, ClusterListener, ClusterTapListener, MapObjectTapListener, MapItemDelegate {

    private val state: ScreenState<ServiceMapState> by instance()

    private val accountManager: AccountManager by instance()
    private val bundler: Bundler by instance()
    private val messageDisplay: MessageDisplay by instance()
    private val userLocationProvider: UserLocationProvider by instance()

    private val api: DobroApi by instance()

    private lateinit var binding: FragmentServiceMapBinding

    private var adapter: MapItemAdapter? = null
    private var snapHelper: SnapHelper? = null

    private var clusterizedCollection: ClusterizedPlacemarkCollection? = null
    private var selectedPointsCollection: MapObjectCollection? = null

    private val imageUnselectedProvider by lazy {
        ImageProvider.fromResource(
            requireContext(),
            R.drawable.img_map_pin
        )
    }

    private val imageSelectedProvider by lazy {
        ImageProvider.fromResource(
            requireContext(),
            R.drawable.img_map_pin_selected
        )
    }

    private val defaultInitLocation by lazy { Location.restorePopularCities().first() }

    private val requestCodeSelectAddress: String =
        RequestCodes.scopedBy<ServiceMapFragment>("select_address")

    private val requestCodeConfirmSelected: String =
        RequestCodes.scopedBy<ServiceMapFragment>("confirm_selected")

    private val locationRequestCode: Int = RequestCodes.permission()
    private val locationPermissionFromSettingsRequestCode: Int = RequestCodes.result()

    private var service: Service? = null

    @CheckReturnValue
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        HasCustomToolbar.CustomToolbar.None

    init {
        setHasOptionsMenu(false)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = FragmentServiceMapBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onStop() {
        binding.map.onStop()
        MapKitFactory.getInstance().onStop()
        super.onStop()
    }

    override fun onStart() {
        super.onStart()
        MapKitFactory.getInstance().onStart()
        binding.map.onStart()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        MapKitFactory.initialize(requireContext())

        service = arguments?.getParams(bundler)
        if (service == null) return
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        selectedPointsCollection = binding.map.map.addMapObjectLayer("selectedPointsCollection")
        adapter = MapItemAdapter().apply {
            attachDelegate(this@ServiceMapFragment)
        }
        snapHelper = snapHelper()
        metricManager.sendSimpleEvent(AnalyticsConstants.Screen.showScreen + screenName)

        clusterizedCollection = binding.map.map.mapObjects.addClusterizedPlacemarkCollection(this)

        binding.apply {
            back.setOnClickListener { findNavController().navigateUp() }
            skeleton.shimmer.isGoneVisible = true
            skeleton.shimmer.isShimmerAnimated = true

            title.text = requireContext().spannedString {
                it.append("Выберите Добро.Центр на карте")
                    .append(Characters.asterisk)
                    .spans { ForegroundColorSpan(requireContext().getColor(R.color.application___color__accent)) }
            }
            mapItems.adapter = adapter
            mapItems.addSpacingItemDecoration { it.horizontalSpacing(8.dp) }
            mapItems.hasFixedSize()
            snapHelper?.attachToRecyclerView(binding.mapItems)
            content.isGoneVisible = false
            progress.isGoneVisible = true
            selectFromList.setOnClickListener {
                state.sendWhen<ServiceMapState.Processing> { switchBottomSheetState() }
            }
            close.setOnClickListener {
                state.sendWhen<ServiceMapState.Processing> { switchBottomSheetState() }
            }
            select.setOnClickListener {
                getCurrentProcessingState()?.locations?.firstOrNull { it.location.second.isSelected }
                    ?.let {
                        ServiceMapConfirmDialog
                            .navigate(findNavController(), bundler)
                            .toServiceMapConfirm(
                                it.location.second.item,
                                requestCodeConfirmSelected
                            )
                    } ?: also {
                    messageDisplay.showMessage("Выберите Добро.Центр".r)
                }
            }
            settlement.setOnClickListener {
                AddressSelectorFragment.createSettlement(
                    requestKey = requestCodeSelectAddress,
                    country = Country.Special(CountryName.Russia.title),
                    shouldAskLocationPermission = false
                )
                    .show(parentFragmentManager, null)
            }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<ServiceMapState.Idle>()
                .subscribeToFlow { state ->
                    <EMAIL><ServiceMapState.Idle> {
                        init(null, null)
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<ServiceMapState.Processing>()
                .onEach {
                    delay(200)
                }
                .subscribeToFlow {
                    state.sendWhen<ServiceMapState.Processing> {
                        updateLocationPermissionsState {
                            when (this) {
                                is LocationPermissionState.Idle -> {
                                    checkPermissionsAndOpenIfGranted(checkPermissionManager)
                                }

                                is LocationPermissionState.PermissionsCheck -> {
                                    updatePermissionsState { resetExplained() }
                                }

                                is LocationPermissionState.Ready -> {
                                    start()
                                }

                                else -> {
                                    this
                                }
                            }
                        }
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .handleLoadingBySelectorFlow<ServiceMapState.GetSettlementProcessing,
                    PersistentList<AddressResponse>, UserGeoCoordinates>(
                    scope = this,
                    toTaskState = { it.state },
                    task = {
                        api.overview().getAddressSuggestionLocationData(it.coordinates)
                    },
                    onError = {
                        messageDisplay.showMessage("Не удалось загрузить данные".r)
                        logger.warning { "Ошибка поиска адреса." }
                    },
                    onUpdateState = { updateTask(it) }
                )
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<ServiceMapState.Processing>()
                .map { it.locationPermissionState }
                .filterIsInstance<LocationPermissionState.Process>()
                .filter {
                    ActivityCompat.checkSelfPermission(
                        requireContext(),
                        Manifest.permission.ACCESS_FINE_LOCATION
                    ) == PackageManager.PERMISSION_GRANTED
                        && ActivityCompat.checkSelfPermission(
                        requireContext(),
                        Manifest.permission.ACCESS_COARSE_LOCATION
                    ) == PackageManager.PERMISSION_GRANTED
                }
                .flatMapMerge { userLocationProvider.requestCurrentLocation().asFlow() }
                .distinctUntilChangedBy { location -> location::class }
                .subscribeToFlow { locationRequestResult ->
                    fun onFailure(error: Throwable) {
                        accountManager.getUserLocation()?.let { location ->
                            state.sendWhen<ServiceMapState.Processing> {
                                updateSettlement(location)
                            }
                        } ?: run {
                            when (error) {
                                is TimeoutException -> {
                                    messageDisplay.showMessage(
                                        R.string.login__onboarding___location__auto__timeout.rString
                                    )
                                }

                                else -> {
                                    messageDisplay.showMessage(
                                        R.string.login__onboarding___location__auto__provider_error.rString
                                    )
                                }
                            }

                            state.sendWhen<ServiceMapState.Processing> {
                                updateLocationPermissionsState {
                                    when (this) {
                                        is LocationPermissionState.Process -> {
                                            error()
                                        }

                                        else -> this
                                    }
                                }
                            }
                        }
                    }

                    locationRequestResult.fold(
                        onSuccess = { location ->
                            location.first?.let {
                                updateLocationPermissionState(it)
                            } ?: onFailure(TimeoutException())
                        },
                        onFailure = { error ->
                            onFailure(error)
                        }
                    )
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<ServiceMapState.Completed>()
                .subscribeToFlow {
                    when (it) {
                        is ServiceMapState.Completed.Success -> {
                            accountManager.setUserLocation(it.settlementLocation)
                            state.sendWhen<ServiceMapState.Completed.Success> {
                                backToProcessing()
                            }
                            state.sendWhen<ServiceMapState.Processing> {
                                updateSettlement(it.settlementLocation)
                            }
                        }

                        is ServiceMapState.Completed.Failed -> {
                            if (it.error !== null) {
                                messageDisplay.showMessage(it.error)
                            }

                            state.sendWhen<ServiceMapState.Completed.Failed> {
                                backToProcessing()
                            }
                        }
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<ServiceMapState.Processing>()
                .distinctUntilChangedBy { processing -> processing.bottomSheetState }
                .subscribeToFlow { state ->
                    observeBottomSheetState(state)
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<ServiceMapState.Processing>()
                .distinctUntilChangedBy { processing -> processing.locations }
                .subscribeToFlow { state ->
                    val selectedPosition =
                        state.locations?.indexOfFirst { it.location.second.isSelected }

                    adapter?.submitList(state.locations?.map { it.location.second }) {
                        if ((state.bottomSheetState as? BottomSheetState.Collapsed)?.shouldScrollToBegin == true) {
                            binding.mapItems.scrollToPosition(0)
                            <EMAIL><ServiceMapState.Processing> {
                                updateBottomSheetState(
                                    state.bottomSheetState.copy(
                                        shouldScrollToBegin = false
                                    )
                                )
                            }
                        }
                        if (selectedPosition != -1 && selectedPosition != null) {
                            binding.mapItems.smoothScrollToPosition(selectedPosition)
                        }
                        binding.progress.gone()
                        binding.content.visible()
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<ServiceMapState.Processing>()
                .distinctUntilChangedBy { processing ->
                    processing.selectedPoint?.location?.second
                }
                .mapNotNull { it.selectedPoint }
                .subscribeToFlow { selectedPoint ->
                    selectedPoint.location.second.item.value.settlement?.coordinates?.let { coordinates ->
                        val newObject = selectIcon(
                            selectedPoint.location.first,
                            coordinates,
                            selectedPoint.location.second.item.value.remoteId
                        )
                        state.sendWhen<ServiceMapState.Processing> {
                            updateSelectedMapObject(
                                selectedPoint.location.second.item.value.remoteId,
                                newObject
                            )
                        }
                        moveCameraTo(coordinates, 10f)
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<ServiceMapState.Processing>()
                .distinctUntilChangedBy { processing -> processing.settlement }
                .filter { it.cardReloading }
                .mapNotNull { it.settlement }
                .sample(500.milliseconds)
                .subscribeToFlow { location ->
                    binding.settlement.text = location.settlement
                    binding.content.gone()
                    binding.progress.visible()
                    moveCameraTo(location.coordinates, 10f)
                    loadServicePoints(service!!.value.serviceId, location)
                }
        }

        parentFragmentManager.setFragmentResultListener(
            requestCodeSelectAddress,
            viewLifecycleOwner
        ) { _, bundle ->
            bundler.fromBundle<Location>(bundle)?.let { result ->
                getCurrentProcessingState()?.selectedPoint?.let { selectedPoint ->
                    selectedPoint.location.second.item.value.settlement?.coordinates?.let { coordinates ->
                        unselectIcon(
                            selectedPoint.location.first,
                            coordinates,
                            selectedPoint.location.second.item.value.remoteId
                        )
                    }
                }
                state.sendWhen<ServiceMapState.Processing> {
                    updateSettlement(result)
                }
            }
        }

        parentFragmentManager.setFragmentResultListener(
            requestCodeConfirmSelected,
            viewLifecycleOwner
        ) { _, bundle ->
            bundler.fromBundle<MapItem>(bundle)?.let { result ->
                val postServiceRequestBody = PostServiceRequestBody(
                    formData = service!!.value.selectedFields,
                    volunteerId = accountManager.getInfo()!!.identity.unwrap(),
                    serviceId = service!!.value.serviceId,
                    dobroCenterId = result.value.remoteId
                )
                postRequest(postServiceRequestBody)
            }
        }
    }

    private fun View.setHeight(height: Int) {
        layoutParams.height = pxFromDp(requireContext(), height.toFloat()).toInt()
        requestLayout()
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        handleLocationPermissions()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            locationRequestCode -> {
                state.sendWhen<ServiceMapState.Processing> {
                    updateLocationPermissionsState {
                        if (this is LocationPermissionState.PermissionsCheck) {
                            updatePermissionsState {
                                handleResponse(
                                    checkPermissionManager,
                                    permissions,
                                    grantResults
                                )
                            }
                        } else {
                            this
                        }
                    }
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            locationPermissionFromSettingsRequestCode ->
                state.sendWhen<ServiceMapState.Processing> {
                    updateLocationPermissionsState {
                        if (this is LocationPermissionState.PermissionsCheck) {
                            updatePermissionsState {
                                handleResponse(
                                    checkPermissionManager,
                                    LocationPermissionState.Idle.locationPermissions.toTypedArray(),
                                    LocationPermissionState.Idle.locationPermissions.map {
                                        ContextCompat.checkSelfPermission(requireActivity(), it)
                                    }.toIntArray()
                                )
                            }
                        } else {
                            this
                        }
                    }
                }
        }
    }

    private fun CoroutineScope.loadServicePoints(serviceId: Long, location: Location) {
        safeLaunch(
            onError = {
                messageDisplay.showMessage("Не удалось загрузить информацию".r)
                logger.warning { "Failed to load service" }
            }
        ) {
            val result = api.dobroCenter()
                .getServiceMap(serviceId, location.coordinates.formatToQueryString())
            loadMapItems(result, location)
        }
    }

    private fun loadMapItems(
        service: ResponseItemsFeatures<PersistentList<MapLocation>>,
        settlement: Location
    ) {
        viewLifecycleOwner.repeatOnCreate(
            onError = {
                messageDisplay.showMessage("Не удалось загрузить данные".r)
            }
        ) {
            val locations = mutableListOf<MapItemAdapter.MapItem>()
            service.items.features.forEach { mapLocation ->
                val mapItem = api.dobroCenter().getMapItem(mapLocation.data.id)
                locations.add(
                    MapItemAdapter.MapItem(
                        isSelected = false,
                        item = mapItem
                    )
                )
            }
            val locationMapObjects: List<MapItemPoint> = setupMap(locations)

            withContext(Dispatchers.Main) {
                state.sendWhen<ServiceMapState.Processing> {
                    reload(locationMapObjects, settlement)
                }
                state.sendWhen<ServiceMapState.Idle> {
                    init(locationMapObjects, settlement)
                }
            }

            if (service.items.features.size == 0) {
                binding.progress.gone()
                binding.content.visible()
            }
        }
    }

    private fun postRequest(postServiceRequestBody: PostServiceRequestBody) {
        viewLifecycleOwner.repeatOnResume(
            dispatcher = Dispatchers.IO,
            logger = logger,
            logProvider = { "Failed to post request" },
            onError = {
                messageDisplay.showMessage(it.message?.r ?: "Не удалось отправить заявку".r)
            }
        ) {
            api.dobroCenter().postServiceRequest(postServiceRequestBody)

            withContext(Dispatchers.Main) {
                findNavController().navigate(R.id.main__dobro_center_map_to_main__dobro_center_success)
            }
        }
    }

    private fun setupMap(location: List<MapItemAdapter.MapItem>): List<MapItemPoint> {
        val iconStyle = IconStyle()
        clusterizedCollection?.clear()
        val mapObjects: MutableList<MapItemPoint> = mutableListOf()
        return try {
            location.forEach { mapLocation ->
                val latitude = mapLocation.item.value.settlement?.coordinates?.latitude
                val longitude = mapLocation.item.value.settlement?.coordinates?.longitude

                if (latitude != null && longitude != null) {
                    val marker: PlacemarkMapObject = clusterizedCollection!!.addPlacemark(
                        Point(latitude, longitude),
                        imageUnselectedProvider, iconStyle
                    )
                    marker.userData = mapLocation.item.value.remoteId
                    mapObjects.add(MapItemPoint(10f, marker to mapLocation))
                }
            }
            clusterizedCollection?.clusterPlacemarks(60.0, 15)
            clusterizedCollection?.removeTapListener(this)
            clusterizedCollection?.addTapListener(this)
            mapObjects
        } catch (e: Exception) {
            // проверить есть ли exception
            emptyList()
        }
    }

    private fun observeBottomSheetState(state: ServiceMapState.Processing) {
        when (state.bottomSheetState) {
            is BottomSheetState.Expanded -> {
                binding.close.visible()
                binding.selectFromList.gone()
                binding.settlement.gone()
                binding.bottomSheet.setHeight(state.bottomSheetState.height)
                binding.mapItems.layoutManager = LinearLayoutManager(
                    requireContext(),
                    LinearLayoutManager.VERTICAL,
                    false
                )
                binding.mapItems.requestLayout()
                val selectedPosition =
                    state.locations?.indexOfFirst {
                        it.location.second.item.value.remoteId ==
                            state.selectedPoint?.location?.second?.item?.value?.remoteId
                    }
                binding.mapItems.post {
                    binding.apply {
                        if (selectedPosition != -1 && selectedPosition != null) {
                            Handler(Looper.getMainLooper()).postDelayed({
                                val offset = binding.mapItems.getChildAt(selectedPosition).top - 350
                                binding.scrollView.smoothScrollTo(0, offset)
                            }, 300)
                        }
                    }
                }
            }

            else -> {
                binding.close.gone()
                binding.selectFromList.visible()
                binding.settlement.visible()
                binding.bottomSheet.layoutParams =
                    FrameLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT, Gravity.BOTTOM)
                binding.mapItems.layoutManager = LinearLayoutManager(
                    requireContext(),
                    LinearLayoutManager.HORIZONTAL,
                    false
                )
                binding.mapItems.requestLayout()
                snapHelper?.attachToRecyclerView(binding.mapItems)
                val selectedPosition =
                    state.locations?.indexOfFirst {
                        it.location.second.item.value.remoteId ==
                            state.selectedPoint?.location?.second?.item?.value?.remoteId
                    }
                binding.mapItems.post {
                    binding.apply {
                        if (selectedPosition != -1 && selectedPosition != null) {
                            binding.mapItems.scrollToPosition(selectedPosition)
                        }
                    }
                }
            }
        }
    }

    private fun moveCameraTo(coordinates: GeoPoint.Coordinates, zoom: Float? = 10f) {
        binding.map.map.move(
            CameraPosition(
                Point(coordinates.latitude - 0.1, coordinates.longitude),
                zoom ?: binding.map.map.cameraPosition.zoom,
                0f,
                0f
            ),
            Animation(Animation.Type.LINEAR, 1f)
        ) {}
    }

    private fun getCurrentProcessingState(): ServiceMapState.Processing? {
        return state.value as? ServiceMapState.Processing
    }

    override fun onClusterAdded(cluster: Cluster) {
        cluster.appearance.setIcon(
            MapClusterImageProvider(requireContext(), Integer.toString(cluster.size))
        )
        cluster.addClusterTapListener(this)
    }

    override fun onClusterTap(p0: Cluster): Boolean {
        binding.map.map.move(
            CameraPosition(
                p0.appearance.geometry,
                binding.map.map.cameraPosition.zoom + 2,
                0f,
                0f
            ), Animation(Animation.Type.LINEAR, 1f), {})
        return true
    }

    override fun onMapObjectTap(mapObject: MapObject, point: Point): Boolean {
        if (mapObject !is PlacemarkMapObject) return true

        getCurrentProcessingState()?.selectedPoint?.let { selectedPoint ->
            selectedPoint.location.second.item.value.settlement?.coordinates?.let { coordinates ->
                unselectIcon(
                    selectedPoint.location.first,
                    coordinates,
                    selectedPoint.location.second.item.value.remoteId
                )
            }
        }
        (mapObject.userData as? Long)?.let { identity ->
            state.sendWhen<ServiceMapState.Processing> {
                itemSelected(identity, 10f)
            }
        }
        return true
    }

    /** [mapObject] - объект на который нажали, но он еще не выбран
     *  [coordinates] - координаты, куда поставить метку
     *  [identity] - идентификатор точки, который будет зашит в userData **/
    private fun selectIcon(
        mapObject: MapObject,
        coordinates: GeoPoint.Coordinates,
        identity: Long
    ): MapObject {
        try {
            clusterizedCollection?.remove(mapObject)
        } catch (e: Exception) {
            // Do nothing
        }
        val marker: PlacemarkMapObject = selectedPointsCollection!!.addPlacemark(
            Point(coordinates.latitude, coordinates.longitude),
            imageSelectedProvider
        )
        marker.userData = identity

        return marker
    }

    /** [mapObject] - объект который нужно удалить
     *  [coordinates] - координаты, куда поставить обычную метку
     *  [identity] - идентификатор новой точки, который будет зашит в userData **/
    private fun unselectIcon(
        mapObject: MapObject,
        coordinates: GeoPoint.Coordinates,
        identity: Long
    ) {
        val marker: PlacemarkMapObject = clusterizedCollection!!.addPlacemark(
            Point(coordinates.latitude, coordinates.longitude),
            imageUnselectedProvider
        )
        marker.userData = identity
        selectedPointsCollection?.clear()
        state.sendWhen<ServiceMapState.Processing> {
            updatedMapObject(identity, marker)
        }
    }

    private fun snapHelper(): LinearSnapHelper {
        return object : LinearSnapHelper() {
            override fun findTargetSnapPosition(
                layoutManager: RecyclerView.LayoutManager,
                velocityX: Int,
                velocityY: Int
            ): Int {
                val centerView: View =
                    findSnapView(layoutManager) ?: return RecyclerView.NO_POSITION
                val position: Int = layoutManager.getPosition(centerView)
                var targetPosition = -1
                if (layoutManager.canScrollHorizontally()) {
                    targetPosition = if (velocityX < 0) {
                        position - 1
                    } else {
                        position + 1
                    }
                }
                if (layoutManager.canScrollVertically()) {
                    targetPosition = if (velocityY < 0) {
                        position - 1
                    } else {
                        position + 1
                    }
                }
                val firstItem = 0
                val lastItem: Int = layoutManager.itemCount - 1
                targetPosition = lastItem.coerceAtMost(targetPosition.coerceAtLeast(firstItem))
                return targetPosition
            }
        }
    }

    override fun onItemClicked(mapItem: MapItemAdapter.MapItem) {
        getCurrentProcessingState()?.selectedPoint?.let { selectedPoint ->
            selectedPoint.location.second.item.value.settlement?.coordinates?.let { coordinates ->
                unselectIcon(
                    selectedPoint.location.first,
                    coordinates,
                    selectedPoint.location.second.item.value.remoteId
                )
            }
        }
        state.sendWhen<ServiceMapState.Processing> {
            itemSelected(mapItem.item.value.remoteId, 10f)
        }
    }

    private fun createTestPoints(count: Int): List<Point> {
        val points = ArrayList<Point>()
        val random = Random()
        for (i in 0 until count) {
            val clusterCenter = defaultInitLocation.coordinates
            val latitude = clusterCenter.latitude + Math.random() - 0.5
            val longitude = clusterCenter.longitude + Math.random() - 0.5
            points.add(
                Point(
                    String.format(Locale.getDefault(), "%.7f", latitude).toDouble(),
                    String.format(Locale.getDefault(), "%.7f", longitude).toDouble()
                )
            )
        }
        return points
    }

    private fun updateLocationPermissionState(location: android.location.Location) {
        try {
            state.sendWhen<ServiceMapState.Processing> {
                updateLocationPermissionsState {
                    when (this) {
                        is LocationPermissionState.Process -> {
                            get(
                                GeoPoint.Coordinates(
                                    location.latitude,
                                    location.longitude
                                )
                            )
                        }

                        else -> this
                    }
                }
            }
        } catch (_: Exception) {
        }
    }

    @CheckReturnValue
    private fun handleLocationPermissions() {
        var permissionDialog: Dialog? = null

        fun dismissPermissionDialog() {
            val dialog: Dialog? = permissionDialog
            if (dialog !== null) {
                dialog.dismiss()
            }
            permissionDialog = null
        }

        dismissPermissionDialog()

        viewLifecycleOwner.repeatOnResume {
            state
                .observe()
                .subscribeToFlow {
                    if (it is ServiceMapState.Processing &&
                        it.locationPermissionState is LocationPermissionState.PermissionsCheck
                    ) {
                        val locationPermissionState = it.locationPermissionState
                        val permissionsState: PermissionsState.NotAllGranted =
                            locationPermissionState.permissionsState

                        if (permissionsState.isAllDeniedExplained()) {
                            permissionsState.requestDeniedOnce(
                                checkPermissionManager,
                                locationRequestCode
                            )

                            dismissPermissionDialog()
                        } else {
                            if (permissionDialog == null) {
                                val dialog: AlertDialog = requireContext().dialog {
                                    it.message(R.string.location__dialog_text.rText)
                                        .okButton {
                                            val rationale =
                                                LocationPermissionState.Idle.locationPermissions.map { permission ->
                                                    CheckPermissionFragmentManager(this@ServiceMapFragment).shouldExplain(
                                                        permission
                                                    )
                                                }.any { equals(false) }

                                            if (rationale) {
                                                dismissPermissionDialog()

                                                permissionsState.requestDenied(
                                                    checkPermissionManager,
                                                    locationRequestCode
                                                )
                                            } else {
                                                dismissPermissionDialog()

                                                startActivityForResult(
                                                    Intent(
                                                        Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                                                        Uri.fromParts(
                                                            "package",
                                                            requireActivity().packageName,
                                                            null
                                                        )
                                                    ),
                                                    locationPermissionFromSettingsRequestCode
                                                )
                                            }
                                        }
                                        .cancelButton {
                                            safeLaunch(
                                                logger = logger,
                                                logProvider = { "Failed to load \"my\" profile." },
                                                onError = {
                                                    state.sendWhen<ServiceMapState.Processing> {
                                                        updateSettlement(accountManager.getUserLocation())
                                                    }
                                                }
                                            ) {
                                                val result = api.profile().getUserMe()

                                                state.sendWhen<ServiceMapState.Processing> {
                                                    updateSettlement(result.settlement)
                                                }
                                            }
                                        }
                                }

                                dialog.setOnDismissListener {
                                    locationPermissionState.updatePermissionsState { markAllExplained() }
                                }

                                dialog.show()

                                permissionDialog = dialog
                            }
                        }
                    } else {
                        dismissPermissionDialog()
                    }
                }
        }
    }

    private fun pxFromDp(context: Context, dp: Float): Float {
        return dp * context.resources.displayMetrics.density
    }
}
