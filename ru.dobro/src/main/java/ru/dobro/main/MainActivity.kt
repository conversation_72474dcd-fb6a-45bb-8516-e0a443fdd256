package ru.dobro.main

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.IdRes
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavOptions
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.bottomnavigation.BottomNavigationView
import common.library.android.FragmentLifecycleEvent
import common.library.android.inflateBy
import common.library.android.inputMethodManager
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.intent.tryStartActivityBy
import common.library.android.intent.view
import common.library.android.onFragmentLifecycleChanged
import common.library.android.resource.withOnCreateResourcesScope
import common.library.android.rx.throttleViewUpdates
import common.library.core.ifAllNotNull
import common.library.core.logging.logWarning
import common.library.core.orFalse
import common.library.core.rx.RxSchedulers
import common.library.core.rx.ofSubtype
import common.library.core.variable.Agent
import common.library.core.variable.ScreenState
import common.library.core.variable.agent
import common.library.mobile.services.InAppUpdatesProvider
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.rxkotlin.ofType
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.schedulers.Schedulers
import kotlinx.collections.immutable.PersistentSet
import kotlinx.collections.immutable.persistentSetOf
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.R
import ru.dobro.account_disabled.AccountDisabledFragment
import ru.dobro.api.DobroApi
import ru.dobro.app_update.AppUpdateRequestFragment
import ru.dobro.authorization.AuthorizationFragment
import ru.dobro.core.BottomNavigationClickHandler
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.RequestCodes
import ru.dobro.core.ScaffoldActivity
import ru.dobro.core.account.AccountInfo
import ru.dobro.core.account.AccountManager
import ru.dobro.core.account.AccountType
import ru.dobro.core.analytic.AnalyticManager
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.metric.MetricManager
import ru.dobro.core.notifications.NotificationInfo
import ru.dobro.core.settings.Settings
import ru.dobro.databinding.MainContentBinding
import ru.dobro.domain.EventId
import ru.dobro.domain.OrganizationId
import ru.dobro.domain.OrganizerId
import ru.dobro.domain.UserId
import ru.dobro.domain.VacancyId
import ru.dobro.main.event.EventFragment
import ru.dobro.main.events.events_search.EventsSearchMapState
import ru.dobro.main.notifications.initialiseNotificationsCount
import ru.dobro.main.notifications_settings.initNotificationsBounds
import ru.dobro.main.organization.OrganizationRequest
import ru.dobro.main.profile.ProfileFragment
import ru.dobro.main.profile.VolunteerBookFragment
import ru.dobro.main.profile.friends.FriendsFragment
import ru.dobro.main.requests.RequestsFilterState
import ru.dobro.main.vacancy.VacancyFragment
import ru.dobro.registration_social_network.SocialNetworkRegistrationFragment
import ru.dobro.reset_password.NewPasswordFragment
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeParseException
import javax.annotation.CheckReturnValue

class MainActivity : ScaffoldActivity(
    R.id.main___navigation_host_container,
    contentId = R.layout.main___content,
    bottomNavigationConfig = BottomNavigationConfig(
        bottomNavigationId = R.layout.main___bottom_content,
        tabsGraphId = R.navigation.main
    ),
    dependency = {
        bind<Agent<RequestsFilterState>>() with singleton { agent(RequestsFilterState.Idle) }
        bind<Agent<EventsSearchMapState>>() with singleton { agent(EventsSearchMapState.Idle) }
        bind<ScreenState<EventsSearchMapState>>() with singleton { ScreenState(EventsSearchMapState.Idle) }
    }
) {
    private lateinit var binding: MainContentBinding
    private val _accountManager: AccountManager by instance()

    private val _bundler: Bundler by instance()

    private val _settings: Settings by instance()

    private val firebaseAnalyticManager: AnalyticManager by instance()

    private val metricManager: MetricManager by instance()

    private val _inAppUpdatesProvider: InAppUpdatesProvider by instance()

    private val _api: DobroApi by instance()

    private val _requestCodeAppUpdate: String =
        RequestCodes.scopedBy<MainActivity>("app_update")

    fun getAccountManager(): AccountManager = _accountManager

    override val defaultCustomToolbar: HasCustomToolbar = object : HasCustomToolbar {
        @CheckReturnValue
        override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
            object : HasCustomToolbar.CustomToolbar.Layout() {
                @CheckReturnValue
                override fun onCreateView(container: ViewGroup): View =
                    container.context.inflateBy(R.layout.main___toolbar, container)

                @CheckReturnValue
                override fun getAppBarLayout(view: View): AppBarLayout? =
                    view.findViewById(R.id.main___toolbar__app_bar)

                @CheckReturnValue
                override fun getToolbar(view: View): Toolbar =
                    view.findViewById(R.id.main___toolbar)
            }
    }

    override val topLevelDestinationIds: PersistentSet<Int> = persistentSetOf(
        R.id.main__overview,
        R.id.main__notifications,
        R.id.main__profile_me,
        R.id.main__dobro_center,
        R.id.profileMeOrganizationFragment,
        R.id.main__about
    )

    override val bottomNavigationTabDestinationIds by lazy {
        listOf(
            R.id.main__overview,
            R.id.main__notifications,
            R.id.main__posts__or__dobro__center,
            R.id.main__about,
            R.id.main__profile_me,
        )
    }

    private var bottomNavigationCurrentTab: Int = 0
    private var bottomNavigationPreviousTab: Int = 0

    private val _pendingRefreshDestinationIds = mutableSetOf<Int>()

    private var _postMenuItemTimesClick = 0

    private var lastOpenedDeeplink: Intent? = null

    private var previousDestinationId: Int? = null

    @CheckReturnValue
    private fun _isTopLevelDestination(@IdRes id: Int): Boolean = id in topLevelDestinationIds

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        parseIntent(intent)
    }

    private var _bottomNavigationClickHandler: BottomNavigationClickHandler? = null

    private val _requestCodeAuthorization: String =
        RequestCodes.scopedBy<AuthorizationFragment>("authorization")

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        outState.putInt("bottomNavigationCurrentTab", bottomNavigationCurrentTab)
    }

    override fun onCreate(savedInstanceState: Bundle?) = withOnCreateResourcesScope {
        super.onCreate(savedInstanceState)

        if (savedInstanceState != null) {
            val savedCurrentTab = savedInstanceState.getInt("bottomNavigationCurrentTab", -1)
            bottomNavigationCurrentTab =
                savedCurrentTab.takeIf { it != -1 } ?: bottomNavigationTabDestinationIds[0]
        } else {
            bottomNavigationCurrentTab = bottomNavigationTabDestinationIds[0]
        }

        selectBottomNavigationTab(bottomNavigationCurrentTab)

        findViewById<BottomNavigationView>(R.id.main___bottom_navigation).setOnItemSelectedListener {
            when (it.itemId) {
                R.id.main__overview -> {
                    if (bottomNavigationCurrentTab != bottomNavigationTabDestinationIds[0]) {
                        selectBottomNavigationTab(
                            bottomNavigationTabDestinationIds[0]
                        )
                    }
                    dropCurrentTabBackStackIfNeeded(it.itemId)

                    _accountManager.getInfo()?.identity?.let { userId ->
                        initialiseNotificationsCount(
                            userId = userId,
                            activity = this@MainActivity
                        )
                    }

                    true
                }

                R.id.main__notifications -> {
                    if (bottomNavigationCurrentTab != bottomNavigationTabDestinationIds[1]) {
                        selectBottomNavigationTab(
                            bottomNavigationTabDestinationIds[1]
                        )
                    }
                    dropCurrentTabBackStackIfNeeded(it.itemId)

                    hideNotificationsCountBadge()

                    true
                }

                R.id.main__posts__or__dobro__center -> {
                    if (bottomNavigationCurrentTab != bottomNavigationTabDestinationIds[2]) {
                        selectBottomNavigationTab(
                            bottomNavigationTabDestinationIds[2]
                        )
                    }
                    dropCurrentTabBackStackIfNeeded(it.itemId)
                    _postMenuItemTimesClick += 1
                    if (_postMenuItemTimesClick >= 3) {
                        findViewById<BottomNavigationView>(R.id.main___bottom_navigation).removeBadge(
                            R.id.main__posts__or__dobro__center
                        )
                        _settings.setPostsBadgeShown(false)
                    }

                    _accountManager.getInfo()?.identity?.let { userId ->
                        initialiseNotificationsCount(
                            userId = userId,
                            activity = this@MainActivity
                        )
                    }

                    true
                }

                R.id.main__about -> {
                    if (bottomNavigationCurrentTab != bottomNavigationTabDestinationIds[3]) {
                        selectBottomNavigationTab(
                            bottomNavigationTabDestinationIds[3]
                        )
                    }
                    dropCurrentTabBackStackIfNeeded(it.itemId)

                    _accountManager.getInfo()?.identity?.let { userId ->
                        initialiseNotificationsCount(
                            userId = userId,
                            activity = this@MainActivity
                        )
                    }

                    true
                }

                R.id.main__profile_me -> {
                    if (bottomNavigationCurrentTab != bottomNavigationTabDestinationIds[4]) {
                        selectBottomNavigationTab(
                            bottomNavigationTabDestinationIds[4]
                        )
                    }
                    dropCurrentTabBackStackIfNeeded(it.itemId)

                    _accountManager.getInfo()?.identity?.let { userId ->
                        initialiseNotificationsCount(
                            userId = userId,
                            activity = this@MainActivity
                        )
                    }

                    true
                }

                else -> {
                    false
                }
            }
        }

        findViewById<BottomNavigationView>(R.id.main___bottom_navigation).setOnItemReselectedListener {
            when (it.itemId) {
                R.id.main__overview -> {
                    if (requireNavController().currentDestination?.id != R.id.main__overview) {
                        requireNavController().popBackStack(R.id.main__overview, false)
                    } else {
                        _bottomNavigationClickHandler?.onBottomNavigationClick()
                    }
                }

                R.id.main__notifications -> {
                    if (requireNavController().currentDestination?.id != R.id.main__notifications) {
                        requireNavController().popBackStack(R.id.main__notifications, false)
                    } else {
                        _bottomNavigationClickHandler?.onBottomNavigationClick()
                    }
                }

                R.id.main__posts__or__dobro__center -> {
                    if (requireNavController().currentDestination?.id != R.id.main__posts__or__dobro__center) {
                        requireNavController().popBackStack(
                            R.id.main__posts__or__dobro__center,
                            false
                        )
                    } else {
                        _bottomNavigationClickHandler?.onBottomNavigationClick()
                    }
                }

                R.id.main__about -> {
                    if (requireNavController().currentDestination?.id != R.id.main__about) {
                        requireNavController().popBackStack(R.id.main__about, false)
                    }
                }

                R.id.main__profile_me -> {
                    if (requireNavController().currentDestination?.id != R.id.main__profile_me) {
                        requireNavController().popBackStack(R.id.main__profile_me, false)
                    }
                }

                else -> {}
            }
        }

        updateBottomNav()
        binding = MainContentBinding.inflate(layoutInflater)

        parseIntent(intent)

        val accountInfo: AccountInfo? = _accountManager.getInfo()

        if (accountInfo == null) {
            logger.debug { "No account info." }

            _accountManager.removeAccount()
            hideNotificationsCountBadge()
        }

        // Логирование бэкстека навигации
        lifecycleScope.launch {
            requireNavController().currentBackStack.collectLatest {
                val currentFragment = it.map { it.destination.displayName }
                Log.d(
                    "NavigationListener",
                    "Navigation history: ${currentFragment.joinToString(" -> ")}"
                )
            }
        }

        supportFragmentManager
            .onFragmentLifecycleChanged(true)
            .ofSubtype<FragmentLifecycleEvent.Resumed>()
            .map { it.fragment }
            .ofType<BottomNavigationClickHandler>()
            .subscribeBy {
                _bottomNavigationClickHandler = it
            }
            .scopedByDestroy()

        if (!_settings.isAppUpdateShown()) {
            _inAppUpdatesProvider.getUpdates()
                .subscribeOn(Schedulers.single())
                .throttleViewUpdates()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeBy { updateInfo ->
                    if (updateInfo.isUpdateAvailable) {
                        AppUpdateRequestFragment
                            .create(
                                alias = updateInfo.alias,
                                resultCode = _requestCodeAppUpdate,
                                bundler = _bundler
                            )
                            .show(supportFragmentManager, null)
                    }
                    _settings.setAppUpdateChecked()
                }
                .scoped()
        }

        lifecycleScope.launch {
            initNotificationsBounds()
        }

        _settings.flags.setHomeFragmentShown(false)
        _settings.flags.setHomeLocationRequested(false)
        _settings.flags.setSearchFragmentShown(false)
        _settings.flags.setLocationSelectedByUser(false)

        _api.profile()
            .getUserMeAsync()
            .subscribeOn(RxSchedulers.computation())
            .logWarning(logger) { "Failed to load \"my\" profile." }
            .observeOn(RxSchedulers.main())
            .subscribe({
                if (it.statistic.friendsCount > 0) {
                    metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.doesHaveAnyFriend)
                    firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Main.doesHaveAnyFriend)
                }
            }, { })
            .scoped()

        _accountManager.getInfo()?.identity?.let { userId ->
            initialiseNotificationsCount(
                userId = userId,
                activity = this@MainActivity
            )
        }
    }

    fun updateBottomNav() {
        val bottomNavigation = findViewById<BottomNavigationView>(R.id.main___bottom_navigation)

        bottomNavigation.labelVisibilityMode = if (isOrganizerBottomMenu()) {
            BottomNavigationView.LABEL_VISIBILITY_UNLABELED
        } else {
            BottomNavigationView.LABEL_VISIBILITY_LABELED
        }

        val oldOverviewTabName = bottomNavigation.menu.getItem(0).title
        bottomNavigation.menu.getItem(0).apply {
            if (isOrganizerBottomMenu()) {
                icon = ContextCompat.getDrawable(this@MainActivity, R.drawable.main___crm__selector)
                title = getString(R.string.main___bottom_navigation__crm)
            } else {
                icon =
                    ContextCompat.getDrawable(this@MainActivity, R.drawable.main___home__selector)
                title = getString(R.string.main___bottom_navigation__home)
            }
        }
        val newOverviewTabName = bottomNavigation.menu.getItem(0).title
        if (
            oldOverviewTabName != newOverviewTabName &&
            bottomNavigation.selectedItemId != R.id.main__overview
        ) {
            scheduleBottomNavigationTabRefresh(R.id.main__overview)
        }

        val oldPostsOrDobroCenterTabName = bottomNavigation.menu.getItem(2).title
        bottomNavigation.menu.getItem(2).apply {
            if (isOrganizerBottomMenu()) {
                bottomNavigation.removeBadge(R.id.main__posts__or__dobro__center)

                icon = ContextCompat.getDrawable(
                    this@MainActivity,
                    R.drawable.main___dobro_center__selector
                )
                title = getString(R.string.main___bottom_navigation__dobro_center)
            } else {
                bottomNavigation.setupBadge()

                icon =
                    ContextCompat.getDrawable(this@MainActivity, R.drawable.main___posts__selector)
                title = getString(R.string.main___bottom_navigation__posts)
            }
        }
        val newPostsOrDobroCenterTabName = bottomNavigation.menu.getItem(2).title
        if (
            oldPostsOrDobroCenterTabName != newPostsOrDobroCenterTabName &&
            bottomNavigation.selectedItemId != R.id.main__posts__or__dobro__center
        ) {
            scheduleBottomNavigationTabRefresh(R.id.main__posts__or__dobro__center)
        }
    }

    private fun BottomNavigationView.setupBadge() {
        if (_settings.isPostsBadgeShown()) {
            val bottomNavigation = this
            val text = "Новое"
            val badge = bottomNavigation.getOrCreateBadge(R.id.main__posts__or__dobro__center)
            badge.isVisible = true
            badge.setTextAppearance(R.style.Application_Design_BottomMenu_TextAppearance)
            badge.text = text
            badge.badgeTextColor = ContextCompat.getColor(
                bottomNavigation.context,
                R.color.application___color__orange_variant
            )
            badge.backgroundColor = ContextCompat.getColor(
                bottomNavigation.context,
                R.color.application___color__orange_variant_2
            )

            val textAppearance = TextView(bottomNavigation.context).apply {
                setTextAppearance(R.style.Application_Design_BottomMenu_TextAppearance)
            }
            val textWidth = textAppearance.paint.measureText(text).toInt()
            badge.horizontalOffsetWithText = textWidth / 2
            badge.verticalOffset = 13
        }
    }

    fun setNotificationsCountBadge(count: Int) {
        val bottomNavigation = findViewById<BottomNavigationView>(R.id.main___bottom_navigation)
        val badge = bottomNavigation.getOrCreateBadge(R.id.main__notifications)
        if (count < 1) {
            badge.isVisible = false
            return
        }
        badge.isVisible = true
        badge.setTextAppearance(R.style.Application_Design_BottomMenu_TextAppearance)
        badge.text = count.takeIf { it < 100 }?.toString() ?: "99+"
        badge.badgeTextColor = ContextCompat.getColor(bottomNavigation.context, R.color.white)
        badge.backgroundColor =
            ContextCompat.getColor(bottomNavigation.context, R.color.application___color__primary)
        badge.horizontalOffsetWithText = 15
        badge.verticalOffset = 13
    }

    fun hideNotificationsCountBadge() {
        val bottomNavigation = findViewById<BottomNavigationView>(R.id.main___bottom_navigation)
        bottomNavigation.removeBadge(R.id.main__notifications)
    }

    fun isOrganizerBottomMenu(): Boolean {
        val isValidOrganization = when (val type = _accountManager.getAccountType()) {
            is AccountType.LegalOrganization -> true
            is AccountType.PhysicOrganization -> true
            AccountType.Physic -> false
        }
        return isValidOrganization
    }

    private var lastOpenedEventId: Long? = null
    private fun parseIntent(intent: Intent?) {
        if (intent == null) return

        val data = intent.data

        when (data?.scheme) {
            "app", "dobro" -> {
                ifAllNotNull(
                    data.getQueryParameter("remoteId"),
                    data.getQueryParameter("type"),
                    data.getQueryParameter("newUser").toBoolean()
                ) { remoteId, type, isNewUser ->
                    if (isNewUser) {
                        ifAllNotNull(
                            data.getQueryParameter("firstName"),
                            data.getQueryParameter("lastName")
                        ) { name, surname ->
                            val email = data.getQueryParameter("email")
                            val remoteToken = data.getQueryParameter("remoteToken")

                            val birthDaySource = data.getQueryParameter("birthday")

                            var birthdate: ZonedDateTime? = null

                            // часть соцсетей возвращает ZonedDateTime, часть - LocalDate
                            if (birthDaySource.isNullOrEmpty().not()) {
                                birthdate = try {
                                    ZonedDateTime.parse(birthDaySource)
                                } catch (e: DateTimeParseException) {
                                    val birthDateLocalDate: LocalDate =
                                        LocalDate.parse(birthDaySource)

                                    ZonedDateTime.of(
                                        birthDateLocalDate,
                                        LocalTime.now(),
                                        ZoneId.systemDefault()
                                    )
                                }
                            }

                            SocialNetworkRegistrationFragment
                                .navigate(
                                    requireNavController(),
                                    _bundler
                                )
                                .toRegistration(
                                    actionId = R.id.main__social_network_registration,
                                    firstName = name,
                                    lastName = surname,
                                    birthDate = birthdate,
                                    remoteId = remoteId,
                                    type = type,
                                    email = email,
                                    emailConfirmed = data.getQueryParameter("emailConfirmed")
                                        .toBoolean(),
                                    remoteToken = remoteToken
                                )
                        }
                    } else {
                        AuthorizationFragment
                            .navigate(
                                requireNavController(),
                                _bundler
                            )
                            .toAuthorizationBySocialNetwork(
                                R.id.authorization,
                                remoteId = remoteId,
                                type = type,
                                resultKey = _requestCodeAuthorization,
                                remoteToken = data.getQueryParameter("remoteToken"),
                            )
                    }
                }
            }

            "https" -> {
                lastOpenedDeeplink = intent

                if (data.host?.contains("dobro") == true
                    && data.pathSegments.contains("password")
                    && data.pathSegments.contains("reset")
                ) {
                    val token = data.pathSegments[data.pathSegments.size - 2]
                    val userId =
                        UserId.restore(data.pathSegments[data.pathSegments.size - 1].toLong())

                    NewPasswordFragment
                        .navigate(
                            requireNavController(),
                            _bundler
                        )
                        .toNewPassword(
                            R.id.reset_password__new_password,
                            token,
                            userId
                        )
                }
                if (data.host?.contains("dobro") == true
                    && data.pathSegments.contains("restore-account")
                ) {
                    AccountDisabledFragment
                        .navigate(requireNavController(), _bundler)
                        .toAccountDisabledWithRecreate(recreateActivity = true)
                }
            }
        }

        if (data?.pathSegments?.contains("vacancy").orFalse()) {
            data?.lastPathSegment?.toLongOrNull()?.let {
                setBottomNavigationSelectedItemId(R.id.main__overview)
                VacancyFragment.navigate(requireNavController(), _bundler).toVacancy(
                    actionId = R.id.main__vacancy,
                    vacancyId = VacancyId.restore(it),
                    fromDeepLink = true
                )
            }
        }

        if (data?.pathSegments?.contains("courses").orFalse()) {
            data?.lastPathSegment?.toLongOrNull()?.let { course ->
                val url = Uri.parse("https://edu.dobro.ru/courses/$course/")
                val bundle = bundleOf("URL" to url.toString())
                setBottomNavigationSelectedItemId(R.id.main__overview)
                requireNavController().navigate(
                    R.id.webView,
                    bundle
                )
            } ?: also {
                data?.let { uri ->
                    startActivity(Intent(Intent.ACTION_VIEW, uri))
                }
            }
        }

        if (data?.pathSegments?.contains("event").orFalse()) {
            val vacancyId = data?.fragment?.toLongOrNull()
            val eventId = data?.lastPathSegment?.toLongOrNull()
            when {
                vacancyId != null -> {
                    if (requireNavController().currentDestination?.id != R.id.main__vacancy) {
                        setBottomNavigationSelectedItemId(R.id.main__overview)
                        VacancyFragment.navigate(requireNavController(), _bundler)
                            .toVacancy(
                                actionId = R.id.main__vacancy,
                                vacancyId = VacancyId.restore(vacancyId),
                                fromDeepLink = true
                            )
                    }
                }

                eventId != lastOpenedEventId && eventId != null -> {
                    setBottomNavigationSelectedItemId(R.id.main__overview)
                    EventFragment.navigate(requireNavController(), _bundler).toEvent(
                        actionId = R.id.main__event,
                        id = EventId.restore(eventId),
                        fromDeepLink = true
                    )
                    lastOpenedEventId = eventId
                }
            }
        }

        if (data?.pathSegments?.contains("volunteers").orFalse()) {
            data?.lastPathSegment?.toLongOrNull()?.let {
                if (requireNavController().currentDestination?.id != R.id.public_profile) {
                    setBottomNavigationSelectedItemId(R.id.main__overview)
                    ProfileFragment.navigate(requireNavController(), _bundler).to(
                        actionId = R.id.public_profile,
                        id = UserId.restore(it),
                        requestKey = "",
                        fromDeepLink = true
                    )
                }
            }
        }

        if (data?.pathSegments?.contains("organizations").orFalse()) {
            data?.pathSegments?.get(1)?.toLongOrNull()?.let {
                if (requireNavController().currentDestination?.id != R.id.main__organization) {
                    setBottomNavigationSelectedItemId(R.id.main__overview)
                    requireNavController().navigate(
                        R.id.main__organization,
                        OrganizationRequest(
                            organizerId = OrganizerId.restore(0),
                            organizationId = OrganizationId.restore(it),
                            fromDeeplink = true
                        ).intoBundle(_bundler)
                    )
                }
            }
        }

        if (data?.pathSegments?.contains("resume").orFalse()) {
            data?.pathSegments?.get(1)?.toLongOrNull()?.let {
                if (requireNavController().currentDestination?.id != R.id.main__profile___volunteer_book) {
                    setBottomNavigationSelectedItemId(R.id.main__overview)
                    VolunteerBookFragment.navigate(requireNavController(), _bundler).to(
                        actionId = R.id.main__profile___volunteer_book,
                        id = UserId.restore(it),
                        fromDeepLink = true
                    )
                }
            }
        }

        if (data?.pathSegments?.contains("offervolunteer").orFalse()) {
            val bundle = bundleOf("URL" to data.toString())
            requireNavController().navigate(
                R.id.webView,
                bundle
            )
        }

        if (intent.getBooleanExtra("authorization", false)) {
            AuthorizationFragment
                .navigate(
                    requireNavController(),
                    _bundler
                )
                .toAuthorizationForResult(
                    actionId = R.id.authorization,
                    resultKey = _requestCodeAuthorization
                )
        }

        val notificationInfoToVacancy: NotificationInfo.ToVacancy? = intent.getParams(_bundler)
        if (notificationInfoToVacancy != null) {
            VacancyFragment
                .navigate(
                    requireNavController(),
                    _bundler
                )
                .toVacancy(
                    R.id.main__vacancy,
                    notificationInfoToVacancy.id
                )
        }

        val notificationInfoToEvent: NotificationInfo.ToEvent? = intent.getParams(_bundler)
        if (notificationInfoToEvent != null) {
            EventFragment
                .navigate(
                    requireNavController(),
                    _bundler
                )
                .toEvent(
                    R.id.main__event,
                    notificationInfoToEvent.id
                )
        }

        val notificationInfoToUserRequests: NotificationInfo.ToUserRequests? =
            intent.getParams(_bundler)
        if (notificationInfoToUserRequests != null) {
            requireNavController().navigate(R.id.main__requests)
        }

        val notificationInfoToBrowser: NotificationInfo.ToBrowser? = intent.getParams(_bundler)
        if (notificationInfoToBrowser != null) {
            tryStartActivityBy {
                it.view(notificationInfoToBrowser.url)
            }
        }

        val notificationInfoToFriendsRequests: NotificationInfo.ToFriendsRequests? =
            intent.getParams(_bundler)
        if (notificationInfoToFriendsRequests != null) {
            metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.friendRequestPushClick)
            firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Main.friendRequestPushClick)

            FriendsFragment
                .navigate(requireNavController(), _bundler)
                .toFriendsRequests(
                    actionId = R.id.friendsFragment
                )
        }
    }

    fun triggerLastOpenedDeeplink() {
        CoroutineScope(Dispatchers.Main).launch {
            delay(100)
            parseIntent(lastOpenedDeeplink)
        }
    }

    override fun onBottomNavigationTabSelected(tab: Int) {
        bottomNavigationPreviousTab = bottomNavigationCurrentTab
        bottomNavigationCurrentTab = tab
    }

    fun setBottomNavigationSelectedItemId(@IdRes id: Int) {
        val bottomNavigation = findViewById<BottomNavigationView>(R.id.main___bottom_navigation)
        if (bottomNavigationCurrentTab != id) {
            bottomNavigation.selectedItemId = id
        }
    }

    fun setBottomNavigationPreviousTab() {
        setBottomNavigationSelectedItemId(bottomNavigationPreviousTab)
    }

    fun scheduleBottomNavigationAllTabsRefresh() {
        _pendingRefreshDestinationIds.addAll(bottomNavigationTabDestinationIds)
        _pendingRefreshDestinationIds.remove(bottomNavigationCurrentTab)
    }

    fun scheduleBottomNavigationTabRefresh(@IdRes tab: Int) {
        _pendingRefreshDestinationIds.add(tab)
    }

    private fun dropCurrentTabBackStackIfNeeded(menuItemId: Int) {
        if (menuItemId in _pendingRefreshDestinationIds) {
            _pendingRefreshDestinationIds.remove(menuItemId)
            dropTabBackstack(menuItemId)
        }
        requireNavController().popBackStack(R.id.organizationStatusHelpFragment, true)
    }

    private fun dropTabBackstack(menuItemId: Int) {
        requireNavController().navigate(
            menuItemId,
            null,
            NavOptions.Builder().setPopUpTo(R.id.main_navigation, false).build()
        )
    }

    fun dropCurrentTabBackstack() {
        dropTabBackstack(bottomNavigationCurrentTab)
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        if (requireNavController().currentDestination?.id != R.id.authorization) {
            if (currentFocus != null) {
                inputMethodManager.hideSoftInputFromWindow(currentFocus!!.windowToken, 0)
            }
        }

        return super.dispatchTouchEvent(ev)
    }
}
