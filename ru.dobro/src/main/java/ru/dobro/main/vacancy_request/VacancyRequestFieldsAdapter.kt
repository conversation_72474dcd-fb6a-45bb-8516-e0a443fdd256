package ru.dobro.main.vacancy_request

import android.text.InputType
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import common.library.android.string.span.spannedString
import common.library.android.widget.gone
import common.library.android.widget.isGoneVisible
import common.library.core.Characters
import common.library.core.EqualityComparable
import common.library.core.EqualityContentComparable
import common.library.core.contract.ContractException
import common.library.core.contract.unreachable
import common.library.core.rx.invoke
import io.reactivex.Flowable
import io.reactivex.processors.PublishProcessor
import ru.dobro.R
import ru.dobro.core.setHtmlText
import ru.dobro.core.validation.Ui.makeAccent
import ru.dobro.core.validation.Ui.makeDefault
import ru.dobro.databinding.MainVacancyRequestCheckFieldBinding
import ru.dobro.databinding.MainVacancyRequestTextFieldBinding
import ru.dobro.domain.vacancy.VacancyRequestField
import ru.dobro.domain.vacancy.VacancyRequestFieldId
import ru.dobro.domain.vacancy.VacancyRequestFieldType

class VacancyRequestFieldsAdapter :
    ListAdapter<VacancyRequestFieldsAdapter.Item, VacancyRequestFieldsAdapter.ViewHolder>(diffUtils) {

    private val _onTextChanged: PublishProcessor<Pair<String, VacancyRequestFieldId>> =
        PublishProcessor.create()
    val onTextChanged: Flowable<Pair<String, VacancyRequestFieldId>> = _onTextChanged.hide()

    private val _onIntegerChanged: PublishProcessor<Pair<String, VacancyRequestFieldId>> =
        PublishProcessor.create()
    val onIntegerChanged: Flowable<Pair<String, VacancyRequestFieldId>> = _onIntegerChanged.hide()

    private val _onCheckboxClickListener: PublishProcessor<VacancyRequestFieldId> =
        PublishProcessor.create()
    val onCheckboxClickListener: Flowable<VacancyRequestFieldId> = _onCheckboxClickListener.hide()

    companion object {
        val diffUtils = object : DiffUtil.ItemCallback<Item>() {
            override fun areItemsTheSame(
                oldItem: Item,
                newItem: Item
            ): Boolean {
                return oldItem.hashCode() == newItem.hashCode()
            }

            override fun areContentsTheSame(
                oldItem: Item,
                newItem: Item
            ): Boolean {
                return oldItem == newItem
            }
        }
    }

    fun add(newItem: Item) {
        val updatedList = currentList.toMutableList()
        updatedList.add(newItem)
        submitList(updatedList)
        notifyDataSetChanged()
    }

    fun makeAccent(field: VacancyRequestField): Int {
        val position = currentList.indexOfFirst { it.field == field }
        if (position != -1) {
            currentList[position].isAccent = true
            notifyItemChanged(position)
        }
        return position
    }

    sealed class Item(
        open val field: VacancyRequestField,
        open var isAccent: Boolean
    ) : EqualityComparable<Item>, EqualityContentComparable<Item> {
        data class Text(
            override val field: VacancyRequestField,
            override var isAccent: Boolean = false,
            val data: String?
        ) : Item(field, isAccent) {
            override fun contentEqualTo(other: Item?): Boolean = equalTo(other)

            override fun contentHashCode(): Int = field.hashCode()

            override fun equalTo(other: Item?): Boolean {
                if (other !is Text) {
                    return false
                }

                return field.identity == other.field.identity
            }
        }

        data class Integer(
            override val field: VacancyRequestField,
            override var isAccent: Boolean = false,
            val data: Int?
        ) : Item(field, isAccent) {
            override fun contentEqualTo(other: Item?): Boolean = equalTo(other)

            override fun contentHashCode(): Int = field.hashCode()

            override fun equalTo(other: Item?): Boolean {
                if (other !is Integer) {
                    return false
                }

                return field.identity == other.field.identity
            }
        }

        data class Checkbox(
            override val field: VacancyRequestField,
            override var isAccent: Boolean = false,
            val isChecked: Boolean
        ) : Item(field, isAccent) {
            override fun contentEqualTo(other: Item?): Boolean = equalTo(other)

            override fun contentHashCode(): Int = field.hashCode()

            override fun equalTo(other: Item?): Boolean {
                if (other !is Checkbox) {
                    return false
                }

                return field.identity == other.field.identity
            }
        }
    }

    private fun _getItemViewType(item: Item?): VacancyRequestFieldType =
        when (item) {
            is Item.Text -> VacancyRequestFieldType.Text
            is Item.Integer -> VacancyRequestFieldType.Integer
            is Item.Checkbox -> VacancyRequestFieldType.Checkbox
            null -> throw ContractException("Unsupported view type")
        }

    override fun getItemViewType(position: Int): Int = _getItemViewType(getItem(position)).ordinal

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder =
        when (common.library.core.enumValueOf<VacancyRequestFieldType>(viewType)) {
            VacancyRequestFieldType.Text -> ViewHolder.TextViewHolder(
                binding = MainVacancyRequestTextFieldBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )

            VacancyRequestFieldType.Integer -> ViewHolder.IntegerViewHolder(
                binding = MainVacancyRequestTextFieldBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )

            VacancyRequestFieldType.Checkbox -> ViewHolder.CheckboxViewHolder(
                binding = MainVacancyRequestCheckFieldBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )

            else -> unreachable()
        }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        when (holder) {
            is ViewHolder.TextViewHolder -> holder.bind(
                item = getItem(position) as Item.Text,
                onTextChangedListener = _onTextChanged
            )

            is ViewHolder.IntegerViewHolder -> holder.bind(
                item = getItem(position) as Item.Integer,
                onTextChangedListener = _onIntegerChanged
            )

            is ViewHolder.CheckboxViewHolder -> holder.bind(
                item = getItem(position) as Item.Checkbox,
                onClickListener = _onCheckboxClickListener
            )
        }
    }

    sealed class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        class TextViewHolder(
            private val binding: MainVacancyRequestTextFieldBinding
        ) : ViewHolder(binding.root) {
            fun bind(
                item: Item.Text,
                onTextChangedListener: PublishProcessor<Pair<String, VacancyRequestFieldId>>
            ) = with(binding) {
                mainVacancyRequestFieldsTextTitle.text =
                    root.context.spannedString {
                        it.append(item.field.title)
                            .append(if (item.field.isRequired) Characters.asterisk else Characters.nullCharacter)
                            .spans { ForegroundColorSpan(root.context.getColor(R.color.application___color__accent)) }
                    }
                mainVacancyRequestFieldsTextSubtitle.setHtmlText(
                    item.field.hint.orEmpty(), linkColor = root.context.getColor(
                        R.color.application___color__icons
                    )
                )
                mainVacancyRequestFieldsTextSubtitle.isGoneVisible =
                    !item.field.hint.isNullOrEmpty()
                item.data?.let {
                    mainVacancyRequestFieldsText.setText(it)
                    mainVacancyRequestFieldsText.isEnabled = item.field.isEnabled
                }

                mainVacancyRequestFieldsText.addTextChangedListener {
                    if (!it.isNullOrEmpty()) {
                        mainVacancyRequestFieldsTextInputContainer.makeDefault(
                            mainVacancyRequestFieldsText
                        )
                    }

                    onTextChangedListener.invoke(it.toString() to item.field.identity)
                }

                if (item.isAccent) {
                    mainVacancyRequestFieldsTextInputContainer.makeAccent(
                        mainVacancyRequestFieldsText,
                        "* Обязательное поле"
                    )
                }
            }
        }

        class IntegerViewHolder(
            private val binding: MainVacancyRequestTextFieldBinding
        ) : ViewHolder(binding.root) {
            fun bind(
                item: Item.Integer,
                onTextChangedListener: PublishProcessor<Pair<String, VacancyRequestFieldId>>
            ) = with(binding) {
                mainVacancyRequestFieldsTextTitle.text =
                    root.context.spannedString {
                        it.append(item.field.title)
                            .append(if (item.field.isRequired) Characters.asterisk else Characters.nullCharacter)
                            .spans { ForegroundColorSpan(root.context.getColor(R.color.application___color__accent)) }
                    }
                mainVacancyRequestFieldsTextSubtitle.setHtmlText(
                    item.field.hint.orEmpty(), linkColor = root.context.getColor(
                        R.color.application___color__icons
                    )
                )
                mainVacancyRequestFieldsTextSubtitle.isGoneVisible =
                    !item.field.hint.isNullOrEmpty()
                item.data?.let {
                    mainVacancyRequestFieldsText.setText(it.toString())
                    mainVacancyRequestFieldsText.isEnabled = item.field.isEnabled
                }
                mainVacancyRequestFieldsText.inputType =
                    InputType.TYPE_CLASS_NUMBER
                mainVacancyRequestFieldsText.addTextChangedListener {
                    if (!it.isNullOrEmpty()) {
                        mainVacancyRequestFieldsTextInputContainer.makeDefault(
                            mainVacancyRequestFieldsText
                        )
                    }

                    onTextChangedListener.invoke(it.toString() to item.field.identity)
                }

                if (item.isAccent) {
                    mainVacancyRequestFieldsTextInputContainer.makeAccent(
                        mainVacancyRequestFieldsText,
                        "* Обязательное поле"
                    )
                }
            }
        }

        class CheckboxViewHolder(
            private val binding: MainVacancyRequestCheckFieldBinding
        ) : ViewHolder(binding.root) {
            fun bind(
                item: Item.Checkbox,
                onClickListener: PublishProcessor<VacancyRequestFieldId>
            ) = with(binding) {
                mainVacancyRequestFieldsCheckTitle.text =
                    root.context.spannedString {
                        it.append(item.field.title)
                            .append(if (item.field.isRequired) Characters.asterisk else Characters.nullCharacter)
                            .spans { ForegroundColorSpan(root.context.getColor(R.color.application___color__accent)) }
                    }
                mainVacancyRequestFieldsCheckSubtitle.setHtmlText(
                    item.field.hint.orEmpty(),
                    linkColor = root.context.getColor(R.color.application___color__icons)
                )
                mainVacancyRequestFieldsCheckSubtitle.isGoneVisible =
                    !item.field.hint.isNullOrEmpty()
                mainVacancyRequestFieldsCheck.isChecked = item.isChecked
                mainVacancyRequestFieldsCheckText.setHtmlText(item.field.title)
                mainVacancyRequestFieldsCheck.setOnClickListener {
                    mainVacancyRequestFieldsCheckError.gone()
                    onClickListener.invoke(item.field.identity)
                }
            }
        }
    }
}
