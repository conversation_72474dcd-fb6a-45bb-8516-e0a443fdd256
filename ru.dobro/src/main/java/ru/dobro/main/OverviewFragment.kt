package ru.dobro.main

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.databinding.FragmentOverviewBinding
import ru.dobro.main.crm.CrmFragment
import ru.dobro.main.home.HomeFragment

class OverviewFragment : Fragment(), HasCustomToolbar {
    private lateinit var binding: FragmentOverviewBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentOverviewBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onStart() {
        super.onStart()

        when ((requireActivity() as? MainActivity)?.isOrganizerBottomMenu()) {
            true -> {
                childFragmentManager
                    .beginTransaction()
                    .replace(ru.dobro.R.id.overviewFragmentContainer, CrmFragment())
                    .commit()
            }

            else -> {
                childFragmentManager
                    .beginTransaction()
                    .replace(ru.dobro.R.id.overviewFragmentContainer, HomeFragment())
                    .commit()
            }
        }
    }

    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        HasCustomToolbar.CustomToolbar.None
}
