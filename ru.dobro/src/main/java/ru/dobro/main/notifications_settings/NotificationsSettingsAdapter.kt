package ru.dobro.main.notifications_settings

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import common.library.android.widget.isGoneVisible
import common.library.android.widget.recycler_view.context
import common.library.android.widget.recycler_view.getRxAsyncDiffer
import common.library.android.widget.scope
import common.library.core.EqualityComparable
import common.library.core.EqualityComparators
import common.library.core.EqualityContentComparable
import common.library.core.contract.ContractException
import common.library.core.identityHashCode
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import ru.dobro.R
import ru.dobro.databinding.MainNotificationsSettingsItemBinding
import ru.dobro.databinding.MainNotificationsSettingsTitleBinding
import ru.dobro.domain.NotificationSetting
import ru.dobro.images
import javax.annotation.CheckReturnValue

class NotificationsSettingsAdapter :
    ListAdapter<NotificationsSettingsAdapter.Item, NotificationsSettingsAdapter.ViewHolder>(
        getRxAsyncDiffer(
            EqualityComparators.natural(),
            EqualityComparators.content()
        )
    ) {

    private val _onNotificationCheckedClick: MutableSharedFlow<Item> = MutableSharedFlow()

    val onNotificationCheckedClick: Flow<Item> get() = _onNotificationCheckedClick

    @CheckReturnValue
    override fun getItemViewType(position: Int): Int = getItemViewType(getItem(position)).ordinal

    @CheckReturnValue
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder =
        when (common.library.core.enumValueOf<ViewType>(viewType)) {
            ViewType.Notifications -> ViewHolder.Notifications(
                binding = MainNotificationsSettingsItemBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                ),
                onItemCheckedClick = _onNotificationCheckedClick
            )

            ViewType.PushNotifications -> ViewHolder.PushNotifications(
                binding = MainNotificationsSettingsItemBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                ),
                onItemCheckedClick = _onNotificationCheckedClick
            )

            ViewType.NotificationsTitle -> ViewHolder.NotificationsTitle(
                binding = MainNotificationsSettingsTitleBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                ),
            )

            ViewType.PushNotificationsTitle -> ViewHolder.PushNotificationsTitle(
                binding = MainNotificationsSettingsTitleBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                ),
            )
        }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) =
        when (holder) {
            is ViewHolder.Notifications -> holder.bind(getItem(position) as Item.Notifications)
            is ViewHolder.PushNotifications -> holder.bind(getItem(position) as Item.PushNotifications)
            is ViewHolder.PushNotificationsTitle -> holder.bind()
            is ViewHolder.NotificationsTitle -> holder.bind()
        }

    @CheckReturnValue
    private fun getItemViewType(item: Item?): ViewType = when (item) {
        is Item.Notifications -> ViewType.Notifications
        is Item.PushNotifications -> ViewType.PushNotifications
        is Item.NotificationsTitle -> ViewType.NotificationsTitle
        is Item.PushNotificationsTitle -> ViewType.PushNotificationsTitle
        null -> throw ContractException("Unsupported view type")
    }

    enum class ViewType {
        Notifications,
        PushNotifications,
        NotificationsTitle,
        PushNotificationsTitle,
    }

    sealed class Item : EqualityComparable<Item>, EqualityContentComparable<Item> {
        data class Notifications(
            val notification: NotificationSetting,
        ) : Item() {
            @CheckReturnValue
            override fun equalTo(other: Item?): Boolean {
                if (other !is Notifications) {
                    return false
                }

                return notification.equalTo(other.notification)
            }

            @CheckReturnValue
            override fun equals(other: Any?): Boolean {
                if (other !is Notifications) {
                    return false
                }

                return equalTo(other)
            }

            @CheckReturnValue
            override fun hashCode(): Int = notification.hashCode()

            @CheckReturnValue
            override fun contentEqualTo(other: Item?): Boolean {
                if (other !is Notifications) {
                    return false
                }

                return notification.contentEqualTo(other.notification)
            }

            @CheckReturnValue
            override fun contentHashCode(): Int = notification.contentHashCode()
        }

        data class PushNotifications(
            val pushNotification: NotificationSetting,
        ) : Item() {
            @CheckReturnValue
            override fun equalTo(other: Item?): Boolean {
                if (other !is PushNotifications) {
                    return false
                }

                return pushNotification.equalTo(other.pushNotification)
            }

            @CheckReturnValue
            override fun equals(other: Any?): Boolean {
                if (other !is PushNotifications) {
                    return false
                }

                return equalTo(other)
            }

            @CheckReturnValue
            override fun hashCode(): Int = pushNotification.hashCode()

            @CheckReturnValue
            override fun contentEqualTo(other: Item?): Boolean {
                if (other !is PushNotifications) {
                    return false
                }

                return pushNotification.contentEqualTo(other.pushNotification)
            }

            @CheckReturnValue
            override fun contentHashCode(): Int = pushNotification.contentHashCode()
        }

        object NotificationsTitle : Item() {
            @CheckReturnValue
            override fun equalTo(other: Item?): Boolean = this === NotificationsTitle

            @CheckReturnValue
            override fun equals(other: Any?): Boolean = this === NotificationsTitle

            @CheckReturnValue
            override fun hashCode(): Int = identityHashCode()

            @CheckReturnValue
            override fun contentEqualTo(other: Item?): Boolean = this === NotificationsTitle

            @CheckReturnValue
            override fun contentHashCode(): Int = identityHashCode()
        }

        object PushNotificationsTitle : Item() {
            @CheckReturnValue
            override fun equalTo(other: Item?): Boolean = this === PushNotificationsTitle

            @CheckReturnValue
            override fun equals(other: Any?): Boolean = this === PushNotificationsTitle

            @CheckReturnValue
            override fun hashCode(): Int = identityHashCode()

            @CheckReturnValue
            override fun contentEqualTo(other: Item?): Boolean = this === PushNotificationsTitle

            @CheckReturnValue
            override fun contentHashCode(): Int = identityHashCode()
        }
    }

    sealed class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        class PushNotifications(
            private val binding: MainNotificationsSettingsItemBinding,
            private val onItemCheckedClick: MutableSharedFlow<Item>
        ) : ViewHolder(binding.root) {
            fun bind(item: Item.PushNotifications) {
                itemView.apply {
                    setOnClickListener {
                        binding.scope?.launch {
                            onItemCheckedClick.emit(item)
                        }
                    }

                    binding.mainNotificationsSettingsItemTitle.text = item.pushNotification.title
                    binding.mainNotificationsSettingsItemSubtitle.text =
                        item.pushNotification.subtitle
                    binding.mainNotificationsSettingsItemSubtitle.isGoneVisible =
                        item.pushNotification.subtitle != null

                    images.load(item.pushNotification.avatar)
                        .circleCrop()
                        .into(binding.mainNotificationsSettingsItemAvatar)

                    binding.mainNotificationsSettingsItemSwitch.isChecked =
                        item.pushNotification.isChecked
                }
            }
        }

        class Notifications(
            private val binding: MainNotificationsSettingsItemBinding,
            private val onItemCheckedClick: MutableSharedFlow<Item>
        ) : ViewHolder(binding.root) {
            fun bind(item: Item.Notifications) {
                itemView.apply {
                    setOnClickListener {
                        binding.scope?.launch {
                            onItemCheckedClick.emit(item)
                        }
                    }

                    binding.mainNotificationsSettingsItemTitle.text = item.notification.title
                    binding.mainNotificationsSettingsItemSubtitle.text = item.notification.subtitle
                    binding.mainNotificationsSettingsItemSubtitle.isGoneVisible =
                        item.notification.subtitle != null

                    images.load(item.notification.avatar)
                        .circleCrop()
                        .into(binding.mainNotificationsSettingsItemAvatar)

                    binding.mainNotificationsSettingsItemSwitch.isChecked =
                        item.notification.isChecked
                }
            }
        }

        class NotificationsTitle(
            private val binding: MainNotificationsSettingsTitleBinding
        ) : ViewHolder(binding.root) {
            fun bind() {
                binding.mainNotificationsTitle.text =
                    context.getString(R.string.main__notifications_settings___notification_title)
            }
        }

        class PushNotificationsTitle(
            private val binding: MainNotificationsSettingsTitleBinding
        ) : ViewHolder(binding.root) {
            fun bind() {
                binding.mainNotificationsTitle.text =
                    context.getString(R.string.main__notifications_settings___push_title)
            }
        }
    }
}
