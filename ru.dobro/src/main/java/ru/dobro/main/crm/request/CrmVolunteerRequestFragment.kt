package ru.dobro.main.crm.request

import android.content.ClipData
import android.content.res.ColorStateList
import android.os.Bundle
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.Toolbar
import androidx.core.os.bundleOf
import androidx.navigation.fragment.findNavController
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.google.android.material.appbar.AppBarLayout
import common.library.android.clipboardManager
import common.library.android.inflateBy
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.message.MessageDisplay
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.string.RString
import common.library.android.string.r
import common.library.android.string.span.spannedString
import common.library.android.widget.gone
import common.library.android.widget.isGoneVisible
import common.library.android.widget.onRefresh
import common.library.android.widget.visible
import common.library.android.widget.wrapIntoSwipeRefresh
import common.library.core.Characters
import common.library.core.lazyGet
import common.library.core.rx.RxSchedulers
import common.library.core.rx.ofSubtype
import common.library.core.variable.Agent
import common.library.core.variable.agent
import common.library.core.variable.sendWhen
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.rx2.asFlowable
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.R
import ru.dobro.api.DobroApi
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.RequestCodes
import ru.dobro.core.error.ErrorHandler
import ru.dobro.core.error.wrap
import ru.dobro.core.format.DisplayFormat
import ru.dobro.databinding.MainCrmVolunteerRequestFragmentBinding
import ru.dobro.domain.CrmVacancyRequestData
import ru.dobro.domain.CrmVacancyStatus
import ru.dobro.domain.VacancyId
import ru.dobro.domain.getName
import ru.dobro.domain.profile.achievements.trophy.Trophy
import ru.dobro.images
import ru.dobro.main.crm.accept_reject.RejectReasonDialogFragment
import ru.dobro.main.event.EventFragment
import ru.dobro.main.profile.achievements.adapter.TrophiesAdapter
import ru.dobro.main.profile.achievements.info.AchievementInfoFragment
import javax.annotation.CheckReturnValue

class CrmVolunteerRequestFragment(override val screenName: String = "CrmSetApplicationHoursFragment") :
    BaseFragment({
        bind<Agent<CrmVolunteerRequestState>>() with singleton {
            agent(
                CrmVolunteerRequestState.Idle(-1)
            )
        }
    }), HasCustomToolbar {
    private val _state: Agent<CrmVolunteerRequestState> by instance()
    private lateinit var binding: MainCrmVolunteerRequestFragmentBinding
    private lateinit var _swipeRefreshLayout: SwipeRefreshLayout
    private val _messageDisplay: MessageDisplay by instance()
    private val _errorHandler: ErrorHandler by instance()
    private val _bundler: Bundler by instance()
    private val _api: DobroApi by instance()
    private val _displayFormat: DisplayFormat by instance()

    private val fieldsAdapter by lazyGet {
        FieldsAdapter()
    }

    private val _reviewsAdapter: ReviewsAboutVolunteerAdapter by lazyGet {
        ReviewsAboutVolunteerAdapter(
            _displayFormat
        )
    }
    private val _trophiesAdapter: TrophiesAdapter by lazyGet { TrophiesAdapter() }

    private val _requestCodeRejectReason: String =
        RequestCodes.scopedBy<EventFragment>("reject_reason")

    init {
        setHasOptionsMenu(true)
    }

    @CheckReturnValue
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        object :
            HasCustomToolbar.CustomToolbar.Layout() {
            @CheckReturnValue
            override fun onCreateView(container: ViewGroup): View =
                container.context.inflateBy(
                    R.layout.application___toolbar__transparent_accent,
                    container
                )

            @CheckReturnValue
            override fun getAppBarLayout(view: View): AppBarLayout? {
                return view.findViewById(R.id.main__task__appbar__transparent)
            }

            @CheckReturnValue
            override fun getToolbar(view: View): Toolbar {
                return view.findViewById(R.id.application___toolbar)
            }
        }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.main__crm_volunteer_request, menu)
        super.onCreateOptionsMenu(menu, inflater)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean =
        when (item.itemId) {
            R.id.main__crm_volunteer_request__reserve -> {
                _swipeRefreshLayout.isRefreshing = true
                vacancyPair?.first?.let {
                    _api.overview().reserveVacancy(VacancyId.restore(it))
                        .subscribeOn(RxSchedulers.io())
                        .observeOn(RxSchedulers.main())
                        .doOnError { _messageDisplay.showMessage("Не удалось загрузить данные".r) }
                        .subscribeBy(
                            onSuccess = {
                                _swipeRefreshLayout.isRefreshing = false
                                makeIdle()
                                _messageDisplay.showMessage("Добавлен(-а) в резерв".r)
                            },
                            onError = {
                                _swipeRefreshLayout.isRefreshing = false
                            }
                        ).scopedByDestroy()
                }
                true
            }

            else -> super.onOptionsItemSelected(item)
        }

    @CheckReturnValue
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = MainCrmVolunteerRequestFragmentBinding.inflate(inflater)
        _swipeRefreshLayout = binding.root.wrapIntoSwipeRefresh()
        return _swipeRefreshLayout
    }

    private var vacancyPair: Pair<Long, String>? = null
    private var status: CrmVacancyStatus? = null
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.mainCrmVolunteerRequestFragmentTrophiesList.adapter = _trophiesAdapter
        binding.mainCrmVolunteerRequestFragmentReviewList.adapter = _reviewsAdapter

        vacancyPair = arguments?.getParams(_bundler)
        if (vacancyPair == null) {
            findNavController().navigateUp()
            return
        }

        _state.sendWhen<CrmVolunteerRequestState, CrmVolunteerRequestState.Idle> { init() }

        _swipeRefreshLayout.isRefreshing = true
        binding.mainCrmVolunteerRequestFragmentContainer.gone()

        loadProfile()
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        _swipeRefreshLayout
            .onRefresh
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                loadProfile()
            }
            .scoped()

        binding.mainCrmAcceptItemReject.setOnClickListener {
            _swipeRefreshLayout.isRefreshing = true
            RejectReasonDialogFragment.create(
                _requestCodeRejectReason,
                _bundler,
                VacancyId.restore(vacancyPair?.first ?: -1L)
            )
                .show(parentFragmentManager, null)

            RejectReasonDialogFragment
                .onResult(
                    this@CrmVolunteerRequestFragment,
                    _requestCodeRejectReason,
                    _bundler
                )
                .subscribeBy {
                    _swipeRefreshLayout.isRefreshing = false
                    makeRejected()
                }
                .scoped()
        }

        binding.mainCrmAcceptItemAccept.setOnClickListener {
            _swipeRefreshLayout.isRefreshing = true
            _api.overview().acceptVacancy(VacancyId.restore(vacancyPair?.first ?: -1L))
                .subscribeOn(RxSchedulers.io())
                .observeOn(RxSchedulers.main())
                .subscribeBy(
                    onSuccess = {
                        _swipeRefreshLayout.isRefreshing = false
                        makeAccepted()
                    },
                    onError = {
                        _swipeRefreshLayout.isRefreshing = false
                    }
                ).scoped()
        }

        _trophiesAdapter
            .onCardClick
            .asFlowable()
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                val bundle = bundleOf(
                    AchievementInfoFragment.name to it.item.title,
                    AchievementInfoFragment.description to it.item.description,
                    AchievementInfoFragment.imageUrl to it.item.image.toString(),
                    AchievementInfoFragment.progress to it.item.progress
                )
                findNavController().navigate(
                    R.id.action_crmVolunteerRequestFragment_to_trophyInfoFragment,
                    bundle
                )
            }
            .scoped()

        _reviewsAdapter
            .onEventClick
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy { eventId ->
                eventId?.let {
                    EventFragment.navigate(findNavController(), _bundler)
                        .toEvent(R.id.action_crmVolunteerRequestFragment_to_main__event, it)
                }
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<CrmVolunteerRequestState.Processing>()
            .distinctUntilChanged { processing -> processing.reviewPage }
            .filter { it.reviewPage > 1 }
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _api.overview().getReviewsAboutVolunteer(
                    vacancyPair?.first ?: -1, page = it.reviewPage
                )
                    .subscribeOn(RxSchedulers.io())
                    .observeOn(RxSchedulers.main())
                    .subscribeBy(
                        onSuccess = { reviews ->
                            binding.mainCrmVolunteerRequestFragmentReviewCount.text =
                                reviews.meta.totalItems.toInt().toString()
                            binding.mainCrmVolunteerRequestFragmentReviewListMore.isGoneVisible =
                                reviews.meta.lastPage != it.reviewPage
                            _reviewsAdapter.submitList(reviews.data)
                        },
                        onError = {
                            _messageDisplay.showMessage(
                                _errorHandler.wrap(it) ?: "Не удалось загрузить комментарии".r
                            )
                        }
                    ).scoped()
            }
            .scoped()

        binding.mainCrmVolunteerRequestFragmentReviewListMore.setOnClickListener {
            _state.sendWhen<CrmVolunteerRequestState, CrmVolunteerRequestState.Processing> {
                inputNextPage(this.reviewPage + 1)
            }
        }

        binding.mainCrmVolunteerRequestFragmentVolunteerBook.setOnClickListener {
            (_state.currentValue as? CrmVolunteerRequestState.Processing)?.let {
                findNavController().navigate(
                    R.id.action_crmVolunteerRequestFragment_to_main__profile___volunteer_book,
                    it.userId?.intoBundle(_bundler)
                )
            }
        }
    }

    private fun loadTrophies(volunteer: CrmVacancyRequestData.Volunteer) {
        if (_trophiesAdapter.currentList.isEmpty()) {
            val volunteerTrophiesList =
                java.util.Collections.synchronizedList(mutableListOf<Trophy>())
            binding.mainCrmVolunteerRequestFragmentTrophy.visibility =
                if (volunteer.trophies.isNullOrEmpty()) View.GONE else View.VISIBLE
//            this.volunteerTrophies?.forEach { volunteerTrophy -> //input: "/api/v2/volunteer_trophies/trophy=1;volunteer=9000065"
//                val split = volunteerTrophy.split("=", ";", "=")
//                val volunteerId = split[3]
//                val trophyNumber = split[1]
//                _dobroApi.profile().getVolunteerTrophy(trophyNumber.toInt(), volunteerId.toInt())
//                    .subscribeOn(Schedulers.io())
//                    .observeOn(AndroidSchedulers.mainThread())
//                    .doOnSuccess { trophy ->
//                        volunteerTrophiesList.add(trophy)
//                        val mapContent = volunteerTrophiesList.map {
//                            TrophiesAdapter.Trophy(
//                                TrophiesAdapter.Item(
//                                    it.identity,
//                                    it.title,
//                                    it.description,
//                                    it.image
//                                )
//                            )
//                        }
//                        _trophiesAdapter.submitList(mapContent)
//                        main__profile_me___about__trophies_count?.text =
//                            volunteerTrophiesList.count().toString()
//                    }
//                    .subscribe()
//            }
        } else {
            binding.mainCrmVolunteerRequestFragmentTrophiesCount.text =
                _trophiesAdapter.currentList.count().toString()
        }
    }

    private fun loadProfile() {
        _api.overview().getVacancyRequest(
            vacancyId = VacancyId.restore(vacancyPair?.first ?: -1L)
        )
            .subscribeOn(RxSchedulers.io())
            .observeOn(RxSchedulers.main())
            .subscribeBy(
                onSuccess = { request ->
                    val noEmptyFields = request.fields.filter { it.value.isNotBlank() }
                    if (noEmptyFields.isNotEmpty()) {
                        binding.mainCrmVolunteerRequestFragmentContactsFields.adapter =
                            fieldsAdapter
                        binding.mainCrmVolunteerRequestFragmentContactsFieldsTilte.visible()
                        fieldsAdapter.submitList(noEmptyFields)
                    }

                    binding.mainCrmVolunteerRequestFragmentVolunteerBook.isGoneVisible =
                        request.volunteer.evkVisible

                    _swipeRefreshLayout.isRefreshing = false
                    loadTrophies(request.volunteer)
                    _state.sendWhen<CrmVolunteerRequestState, CrmVolunteerRequestState.Processing> {
                        inputUserId(request.volunteer.id)
                    }
                    binding.mainCrmVolunteerRequestFragmentContainer.visible()

                    status = request.status

                    binding.mainCrmVolunteerRequestFragmentEventName.text = request.eventName
                    binding.mainCrmVolunteerRequestFragmentEventPeriodText.text =
                        request.vacancy.vacancyPeriodTitle
                    binding.mainCrmVolunteerRequestFragmentEventRequestsText.text =
                        vacancyPair?.second
                    binding.mainCrmVolunteerRequestFragmentContactsLocation.text =
                        request.volunteer.settlement
                    binding.mainCrmVolunteerRequestFragmentContactsEmail.text =
                        requireContext().spannedString {
                            it.append("Email")
                                .append(Characters.space)
                                .append(request.volunteer.email)
                                .spans { ForegroundColorSpan(requireContext().getColor(R.color.application___color__primary)) }
                        }

                    binding.mainCrmVolunteerRequestFragmentContactsEmail.setOnClickListener {
                        request.volunteer.email?.let { email ->
                            val clipData = ClipData.newPlainText("text", email)
                            val clipboardManager = context?.clipboardManager
                            if (clipboardManager !== null) {
                                clipboardManager.setPrimaryClip(clipData)
                                _messageDisplay.showMessage(
                                    RString.string(
                                        "Почта скопирована",
                                        null
                                    )
                                )
                            }
                        }
                    }

                    binding.mainCrmAcceptItemName.text = request.volunteer.getName()
                    binding.mainCrmAcceptItemId.text = requireContext().spannedString {
                        it
                            .append(R.string.main__profile___id)
                            .append(Characters.space)
                            .append(request.volunteer.id.unwrap().toString())
                    }
                    images.load(request.volunteer.avatar)
                        .circleCrop()
                        .placeholder(R.drawable.application___account)
                        .transition(DrawableTransitionOptions.withCrossFade())
                        .into(binding.mainCrmAcceptItemAvatar)

                    binding.mainCrmVolunteerRequestFragmentContactsHours.text =
                        getString(
                            R.string.main_crm_hours,
                            request.volunteer.statistic.hours.toString()
                        )
                    binding.mainCrmVolunteerRequestFragmentContactsVerifiedHours.text =
                        getString(
                            R.string.main_crm_verified_hours,
                            request.volunteer.statistic.verifiedHours.toString()
                        )
                    binding.mainCrmVolunteerRequestFragmentContactsTrophy.text = getString(
                        R.string.main_crm_trophies,
                        request.volunteer.trophies?.count()?.toString() ?: "0"
                    )
                    binding.mainCrmVolunteerRequestFragmentContactsEvents.text = getString(
                        R.string.main_crm_events,
                        request.volunteer.statistic.eventsCount.toString()
                    )
                    when (request.status) {
                        CrmVacancyStatus.Accepted -> makeAccepted()
                        CrmVacancyStatus.Reserved -> {}
                        CrmVacancyStatus.RejectedByOrganizer -> makeRejected()
                        else -> {
                            // Do nothing
                        }
                    }

                    val processingState =
                        (_state.currentValue as? CrmVolunteerRequestState.Processing)
                    _api.overview().getReviewsAboutVolunteer(
                        request.volunteer.id.unwrap(), page = 1
                    )
                        .subscribeOn(RxSchedulers.io())
                        .observeOn(RxSchedulers.main())
                        .subscribeBy(
                            onSuccess = { reviews ->
                                binding.mainCrmVolunteerRequestFragmentReviewCount.text =
                                    reviews.meta.totalItems.toInt().toString()
                                binding.mainCrmVolunteerRequestFragmentReviewListMore.isGoneVisible =
                                    reviews.meta.lastPage != processingState?.reviewPage
                                _reviewsAdapter.submitList(reviews.data)
                                _state.sendWhen<CrmVolunteerRequestState, CrmVolunteerRequestState.Processing> {
                                    inputVolunteerId(request.volunteer.id.unwrap())
                                }
                            },
                            onError = {
                                _messageDisplay.showMessage(
                                    _errorHandler.wrap(it) ?: "Не удалось загрузить комментарии".r
                                )
                            }
                        )
                        .scopedByDestroy()
                },
                onError = {
                    _messageDisplay.showMessage(
                        _errorHandler.wrap(it) ?: "Не удалось загрузить заявки".r
                    )
                }
            )
            .scopedByDestroy()
    }

    private fun makeAccepted() {
        binding.mainCrmAcceptItemAccept.setTextColor(resources.getColor(R.color.white, null))
        binding.mainCrmAcceptItemAccept.text = "Одобрена"
        binding.mainCrmAcceptItemAccept.backgroundTintList = ColorStateList.valueOf(
            resources.getColor(
                R.color.application___color__secondary_variant,
                null
            )
        )
        binding.mainCrmAcceptItemAccept.iconTint =
            ColorStateList.valueOf(resources.getColor(R.color.white, null))

        binding.mainCrmAcceptItemReject.setTextColor(
            resources.getColor(
                R.color.application___color__accent_variant,
                null
            )
        )
        binding.mainCrmAcceptItemReject.backgroundTintList =
            ColorStateList.valueOf(resources.getColor(R.color.white, null))
        binding.mainCrmAcceptItemReject.iconTint = ColorStateList.valueOf(
            resources.getColor(
                R.color.application___color__accent_variant,
                null
            )
        )
        binding.mainCrmAcceptItemReject.text = "Отклонить"
    }

    private fun makeIdle() {
        binding.mainCrmAcceptItemAccept.setTextColor(
            resources.getColor(
                R.color.application___color__secondary_variant,
                null
            )
        )
        binding.mainCrmAcceptItemAccept.backgroundTintList =
            ColorStateList.valueOf(resources.getColor(R.color.white, null))
        binding.mainCrmAcceptItemAccept.iconTint = ColorStateList.valueOf(
            resources.getColor(
                R.color.application___color__secondary_variant,
                null
            )
        )
        binding.mainCrmAcceptItemAccept.text = "Одобрить"

        binding.mainCrmAcceptItemReject.setTextColor(
            resources.getColor(
                R.color.application___color__accent_variant,
                null
            )
        )
        binding.mainCrmAcceptItemReject.backgroundTintList =
            ColorStateList.valueOf(resources.getColor(R.color.white, null))
        binding.mainCrmAcceptItemReject.iconTint = ColorStateList.valueOf(
            resources.getColor(
                R.color.application___color__accent_variant,
                null
            )
        )
        binding.mainCrmAcceptItemReject.text = "Отклонить"
    }

    private fun makeRejected() {
        binding.mainCrmAcceptItemReject.setTextColor(resources.getColor(R.color.white, null))
        binding.mainCrmAcceptItemReject.backgroundTintList = ColorStateList.valueOf(
            resources.getColor(
                R.color.application___color__accent_variant,
                null
            )
        )
        binding.mainCrmAcceptItemReject.iconTint =
            ColorStateList.valueOf(resources.getColor(R.color.white, null))
        binding.mainCrmAcceptItemReject.text = "Отклонена"

        binding.mainCrmAcceptItemAccept.setTextColor(
            resources.getColor(
                R.color.application___color__secondary_variant,
                null
            )
        )
        binding.mainCrmAcceptItemAccept.backgroundTintList =
            ColorStateList.valueOf(resources.getColor(R.color.white, null))
        binding.mainCrmAcceptItemAccept.iconTint = ColorStateList.valueOf(
            resources.getColor(
                R.color.application___color__secondary_variant,
                null
            )
        )
        binding.mainCrmAcceptItemAccept.text = "Одобрить"
    }
}
