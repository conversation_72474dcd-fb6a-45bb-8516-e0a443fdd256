package ru.dobro.main.crm.tabs.hours

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.Toolbar
import androidx.core.view.isGone
import androidx.navigation.fragment.findNavController
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.android.material.appbar.AppBarLayout
import common.library.android.dimension.dp
import common.library.android.inflateBy
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.message.MessageDisplay
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.string.r
import common.library.android.string.span.spannedString
import common.library.android.widget.gone
import common.library.android.widget.invisible
import common.library.android.widget.isGoneVisible
import common.library.android.widget.onRefresh
import common.library.android.widget.recycler_view.addSpacingItemDecoration
import common.library.android.widget.visible
import common.library.android.widget.wrapIntoSwipeRefresh
import common.library.core.rx.RxSchedulers
import common.library.core.rx.ofSubtype
import common.library.core.variable.Agent
import common.library.core.variable.agent
import common.library.core.variable.sendWhen
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.collections.immutable.toPersistentList
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.R
import ru.dobro.api.DobroApi
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.RequestCodes
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.databinding.FragmentHoursBinding
import ru.dobro.domain.CrmVacancyRequest
import ru.dobro.domain.VacancyId
import ru.dobro.domain.nav_params.CrmSetHoursMultipleNavigation
import ru.dobro.domain.nav_params.CrmSetHoursNavigationData
import ru.dobro.domain.nav_params.CrmSetHoursSingleNavigation
import ru.dobro.domain.nav_params.CrmSetUnsetHoursParams
import ru.dobro.domain.nav_params.CrmSetUnsetHoursParamsData
import ru.dobro.main.crm.accept_reject.CrmAcceptRejectApplicationState
import ru.dobro.main.crm.adapter.CrmEventAdapterData
import ru.dobro.main.crm.set_hours.CrmSetHoursAdapter
import ru.dobro.main.crm.set_hours.SetHoursDialogFragment
import ru.dobro.main.crm.set_hours.exception.CrmSetApplicationHoursExceptionFragment
import ru.dobro.main.event.EventFragment
import javax.annotation.CheckReturnValue

class HoursFragment :
    BaseFragment({
        bind<Agent<HoursState>>() with singleton {
            agent(
                HoursState.Idle(0)
            )
        }
    }), HasCustomToolbar {
    private val _state: Agent<HoursState> by instance()
    private lateinit var binding: FragmentHoursBinding

    private lateinit var _swipeRefreshLayout: SwipeRefreshLayout
    private val _messageDisplay: MessageDisplay by instance()
    private val _bundler: Bundler by instance()
    private val _api: DobroApi by instance()

    private val _adapterSetHours: CrmSetHoursAdapter by lazy { CrmSetHoursAdapter() }

    private val _requestCodeSetHours: String = RequestCodes.scopedBy<EventFragment>("set_hours")

    override val screenName: String = AnalyticsConstants.Screen.Main.crmSetApplicationHours

    companion object {
        const val requestCodeUnsetHours: String = "unset_hours"

        fun create(
            vacancy: CrmEventAdapterData.Vacancy,
            bundler: Bundler,
            exceptionUsers: List<CrmVacancyRequest>
        ): HoursFragment {
            return HoursFragment().apply {
                arguments = RequestHours(vacancy, exceptionUsers).intoBundle(bundler)
            }
        }
    }

    init {
        setHasOptionsMenu(false)
    }

    @CheckReturnValue
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        object :
            HasCustomToolbar.CustomToolbar.Layout() {
            @CheckReturnValue
            override fun onCreateView(container: ViewGroup): View =
                container.context.inflateBy(
                    R.layout.application___toolbar__transparent_accent,
                    container
                )

            @CheckReturnValue
            override fun getAppBarLayout(view: View): AppBarLayout? {
                return view.findViewById(R.id.main__task__appbar__transparent)
            }

            @CheckReturnValue
            override fun getToolbar(view: View): Toolbar {
                return view.findViewById(R.id.application___toolbar)
            }
        }

    @CheckReturnValue
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = FragmentHoursBinding.inflate(layoutInflater)
        _swipeRefreshLayout = binding.root.wrapIntoSwipeRefresh()
        return _swipeRefreshLayout
    }

    private var vacancy: CrmEventAdapterData.Vacancy? = null
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.setHoursContainer.layoutTransition.setAnimateParentHierarchy(false)

        val request: RequestHours? = arguments?.getParams(_bundler)
        if (request == null) {
            findNavController().navigateUp()
            return
        }

        vacancy = request.vacancy

        _state.sendWhen<HoursState, HoursState.Idle> { init(request.exceptionUsers) }

        setupView()
    }

    fun onUserListChanged(crmVacancyRequest: List<CrmVacancyRequest>) {
        val attemptHoursOld = (_state.currentValue as HoursState.Processing).exceptionHours
        _state.sendWhen<HoursState, HoursState.Processing> {
            setExceptionUsers(attemptHoursOld, crmVacancyRequest)
        }
    }

    private fun setupView() {
        binding.apply {
            setHoursList.apply {
                adapter = _adapterSetHours
                addSpacingItemDecoration {
                    it.verticalSpacing(8.dp)
                }
            }

            warningText.text = requireContext().spannedString {
                it.append("Обратите внимание")
                    .styleBold()
                    .append(" есть волонтеры, которым не удалось проставить часы")
                    .styleNormal()
            }
            showWarningList.setOnClickListener {
                findNavController().navigate(
                    R.id.action_crmSetApplicationHoursFragment_to_CrmSetUnsetApplicationsHoursFragment,
                    CrmSetUnsetHoursParams.restore(
                        CrmSetUnsetHoursParamsData(
                            vacancyId = vacancy?.vacancyId ?: VacancyId.restore(-1L),
                            attemptHours = (_state.currentValue as? HoursState.Processing)?.exceptionHours
                                ?: 0,
                            crmVacancyRequests = (_state.currentValue as? HoursState.Processing)?.exceptionUsers.orEmpty()
                                .toPersistentList()
                        )
                    ).intoBundle(_bundler)
                )
            }
            selectVolunteers.setOnClickListener {
                _state.sendWhen<HoursState, HoursState.Processing> {
                    setSelectUsersMode(
                        HoursState.SelectVolunteersMode.Multiple
                    )
                }
            }
            cancelEveryone.setOnClickListener {
                _state.sendWhen<HoursState, HoursState.Processing> {
                    setSelectUsersMode(
                        HoursState.SelectVolunteersMode.Single
                    )
                }
            }
            selectEveryone.setOnClickListener {
                _state.sendWhen<HoursState, HoursState.Processing> {
                    setAllSelected(
                        true
                    )
                }
            }
            setHoursForAll.setOnClickListener {
                showSetHoursDialog()
            }

            setHoursListMore.setOnClickListener {
                binding.apply {
                    setHoursListMore.invisible()
                    setHoursProgressMore.visible()
                }
                _state.sendWhen<HoursState, HoursState.Processing> {
                    setCurrentPage(this.currentPage + 1)
                }
            }
        }
    }

    private fun showSetHoursDialog() {
        (_state.currentValue as? HoursState.Processing)?.let { state ->
            if (state.users.count { it.isSelected } == 1) {
                state.users.firstOrNull()?.let { showSetHoursDialog(it.item) }
            } else if (state.isAllSelected || state.users.count { it.isSelected } > 1) {
                SetHoursDialogFragment.create(
                    bundler = _bundler,
                    crmSetHoursMultipleNavigation = CrmSetHoursMultipleNavigation.restore(
                        CrmSetHoursNavigationData.MultipleData(
                            requestCode = _requestCodeSetHours,
                            vacancyId = vacancy?.vacancyId ?: VacancyId.restore(-1),
                            massIds = if (state.isAllSelected) {
                                listOf()
                            } else {
                                state.users.filter { it.isSelected }
                                    .map { it.item.id }
                            },
                            massExcludeIds = state.users.filter { !it.isSelected }
                                .map { it.item.id },
                            massToAll = state.isAllSelected
                        )
                    )
                )
                    .show(parentFragmentManager, null)
                handleSetHoursDialogResult()
            } else {
                _messageDisplay.showMessage("Выберите волонтеров, которым хотите проставить часы".r)
            }
        }
    }

    private fun handleSetHoursDialogResult() {
        SetHoursDialogFragment
            .onResultMultiple(
                this@HoursFragment,
                _requestCodeSetHours,
                _bundler
            )
            .subscribeBy { pair ->
                _state.sendWhen<HoursState, HoursState.Processing> {
                    setForceUpdate(true)
                }
                if (pair.second.isNotEmpty()) {
                    _state.sendWhen<HoursState, HoursState.Processing> {
                        setExceptionUsers(pair.first.toInt(), pair.second)
                    }
                    CrmSetApplicationHoursExceptionFragment.create(
                        _bundler,
                        CrmSetUnsetHoursParams.restore(
                            CrmSetUnsetHoursParamsData(
                                vacancyId = vacancy?.vacancyId ?: VacancyId.restore(
                                    -1L
                                ),
                                attemptHours = pair.first.toInt(),
                                crmVacancyRequests = pair.second.toPersistentList()
                            )
                        )
                    ) {
                        _state.sendWhen<HoursState, HoursState.Processing> {
                            setSelectUsersMode(HoursState.SelectVolunteersMode.Single)
                        }
                    }.show(childFragmentManager, "ExceptonDialog")
                } else {
                    _state.sendWhen<HoursState, HoursState.Processing> {
                        setSelectUsersMode(
                            HoursState.SelectVolunteersMode.Single
                        )
                    }
                }
            }
            .scopedByDestroy()
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<HoursState.Processing>()
            .distinctUntilChanged { processing -> processing.exceptionUsers }
            .map { it.exceptionUsers }
            .observeOn(RxSchedulers.main())
            .subscribeBy { unsetHoursRequest ->
                binding.unsetHoursWarning.isGoneVisible = unsetHoursRequest.isNotEmpty()
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<HoursState.Processing>()
            .distinctUntilChanged { processing -> processing.users }
            .observeOn(RxSchedulers.main())
            .subscribeBy { state ->
                binding.setHoursProgressMore.gone()
                binding.selectVolunteers.isGoneVisible =
                    state.users.size > 0 && state.users.all { it.item.maxHours > 0 } && binding.setMultipleHoursHeader.isGone
                _swipeRefreshLayout.isRefreshing = false
                _adapterSetHours.submitList(state.users)
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<HoursState.Processing>()
            .distinctUntilChanged { processing -> processing.currentPage }
            .map { it.currentPage }
            .filter { it > 0 }
            .observeOn(RxSchedulers.main())
            .subscribeBy { page ->
                _swipeRefreshLayout.isRefreshing = true
                _api.overview().getCrmVacancyRequests(
                    page = page,
                    vacancyId = vacancy!!.vacancyId,
                    status = CrmAcceptRejectApplicationState.SelectedFilter.Accepted.filterId
                )
                    .subscribeOn(RxSchedulers.io())
                    .observeOn(RxSchedulers.main())
                    .subscribe({
                        _swipeRefreshLayout.isRefreshing = false
                        _state.sendWhen<HoursState, HoursState.Processing> {
                            addUsers(it.data.map {
                                CrmSetHoursAdapter.SetHoursItem(item = it)
                            }.toPersistentList())
                        }
                        binding.setHoursListMore.isGoneVisible = it.meta.lastPage > page
                        binding.setHoursProgressMore.gone()
                    }, {
                        _messageDisplay.showMessage(getString(R.string.general_load_requests_failed).r)
                        _swipeRefreshLayout.isRefreshing = false
                        binding.setHoursProgressMore.gone()
                        binding.setHoursListMore.isEnabled = true
                    })
                    .scopedByDestroy()
            }
            .scopedByDestroy()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<HoursState.Processing>()
            .distinctUntilChanged { processing -> processing.forceUpdate }
            .filter { it.forceUpdate }
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _swipeRefreshLayout.isRefreshing = true
                _api.overview().getCrmVacancyRequests(
                    page = 1,
                    vacancyId = vacancy!!.vacancyId,
                    status = CrmAcceptRejectApplicationState.SelectedFilter.Accepted.filterId
                )
                    .subscribeOn(RxSchedulers.io())
                    .observeOn(RxSchedulers.main())
                    .subscribe({
                        _swipeRefreshLayout.isRefreshing = false
                        _state.sendWhen<HoursState, HoursState.Processing> {
                            addUsers(it.data.map {
                                CrmSetHoursAdapter.SetHoursItem(item = it)
                            }.toPersistentList())
                        }
                        _state.sendWhen<HoursState, HoursState.Processing> {
                            setForceUpdate(false)
                        }
                        binding.setHoursListMore.isGoneVisible = it.meta.lastPage > 1
                    }, {
                        _messageDisplay.showMessage(getString(R.string.general_load_requests_failed).r)
                        _swipeRefreshLayout.isRefreshing = false
                        binding.setHoursProgressMore.gone()
                        binding.setHoursListMore.isEnabled = true
                    })
                    .scopedByDestroy()
            }
            .scopedByDestroy()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<HoursState.Processing>()
            .distinctUntilChanged { processing -> processing.exceptionUsers }
            .map { it.exceptionUsers }
            .observeOn(RxSchedulers.main())
            .subscribeBy { unsetHoursRequest ->
                binding.unsetHoursWarning.isGoneVisible = unsetHoursRequest.isNotEmpty()
            }
            .scopedByDestroy()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<HoursState.Processing>()
            .distinctUntilChanged { processing -> processing.selectVolunteersMode }
            .observeOn(RxSchedulers.main())
            .subscribeBy { state ->
                when (state.selectVolunteersMode) {
                    HoursState.SelectVolunteersMode.Single -> {
                        binding.apply {
                            selectVolunteers.isGoneVisible =
                                state.users.size > 0 && state.users.all { it.item.maxHours > 0 }
                            setMultipleHoursHeader.gone()
                        }
                    }

                    HoursState.SelectVolunteersMode.Multiple -> {
                        binding.apply {
                            selectVolunteers.gone()
                            setMultipleHoursHeader.visible()
                        }
                    }
                }
            }
            .scoped()

        _swipeRefreshLayout
            .onRefresh
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state.sendWhen<HoursState, HoursState.Processing> {
                    setForceUpdate(true)
                }
            }
            .scoped()

        _adapterSetHours
            .onSelectClicked
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy { request ->
                _state.sendWhen<HoursState, HoursState.Processing> {
                    inputSelectedRequests(request)
                }
            }
            .scoped()

        _adapterSetHours
            .onSetHoursClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy { request ->
                showSetHoursDialog(request.item)
            }
            .scoped()
    }

    //    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
//        super.onViewCreated(view, savedInstanceState)
//
//        vacancy = arguments?.getParams(_bundler)
//        if (vacancy == null) {
//            findNavController().navigateUp()
//            return
//        }
//
//        binding.setHoursList.apply {
//            adapter = _adapterSetHours
//            addSpacingItemDecoration {
//                it.verticalSpacing(8.dp)
//            }
//        }
//
//        binding.applyRejectList.apply {
//            adapter = _adapterAcceptReject
//            addSpacingItemDecoration {
//                it.verticalSpacing(8.dp)
//            }
//        }
//
//        binding.apply {
//            warningText.text = view.context.spannedString {
//                it.append("Обратите внимание")
//                    .styleBold()
//                    .append(" есть волонтеры, которым не удалось проставить часы")
//                    .styleNormal()
//            }
//            showWarningList.setOnClickListener {
//                findNavController().navigate(
//                    R.id.action_crmSetApplicationHoursFragment_to_CrmSetUnsetApplicationsHoursFragment,
//                    CrmSetUnsetHoursParams.restore(
//                        CrmSetUnsetHoursParamsData(
//                            vacancyId = vacancy?.vacancyId ?: VacancyId.restore(-1L),
//                            attemptHours = (_state.currentValue as? CrmSetApplicationHoursState.Processing)?.attemptHours
//                                ?: 0,
//                            crmVacancyRequests = (_state.currentValue as? CrmSetApplicationHoursState.Processing)?.unsetHoursRequest.orEmpty()
//                                .toPersistentList()
//                        )
//                    ).intoBundle(_bundler)
//                )
//            }
//            selectEveryone.setOnClickListener {
//                _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                    isAllSelected(
//                        true
//                    )
//                }
//            }
//            cancelEveryone.setOnClickListener {
//                _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                    inputSelectVolunteersMode(
//                        CrmSetApplicationHoursState.SelectVolunteersMode.Single
//                    )
//                }
//            }
//            setHoursForAll.setOnClickListener {
//                (_state.currentValue as? CrmSetApplicationHoursState.Processing)?.let { state ->
//                    if (state.requestList.count { it.isSelected } == 1) {
//                        state.requestList.firstOrNull()?.let { showSetHoursDialog(it.item) }
//                    } else if (state.isAllSelected || state.requestList.count { it.isSelected } > 1) {
//                        SetHoursDialogFragment.create(
//                            bundler = _bundler,
//                            crmSetHoursMultipleNavigation = CrmSetHoursMultipleNavigation.restore(
//                                CrmSetHoursNavigationData.MultipleData(
//                                    requestCode = _requestCodeSetHours,
//                                    vacancyId = vacancy?.vacancyId ?: VacancyId.restore(-1),
//                                    massIds = if (state.isAllSelected) listOf() else state.requestList.filter { it.isSelected }
//                                        .map { it.item.id },
//                                    massExcludeIds = state.requestList.filter { !it.isSelected }
//                                        .map { it.item.id },
//                                    massToAll = state.isAllSelected
//                                )
//                            )
//                        )
//                            .show(parentFragmentManager, null)
//
//                        SetHoursDialogFragment
//                            .onResultMultiple(
//                                this@HoursFragment,
//                                _requestCodeSetHours,
//                                _bundler
//                            )
//                            .subscribeBy { pair ->
//                                _swipeRefreshLayout.isRefreshing = false
//                                _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                                    loadRequestPage(-1)
//                                }
//                                _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                                    loadRequestPage(1)
//                                }
//                                if (pair.second.isNotEmpty()) {
//                                    findNavController().navigate(
//                                        R.id.action_crmSetApplicationHoursFragment_to_crmSetApplicationHoursExceptionFragment,
//                                        CrmSetUnsetHoursParams.restore(
//                                            CrmSetUnsetHoursParamsData(
//                                                vacancyId = vacancy?.vacancyId ?: VacancyId.restore(
//                                                    -1L
//                                                ),
//                                                attemptHours = pair.first.toInt(),
//                                                crmVacancyRequests = pair.second.toPersistentList()
//                                            )
//                                        ).intoBundle(_bundler)
//                                    )
//                                } else {
//                                    _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                                        inputSelectVolunteersMode(
//                                            CrmSetApplicationHoursState.SelectVolunteersMode.Single
//                                        )
//                                    }
//                                }
//                            }
//                            .scopedByDestroy()
//                    } else {
//                        _messageDisplay.showMessage("Выберите волонтеров, которым хотите проставить часы".r)
//                    }
//                }
//            }
//
//            CrmSetUnsetApplicationsHoursFragment
//                .onResult(
//                    this@HoursFragment,
//                    requestCodeUnsetHours,
//                    _bundler
//                )
//                .subscribeBy { crmVacancyRequest ->
//                    _swipeRefreshLayout.isRefreshing = false
//                    val attemptHoursOld =
//                        (_state.currentValue as CrmSetApplicationHoursState.Processing).attemptHours
//                    _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                        reset(crmVacancyRequest, attemptHoursOld)
//                    }
//                }
//                .scopedByDestroy()
//
//            CrmSetApplicationHoursExceptionFragment
//                .onResult(
//                    this@HoursFragment,
//                    OBSERVING_CODE,
//                    _bundler
//                )
//                .subscribeBy {
//                    _swipeRefreshLayout.isRefreshing = false
//                    _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                        reset(it.data.crmVacancyRequests, it.data.attemptHours)
//                    }
//                }
//                .scopedByDestroy()
//        }
//
//
//
//        _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Idle> { init() }
//
//        _state
//            .observeOn(RxSchedulers.computation())
//            .ofSubtype<CrmSetApplicationHoursState.Processing>()
//            .distinctUntilChanged { it -> it.requestPage }
//            .map { it.requestPage }
//            .filter { it > 0 }
//            .observeOn(RxSchedulers.main())
//            .subscribeBy { page ->
//                if (page == 1){
//                    // Скрываем _adapterAcceptReject, чтобы он всегда появлялся только после загрузки _adapterSetHours
//                    binding.apply {
//                        progress.visible()
//                        applyRejectContainer.gone()
//                    }
//                }
//                _api.overview().getCrmVacancyRequests(
//                    page = page,
//                    query = _query,
//                    vacancyId = vacancy!!.vacancyId,
//                    status = persistentListOf(100, 200, 300, 600)
//                )
//                    .subscribeOn(RxSchedulers.io())
//                    .observeOn(RxSchedulers.main())
//                    .subscribe({
//                        val currentState =
//                            _state.currentValue as? CrmSetApplicationHoursState.Processing
//                        binding.setHoursProgressMore.gone()
//                        binding.setHoursListMore.visible()
//                        if (page != 1) {
//                            _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                                inputRequestList(requestList.plus(it.data.map {
//                                    CrmSetHoursAdapter.SetHoursItem(
//                                        isSelected = currentState?.isAllSelected ?: false,
//                                        item = it
//                                    )
//                                }).toPersistentList())
//                            }
//                        } else {
//                            binding.apply {
//                                progress.gone()
//                                applyRejectContainer.visible()
//                            }
//                            _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                                inputRequestList(it.data.map {
//                                    CrmSetHoursAdapter.SetHoursItem(
//                                        isSelected = currentState?.isAllSelected ?: false,
//                                        item = it
//                                    )
//                                }.toPersistentList())
//                            }
//                        }
//                        binding.setHoursListMore.isGoneVisible =
//                            it.meta.lastPage != page
//                    }, {
//                        _messageDisplay.showMessage(getString(R.string.general_load_requests_failed).r)
//                        binding.setHoursProgressMore.gone()
//                        binding.setHoursListMore.isEnabled = true
//                        binding.applyRejectList.visible()
//                    })
//                    .scopedByDestroy()
//            }
//            .scopedByDestroy()
//
//        _state
//            .observeOn(RxSchedulers.computation())
//            .ofSubtype<CrmSetApplicationHoursState.Processing>()
//            .distinctUntilChanged { it -> it.acceptRejectPage }
//            .map { it.acceptRejectPage }
//            .filter { it > 0 }
//            .observeOn(RxSchedulers.main())
//            .subscribeBy { page ->
//                binding.applyRejectProgressMore.visible()
//                _api.overview().getCrmVacancyRequests(
//                    page = page,
//                    query = _query,
//                    vacancyId = vacancy!!.vacancyId,
//                    status = _selectedFilter ?: persistentListOf(100, 400, 500)
//                )
//                    .subscribeOn(RxSchedulers.io())
//                    .observeOn(RxSchedulers.main())
//                    .doOnError { _messageDisplay.showMessage(getString(R.string.general_load_requests_failed).r) }
//                    .subscribe({
//                        binding.noSuchVolunteers.isGoneVisible = it.data.isEmpty()
//                        binding.applyRejectProgressMore.gone()
//                        binding.applyRejectListMore.visible()
//                        _swipeRefreshLayout.isRefreshing = false
//                        if (page != 1) {
//                            _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                                inputAcceptRejectList(
//                                    acceptRejectList.plus(it.data).toPersistentList()
//                                )
//                            }
//                        } else {
//                            _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                                inputAcceptRejectList(it.data)
//                            }
//                        }
//                        binding.applyRejectListMore.isGoneVisible =
//                            it.meta.lastPage != page
//                    }, {
//                        binding.applyRejectProgressMore.gone()
//                        binding.applyRejectListMore.invisible()
//                        _swipeRefreshLayout.isRefreshing = false
//                    })
//                    .scopedByDestroy()
//            }
//            .scopedByDestroy()
//
//    }
//
//    override fun onResume() = withOnResumeResourcesScope {
//        super.onResume()
//
//        binding.title.text = vacancy?.title
//
//        binding.filtersAll.setOnClickListener {
//            _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                setSelectedFilter(
//                    CrmAcceptRejectApplicationState.SelectedFilter.All
//                )
//            }
//        }
//
//        binding.selectVolunteers.setOnClickListener {
//            _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                inputSelectVolunteersMode(
//                    CrmSetApplicationHoursState.SelectVolunteersMode.Multiple
//                )
//            }
//        }
//
//        binding.filtersAccepted.setOnClickListener {
//            _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                setSelectedFilter(
//                    CrmAcceptRejectApplicationState.SelectedFilter.Accepted
//                )
//            }
//        }
//
//        binding.filtersRejected.setOnClickListener {
//            _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                setSelectedFilter(
//                    CrmAcceptRejectApplicationState.SelectedFilter.Rejected
//                )
//            }
//        }
//
//        binding.filtersReserved.setOnClickListener {
//            _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                setSelectedFilter(
//                    CrmAcceptRejectApplicationState.SelectedFilter.Reserve
//                )
//            }
//        }
//
//        _state
//            .observeOn(RxSchedulers.computation())
//            .ofSubtype<CrmSetApplicationHoursState.Processing>()
//            .distinctUntilChanged { it -> it.selectVolunteersMode }
//            .observeOn(RxSchedulers.main())
//            .subscribeBy { state ->
//                when (state.selectVolunteersMode) {
//                    CrmSetApplicationHoursState.SelectVolunteersMode.Single -> {
//                        binding.apply {
//                            selectVolunteers.visible()
//                            setMultipleHoursHeader.gone()
//                        }
//                    }
//                    CrmSetApplicationHoursState.SelectVolunteersMode.Multiple -> {
//                        binding.apply {
//                            selectVolunteers.gone()
//                            setMultipleHoursHeader.visible()
//                        }
//                    }
//                }
//            }
//            .scoped()
//
//        _state
//            .observeOn(RxSchedulers.computation())
//            .ofSubtype<CrmSetApplicationHoursState.Processing>()
//            .distinctUntilChanged { it -> it.acceptRejectList }
//            .observeOn(RxSchedulers.main())
//            .subscribeBy { state ->
//                binding.filters.isGoneVisible =
//                    !(state.selectedFilter == CrmAcceptRejectApplicationState.SelectedFilter.All && state.acceptRejectList.isEmpty())
//                binding.applyRejectProgressMore.gone()
//                _adapterAcceptReject.submitList(state.acceptRejectList)
//            }
//            .scoped()
//
//        binding.setHoursListMore
//            .onClick
//            .observeOn(RxSchedulers.computation())
//            .throttleUserInput()
//            .observeOn(RxSchedulers.main())
//            .subscribeBy {
//                binding.apply {
//                    setHoursListMore.invisible()
//                    setHoursProgressMore.visible()
//                }
//                _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                    loadRequestPage(this.requestPage + 1)
//                }
//            }
//            .scoped()
//
//        binding.applyRejectListMore
//            .onClick
//            .observeOn(RxSchedulers.computation())
//            .throttleUserInput()
//            .observeOn(RxSchedulers.main())
//            .subscribeBy {
//                binding.apply {
//                    applyRejectListMore.invisible()
//                    applyRejectProgressMore.visible()
//                }
//                _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                    loadAcceptRejectPage(this.acceptRejectPage + 1)
//                }
//            }
//            .scoped()
//
//        _adapterSetHours
//            .onSetHoursClick
//            .observeOn(RxSchedulers.computation())
//            .throttleUserInput()
//            .observeOn(RxSchedulers.main())
//            .subscribeBy { request ->
//                showSetHoursDialog(request.item)
//            }
//            .scoped()
//
//        _state
//            .observeOn(RxSchedulers.computation())
//            .ofSubtype<CrmSetApplicationHoursState.Processing>()
//            .distinctUntilChanged { it -> it.selectedFilter }
//            .map { it.selectedFilter }
//            .observeOn(RxSchedulers.main())
//            .subscribeBy { selectedFilter ->
//                when (selectedFilter) {
//                    CrmAcceptRejectApplicationState.SelectedFilter.All,
//                    CrmAcceptRejectApplicationState.SelectedFilter.Accepted -> {
//                        binding.setHoursContainer.visible()
//                    }
//                    else -> {
//                        binding.setHoursContainer.gone()
//                    }
//                }
//                _selectedFilter = selectedFilter.filterId
//                handleFilterChanges(selectedFilter)
//                _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                    loadAcceptRejectPage(
//                        -1
//                    )
//                }
//                _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                    loadAcceptRejectPage(
//                        1
//                    )
//                }
//            }
//            .scoped()
//
//
//        _adapterAcceptReject
//            .onAcceptClick
//            .observeOn(RxSchedulers.main())
//            .subscribeBy { request ->
//                if (request.hours > 0) {
//                    _cantChangeStatusDialog.show(childFragmentManager, "CantChangeStatusDialog")
//                } else {
//                    _api.overview().acceptVacancy(VacancyId.restore(request.id))
//                        .subscribeOn(RxSchedulers.io())
//                        .observeOn(RxSchedulers.main())
//                        .subscribe({
//                            _swipeRefreshLayout.isRefreshing = false
//                            _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                                inputAcceptRejectList(
//                                    this.acceptRejectList.toMutableList()
//                                        .apply { removeIf { it.id == request.id } }
//                                        .toPersistentList()
//                                )
//                            }
//                            (_state.currentValue as? CrmSetApplicationHoursState.Processing)?.let {
//                                if (it.requestList.firstOrNull { it.item.id == request.id } == null) {
//                                    _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                                        inputRequestList(this.requestList.toMutableList().apply {
//                                            add(
//                                                CrmSetHoursAdapter.SetHoursItem(
//                                                    isSelected = false,
//                                                    item = CrmVacancyRequest.restore(
//                                                        id = request.id,
//                                                        status = CrmVacancyStatus.Accepted,
//                                                        maxHours = 100f,
//                                                        hours = request.hours,
//                                                        volunteer = request.volunteer,
//                                                        vacancy = request.vacancy,
//                                                        eventName = request.eventName
//                                                    )
//                                                )
//                                            )
//                                        }.toPersistentList())
//                                    }
//                                }
//                            }
//                        }, {
//                            _swipeRefreshLayout.isRefreshing = false
//                            _messageDisplay.showMessage("Не удалось добавить в резерв".r)
//                        })
//                        .scoped()
//                }
//            }
//            .scoped()
//
//
//        _adapterAcceptReject
//            .onRejectClick
//            .observeOn(RxSchedulers.main())
//            .subscribeBy { request ->
//                if (request.hours > 0) {
//                    _cantChangeStatusDialog.show(childFragmentManager, "CantChangeStatusDialog")
//                } else {
//                    RejectReasonDialogFragment.create(
//                        _requestCodeRejectReason,
//                        _bundler,
//                        VacancyId.restore(request.id)
//                    )
//                        .show(parentFragmentManager, null)
//
//                    RejectReasonDialogFragment
//                        .onResult(
//                            this@HoursFragment,
//                            _requestCodeRejectReason,
//                            _bundler
//                        )
//                        .subscribeBy {
//                            _swipeRefreshLayout.isRefreshing = false
//                            _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                                inputAcceptRejectList(
//                                    this.acceptRejectList.toMutableList()
//                                        .apply { removeIf { it.id == request.id } }
//                                        .toPersistentList()
//                                )
//                            }
//                            (_state.currentValue as? CrmSetApplicationHoursState.Processing)?.let {
//                                if (it.requestList.firstOrNull { it.item.id == request.id } != null) {
//                                    _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                                        inputRequestList(this.requestList.toMutableList().apply {
//                                            removeIf { it.item.id == request.id }
//                                        }.toPersistentList())
//                                    }
//                                }
//                            }
//                        }
//                        .scoped()
//                }
//            }
//            .scoped()
//
//        _swipeRefreshLayout
//            .onRefresh
//            .observeOn(RxSchedulers.computation())
//            .throttleUserInput()
//            .observeOn(RxSchedulers.main())
//            .subscribeBy {
//                _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                    loadAcceptRejectPage(
//                        -1
//                    )
//                }
//                _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                    loadAcceptRejectPage(
//                        1
//                    )
//                }
//                _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                    loadRequestPage(
//                        -1
//                    )
//                }
//                _state.sendWhen<CrmSetApplicationHoursState, CrmSetApplicationHoursState.Processing> {
//                    loadRequestPage(
//                        1
//                    )
//                }
//            }
//            .scoped()
//
//        _adapterAcceptReject
//            .onReserveClick
//            .observeOn(RxSchedulers.main())
//            .subscribeBy { request ->
//                if (request.hours > 0) {
//                    _cantChangeStatusDialog.show(childFragmentManager, "CantChangeStatusDialog")
//                } else {
//                    _swipeRefreshLayout.isRefreshing = true
//                    _api.overview().reserveVacancy(VacancyId.restore(request.id))
//                        .subscribeOn(RxSchedulers.io())
//                        .observeOn(RxSchedulers.main())
//                        .subscribe({
//                            _swipeRefreshLayout.isRefreshing = false
//                            _messageDisplay.showMessage("Добавлен(-а) в резерв".r)
//                            _adapterAcceptReject.updateElement(
//                                CrmAcceptRejectAdapter.RequestStatus.Idle,
//                                setOf(request.id)
//                            )
//                        }, {
//                            _swipeRefreshLayout.isRefreshing = false
//                            _messageDisplay.showMessage("Не удалось добавить в резерв".r)
//                        })
//                        .scoped()
//                }
//            }
//            .scoped()
//
//        _adapterAcceptReject
//            .onShowRequestClick
//            .observeOn(RxSchedulers.main())
//            .subscribeBy { request ->
//                findNavController().navigate(
//                    R.id.action_crmSetApplicationHoursFragment_to_crmVolunteerRequestFragment,
//                    (request.id to vacancy?.title).intoBundle(_bundler)
//                )
//            }
//            .scoped()
//    }
//
    private fun showSetHoursDialog(crmVacancyRequest: CrmVacancyRequest) {
        SetHoursDialogFragment.create(
            bundler = _bundler,
            crmSetHoursSingleNavigation = CrmSetHoursSingleNavigation.restore(
                CrmSetHoursNavigationData.SingleData(
                    requestCode = _requestCodeSetHours,
                    vacancyId = VacancyId.restore(crmVacancyRequest.id),
                    currentHours = crmVacancyRequest.hours,
                    maxHours = crmVacancyRequest.maxHours,
                )
            )
        )
            .show(parentFragmentManager, null)

        SetHoursDialogFragment
            .onResult(
                this@HoursFragment,
                _requestCodeSetHours,
                _bundler
            )
            .subscribeBy { time ->
                _state.sendWhen<HoursState, HoursState.Processing> {
                    refreshList()
                }
            }
            .scopedByDestroy()
    }

    /** Проверяет одинаковые ли списки, порядок не имеет значения */
    infix fun <T> List<T>.equalsIgnoreOrder(other: List<T>) =
        this.size == other.size && this.toSet() == other.toSet()

    internal fun <T> List<T>.getDistinctLists(other: List<T>): List<T> {
        return (this.subtract(other.toSet()) + other.subtract(this.toSet())).toList()
    }
}
