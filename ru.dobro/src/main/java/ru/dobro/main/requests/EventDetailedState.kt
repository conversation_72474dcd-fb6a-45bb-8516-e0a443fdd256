package ru.dobro.main.requests

import common.library.core.state.load.LoadDataByIdentityState
import ru.dobro.domain.EventId
import ru.dobro.domain.event.Event
import ru.dobro.main.event.EventDataState
import javax.annotation.CheckReturnValue

sealed class EventDetailedState {
    object Idle : EventDetailedState() {
        @CheckReturnValue
        fun load(eventId: EventId): EventDetailedState =
            DisplayingEventDetailed(
                eventDataState = LoadDataByIdentityState.Idle.empty<Event, EventId>()
                    .load(eventId),
                eventId = eventId
            )
    }

    data class DisplayingEventDetailed(
        val eventId: EventId,
        val eventDataState: EventDataState,
    ) : EventDetailedState() {
        fun refresh(): DisplayingEventDetailed = copy(
            eventDataState = when (eventDataState) {
                is LoadDataByIdentityState.Idle.Empty -> eventDataState.load(eventId)
                is LoadDataByIdentityState.Idle.Loaded -> eventDataState.refresh()
                is LoadDataByIdentityState.Idle.Failed -> eventDataState.retry()
                else -> eventDataState
            }
        )

        @CheckReturnValue
        inline fun updateEventDataState(
            crossinline action: EventDataState.() -> EventDataState
        ): EventDetailedState = copy(eventDataState = eventDataState.action())
    }
}
