package ru.dobro.main.requests

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.Toolbar
import androidx.constraintlayout.motion.widget.MotionLayout
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.rxjava2.flowable
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.android.material.appbar.AppBarLayout
import common.library.android.inflateBy
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.message.MessageDisplay
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.string.r
import common.library.android.widget.gone
import common.library.android.widget.isGoneVisible
import common.library.android.widget.onClick
import common.library.android.widget.onRefresh
import common.library.android.widget.visible
import common.library.android.widget.wrapIntoSwipeRefresh
import common.library.core.resource.ResourcesScope
import common.library.core.rx.RxSchedulers
import common.library.core.rx.ofSubtype
import common.library.core.variable.Agent
import common.library.core.variable.agent
import common.library.core.variable.sendWhen
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.rx2.asObservable
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.App
import ru.dobro.R
import ru.dobro.api.DobroApi
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.account.AccountManager
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.error.ErrorHandler
import ru.dobro.core.isShimmerAnimated
import ru.dobro.databinding.MainRequestsViewerBinding
import ru.dobro.domain.overview.VacancyRequest
import ru.dobro.domain.overview.VacancyRequestId
import ru.dobro.main.search.SearchFragment
import javax.annotation.CheckReturnValue

class RequestsViewerFragment : BaseFragment({
    bind<RequestsAdapter>() with singleton { RequestsAdapter(instance()) }
    bind<Agent<RequestsState>>() with singleton { agent(RequestsState.Idle) }
}), HasCustomToolbar {

    private lateinit var binding: MainRequestsViewerBinding

    private lateinit var _swipeRefreshLayout: SwipeRefreshLayout

    private val _state: Agent<RequestsState> by instance()

    private val _requestsAdapter: RequestsAdapter by instance()

    private val _messageDisplay: MessageDisplay by instance()

    private val _errorHandler: ErrorHandler by instance()

    private val _dobroApi: DobroApi by instance()

    private val _bundler: Bundler by instance()

    private val _accountManager: AccountManager by instance()

//    private val _hiddenVacancyRequestsDao: HiddenVacancyRequestDao by instance()

    override val screenName: String
        get() = AnalyticsConstants.Screen.Main.requests

    @CheckReturnValue
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar = object :
        HasCustomToolbar.CustomToolbar.Layout() {
        @CheckReturnValue
        override fun onCreateView(container: ViewGroup): View =
            container.context.inflateBy(R.layout.application___toolbar__empty, container)

        @CheckReturnValue
        override fun getAppBarLayout(view: View): AppBarLayout? = null

        @CheckReturnValue
        override fun getToolbar(view: View): Toolbar {
            return view.findViewById(R.id.application___toolbar)
        }
    }

    @CheckReturnValue
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = MainRequestsViewerBinding.inflate(inflater)
        _swipeRefreshLayout = binding.root.wrapIntoSwipeRefresh()
        return _swipeRefreshLayout
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.requests.adapter = _requestsAdapter

        binding.requests.isGoneVisible = false
        binding.skeleton.mainRequestsSkeletonShimmer.isGoneVisible = true
        binding.skeleton.mainRequestsSkeletonShimmer.isShimmerAnimated = true
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        val request: RequestsFilterRequest? = arguments?.getParams(_bundler)

        if (request === null) {
            logger.debug { "No account info." }

            findNavController().navigateUp()

            return
        }

        val accountInfo = _accountManager.getInfo()

        if (accountInfo === null) {
            logger.debug { "No account info." }

            findNavController().navigateUp()

            return
        }

        (parentFragment?.view as? MotionLayout)?.transitionToStart()

        _state
            .observeOn(RxSchedulers.computation())
            .toObservable()
            .ofSubtype<RequestsState.Idle>()
            .subscribeBy {
                _state.sendWhen<RequestsState, RequestsState.Idle> {
                    load(accountInfo.identity)
                }
            }
            .scoped()

        _swipeRefreshLayout
            .onRefresh
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _requestsAdapter.refresh()
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<RequestsState.Processing>()
            .distinctUntilChanged()
            .flatMap {
                Pager(PagingConfig(App.defaultPageSize), 1) {
                    val source = RequestsViewerSource(
                        _api = _dobroApi,
                        _userId = it.userId,
                        _logger = logger,
                        _request = request,
                        _messageDisplay = _messageDisplay
                    )
                    source
                }
                    .flowable
            }
            .observeOn(RxSchedulers.main())
            .subscribeBy { data: PagingData<VacancyRequest> ->
                _requestsAdapter.submitData(lifecycle, data)
            }
            .scoped()

        _requestsAdapter
            .loadStateFlow
            .distinctUntilChanged()
            .asObservable()
            .observeOn(RxSchedulers.computation())
            .filter { it.refresh is LoadState.NotLoading && _requestsAdapter.itemCount > 0 }
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.mainRequestsEmpty.gone()
                binding.requests.visible()
                binding.skeleton.mainRequestsSkeletonShimmer.isGoneVisible = false
                binding.skeleton.mainRequestsSkeletonShimmer.isShimmerAnimated = false

                (parentFragment as? RequestsFragment)?.updateTabsCount()
            }
            .scoped()

        _requestsAdapter
            .loadStateFlow
            .distinctUntilChanged()
            .asObservable()
            .observeOn(RxSchedulers.computation())
            .filter { it.refresh is LoadState.NotLoading && _requestsAdapter.itemCount == 0 }
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.mainRequestsEmpty.visible()
                binding.requests.gone()
                binding.skeleton.mainRequestsSkeletonShimmer.isGoneVisible = false
                binding.skeleton.mainRequestsSkeletonShimmer.isShimmerAnimated = false
            }
            .scoped()

        _requestsAdapter
            .loadStateFlow
            .distinctUntilChanged()
            .asObservable()
            .observeOn(RxSchedulers.computation())
            .map { it.refresh is LoadState.Loading && _requestsAdapter.itemCount > 0 }
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _swipeRefreshLayout.isRefreshing = it
            }
            .scoped()

        binding.mainRequestsSearch
            .onClick
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                SearchFragment.navigate(findNavController(), _bundler).toSearch(
                    actionId = R.id.main__search,
                    shouldAskLocationPermission = true
                )
            }
            .scoped()

        /*_state
            .handlePlainLoading<
                RequestsState,
                RequestsState.Processing,
                PersistentList<Task>>(
                toTaskState = { it.tasksDataState },
                task = {
                    _targetedHelpApi.overview().getMyTaskDataAsync()
                        .logWarning(logger) { "Failed to load \"my\" tasks." }
                },
                updateState = { updateTaskDataState(it) }
            )
            .scoped()*/

        _requestsAdapter
            .onVacancyClick
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.tasksOnTaskClick)
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Main.requestsOnVacancyClick)

                findNavController().navigate(
                    R.id.main__requests__to__main__vacancy,
                    it.intoBundle(_bundler)
                )
            }
            .scoped()

        _requestsAdapter
            .onRemoveClick
            .throttleUserInput()
            .observeOn(RxSchedulers.io())
            .subscribeBy { vacancyId ->
                removeMyVacancyRequest(vacancyId)
            }
            .scoped()

        _requestsAdapter
            .onVacancyRequestClick
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy { vacancyRequest: VacancyRequest ->
                findNavController().navigate(
                    R.id.main__requests_to_eventDetailedFragment,
                    vacancyRequest.intoBundle(_bundler)
                )
            }
            .scoped()
    }

    private fun ResourcesScope.removeMyVacancyRequest(vacancyId: VacancyRequestId) {
        fun isLoading(isLoading: Boolean) {
            requireActivity().runOnUiThread {
                _swipeRefreshLayout.isRefreshing = isLoading
            }
        }

        isLoading(true)
        _dobroApi.overview()
            .removeMyVacancyRequest(vacancyId = vacancyId.unwrap())
            .subscribeOn(RxSchedulers.io())
            .observeOn(RxSchedulers.main())
            .subscribe({
                isLoading(false)

                _requestsAdapter.removeItemById(vacancyId)
                if (_requestsAdapter.itemCount == 1) {
                    binding.mainRequestsEmpty.visible()
                    binding.requests.gone()
                }

                (parentFragment as? RequestsFragment)?.updateTabsCount()
            }, {
                isLoading(false)

                _messageDisplay.showMessage("Не удалось выполнить операцию, попробуйте позже".r)
            })
            .scoped()
    }
}
