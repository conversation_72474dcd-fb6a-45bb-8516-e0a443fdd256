package ru.dobro.main.requests

import android.content.ClipData
import android.content.res.ColorStateList
import android.net.Uri
import android.os.Bundle
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.navigation.fragment.findNavController
import com.google.android.material.appbar.AppBarLayout
import common.library.android.clipboardManager
import common.library.android.getSupportColor
import common.library.android.inflateBy
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.intent.newTask
import common.library.android.intent.send
import common.library.android.intent.text
import common.library.android.intent.tryStartActivityBy
import common.library.android.intent.view
import common.library.android.message.MessageDisplay
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.string.r
import common.library.android.string.rString
import common.library.android.string.span.spannedString
import common.library.android.widget.gone
import common.library.android.widget.isGoneVisible
import common.library.android.widget.visible
import common.library.core.Characters
import common.library.core.contract.ContractException
import common.library.core.data.MimeType
import common.library.core.orFalse
import common.library.core.rx.RxSchedulers
import common.library.core.rx.ofSubtype
import common.library.core.variable.Agent
import common.library.core.variable.agent
import common.library.core.variable.sendWhen
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.rxkotlin.subscribeBy
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.R
import ru.dobro.api.DobroApi
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.format.DisplayFormat
import ru.dobro.core.metric.Param
import ru.dobro.databinding.FragmentEventDetailedBinding
import ru.dobro.domain.Organizer
import ru.dobro.domain.OrganizerType
import ru.dobro.domain.UserId
import ru.dobro.domain.overview.VacancyRequest
import ru.dobro.domain.overview.VacancyRequestStatus
import ru.dobro.images
import ru.dobro.main.map.MapFragment
import ru.dobro.main.organization.OrganizationRequest
import javax.annotation.CheckReturnValue

/**
 * Required param [VacancyRequest]
 */
class EventDetailedFragment(override val screenName: String = AnalyticsConstants.Screen.Main.eventDetailed) :
    BaseFragment({
        bind<Agent<EventDetailedState>>() with singleton { agent(EventDetailedState.Idle) }
    }), HasCustomToolbar {
    private lateinit var binding: FragmentEventDetailedBinding

    private val _bundler: Bundler by instance()

    private val _api: DobroApi by instance()

    private val _messageDisplay: MessageDisplay by instance()

    private val _displayFormat: DisplayFormat by instance()

    private val _state: Agent<EventDetailedState> by instance()

    private var vacancyRequest: VacancyRequest? = null
    private var organizer: Organizer? = null

    @CheckReturnValue
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar = object :
        HasCustomToolbar.CustomToolbar.Layout() {
        @CheckReturnValue
        override fun onCreateView(container: ViewGroup): View =
            container.context.inflateBy(R.layout.application___toolbar__white, container)

        @CheckReturnValue
        override fun getAppBarLayout(view: View): AppBarLayout? = null

        @CheckReturnValue
        override fun getToolbar(view: View): Toolbar {
            return view.findViewById(R.id.application___toolbar)
        }
    }

    init {
        setHasOptionsMenu(true)
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.application__share, menu)
        super.onCreateOptionsMenu(menu, inflater)
    }

    override fun onPrepareOptionsMenu(menu: Menu) {
        super.onPrepareOptionsMenu(menu)

        val currentState = _state.currentValue
        if (currentState is EventDetailedState.DisplayingEventDetailed) {
            menu.findItem(R.id.application___share).apply {
                setIconTintList(
                    ColorStateList.valueOf(
                        ContextCompat.getColor(
                            requireContext(),
                            R.color.application___color__primary
                        )
                    )
                )
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.application___share -> {
                onShareClick()
                true
            }

            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = FragmentEventDetailedBinding.inflate(inflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        vacancyRequest = arguments?.getParams(_bundler)

        if (vacancyRequest === null) {
            logger.warning { "Failed to get event data" }

            findNavController().navigateUp()

            return
        }
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()
        _state
            .sendWhen<EventDetailedState, EventDetailedState.DisplayingEventDetailed> {
                refresh()
            }

        _state
            .ofSubtype<EventDetailedState.DisplayingEventDetailed>()
            .subscribeBy {
                vacancyRequest?.let {
                    bindEvent(it)
                } ?: findNavController().navigateUp()
                organizer?.let {
                    bindOrganizer(it)
                } ?: binding.organizerContainer.gone()
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<EventDetailedState.Idle>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state
                    .sendWhen<EventDetailedState, EventDetailedState.Idle> {
                        load(vacancyRequest!!.vacancy.event!!.identity)
                    }
                bindEvent(vacancyRequest!!)
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<EventDetailedState.Idle>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _api
                    .overview()
                    .getEventAsync(vacancyRequest!!.vacancy.event!!.identity)
                    .subscribeOn(RxSchedulers.computation())
                    .observeOn(AndroidSchedulers.mainThread())
                    .doOnError { _messageDisplay.showMessage("Не удалось загрузить данные".r) }
                    .subscribe({
                        organizer = it.organizer
                        bindOrganizer(organizer!!)
                    }, {
                        binding.organizerContainer.gone()
                        binding.progressOrganization.gone()
                    })
                    .scoped()
            }
            .scoped()
    }

    private fun bindEvent(vacancyRequest: VacancyRequest) {
        binding.progress.gone()

        val statusTextColor = when (vacancyRequest.status) {
            VacancyRequestStatus.New -> requireContext().getColor(R.color.main__requests___status__new)
            VacancyRequestStatus.Accepted -> requireContext().getColor(R.color.main__requests___status__accepted)
            VacancyRequestStatus.CanceledByVolunteer -> requireContext().getColor(R.color.main__requests___status__canceled)
            VacancyRequestStatus.InReserve -> requireContext().getColor(R.color.main__requests___status__reserved)
            VacancyRequestStatus.RejectByOrganization -> requireContext().getColor(R.color.main__requests___status__rejected)
            VacancyRequestStatus.Removed -> requireContext().getColor(R.color.main__requests___status__removed)
            VacancyRequestStatus.NonAttendance -> requireContext().getColor(R.color.main__requests___status__non_attendance)
            else -> throw ContractException("Unsupported status type")
        }

        val statusBackgroundColor = when (vacancyRequest.status) {
            VacancyRequestStatus.New -> requireContext().getColor(R.color.main__requests___status__new_background)
            VacancyRequestStatus.Accepted -> requireContext().getColor(R.color.main__requests___status__accepted_background)
            VacancyRequestStatus.CanceledByVolunteer -> requireContext().getColor(R.color.main__requests___status__canceled_background)
            VacancyRequestStatus.InReserve -> requireContext().getColor(R.color.main__requests___status__reserved_background)
            VacancyRequestStatus.RejectByOrganization -> requireContext().getColor(R.color.main__requests___status__rejected_background)
            VacancyRequestStatus.Removed -> requireContext().getColor(R.color.main__requests___status__removed_background)
            VacancyRequestStatus.NonAttendance -> requireContext().getColor(R.color.main__requests___status__non_attendance_background)
            else -> throw ContractException("Unsupported status type")
        }

        val statusText = when (vacancyRequest.status) {
            VacancyRequestStatus.New -> resources.getString(R.string.main__requests___status__new)
            VacancyRequestStatus.Accepted -> resources.getString(R.string.main__requests___status__accepted)
            VacancyRequestStatus.CanceledByVolunteer -> resources.getString(R.string.main__requests___status__canceled_by_volunteer)
            VacancyRequestStatus.InReserve -> resources.getString(R.string.main__requests___status__reserved)
            VacancyRequestStatus.RejectByOrganization -> resources.getString(R.string.main__requests___status__reject_by_organization)
            VacancyRequestStatus.Removed -> resources.getString(R.string.main__requests___status__removed)
            VacancyRequestStatus.NonAttendance -> resources.getString(R.string.main__requests___status__non_attendance)
            else -> throw ContractException("Unsupported status type")
        }

        binding.status.apply {
            setTextColor(statusTextColor)
            background.setTint(statusBackgroundColor)
            text = statusText
            visible()
        }

        val event = vacancyRequest.vacancy.event
        if (event === null) {
            return
        }

        binding.title.apply {
            text = event.title
            visible()
        }

        val shouldShowOpenMapButton: Boolean = event.address?.isOnline()?.not().orFalse()
        binding.locationShowMap.apply {
            isGoneVisible = shouldShowOpenMapButton
            setOnClickListener {
                firebaseAnalyticManager.sendEventWithId(
                    AnalyticsConstants.Event.Main.eventOnMapClick,
                    event.identity
                )
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.eventOnMapClick)

                event.address?.let { location ->
                    MapFragment
                        .navigate(findNavController(), _bundler)
                        .toMap(R.id.detailed__event__to__main__map, location)
                }
            }
        }

        val shouldShowAddress = event.address != null
        binding.location.apply {
            isGoneVisible = shouldShowAddress
            text = event.address?.title
        }
        binding.locationContainer.isGoneVisible = shouldShowAddress

        val clickablePart = vacancyRequest.vacancy.title
        val notClickablePart =
            getString(R.string.main__requests___vacancy__item__function_title)

        val subtitleSpannedString = requireContext().spannedString {
            it
                .append(notClickablePart)
                .spans { ForegroundColorSpan(requireContext().getColor(R.color.black)) }
                .append(Characters.nonBreakingSpace)
                .append(clickablePart)
        }

        val subtitleClickableSpan: ClickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                findNavController().navigate(
                    R.id.detailed__event__to__main__vacancy,
                    vacancyRequest.vacancy.identity.intoBundle(_bundler)
                )
            }

            override fun updateDrawState(ds: TextPaint) {
                ds.isUnderlineText = false
                ds.color =
                    requireContext().getSupportColor(R.color.application___color__primary)
            }
        }

        subtitleSpannedString.setSpan(
            subtitleClickableSpan,
            notClickablePart.length + 1,
            subtitleSpannedString.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        binding.functionAssistant.apply {
            movementMethod = LinkMovementMethod.getInstance()
            text = subtitleSpannedString
        }

        binding.functionContainer.visible()

        binding.date.text = _displayFormat.dateAndTimeIntervalFormatter().format(
            vacancyRequest.vacancy.period.start.toLocalDateTime()..vacancyRequest.vacancy.period.endInclusive.toLocalDateTime()
        )

//        if (eventDurationTitle != null)
//            binding.date.text = eventDurationTitle
//        else {
//            val startDate =
//                vacancyRequest.vacancy.event?.eventDuration?.start?.toLocalDateTime()
//            val endDate =
//                vacancyRequest.vacancy.event?.eventDuration?.endInclusive?.toLocalDateTime()
//            ifAllNotNull(startDate, endDate) { start, end ->
//                binding.date.text =
//                    _displayFormat.dateAndTimeIntervalShortFormatter()
//                        .format(start..end)
//            }
//        }

        binding.calendarContainer.visible()

        when (vacancyRequest.status) {
            VacancyRequestStatus.CanceledByVolunteer -> {
                binding.rejectContainer.visible()
                binding.rejectTitle.text =
                    resources.getString(R.string.main__requests___status__canceled_by_volunteer__title)
                vacancyRequest.rejectReason?.takeIf { it.isNotEmpty() }?.let {
                    binding.rejectText.text = it
                } ?: binding.rejectContainer.gone()
            }

            VacancyRequestStatus.RejectByOrganization -> {
                binding.rejectContainer.visible()
                binding.rejectTitle.text =
                    resources.getString(R.string.main__requests___status__reject_by_organization__title)
                vacancyRequest.rejectReason?.takeIf { it.isNotEmpty() }?.let {
                    binding.rejectText.text = it
                } ?: binding.rejectContainer.gone()
            }

            else -> binding.rejectContainer.gone()
        }

        vacancyRequest.chatUrl?.let { chatUrl ->
            binding.vkContainer.visible()
            binding.vkOpenChat.setOnClickListener {
                tryStartActivityBy {
                    it
                        .view(Uri.parse(chatUrl))
                        .newTask()
                }
            }
            binding.vkCopyLink.setOnClickListener {
                val clipData =
                    ClipData.newPlainText("text", chatUrl)

                val clipboardManager = context?.clipboardManager
                if (clipboardManager !== null) {
                    clipboardManager.setPrimaryClip(clipData)
                    _messageDisplay.showMessage(R.string.main__requests___vacancy__item__vk__copied.rString)
                }
            }
        }

        val organizationPhone = vacancyRequest.vacancy.event?.organizationPhone
        binding.phoneText.isGoneVisible = organizationPhone != null
        binding.phoneContainer.isGoneVisible = organizationPhone != null
        organizationPhone.let { phone ->
            binding.phoneText.text = requireContext().spannedString {
                it.append(R.string.main__organization___phone_variant)
                    .append(Characters.space)
                    .append(phone)
                    .spans { ForegroundColorSpan(requireContext().getColor(R.color.application___color__primary)) }
            }
            binding.phoneContainer.setOnClickListener {
                tryStartActivityBy {
                    it
                        .view(Uri.parse("tel:$phone"))
                        .newTask()
                }
            }
        }

        val organizationEmail = vacancyRequest.vacancy.event?.organizationEmail
        binding.emailText.isGoneVisible = organizationEmail != null
        binding.emailContainer.isGoneVisible = organizationEmail != null
        organizationEmail.let { email ->
            binding.emailText.text = requireContext().spannedString {
                it.append(R.string.main__organization___email)
                    .append(Characters.space)
                    .append(email)
                    .spans { ForegroundColorSpan(requireContext().getColor(R.color.application___color__primary)) }
            }
            binding.emailContainer.setOnClickListener {
                tryStartActivityBy {
                    it
                        .view(Uri.parse("mailto:$email"))
                        .newTask()
                }
            }
        }

        binding.organizerTitle.visible()
    }

    private fun bindOrganizer(organizer: Organizer) {
        binding.progressOrganization.gone()

        binding.organizer.text = organizer.name
        organizer.rating?.let { rating ->
            binding.organizerRating.text =
                StringBuilder().append(Characters.star)
                    .append(_displayFormat.ratingFormatter().format(rating))
        }

        binding.organizerRating.isGoneVisible = organizer.rating != null
        images
            .load(organizer.icon)
            .circleCrop()
            .into(binding.organizerIcon)
        binding.organizerContainer.apply {
            setOnClickListener {
                when (organizer.type) {
                    OrganizerType.Organization ->
                        findNavController()
                            .navigate(
                                R.id.detailed__event__to__main__organization,
                                OrganizationRequest(
                                    organizerId = organizer.identity,
                                    organizationId = organizer.organizationId
                                ).intoBundle(_bundler)
                            )

                    OrganizerType.Volunteer ->
                        findNavController()
                            .navigate(
                                R.id.detailed__event__to__main__profile,
                                UserId.restore(
                                    organizer.organizationId.unwrap()
                                ).intoBundle(_bundler)
                            )
                }
            }
            visible()
        }
    }

    private fun onShareClick() {
        vacancyRequest?.vacancy?.event?.let { event ->
            firebaseAnalyticManager.sendEventWithId(
                AnalyticsConstants.Event.Main.eventOnShareClick,
                event.identity
            )

            metricManager.sendSimpleEvent(
                AnalyticsConstants.Event.Main.eventOnShareClick,
                Param(
                    param = event.identity,
                    paramType = AnalyticsConstants.Params.id
                )
            )

            tryStartActivityBy {
                it.send(MimeType.Text.plain)
                    .text("https://dobro.ru/event/${event.identity.unwrap()}")
            }
        }
    }
}
