package ru.dobro.main.requests

import java.time.ZonedDateTime
import javax.annotation.CheckReturnValue

sealed class RequestsFilterState {
    data object Idle : RequestsFilterState() {
        @CheckReturnValue
        fun setupInitial(): Displaying =
            Displaying(filterByStatus = setOf(), filterByPeriod = null)
    }

    data class Displaying(
        val filterByPeriod: Pair<ZonedDateTime, ZonedDateTime>? = null,
        val filterByStatus: Set<Int>,
        val errorInvalidFilterShown: Boolean = false,
        val requestsCount: Pair<Int, Int>? = null
    ) : RequestsFilterState() {
        @CheckReturnValue
        fun inputFilterByPeriod(startData: ZonedDateTime, endDate: ZonedDateTime): Displaying =
            copy(filterByPeriod = Pair(startData, endDate))

        @CheckReturnValue
        fun showInvalidFilterError(): RequestsFilterState =
            InvalidFilterError(this)

        fun checkToStatusFilter(): ShowStatusFilterState {
            return ShowStatusFilterState(this, filterByStatus)
        }

        fun clearStatuses(): Displaying = copy(filterByStatus = setOf())

        fun inputRequestsCount(vacanciesCount: Int, visitsCount: Int) =
            copy(requestsCount = vacanciesCount to visitsCount)
    }

    data class ShowStatusFilterState(
        val _displaying: Displaying,
        val newStatuses: Set<Int>
    ) : RequestsFilterState() {
        fun inputStatus(status: Int): ShowStatusFilterState =
            copy(newStatuses = newStatuses + status)

        fun removeStatus(status: Int): ShowStatusFilterState =
            copy(newStatuses = newStatuses - status)

        fun apply(): Displaying = _displaying.copy(filterByStatus = newStatuses)
        fun inputStatusSet(statusSet: Set<Int>): ShowStatusFilterState =
            copy(newStatuses = statusSet)

        fun clearStatuses(): ShowStatusFilterState = copy(newStatuses = setOf())
        fun toDisplaying(): Displaying = _displaying
    }

    data class InvalidFilterError(
        private val _displaying: Displaying
    ) : RequestsFilterState() {
        @CheckReturnValue
        fun backToDisplaying(): Displaying =
            _displaying.copy(errorInvalidFilterShown = true)
    }
}
