package ru.dobro.main.requests

import androidx.paging.PagingState
import androidx.paging.rxjava2.RxPagingSource
import common.library.android.message.MessageDisplay
import common.library.android.string.r
import common.library.core.logging.Logger
import common.library.core.logging.logWarning
import common.library.core.rx.RxSchedulers
import io.reactivex.Single
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import ru.dobro.api.DobroApi
import ru.dobro.domain.UserId
import ru.dobro.domain.overview.VacancyRequest
import java.time.format.DateTimeFormatter
import javax.annotation.CheckReturnValue

class RequestsViewerSource(
    private val _api: DobroApi,
    private val _userId: UserId,
    private val _request: RequestsFilterRequest,
    private val _logger: Logger,
    private val _messageDisplay: MessageDisplay
) : RxPagingSource<Int, VacancyRequest>() {
    override fun getRefreshKey(state: PagingState<Int, VacancyRequest>): Int? =
        state.anchorPosition

    @CheckReturnValue
    private fun _toLoadResult(
        list: PersistentList<VacancyRequest>,
        nextKey: Int?,
        prevKey: Int
    ): LoadResult<Int, VacancyRequest> = LoadResult.Page(
        data = list.toPersistentList(),
        prevKey = if (prevKey <= 0) null else prevKey,
        nextKey = nextKey
    )

    override fun loadSingle(params: LoadParams<Int>): Single<LoadResult<Int, VacancyRequest>> {
        if (params.key === null) {
            return Single.just(
                LoadResult.Page(
                    data = persistentListOf(),
                    prevKey = null,
                    nextKey = null
                )
            )
        }

        return when (params) {
            is LoadParams.Refresh -> {
                _api.overview()
                    .getMyVacancyRequests(
                        page = 1,
                        id = _userId,
                        participant = _request.requestType == RequestType.Variants,
                        fromDate = _request.filterByPeriod?.first?.format(
                            DateTimeFormatter.ofPattern(
                                "yyyy-MM-dd"
                            )
                        ),
                        toDate = _request.filterByPeriod?.second?.format(
                            DateTimeFormatter.ofPattern(
                                "yyyy-MM-dd"
                            )
                        ),
                        statuses = _request.filterByStatus
                    )
                    .map {
                        _toLoadResult(
                            list = it.data,
                            nextKey = if (it.meta.lastPage <= 1) null else 2,
                            prevKey = 0
                        )
                    }
            }

            is LoadParams.Prepend -> {
                _api.overview()
                    .getMyVacancyRequests(
                        page = params.key,
                        id = _userId,
                        participant = _request.requestType == RequestType.Variants,
                        fromDate = _request.filterByPeriod?.first?.format(
                            DateTimeFormatter.ofPattern(
                                "yyyy-MM-dd"
                            )
                        ),
                        toDate = _request.filterByPeriod?.second?.format(
                            DateTimeFormatter.ofPattern(
                                "yyyy-MM-dd"
                            )
                        ),
                        statuses = _request.filterByStatus
                    )
                    .observeOn(RxSchedulers.computation())
                    .map {
                        _toLoadResult(
                            list = it.data,
                            nextKey = if (it.meta.lastPage <= params.key) null else params.key + 1,
                            prevKey = params.key - 1
                        )
                    }
            }

            is LoadParams.Append -> {
                _api.overview()
                    .getMyVacancyRequests(
                        page = params.key,
                        id = _userId,
                        participant = _request.requestType == RequestType.Variants,
                        fromDate = _request.filterByPeriod?.first?.format(
                            DateTimeFormatter.ofPattern(
                                "yyyy-MM-dd"
                            )
                        ),
                        toDate = _request.filterByPeriod?.second?.format(
                            DateTimeFormatter.ofPattern(
                                "yyyy-MM-dd"
                            )
                        ),
                        statuses = _request.filterByStatus
                    )
                    .observeOn(RxSchedulers.computation())
                    .map {
                        _toLoadResult(
                            list = it.data,
                            nextKey = if (it.meta.lastPage <= params.key) null else params.key + 1,
                            prevKey = params.key - 1
                        )
                    }
            }
        }
            .logWarning(_logger) { "Failed to load notifications." }
            .onErrorReturn {
                _messageDisplay.showMessage("Не удалось загрузить данные".r)
                LoadResult.Error(it)
            }
    }
}
