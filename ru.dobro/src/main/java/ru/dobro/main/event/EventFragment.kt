package ru.dobro.main.event

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.res.ColorStateList
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import androidx.annotation.IdRes
import androidx.appcompat.widget.Toolbar
import androidx.core.animation.doOnEnd
import androidx.core.content.ContextCompat
import androidx.core.view.doOnLayout
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import arrow.core.Some
import arrow.core.toOption
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.appbar.MaterialToolbar
import common.library.android.coroutines.repeatOnCreate
import common.library.android.coroutines.repeatOnResume
import common.library.android.coroutines.subscribeToFlow
import common.library.android.coroutines.throttleErrorMessages
import common.library.android.coroutines.throttleUserInput
import common.library.android.coroutines.throttleViewUpdates
import common.library.android.dimension.dp
import common.library.android.inflateBy
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.intent.newTask
import common.library.android.intent.send
import common.library.android.intent.text
import common.library.android.intent.tryStartActivityBy
import common.library.android.intent.view
import common.library.android.message.MessageDisplay
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.string.RString
import common.library.android.string.r
import common.library.android.widget.gone
import common.library.android.widget.isGoneVisible
import common.library.android.widget.onClickAsFlow
import common.library.android.widget.onRefreshAsFlow
import common.library.android.widget.onScrollProgressChanged
import common.library.android.widget.recycler_view.addSpacingItemDecoration
import common.library.android.widget.visible
import common.library.android.widget.wrapIntoSwipeRefresh
import common.library.core.Characters
import common.library.core.color.ColorUtils
import common.library.core.contract.unreachable
import common.library.core.data.MimeType
import common.library.core.lazyGet
import common.library.core.orFalse
import common.library.core.state.load.LoadDataByIdentityState
import common.library.core.time.toEpochMilli
import common.library.core.variable.ScreenState
import kotlinx.collections.immutable.persistentSetOf
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.merge
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.reactive.asFlow
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.R
import ru.dobro.api.DobroApi
import ru.dobro.authorization.AuthorizationFragment
import ru.dobro.authorization.AuthorizationStubFragment
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.RequestCodes
import ru.dobro.core.ToolbarInterpolation
import ru.dobro.core.account.AccountManager
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.error.ErrorHandler
import ru.dobro.core.error.ErrorMessages
import ru.dobro.core.format.DisplayFormat
import ru.dobro.core.isShimmerAnimated
import ru.dobro.core.metric.Param
import ru.dobro.core.setBackgroundAndIconsColorByScroll
import ru.dobro.core.setHtmlText
import ru.dobro.databinding.FragmentEventBinding
import ru.dobro.domain.Category
import ru.dobro.domain.CategoryId
import ru.dobro.domain.EventId
import ru.dobro.domain.OrganizerType
import ru.dobro.domain.UserId
import ru.dobro.domain.Vacancy
import ru.dobro.domain.event.Event
import ru.dobro.images
import ru.dobro.main.event.adapters.CategoryAdapter
import ru.dobro.main.event.adapters.FunctionsAdapter
import ru.dobro.main.event.adapters.FunctionsAdapterDelegate
import ru.dobro.main.event.adapters.TagsAdapter
import ru.dobro.main.map.MapFragment
import ru.dobro.main.organization.DocumentsAdapter
import ru.dobro.main.organization.OrganizationRequest
import ru.dobro.main.profile.AddOrRemoveEventFavoriteRequest
import ru.dobro.main.profile.FavoriteMode
import ru.dobro.main.profile.organization.HelpMessageFragment
import ru.dobro.main.profile.organization.HelpMessageType
import ru.dobro.main.profile.organization.addEvent.AddEventFragment
import ru.dobro.main.profile.organization.addVacancy.AddVacancyFragment
import ru.dobro.main.profile.organization.addVariant.AddVariantFragment
import ru.dobro.main.search.SearchFragment
import ru.dobro.main.vacancies.VacanciesFragment
import ru.dobro.main.vacancy.VacancyFragment
import java.time.ZoneId
import java.util.Calendar
import javax.annotation.CheckReturnValue

/**
 * Required param [EventId]
 */
class EventFragment(
    override val screenName: String = AnalyticsConstants.Screen.Main.event
) : BaseFragment({
    bind<ScreenState<EventState>>() with singleton { ScreenState(EventState.Idle) }
    bind<CategoryAdapter>() with singleton { CategoryAdapter() }
}), HasCustomToolbar {

    private val api: DobroApi by instance()

    private val accountManager: AccountManager by instance()

    private val bundler: Bundler by instance()

    private val messageDisplay: MessageDisplay by instance()

    private val errorHandler: ErrorHandler by instance()

    private val state: ScreenState<EventState> by instance()

    private val categoriesAdapter: CategoryAdapter by instance()

    private val functionsAdapter: FunctionsAdapter = FunctionsAdapter().apply {
        attachDelegate(object : FunctionsAdapterDelegate {
            override fun onFunctionClicked(function: Vacancy) {
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.eventOnVacanciesClick)

                VacancyFragment.navigate(findNavController(), bundler)
                    .toVacancy(
                        R.id.main__event__to__main__vacancy,
                        function.identity
                    )
            }
        })
    }
    private val documentsAdapter: DocumentsAdapter by lazyGet { DocumentsAdapter() }
    private val tagsAdapter = TagsAdapter()

    private val displayFormat: DisplayFormat by instance()

    private val _onEditClick: MutableSharedFlow<ClickType> = MutableSharedFlow()
    private val _onShareClick: MutableSharedFlow<ClickType> = MutableSharedFlow()
    private val _onFavoriteClick: MutableSharedFlow<ClickType> = MutableSharedFlow()

    private lateinit var binding: FragmentEventBinding
    private lateinit var swipeRefreshLayout: SwipeRefreshLayout

    private var scrollButtonAnimatorSet: AnimatorSet? = null

    private val requestCodeAuthorizationStub: String =
        RequestCodes.scopedBy<EventFragment>("authorization_stub")

    private val requestCodeAuthorization: String =
        RequestCodes.scopedBy<AuthorizationFragment>("authorization")

    private var eventId: EventId? = null

    companion object {
        private val _requestCodeOpenedFromDeeplink: String =
            RequestCodes.scopedBy<EventFragment>("opened_from_deep")

        class NavigationContext(
            private val _navigation: NavController,
            private val _bundler: Bundler,
        ) {
            fun toEvent(
                @IdRes actionId: Int,
                id: EventId,
                fromDeepLink: Boolean = false
            ) {
                _navigation.navigate(
                    actionId,
                    id.intoBundle(_bundler).apply {
                        putBoolean(_requestCodeOpenedFromDeeplink, fromDeepLink)
                    }
                )
            }
        }

        @CheckReturnValue
        fun navigate(
            navigation: NavController,
            bundler: Bundler
        ): NavigationContext = NavigationContext(navigation, bundler)
    }

    @CheckReturnValue
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        object :
            HasCustomToolbar.CustomToolbar.Layout(HasCustomToolbar.CustomToolbarStyle.OverlapContent) {
            @CheckReturnValue
            override fun onCreateView(container: ViewGroup): View =
                container.context.inflateBy(
                    R.layout.application___toolbar__transparent,
                    container
                )

            @CheckReturnValue
            override fun getAppBarLayout(view: View): AppBarLayout? {
                return view.findViewById(R.id.main__task__appbar__transparent)
            }

            @CheckReturnValue
            override fun getToolbar(view: View): Toolbar {
                repeatOnCreate {
                    binding.content
                        .onScrollProgressChanged()
                        .asFlow()
                        .subscribeToFlow { scrollY ->
                            view.findViewById<MaterialToolbar>(R.id.application___toolbar)
                                .setBackgroundAndIconsColorByScroll(
                                    scrollFactor = scrollY,
                                    interpolation = ToolbarInterpolation.Builder()
                                        .setOffsetTopBeforeAnimationStartPx(150f)
                                        .build()
                                )

                            val functionsTop = binding.functionsTitle.y - binding.content.bottom
                            if (scrollY > functionsTop) {
                                hideScrollButton()
                            } else {
                                showScrollButton()
                            }
                        }
                }

                return view.findViewById(R.id.application___toolbar)
            }
        }

    init {
        setHasOptionsMenu(true)
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.menu_event, menu)
        super.onCreateOptionsMenu(menu, inflater)
    }

    override fun onPrepareOptionsMenu(menu: Menu) {
        super.onPrepareOptionsMenu(menu)

        val currentState = state.value
        if (currentState is EventState.Displaying) {
            menu.findItem(R.id.menu_event_favourite).apply {
                setIcon(
                    if (currentState.eventDataState is EventDataStateLoaded &&
                        currentState.eventDataState.data.isFavorite
                    ) {
                        R.drawable.application___favorite__selected
                    } else {
                        R.drawable.application___favorite
                    }
                )
                setIconTintList(
                    ColorStateList.valueOf(
                        ColorUtils.blendARGB(
                            ContextCompat.getColor(requireContext(), R.color.white),
                            ContextCompat.getColor(
                                requireContext(),
                                R.color.application___color__primary
                            ),
                            ToolbarInterpolation.Builder().setOffsetTopBeforeAnimationStartPx(150f)
                                .build().ofScrollPx(binding.content.scrollY)
                        )
                    )
                )
            }

            menu.findItem(R.id.menu_event_edit).apply {
                isVisible =
                    currentState.eventDataState is EventDataStateLoaded && currentState.eventDataState.data.canEdit &&
                        currentState.eventDataState.data.isEditable && isUpToDate(currentState.eventDataState.data)
                setIconTintList(
                    ColorStateList.valueOf(
                        ColorUtils.blendARGB(
                            ContextCompat.getColor(requireContext(), R.color.white),
                            ContextCompat.getColor(
                                requireContext(),
                                R.color.application___color__primary
                            ),
                            ToolbarInterpolation.Builder().setOffsetTopBeforeAnimationStartPx(150f)
                                .build().ofScrollPx(binding.content.scrollY)
                        )
                    )
                )
            }

            menu.findItem(R.id.menu_event_share).apply {
                setIconTintList(
                    ColorStateList.valueOf(
                        ColorUtils.blendARGB(
                            ContextCompat.getColor(requireContext(), R.color.white),
                            ContextCompat.getColor(
                                requireContext(),
                                R.color.application___color__primary
                            ),
                            ToolbarInterpolation.Builder().setOffsetTopBeforeAnimationStartPx(150f)
                                .build().ofScrollPx(binding.content.scrollY)
                        )
                    )
                )
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.menu_event_edit -> {
                onEditClick()
                true
            }

            R.id.menu_event_share -> {
                onShareClick()
                true
            }

            R.id.menu_event_favourite -> {
                onFavouriteClick()
                true
            }

            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = FragmentEventBinding.inflate(inflater)
        swipeRefreshLayout = binding.root.wrapIntoSwipeRefresh()
        return swipeRefreshLayout
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        eventId = arguments?.getParams(bundler)

        if (eventId === null) {
            logger.warning { "Failed to get event data" }

            findNavController().navigateUp()

            return
        }

        binding.categories.apply {
            adapter = categoriesAdapter
            addSpacingItemDecoration { it.horizontalSpacing(8.dp) }
        }

        binding.tags.apply {
            adapter = tagsAdapter
        }

        binding.functions.apply {
            adapter = functionsAdapter
            addSpacingItemDecoration { it.verticalSpacing(8.dp) }
        }

        binding.documentsList.adapter = documentsAdapter

        binding.apply {
            more.setOnClickListener {
                state.sendWhen<EventState.Displaying> {
                    nextFunctionPage()
                }
            }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<EventState.Idle>()
                .subscribeToFlow {
                    state
                        .sendWhen<EventState.Idle> {
                            load(eventId!!, accountManager)
                        }
                }
        }

        viewLifecycleOwner.repeatOnResume(
            onError = {
                errorHandler.ofException(it)?.let {
                    messageDisplay.showMessage("Не удалось загрузить функции".r)
                }
            }
        ) {
            state
                .observeWhen<EventState.Displaying>()
                .distinctUntilChangedBy { displaying -> displaying.functionsPage }
                .map { it.functionsPage }
                .subscribeToFlow { page ->
                    binding.apply {
                        more.gone()
                        moreProgress.visible()
                    }

                    val result = api.overview().getEventVacancies(
                        page = page,
                        eventId = eventId!!,
                        status = null,
                        limit = 4
                    )

                    state.sendWhen<EventState.Displaying> {
                        addFunctions(result.data)
                    }

                    binding.moreProgress.gone()

                    if (result.meta.lastPage == page) {
                        binding.more.gone()
                    } else {
                        binding.more.visible()
                    }

                    if (result.meta.totalItems == 0L) {
                        messageDisplay.showMessage("Вам необходимо создать вакансию или вариант посещения".r)
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<EventState.Displaying>()
                .distinctUntilChangedBy { displaying -> displaying.functions }
                .map { it.functions }
                .subscribeToFlow { functions ->
                    binding.functionsTitle.isGoneVisible = functions.isNotEmpty()
                    functionsAdapter.submitData(functions)
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<EventState.Displaying>()
                .distinctUntilChangedBy { displaying -> displaying.expandTextState }
                .map { it.expandTextState }
                .subscribeToFlow { expandState ->
                    when (expandState) {
                        EventState.ExpandState.Expanded -> {
                            binding.description.maxLines = Int.MAX_VALUE
                            binding.descriptionExpand.text =
                                getString(R.string.main__event___description__shorten)
                            binding.description.doOnLayout {
                                binding.description.postDelayed({
                                    if (binding.functionsTitle.y - binding.content.bottom >= 0 && binding.functionsTitle.bottom < binding.content.scrollY) {
                                        showScrollButton()
                                    }
                                }, 300)
                            }
                        }

                        EventState.ExpandState.Collapsed -> {
                            binding.description.maxLines = 6
                            binding.descriptionExpand.text =
                                getString(R.string.main__event___description__expand)
                            binding.description.doOnLayout {
                                binding.description.postDelayed({
                                    if (binding.functionsTitle.y - binding.content.bottom >= 0
                                        && binding.functionsTitle.bottom < binding.content.scrollY
                                    ) {
                                        showScrollButton()
                                    }
                                }, 300)
                            }
                            binding.content.smoothScrollTo(0, binding.description.top)
                        }
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            swipeRefreshLayout
                .onRefreshAsFlow()
                .throttleUserInput()
                .subscribeToFlow {
                    state.sendWhen<EventState.Displaying> { refresh() }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state.handleLoadingByIdentityFlow<EventState.Displaying, Event, EventId>(
                scope = this,
                toTaskState = { it.eventDataState },
                task = {
                    api.overview().getEvent(it)
                },
                onError = {
                    messageDisplay.showMessage("Не удалось загрузить данные".r)
                    logger.warning { "Failed to load event." }
                },
                onUpdateState = { updateEventDataState(it) }
            )
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<EventState.Displaying>()
                .mapNotNull {
                    val toOption = ErrorMessages.ofException(
                        when (it.eventDataState) {
                            is EventDataStateFailed -> errorHandler.ofException(it.eventDataState.error)
                            else -> null
                        }
                    ).toOption()
                    toOption
                }
                .onEach {
                    state.sendWhen<EventState.Displaying> { handleAnyFailed() }
                }
                .filterIsInstance<Some<RString>>()
                .throttleErrorMessages()
                .subscribeToFlow {
                    messageDisplay.showMessage(it.t)
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<EventState.Displaying>()
                .filter {
                    (it.eventDataState is EventDataStateLoaded || it.eventDataState is EventDataStateReloading)
                }
                .distinctUntilChangedBy { event -> event.eventDataState }
                .map {
                    when (it.eventDataState) {
                        is EventDataStateLoaded -> it.eventDataState.data
                        is EventDataStateReloading -> it.eventDataState.previousData
                        is LoadDataByIdentityState.Idle.Ready,
                        is LoadDataByIdentityState.Idle.Empty,
                        is LoadDataByIdentityState.Idle.Failed,
                        is LoadDataByIdentityState.InProgress.Loading -> unreachable()
                    }
                }
                .throttleViewUpdates()
                .subscribeToFlow {
                    bindEvent(it)
                    requireActivity().invalidateOptionsMenu()
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observe()
                .map {
                    it is EventState.Displaying &&
                        ((it.eventDataState is EventDataStateReloading && !it.eventDataState.isBackground))
                }
                .distinctUntilChanged()
                .subscribeToFlow {
                    swipeRefreshLayout.isRefreshing = it
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<EventState.Displaying>()
                .map {
                    (it.eventDataState is EventDataStateLoaded || it.eventDataState is EventDataStateReloading)
                }
                .distinctUntilChanged()
                .subscribeToFlow {
                    binding.content.isGoneVisible = it
                    binding.skeleton.mainEventSkeletonContainer.isGoneVisible = !it
                    binding.skeleton.mainEventSkeletonShimmer.isShimmerAnimated = !it
                }
        }

        val clicksFlow = merge(
            binding.organizer.onClickAsFlow.map { ClickType.Organizer },
            binding.createVacancy.onClickAsFlow.map { ClickType.CreateVacancy },
            binding.createVariant.onClickAsFlow.map { ClickType.CreateVariant },
            binding.visit.onClickAsFlow.map { ClickType.Visit },
            binding.locationShowMap.onClickAsFlow.map { ClickType.Map },
            _onFavoriteClick,
            _onEditClick,
            _onShareClick
        )

        val eventFlow = state
            .observeWhen<EventState.Displaying>()
            .map { it.eventDataState }
            .filterIsInstance<EventDataStateLoaded>()
            .map { it.data }

        viewLifecycleOwner.repeatOnResume {
            clicksFlow
                .throttleUserInput() // реализация ниже
                .combine(eventFlow) { click, event -> click to event }
                .subscribeToFlow { (clickType, event) ->
                    handleClick(clickType, event)
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .handleActionExecution<EventState.AddOrRemoveFavoriteProcessing, AddOrRemoveEventFavoriteRequest>(
                    scope = this,
                    toTaskState = { it.taskState },
                    task = {
                        when (it.favoriteMode) {
                            FavoriteMode.Add -> api.profile().addEventToFavorites(it.eventId)
                            FavoriteMode.Remove -> api.profile()
                                .removeEventFromFavorites(it.eventId)
                        }
                    },
                    onError = {
                        messageDisplay.showMessage("Не удалось загрузить данные".r)
                        logger.warning { "Failed to remove event from favorites" }
                    },
                    onUpdateState = { updateTaskState(it) }
                )
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<EventState.Completed>()
                .subscribeToFlow {
                    when (it) {
                        is EventState.Completed.FavoriteSuccess -> {
                            state.sendWhen<EventState.Completed.FavoriteSuccess> {
                                backToDisplaying()
                            }
                        }

                        is EventState.Completed.FavoriteFailed -> {
                            if (it.error !== null) {
                                messageDisplay.showMessage(it.error)
                            }

                            state.sendWhen<EventState.Completed.FavoriteFailed> {
                                backToDisplaying()
                            }
                        }

                        is EventState.Completed.Fail -> {
                            if (it.error !== null) {
                                messageDisplay.showMessage(it.error)
                            }
                        }
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            categoriesAdapter
                .onItemClick
                .throttleUserInput()
                .subscribeToFlow { category ->
                    SearchFragment
                        .navigate(findNavController(), bundler)
                        .toSearch(
                            R.id.main__search,
                            persistentSetOf<Category>(
                                Category.restore(
                                    id = CategoryId.restore(category.item.id),
                                    title = category.item.title,
                                    icon = category.item.icon,
                                    iconGray = category.item.icon,
                                    color = category.item.color
                                )
                            )
                        )
                }
        }

        viewLifecycleOwner.repeatOnResume {
            documentsAdapter
                .onItemClick
                .throttleUserInput()
                .subscribeToFlow { link ->
                    tryStartActivityBy {
                        it
                            .view(link)
                            .newTask()
                    }
                }
        }

        parentFragmentManager.setFragmentResultListener(
            requestCodeAuthorizationStub,
            viewLifecycleOwner
        ) { _, _ ->
            AuthorizationFragment
                .navigate(
                    findNavController(),
                    bundler
                )
                .toAuthorizationForResult(
                    R.id.main__event__to__authorization,
                    requestCodeAuthorization
                )
        }

        parentFragmentManager.setFragmentResultListener(
            requestCodeAuthorization,
            viewLifecycleOwner
        ) { _, _ ->
            state.sendWhen<EventState.Displaying> {
                refresh()
            }
        }
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        val openedFromDeepLink = arguments?.getBoolean(
            _requestCodeOpenedFromDeeplink,
            false
        ).orFalse()

        if (accountManager.getAccountType() != ru.dobro.core.account.AccountType.Physic && openedFromDeepLink) {
            HelpMessageFragment
                .navigate(findNavController(), bundler)
                .to(
                    actionId = R.id.organizationStatusHelpFragment,
                    type = HelpMessageType.VolunteerDeeplink
                )
        }

        state.sendWhen<EventState.Displaying> {
            refresh()
        }
    }

    private enum class ClickType {
        Organizer,
        Edit,
        Favorite,
        Share,
        Vacancies,
        CreateVacancy,
        CreateVariant,
        Visit,
        Map
    }

    private fun bindEvent(event: Event) {
        tagsAdapter.submitList(event.tags)

        if (event.canEdit) {
            binding.createVacancy.apply {
                isGoneVisible = event.isEditable
                isActivated = event.isEditable
                isEnabled = event.isEditable
            }

            binding.createVariant.apply {
                isGoneVisible = event.isEditable
                isActivated = event.isEditable
                isEnabled = event.isEditable
            }

            val color = when (event.status) {
                else -> requireContext().getColor(R.color.main__requests___status__new)
            }
            binding.statusIcon.apply {
                setColorFilter(color)
                visible()
            }

            binding.status.apply {
                setTextColor(color)
                text = event.statusText
                visible()
            }

//            binding.createVacancy.isGoneVisible = event.hasVacancies || event.hasParticipants
        }

        binding.title.text = event.title

        event.eventDuration?.let { eventDuration ->
            binding.period.text = displayFormat.dateAndTimeIntervalFormatter()
                .format(eventDuration)
        }

        binding.periodContainer.isGoneVisible = event.eventDuration != null

        // Показать переход к карте, только если есть адрес при оффлайн-типе
        val shouldShowOpenMapButton: Boolean = if (event.isOnline) false else event.address != null
        // Скрыть контейнер адреса, только если адреса нет при офлайн-типе
        val shouldShowAddress = event.isOnline || event.address != null

        binding.location.apply {
            isGoneVisible = shouldShowAddress
            text =
                if (event.isOnline) getString(R.string.main__event___is_online) else event.address?.title
        }

        binding.locationShowMap.isGoneVisible = shouldShowOpenMapButton

        // Скрыть, только если адреса нет при офлайн-типе
        binding.locationContainer.isGoneVisible = event.isOnline || event.address != null

        binding.description.apply {
            movementMethod = LinkMovementMethod.getInstance()
            setHtmlText(event.description)
            if ((state.value as? EventState.Displaying)?.expandTextState != EventState.ExpandState.Expanded) {
                postDelayed({
                    if (binding.description.lineCount <= 6) {
                        binding.descriptionExpand.gone()
                    }
                }, 200)
            }
        }
        binding.descriptionExpand.setOnClickListener {
            state.sendWhen<EventState.Displaying> {
                changeExpandState()
            }
        }

        binding.organizer.text = event.organizer.name

        images
            .load(event.organizer.icon)
            .circleCrop()
            .into(binding.organizerIcon)

        event.organizer.rating?.let { rating ->
            binding.organizerRating.text =
                StringBuilder().append(Characters.star)
                    .append(displayFormat.ratingFormatter().format(rating))
        }
        binding.organizerRating.isGoneVisible = event.organizer.rating != null

        images
            .load(event.image?.uri)
            .into(binding.image)

        categoriesAdapter.submitData(event.categories)

        val currentTime: Long = Calendar.getInstance().timeInMillis
        val end = event.eventDuration?.endInclusive?.toEpochMilli()
        end?.let {
            if (currentTime > end) {
//                binding.createVacancy.text = getString(R.string.main__event___vacancies_archived)
//                binding.visit.text = getString(R.string.main__event___vacancies_archived)
//                binding.createVacancy.isActivated = false
//                binding.visit.isActivated = false
            } else {
                // binding.createVacancy.text = getString(R.string.main__event___vacancies)
//                binding.createVacancy.isActivated = true
                binding.visit.text = getString(R.string.main__event___visit_event)
                binding.visit.isActivated = true
            }
        }
        if (!accountManager.hasUserAccount()) {
            binding.visit.apply {
                visible()
                setOnClickListener {
                    AuthorizationStubFragment
                        .navigate(
                            findNavController(),
                            bundler
                        )
                        .toAuthorizationStub(requestCodeAuthorizationStub)
                }
            }
        }

        binding.scrollToFunctions.apply {
            val isVacancyRequestPermitted =
                accountManager.hasUserAccount() && (!event.canEdit || !event.isEditable)

            isGoneVisible = isVacancyRequestPermitted
            binding.description.postDelayed({

                binding.content.doOnLayout {
                    if (binding.content.scrollY == 0
                        || (binding.functionsTitle.y - it.bottom >= 0
                            && binding.functionsTitle.bottom < binding.content.scrollY)
                    ) {
                        showScrollButton()
                    }
                }

                setOnClickListener {
                    hideScrollButton()
                    binding.content.smoothScrollTo(0, binding.functionsTitle.top - 300)
                }
            }, 300)
        }

        documentsAdapter.submitList(event.documents)
        binding.documentsList.isGoneVisible = event.documents.isNotEmpty()
        binding.documentsTitle.isGoneVisible = event.documents.isNotEmpty()
    }

    private fun handleClick(clickType: ClickType, event: Event) {
        when (clickType) {
            ClickType.Organizer -> {
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Main.eventOnOrganizerClick)
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.eventOnOrganizerClick)
                if (event.organizer.type != OrganizerType.Volunteer) {
                    findNavController()
                        .navigate(
                            R.id.main__event__to__main__organization,
                            OrganizationRequest(
                                organizerId = event.organizer.identity,
                                organizationId = event.organizer.organizationId
                            ).intoBundle(bundler)
                        )
                } else {
                    event.organizer.let {
                        findNavController().navigate(
                            R.id.action__to__public_profile,
                            UserId.restore(it.organizationId.unwrap()).intoBundle(bundler)
                        )
                    }
                }
            }

            ClickType.Favorite -> {
                // Do nothing
            }

            ClickType.Share -> {
                // Do nothing
            }

            ClickType.Vacancies -> {
                firebaseAnalyticManager.sendEventWithId(
                    AnalyticsConstants.Event.Main.eventOnVacanciesClick,
                    event.identity
                )
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.eventOnVacanciesClick)

                VacanciesFragment
                    .navigate(
                        findNavController(),
                        bundler
                    )
                    .toEventVacancies(
                        R.id.main__vacancies,
                        eventId = event.identity
                    )
            }

            ClickType.CreateVacancy -> {
                AddVacancyFragment
                    .navigate(
                        findNavController(),
                        bundler
                    )
                    .toVacancy(
                        R.id.addVacancyFragment,
                        id = event.identity
                    )
            }

            ClickType.CreateVariant -> {
                AddVariantFragment
                    .navigate(
                        findNavController(),
                        bundler
                    )
                    .toVariant(
                        R.id.addVariantFragment,
                        id = event.identity
                    )
            }

            ClickType.Visit -> {
                firebaseAnalyticManager.sendEventWithId(
                    AnalyticsConstants.Event.Main.eventOnVisitClick,
                    event.identity
                )
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.eventOnVisitClick)

                VacanciesFragment
                    .navigate(
                        findNavController(),
                        bundler
                    )
                    .toEventParticipantVacancies(
                        R.id.main__vacancies,
                        eventId = event.identity
                    )
            }

            ClickType.Map -> {
                firebaseAnalyticManager.sendEventWithId(
                    AnalyticsConstants.Event.Main.eventOnMapClick,
                    event.identity
                )
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.eventOnMapClick)

                event.address?.let { location ->
                    MapFragment
                        .navigate(findNavController(), bundler)
                        .toMap(R.id.main__event__to__main__map, location)
                }
            }

            ClickType.Edit -> {
                // Do nothing
            }
        }
    }

    private fun onEditClick() {
        getCurrentEventDataState()?.let { loadedEvent ->
            AddEventFragment
                .navigate(
                    findNavController(),
                    bundler
                )
                .toAddEventEdit(
                    actionId = R.id.addEventFragment,
                    event = loadedEvent.data
                )
        }
    }

    private fun onShareClick() {
        getCurrentEventDataState()?.let { loadedEvent ->
            firebaseAnalyticManager.sendEventWithId(
                AnalyticsConstants.Event.Main.eventOnShareClick,
                loadedEvent.identity
            )

            metricManager.sendSimpleEvent(
                AnalyticsConstants.Event.Main.eventOnShareClick,
                Param(
                    param = loadedEvent.identity,
                    paramType = AnalyticsConstants.Params.id
                )
            )

            tryStartActivityBy {
                it.send(MimeType.Text.plain)
                    .text(loadedEvent.data.url.toString())
            }
        }
    }

    private fun onFavouriteClick() {
        getCurrentEventDataState()?.let { loadedEvent ->
            if (accountManager.hasUserAccount() && accountManager.getInfo() != null) {
                firebaseAnalyticManager.sendEventWithId(
                    AnalyticsConstants.Event.Main.eventOnFavoriteClick,
                    loadedEvent.identity
                )

                metricManager.sendSimpleEvent(
                    AnalyticsConstants.Event.Main.eventOnFavoriteClick,
                    Param(
                        param = loadedEvent.identity,
                        paramType = AnalyticsConstants.Params.id
                    )
                )
                state.sendWhen<EventState.Displaying> {
                    addOrRemoveEventToFavorites(loadedEvent.data)
                }
            } else {
                AuthorizationStubFragment
                    .navigate(
                        findNavController(),
                        bundler
                    )
                    .toAuthorizationStub(requestCodeAuthorizationStub)
            }
        }
    }

    private fun getCurrentEventDataState(): EventDataStateLoaded? {
        return ((state.value as? EventState.Displaying)?.eventDataState as? EventDataStateLoaded)
    }

    private fun hideScrollButton() {
        if (scrollButtonAnimatorSet?.isRunning == true) {
            scrollButtonAnimatorSet?.doOnEnd {
                val alphaAnimator = ObjectAnimator.ofFloat(binding.scrollToFunctions, "alpha", 0f)
                val translationAnimator = ObjectAnimator.ofFloat(
                    binding.scrollToFunctions,
                    "translationY",
                    binding.scrollToFunctions.height.toFloat()
                )
                scrollButtonAnimatorSet = AnimatorSet()

                scrollButtonAnimatorSet?.playTogether(alphaAnimator, translationAnimator)
                scrollButtonAnimatorSet?.duration = 200
                scrollButtonAnimatorSet?.interpolator = AccelerateInterpolator()

                scrollButtonAnimatorSet?.start()
            }
            return
        }
        val alphaAnimator = ObjectAnimator.ofFloat(binding.scrollToFunctions, "alpha", 0f)
        val translationAnimator = ObjectAnimator.ofFloat(
            binding.scrollToFunctions,
            "translationY",
            binding.scrollToFunctions.height.toFloat()
        )
        scrollButtonAnimatorSet = AnimatorSet()

        scrollButtonAnimatorSet?.playTogether(alphaAnimator, translationAnimator)
        scrollButtonAnimatorSet?.duration = 200
        scrollButtonAnimatorSet?.interpolator = AccelerateInterpolator()

        scrollButtonAnimatorSet?.start()
    }

    private fun showScrollButton() {
        if (scrollButtonAnimatorSet?.isRunning == true) {
            scrollButtonAnimatorSet?.doOnEnd {
                val alphaAnimator = ObjectAnimator.ofFloat(binding.scrollToFunctions, "alpha", 1f)
                val translationAnimator =
                    ObjectAnimator.ofFloat(binding.scrollToFunctions, "translationY", 0f)
                scrollButtonAnimatorSet = AnimatorSet()

                scrollButtonAnimatorSet?.playTogether(alphaAnimator, translationAnimator)
                scrollButtonAnimatorSet?.duration = 200
                scrollButtonAnimatorSet?.interpolator = DecelerateInterpolator()

                scrollButtonAnimatorSet?.start()
            }
            return
        }
        val alphaAnimator = ObjectAnimator.ofFloat(binding.scrollToFunctions, "alpha", 1f)
        val translationAnimator =
            ObjectAnimator.ofFloat(binding.scrollToFunctions, "translationY", 0f)
        scrollButtonAnimatorSet = AnimatorSet()

        scrollButtonAnimatorSet?.playTogether(alphaAnimator, translationAnimator)
        scrollButtonAnimatorSet?.duration = 200
        scrollButtonAnimatorSet?.interpolator = DecelerateInterpolator()

        scrollButtonAnimatorSet?.start()
    }

    private fun isUpToDate(event: Event): Boolean {
        val currentTime: Long = Calendar.getInstance().timeInMillis
        val end = event.eventDuration?.endInclusive?.atZone(ZoneId.systemDefault())?.toInstant()
            ?.toEpochMilli()
        return end?.let {
            currentTime <= it
        } ?: true
    }
}
