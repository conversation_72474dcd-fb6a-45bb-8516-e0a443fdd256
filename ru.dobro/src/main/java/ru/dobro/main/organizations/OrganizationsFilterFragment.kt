package ru.dobro.main.organizations

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.Toolbar
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import arrow.core.Some
import arrow.core.none
import arrow.core.some
import arrow.core.toOption
import arrow.core.toT
import com.google.android.material.appbar.AppBarLayout
import common.library.android.awaitFragmentResult
import common.library.android.coroutines.repeatOnResume
import common.library.android.coroutines.subscribeToFlow
import common.library.android.coroutines.throttleErrorMessages
import common.library.android.coroutines.throttleUserInput
import common.library.android.coroutines.throttleViewUpdates
import common.library.android.coroutines.withLatestFrom
import common.library.android.inflateBy
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.fromBundle
import common.library.android.intent.bundler.getParams
import common.library.android.message.MessageDisplay
import common.library.android.setFragmentResult
import common.library.android.string.RString
import common.library.android.string.r
import common.library.android.widget.isGoneVisible
import common.library.android.widget.onClickAsFlow
import common.library.core.collection.mapPersistent
import common.library.core.coroutines.filterDefined
import common.library.core.lazyGet
import common.library.core.state.load.LoadDataPlainState
import common.library.core.variable.ScreenState
import kotlinx.collections.immutable.PersistentSet
import kotlinx.collections.immutable.mutate
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.R
import ru.dobro.address_selector.AddressSelectorFragment
import ru.dobro.address_selector.Country
import ru.dobro.address_selector.CountryName
import ru.dobro.api.DobroApi
import ru.dobro.api.overview.getOrganizationsCountByFilter
import ru.dobro.categories_selector.CategoriesDataStateIdleLoading
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.RequestCodes
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.error.ErrorHandler
import ru.dobro.core.error.ErrorMessages
import ru.dobro.databinding.CommonFilterBinding
import ru.dobro.domain.Category
import ru.dobro.domain.Location
import javax.annotation.CheckReturnValue

class OrganizationsFilterFragment : BaseFragment({
    bind<ScreenState<OrganizationsFilterState>>() with singleton {
        ScreenState(OrganizationsFilterState.Idle)
    }
}), HasCustomToolbar {

    private val state: ScreenState<OrganizationsFilterState> by instance()

    private val adapter: OrganizationsFilterAdapter by lazyGet { OrganizationsFilterAdapter() }

    private val messageDisplay: MessageDisplay by instance()

    private val errorHandler: ErrorHandler by instance()

    private val dobroApi: DobroApi by instance()

    private val bundler: Bundler by instance()

    private val requestCodeSelectLocation: String =
        RequestCodes.scopedBy<OrganizationsFilterFragment>("select_location")

    private lateinit var binding: CommonFilterBinding

    override val screenName: String
        get() = AnalyticsConstants.Screen.Main.organizationsFilter

    companion object {
        @CheckReturnValue
        fun onResult(
            receiver: Fragment,
            requestKey: String,
            bundler: Bundler
        ): Flow<OrganizationFilterChoiceData> =
            receiver.awaitFragmentResult(requestKey, bundler)
    }

    init {
        setHasOptionsMenu(true)
    }

    @CheckReturnValue
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        object : HasCustomToolbar.CustomToolbar.Layout() {

            @CheckReturnValue
            override fun onCreateView(container: ViewGroup): View =
                container.context.inflateBy(R.layout.application___toolbar__empty, container)

            @CheckReturnValue
            override fun getAppBarLayout(view: View): AppBarLayout? = null

            @CheckReturnValue
            override fun getToolbar(view: View): Toolbar {
                return view.findViewById(R.id.application___toolbar)
            }
        }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.common__filter, menu)
        super.onCreateOptionsMenu(menu, inflater)
    }

    override fun onPrepareOptionsMenu(menu: Menu) {
        super.onPrepareOptionsMenu(menu)

        val currentState = state.value
        if (currentState is OrganizationsFilterState.Choice) {
            menu.findItem(R.id.common__filter___clear).isEnabled =
                currentState.hasAnyChoice()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean =
        when (item.itemId) {
            R.id.common__filter___clear -> {
                state.sendWhen<OrganizationsFilterState.Choice> {
                    clearFilter()
                }
                true
            }

            else -> super.onOptionsItemSelected(item)
        }

    @CheckReturnValue
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = CommonFilterBinding.inflate(inflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.commonFilterList.adapter = adapter

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<OrganizationsFilterState.Idle>()
                .subscribeToFlow {
                    state.sendWhen<OrganizationsFilterState.Idle> {
                        requestDataLoading()
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observe()
                .map {
                    it is OrganizationsFilterState.DataLoading && it.categoriesDataState is CategoriesDataStateIdleLoading
                        || it is OrganizationsFilterState.Choice && it.organizationsByFilterDataState is OrganizationsByFilterDataStateLoading
                }
                .distinctUntilChanged()
                .subscribeToFlow {
                    binding.commonFilterProgress.isGoneVisible = it
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observe()
                .map {
                    it is OrganizationsFilterState.Choice
                }
                .distinctUntilChanged()
                .subscribeToFlow {
                    binding.commonFilterSubmit.isGoneVisible = it
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<OrganizationsFilterState.Choice>()
                .distinctUntilChangedBy { choice -> choice.choiceData }
                .subscribeToFlow { state ->
                    requireActivity().invalidateOptionsMenu()

                    if (state.hasAnyChoice()/*chosenCategories.isNotEmpty()*/) {
                        <EMAIL><OrganizationsFilterState.Choice> { reload() }
                    }

                    adapter.submitList(
                        persistentListOf<OrganizationsFilterAdapter.Item>().mutate { list ->
                            /*list.add(OrganizationsFilterAdapter.Item.Title("Город"))
                            list.add(OrganizationsFilterAdapter.Item.CitySelectionItem(state.choiceData.chosenLocation))
                            list.add(OrganizationsFilterAdapter.Item.Title("Организации"))
                            list.add(
                                OrganizationsFilterAdapter.Item.VerificationStatusItem(
                                    state.choiceData.chosenVerificationStatus
                                )
                            )*/
                            list.add(OrganizationsFilterAdapter.Item.Title("Направления"))
                            list.addAll(state.allCategories.map {
                                OrganizationsFilterAdapter.Item.CategorySelectItem(
                                    category = it,
                                    isChecked = state.choiceData.chosenCategories.contains(it)
                                )
                            })
                            list.add(OrganizationsFilterAdapter.Item.SelectAll(isChecked = state.choiceData.chosenCategories == state.allCategories))
                        }
                    )
                }
        }

        viewLifecycleOwner.repeatOnResume {
            adapter
                .onVerificationTypeClick
                .throttleUserInput()
                .subscribeToFlow {
                    state.sendWhen<OrganizationsFilterState.Choice> {
                        selectVerificationStatus(it)
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            adapter
                .onCitySelectionClick
                .throttleUserInput()
                .subscribeToFlow {
                    AddressSelectorFragment.createSettlement(
                        requestKey = requestCodeSelectLocation,
                        country = Country.Special(CountryName.Russia.title)
                    ).show(parentFragmentManager, null)
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observe()
                .map {
                    if (it is OrganizationsFilterState.Choice) {
                        when (it.organizationsByFilterDataState) {
                            is OrganizationsByFilterDataStateIdleLoaded -> (it.organizationsByFilterDataState.data toT it.hasAnyChoice()).some()
                            is OrganizationsByFilterDataStateReloading -> (it.organizationsByFilterDataState.previousData toT it.hasAnyChoice()).some()
                            else -> (0 toT it.hasAnyChoice()).some()
                        }
                    } else {
                        none()
                    }
                }
                .filterDefined()
                .throttleViewUpdates()
                .subscribeToFlow { (count, hasAnyChoice) ->
                    if (!hasAnyChoice || count == 0) {
                        binding.commonFilterSubmit.text =
                            getString(R.string.main__organizations___filters__empty_results)
                    } else {
                        binding.commonFilterSubmit.text =
                            getString(
                                R.string.main__organizations___filters__results,
                                count.toInt()
                            )
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .observeWhen<OrganizationsFilterState.DataLoading>()
                .map {
                    ErrorMessages.ofException(
                        when (it.categoriesDataState) {
                            is LoadDataPlainState.Idle.Failed -> errorHandler.ofException(it.categoriesDataState.error)
                            else -> null
                        }
                    ).toOption()
                }
                .onEach {
                    state.sendWhen<OrganizationsFilterState.DataLoading> { handleAll() }
                }
                .filterIsInstance<Some<RString>>()
                .throttleErrorMessages()
                .subscribeToFlow {
                    findNavController().navigateUp()
                    messageDisplay.showMessage(it.t)
                }
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .handleLoadingBySelectorFlow<OrganizationsFilterState.Choice,
                    Long, OrganizationFilterChoiceData>(
                    scope = this,
                    toTaskState = { it.organizationsByFilterDataState },
                    task = {
                        dobroApi
                            .overview()
                            .getOrganizationsCountByFilter(
                                categories = it.chosenCategories.mapPersistent { it.identity },
                                location = it.chosenLocation,
                                verificationStatus = it.chosenVerificationStatus
                            )
                    },
                    onError = {
                        messageDisplay.showMessage("Не удалось загрузить данные".r)
                        logger.warning { "Failed to load organizations count by filter." }
                    },
                    onUpdateState = { updateOrganizationsDataState(it) }
                )
        }

        viewLifecycleOwner.repeatOnResume {
            state
                .handlePlainLoadingFlow<OrganizationsFilterState.DataLoading, PersistentSet<Category>>(
                    scope = this,
                    toTaskState = { it.categoriesDataState },
                    task = {
                        dobroApi.overview().getCategories()
                    },
                    onError = {
                        messageDisplay.showMessage("Не удалось загрузить данные".r)
                        logger.warning { "Failed to load categories" }
                    },
                    onUpdateState = { updateCategoriesDataState(it) }
                )
        }

        viewLifecycleOwner.repeatOnResume {
            adapter
                .onAllCategoriesClick
                .throttleUserInput()
                .subscribeToFlow {
                    state.sendWhen<OrganizationsFilterState.Choice> {
                        selectAllCategories()
                    }
                }
        }

        viewLifecycleOwner.repeatOnResume {
            adapter
                .onCategoryItemClick
                .throttleUserInput()
                .subscribeToFlow {
                    state.sendWhen<OrganizationsFilterState.Choice> {
                        selectCategory(it)
                    }
                }
        }

        val requestKey: String? = arguments?.getParams(bundler)
        if (requestKey === null) {
            logger.debug { "Illegal param requestKey: $requestKey" }
            findNavController().navigateUp()
            return
        }

        viewLifecycleOwner.repeatOnResume {
            binding.commonFilterSubmit
                .onClickAsFlow
                .withLatestFrom(
                    state
                        .observeWhen<OrganizationsFilterState.Choice>()
                        .map { it.choiceData }
                )
                .throttleUserInput()
                .subscribeToFlow { (_, choiceData) ->
                    setFragmentResult(
                        requestKey,
                        choiceData,
                        bundler
                    )
                    findNavController().navigateUp()
                }
        }

        parentFragmentManager.setFragmentResultListener(
            requestCodeSelectLocation,
            viewLifecycleOwner
        ) { _, bundle ->
            bundler.fromBundle<Location>(bundle)?.let { result ->
                state.sendWhen<OrganizationsFilterState.Choice> {
                    selectLocation(result)
                }
            }
        }
    }
}
