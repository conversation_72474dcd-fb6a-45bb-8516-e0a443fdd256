package ru.dobro.main.vacancy

import android.Manifest
import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.content.pm.PackageManager
import android.content.res.ColorStateList
import android.location.Criteria
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.annotation.IdRes
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.Toolbar
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import arrow.core.Some
import arrow.core.toOption
import arrow.core.toT
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import common.library.android.dialog.cancelButton
import common.library.android.dialog.dialog
import common.library.android.dialog.okButton
import common.library.android.dimension.dp
import common.library.android.inflateBy
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.intent.send
import common.library.android.intent.text
import common.library.android.intent.tryStartActivityBy
import common.library.android.message.MessageDisplay
import common.library.android.permissions.PermissionsState
import common.library.android.permissions.checkPermissionManager
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleErrorMessages
import common.library.android.rx.throttleUserInput
import common.library.android.rx.throttleViewUpdates
import common.library.android.string.RString
import common.library.android.string.rString
import common.library.android.string.rText
import common.library.android.widget.gone
import common.library.android.widget.isGoneVisible
import common.library.android.widget.onClick
import common.library.android.widget.onRefresh
import common.library.android.widget.recycler_view.addSpacingItemDecoration
import common.library.android.widget.visible
import common.library.android.widget.wrapIntoSwipeRefresh
import common.library.core.collection.mapPersistent
import common.library.core.contract.unreachable
import common.library.core.data.MimeType
import common.library.core.lazyGet
import common.library.core.location.GeoPoint
import common.library.core.logging.logWarning
import common.library.core.orFalse
import common.library.core.rx.RxSchedulers
import common.library.core.rx.invoke
import common.library.core.rx.ofSubtype
import common.library.core.state.load.LoadDataByIdentityState
import common.library.core.state.load.handleLoadingByIdentity
import common.library.core.state.task.TaskActionState
import common.library.core.state.task.handleActionExecution
import common.library.core.variable.Agent
import common.library.core.variable.agent
import common.library.core.variable.sendWhen
import io.reactivex.Flowable
import io.reactivex.disposables.Disposable
import io.reactivex.processors.PublishProcessor
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.rxkotlin.withLatestFrom
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.App
import ru.dobro.R
import ru.dobro.api.DobroApi
import ru.dobro.authorization.AuthorizationFragment
import ru.dobro.authorization.AuthorizationStubFragment
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.RequestCodes
import ru.dobro.core.account.AccountManager
import ru.dobro.core.account.getOrganizationId
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.error.ErrorHandler
import ru.dobro.core.error.ErrorMessages
import ru.dobro.core.format.DisplayFormat
import ru.dobro.core.isShimmerAnimated
import ru.dobro.core.metric.Param
import ru.dobro.core.setHtmlText
import ru.dobro.databinding.MainVacancyBinding
import ru.dobro.domain.OrganizerType
import ru.dobro.domain.UserId
import ru.dobro.domain.VacancyId
import ru.dobro.domain.vacancy.RequestButtonType
import ru.dobro.domain.vacancy.Vacancy
import ru.dobro.domain.vacancy.VacancyRequest
import ru.dobro.domain.vacancy.VacancyRequestCreateStatus
import ru.dobro.domain.vacancy.VacancyRequestStatus
import ru.dobro.images
import ru.dobro.main.event.EventFragment
import ru.dobro.main.map.MapFragment
import ru.dobro.main.organization.OrganizationRequest
import ru.dobro.main.profile.AddOrRemoveVacancyFavoriteRequest
import ru.dobro.main.profile.FavoriteMode
import ru.dobro.main.profile.organization.HelpMessageFragment
import ru.dobro.main.profile.organization.HelpMessageType
import ru.dobro.main.profile.organization.addVacancy.AddVacancyFragment
import ru.dobro.main.profile.organization.addVariant.AddVariantFragment
import ru.dobro.main.vacancy.LocationPermissionState.Idle.checkPermissionsAndOpenIfGranted
import ru.dobro.main.vacancy.LocationPermissionState.Ready.start
import ru.dobro.main.vacancy.states.CheckInRequest
import ru.dobro.main.vacancy.states.VacancyDataStateFailed
import ru.dobro.main.vacancy.states.VacancyDataStateLoaded
import ru.dobro.main.vacancy.states.VacancyDataStateReloading
import ru.dobro.main.vacancy.states.VacancyState
import ru.dobro.main.vacancy.view_adapters.CategoryAdapter
import ru.dobro.main.vacancy.view_adapters.ConditionsAdapter
import ru.dobro.main.vacancy.view_adapters.RequirementsAdapter
import ru.dobro.main.vacancy.view_adapters.TasksAdapter
import ru.dobro.main.vacancy_request.VacancyRequestFragment
import ru.dobro.main.vacancy_request.VacancyRequestResult
import ru.dobro.main.vacancy_request.states.GetVacancyRequestDataStateIdleFailed
import ru.dobro.main.vacancy_request.states.GetVacancyRequestDataStateIdleLoaded
import ru.dobro.main.vacancy_request.states.GetVacancyRequestDataStateReloading
import ru.dobro.main.vacancy_request_reject.VacancyRequestRejectingFragment
import ru.dobro.main.vacancy_request_reject.VacancyRequestRejectingResult
import javax.annotation.CheckReturnValue

/**
 * Required param [VacancyId]
 * */
class VacancyFragment : BaseFragment({
    bind<Agent<VacancyState>>() with singleton { agent(VacancyState.Idle) }
}), HasCustomToolbar {
    private lateinit var binding: MainVacancyBinding

    override val screenName: String
        get() = AnalyticsConstants.Screen.Main.vacancy

    init {
        setHasOptionsMenu(true)
    }

    private val _locationCheckInRequestCode: Int = RequestCodes.result()

    private val _locationCheckOutRequestCode: Int = RequestCodes.result()

    private enum class ClickType {
        Organizer,
        Event,
        Favorite,
        Edit,
        Share,
        CheckPause,
        Map
    }

    @CheckReturnValue
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        object :
            HasCustomToolbar.CustomToolbar.Layout() {
            @CheckReturnValue
            override fun onCreateView(container: ViewGroup): View =
                container.context.inflateBy(R.layout.application___toolbar__white, container)

            @CheckReturnValue
            override fun getAppBarLayout(view: View): AppBarLayout? = null

            @CheckReturnValue
            override fun getToolbar(view: View): Toolbar {
                return view.findViewById(R.id.application___toolbar)
            }
        }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.main__vacancy, menu)
        super.onCreateOptionsMenu(menu, inflater)
    }

    override fun onPrepareOptionsMenu(menu: Menu) {
        super.onPrepareOptionsMenu(menu)

        val currentState = _state.currentValue
        if (currentState is VacancyState.Displaying) {
            menu.findItem(R.id.main__vacancy___favorite).apply {
                setIcon(
                    if (currentState.vacancyDataState is VacancyDataStateLoaded &&
                        currentState.vacancyDataState.data.isFavorite
                    ) {
                        R.drawable.application___favorite__selected
                    } else {
                        R.drawable.application___favorite
                    }
                )
                setIconTintList(
                    ColorStateList.valueOf(
                        ContextCompat.getColor(
                            requireContext(),
                            R.color.application___color__primary
                        )
                    )
                )
            }
            menu.findItem(R.id.main__vacancy___edit).isVisible =
                currentState.vacancyDataState is VacancyDataStateLoaded &&
                    (currentState.vacancyDataState.data.organizer?.identity == _accountManager.getAccountType()
                        .getOrganizationId()) && currentState.vacancyDataState.data.isEditable
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean =
        when (item.itemId) {
            R.id.main__vacancy___share -> {
                _onShareClick.invoke(ClickType.Share)
                true
            }

            R.id.main__vacancy___favorite -> {
                _onFavoriteClick.invoke(ClickType.Favorite)
                true
            }

            R.id.main__vacancy___edit -> {
                _onEditClick.invoke(ClickType.Edit)
                true
            }

            else -> super.onOptionsItemSelected(item)
        }

    companion object {
        private val _requestCodeOpenedFromDeeplink: String =
            RequestCodes.scopedBy<VacancyFragment>("opened_from_deep")

        class NavigationContext(
            private val _navigation: NavController,
            private val _bundler: Bundler
        ) {
            fun toVacancy(
                @IdRes actionId: Int,
                vacancyId: VacancyId,
                fromDeepLink: Boolean = false
            ) {
                _navigation.navigate(
                    actionId,
                    vacancyId.intoBundle(_bundler).apply {
                        putBoolean(_requestCodeOpenedFromDeeplink, fromDeepLink)
                    }
                )
            }
        }

        @CheckReturnValue
        fun navigate(
            navigation: NavController,
            bundler: Bundler
        ): NavigationContext = NavigationContext(navigation, bundler)
    }

    private val _state: Agent<VacancyState> by instance()

    private val _api: DobroApi by instance()

    private val _bundler: Bundler by instance()

    private val _displayFormat: DisplayFormat by instance()

    private val _messageDisplay: MessageDisplay by instance()

    private val _errorHandler: ErrorHandler by instance()

    private val _categoriesAdapter: CategoryAdapter by lazyGet { CategoryAdapter() }

    private val _requirementAdapter: RequirementsAdapter by lazyGet { RequirementsAdapter() }

    private val _conditionsAdapter: ConditionsAdapter by lazyGet { ConditionsAdapter() }

    private val _tasksAdapter: TasksAdapter by lazyGet { TasksAdapter() }

    private val _accountManager: AccountManager by instance()

    private val _onFavoriteClick: PublishProcessor<ClickType> = PublishProcessor.create()
    private val _onEditClick: PublishProcessor<ClickType> = PublishProcessor.create()
    private val _onShareClick: PublishProcessor<ClickType> = PublishProcessor.create()

    private lateinit var _swipeRefreshLayout: SwipeRefreshLayout

    private val _requestCodeAuthorization: String =
        RequestCodes.scopedBy<AuthorizationFragment>("authorization")

    private val _requestCodeAuthorizationStub: String =
        RequestCodes.scopedBy<VacancyFragment>("authorization_stub_from_vacancy")

    private val _requestCodeVacancyRequestRejecting: String =
        RequestCodes.scopedBy<VacancyFragment>("vacancy_request_rejecting")

    private val _requestCodeVacancyRequest: String =
        RequestCodes.scopedBy<VacancyFragment>("vacancy_request")

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = MainVacancyBinding.inflate(inflater)
        _swipeRefreshLayout = binding.root.wrapIntoSwipeRefresh()
        return _swipeRefreshLayout
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if (!_accountManager.hasUserAccount()) {
            binding.mainVacancyApplyForVacancy.visible()
        }

        binding.mainVacancyCategories.adapter = _categoriesAdapter
        binding.mainVacancyCategories.addSpacingItemDecoration { it.horizontalSpacing(8.dp) }
        binding.mainVacancyRequirements.adapter = _requirementAdapter
        binding.mainVacancyConditions.adapter = _conditionsAdapter
        binding.mainVacancyTasks.adapter = _tasksAdapter
    }

    @SuppressLint("MissingPermission")
    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        val param: VacancyId? = arguments?.getParams(_bundler)

        if (param === null) {
            logger.warning { "Invalid vacancy param ." }

            findNavController().navigateUp()

            return
        }

        val openedFromDeepLink =
            arguments?.getBoolean(_requestCodeOpenedFromDeeplink, false).orFalse()
        if (_accountManager.getAccountType() != ru.dobro.core.account.AccountType.Physic && openedFromDeepLink) {
            HelpMessageFragment
                .navigate(findNavController(), _bundler)
                .to(
                    actionId = R.id.organizationStatusHelpFragment,
                    type = HelpMessageType.VolunteerDeeplink
                )
        }

        _state
            .sendWhen<VacancyState, VacancyState.Displaying> {
                refresh()
            }

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<VacancyState.Idle>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state
                    .sendWhen<VacancyState, VacancyState.Idle> {
                        load(param, _accountManager)
                    }
            }
            .scoped()

        _swipeRefreshLayout
            .onRefresh
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state.sendWhen<VacancyState, VacancyState.Displaying> { refresh() }
            }
            .scoped()

        // load vacancy
        _state
            .handleLoadingByIdentity<
                VacancyState,
                VacancyState.Displaying,
                Vacancy,
                VacancyId>(
                toTaskState = { it.vacancyDataState },
                task = {
                    _api
                        .vacancy()
                        .getVacancyAsync(it)
                        .logWarning(logger) { "Failed to load vacancy." }
                },
                updateState = { updateVacancyDataState(it) }
            )
            .scoped()

        _state
            .handleLoadingByIdentity<
                VacancyState,
                VacancyState.Displaying,
                VacancyRequest,
                VacancyId>(
                toTaskState = { it.vacancyRequestState },
                task = {
                    _api
                        .vacancy()
                        .getVacancyRequestAsync(it)
                        .logWarning(logger) { "Failed to load vacancy request." }
                },
                updateState = { updateVacancyRequestDataState(it) }
            )
            .scoped()

        // message and handle errors
        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<VacancyState.Displaying>()
            .map {
                val toOption = ErrorMessages.ofException(
                    when {
                        it.vacancyDataState is VacancyDataStateFailed -> _errorHandler.ofException(
                            it.vacancyDataState.error
                        )

                        it.vacancyRequestState is GetVacancyRequestDataStateIdleFailed -> it.vacancyRequestState.error
                        else -> null
                    }
                ).toOption()
                toOption
            }
            .ofSubtype<Some<RString>>()
            .throttleErrorMessages()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _messageDisplay.showMessage(it.t)
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<VacancyState.Displaying>()
            .filter {
                (it.vacancyDataState is VacancyDataStateLoaded || it.vacancyDataState is VacancyDataStateReloading)
            }
            .map {
                when (it.vacancyDataState) {
                    is VacancyDataStateLoaded -> it.vacancyDataState.data
                    is VacancyDataStateReloading -> it.vacancyDataState.previousData
                    is LoadDataByIdentityState.Idle.Ready,
                    is LoadDataByIdentityState.Idle.Empty,
                    is LoadDataByIdentityState.Idle.Failed,
                    is LoadDataByIdentityState.InProgress.Loading -> unreachable()
                }
            }
            .throttleViewUpdates()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _bindVacancy(it)
                requireActivity().invalidateOptionsMenu()
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .map {
                (it is VacancyState.Displaying && it.locationPermissionState is LocationPermissionState.Process)
                    || (it is VacancyState.CheckInProcessing && it.taskState is TaskActionState.InProgress)
            }
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.mainVacancyProgress.isGoneVisible = it
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<VacancyState.Displaying>()
            .filter {
                (it.vacancyDataState is VacancyDataStateLoaded || it.vacancyDataState is VacancyDataStateReloading)
                    && (it.vacancyRequestState is GetVacancyRequestDataStateIdleLoaded || it.vacancyRequestState is GetVacancyRequestDataStateReloading)
            }
            .map {
                when (it.vacancyDataState) {
                    is VacancyDataStateLoaded -> it.vacancyDataState.data
                    is VacancyDataStateReloading -> it.vacancyDataState.previousData
                    is LoadDataByIdentityState.Idle.Ready,
                    is LoadDataByIdentityState.Idle.Empty,
                    is LoadDataByIdentityState.Idle.Failed,
                    is LoadDataByIdentityState.InProgress.Loading -> unreachable()
                } toT when (it.vacancyRequestState) {
                    is GetVacancyRequestDataStateIdleLoaded -> it.vacancyRequestState.data
                    is GetVacancyRequestDataStateReloading -> it.vacancyRequestState.previousData
                    is LoadDataByIdentityState.Idle.Ready,
                    is LoadDataByIdentityState.Idle.Empty,
                    is LoadDataByIdentityState.Idle.Failed,
                    is LoadDataByIdentityState.InProgress.Loading -> unreachable()
                }
            }
            .observeOn(RxSchedulers.main())
            .subscribeBy { (vacancy, vacancyRequest) ->
                _bindVacancyBottomUiPart(
                    vacancy,
                    vacancyRequest,
                    vacancy.requestButtonType,
                    vacancy.statusText
                )
            }
            .scoped()

        _state
            .handleActionExecution<
                VacancyState,
                VacancyState.AddOrRemoveFavoriteProcessing,
                AddOrRemoveVacancyFavoriteRequest>(
                toTaskState = { it.taskState },
                task = {
                    when (it.favoriteMode) {
                        FavoriteMode.Add ->
                            _api
                                .profile()
                                .addVacancyToFavoritesAsync(it.vacancyId)
                                .logWarning(logger) { "Failed to add vacancy from favorites" }

                        FavoriteMode.Remove ->
                            _api
                                .profile()
                                .removeVacancyFromFavoritesAsync(it.vacancyId)
                                .logWarning(logger) { "Failed to remove vacancy from favorites" }
                    }
                },
                updateState = { updateTaskState(it) }
            )
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<VacancyState.Completed>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                when (it) {
                    is VacancyState.Completed.SuccessCheckIn -> {
                        _state.sendWhen<VacancyState, VacancyState.Completed.SuccessCheckIn> {
                            reset()
                        }
                    }

                    is VacancyState.Completed.Failed -> {
                        if (it.error !== null) {
                            _messageDisplay.showMessage(it.error)
                        }

                        _state.sendWhen<VacancyState, VacancyState.Completed.Failed> {
                            reset()
                        }
                    }

                    is VacancyState.Completed.FavoriteSuccess -> {
                        _state.sendWhen<VacancyState, VacancyState.Completed.FavoriteSuccess> {
                            refresh()
                        }
                    }

                    is VacancyState.Completed.FavoriteFailed -> {
                        if (it.error !== null) {
                            _messageDisplay.showMessage(it.error)
                        }

                        _state.sendWhen<VacancyState, VacancyState.Completed.FavoriteFailed> {
                            backToDisplaying()
                        }
                    }

                    is VacancyState.Completed.Fail -> {
                        if (it.error !== null) {
                            _messageDisplay.showMessage(it.error)
                        }
                    }
                }
            }
            .scoped()

        _state
            .ofSubtype<VacancyState.Displaying>()
            .map {
                (it.vacancyDataState is VacancyDataStateLoaded || it.vacancyDataState is VacancyDataStateReloading)
                    && (it.vacancyRequestState is GetVacancyRequestDataStateIdleLoaded || it.vacancyRequestState is GetVacancyRequestDataStateReloading)
            }
            .distinctUntilChanged()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                if (!_accountManager.hasUserAccount()) {
                    binding.mainVacancyApplyForVacancy.apply {
                        visible()
                        setOnClickListener {
                            AuthorizationStubFragment
                                .navigate(
                                    findNavController(),
                                    _bundler
                                )
                                .toAuthorizationStub(_requestCodeAuthorizationStub)
                        }
                    }
                } else {
                    binding.mainVacancyApplyForVacancy.isGoneVisible = it
                }
            }
            .scoped()

        binding.mainVacancyApplyForVacancy
            .onClick
            .observeOn(RxSchedulers.computation())
            .withLatestFrom(
                _state
                    .ofSubtype<VacancyState.Displaying>()
                    .filter {
                        (it.vacancyDataState is VacancyDataStateLoaded || it.vacancyDataState is VacancyDataStateReloading)
                            && (it.vacancyRequestState is GetVacancyRequestDataStateIdleLoaded || it.vacancyRequestState is GetVacancyRequestDataStateReloading)
                    }
                    .map {
                        when (it.vacancyDataState) {
                            is VacancyDataStateLoaded -> it.vacancyDataState.data
                            is VacancyDataStateReloading -> it.vacancyDataState.previousData
                            is LoadDataByIdentityState.Idle.Ready,
                            is LoadDataByIdentityState.Idle.Empty,
                            is LoadDataByIdentityState.Idle.Failed,
                            is LoadDataByIdentityState.InProgress.Loading -> unreachable()
                        } toT when (it.vacancyRequestState) {
                            is GetVacancyRequestDataStateIdleLoaded -> it.vacancyRequestState.data
                            is GetVacancyRequestDataStateReloading -> it.vacancyRequestState.previousData
                            is LoadDataByIdentityState.Idle.Ready,
                            is LoadDataByIdentityState.Idle.Empty,
                            is LoadDataByIdentityState.Idle.Failed,
                            is LoadDataByIdentityState.InProgress.Loading -> unreachable()
                        }
                    }
            )
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy { (_, vacancyAndVacancyRequests) ->
                val vacancy = vacancyAndVacancyRequests.a
                val vacancyRequest = vacancyAndVacancyRequests.b

                firebaseAnalyticManager.sendEventWithId(
                    AnalyticsConstants.Event.Main.vacancyOnApplyVacancy,
                    vacancy.identity
                )
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.vacancyOnApplyVacancy)

                val isEsiaRequired =
                    vacancyRequest.requirements.firstOrNull { it.type == "esia-confirmed" && !it.satisfy } != null

                val canBeApplied: Boolean =
                    vacancyRequest.status == VacancyRequestStatus.CanCreate && vacancyRequest.createStatus == VacancyRequestCreateStatus.NotCreated

                val canBeRejected: Boolean =
                    vacancyRequest.requestButtonType == RequestButtonType.Cancel
                val hasBeenRequested: Boolean =
                    vacancyRequest.createStatus == VacancyRequestCreateStatus.Created

                val performAction: VacancyPerformAction = when {
                    isEsiaRequired -> VacancyPerformAction.EsiaRequired
                    canBeApplied -> VacancyPerformAction.Apply
                    !canBeApplied && hasBeenRequested && canBeRejected -> VacancyPerformAction.Cancel
                    else -> VacancyPerformAction.None
                }

                // если юзер авторизован, то нажатие сработает только в состоянии
                // Displaying ( когда загружена вакансия и заявки )
                // иначе ведем на авторизацию
                if (_accountManager.hasUserAccount() && _accountManager.getInfo() != null) {
                    when (performAction) {
                        VacancyPerformAction.Apply -> {
                            metricManager.sendSimpleEvent(
                                AnalyticsConstants.Event.Main.vacancyOnApplyVacancy,
                                Param(
                                    param = vacancy.identity,
                                    paramType = AnalyticsConstants.Params.id
                                )
                            )

                            VacancyRequestFragment
                                .navigate(findNavController(), _bundler)
                                .toVacancyRequestFromVacancyFragment(
                                    vacancy,
                                    _requestCodeVacancyRequest
                                )
                        }

                        VacancyPerformAction.Cancel -> {
                            VacancyRequestRejectingFragment
                                .navigate(
                                    findNavController(),
                                    _bundler
                                )
                                .toVacancyRequestRejectFromVacancyFragment(
                                    vacancy.identity,
                                    _requestCodeVacancyRequestRejecting
                                )
                        }

                        VacancyPerformAction.EsiaRequired -> {
                            findNavController().navigate(
                                R.id.esiaRequestFragment,
                                vacancy.identity.intoBundle(_bundler)
                            )
                        }

                        else -> {
                        }
                    }
                } else {
                    AuthorizationStubFragment
                        .navigate(
                            findNavController(),
                            _bundler
                        )
                        .toAuthorizationStub(_requestCodeAuthorizationStub)
                }
            }
            .scoped()

        Flowable
            .mergeArray(
                binding.mainVacancyOrganizer.onClick.map { ClickType.Organizer },
                binding.mainVacancyOpenEvent.onClick.map { ClickType.Event },
                binding.mainVacancyLocationOpenMap.onClick.map { ClickType.Map },
                _onFavoriteClick,
                _onEditClick,
                _onShareClick
            )
            .withLatestFrom(
                _state
                    .observeOn(RxSchedulers.computation())
                    .ofSubtype<VacancyState.Displaying>()
                    .map { it.vacancyDataState }
                    .ofSubtype<VacancyDataStateLoaded>()
                    .map { it.data }
            )
            .subscribeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy { (clickType: ClickType, vacancy: Vacancy) ->
                when (clickType!!) {
                    ClickType.Organizer -> {
                        if (vacancy.organizer?.type != OrganizerType.Volunteer) {
                            firebaseAnalyticManager.sendEventWithId(
                                AnalyticsConstants.Event.Main.vacancyOnOrganizerClick,
                                vacancy.identity
                            )
                            metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.vacancyOnOrganizerClick)

                            vacancy.organizer?.let {
                                findNavController()
                                    .navigate(
                                        R.id.main__vacancy__to__main__organization,
                                        OrganizationRequest(
                                            organizationId = it.organizationId,
                                            organizerId = it.identity
                                        ).intoBundle(_bundler)
                                    )
                            }
                        } else {
                            vacancy.organizer?.let {
                                it.organizationId
                                findNavController().navigate(
                                    R.id.action__to__public_profile,
                                    UserId.restore(it.organizationId.unwrap()).intoBundle(_bundler)
                                )
                            }
                        }
                    }

                    ClickType.Event -> {
                        vacancy.event?.identity?.let { eventId ->
                            firebaseAnalyticManager.sendEventWithId(
                                AnalyticsConstants.Event.Main.vacancyOnEventClick,
                                eventId
                            )
                            metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.vacancyOnEventClick)

                            EventFragment
                                .navigate(
                                    findNavController(),
                                    _bundler
                                )
                                .toEvent(
                                    R.id.main__event,
                                    eventId
                                )
                        }
                    }

                    ClickType.Favorite -> {
                        if (_accountManager.hasUserAccount() && _accountManager.getInfo() != null) {
                            firebaseAnalyticManager.sendEventWithId(
                                AnalyticsConstants.Event.Main.vacancyOnFavoriteClick,
                                vacancy.identity
                            )

                            metricManager.sendSimpleEvent(
                                AnalyticsConstants.Event.Main.vacancyOnFavoriteClick,
                                Param(
                                    param = vacancy.identity,
                                    paramType = AnalyticsConstants.Params.id
                                )
                            )

                            _state.sendWhen<VacancyState, VacancyState.Displaying> {
                                addOrRemoveEventToFavorites(vacancy)
                            }
                        } else {
                            AuthorizationStubFragment
                                .navigate(
                                    findNavController(),
                                    _bundler
                                )
                                .toAuthorizationStub(_requestCodeAuthorizationStub)
                        }
                    }

                    ClickType.Edit -> {
                        if (vacancy.participant) {
                            AddVariantFragment
                                .navigate(
                                    findNavController(),
                                    _bundler
                                )
                                .toVariantEdit(
                                    actionId = R.id.addVariantFragment,
                                    vacancy = vacancy
                                )
                        } else {
                            AddVacancyFragment
                                .navigate(
                                    findNavController(),
                                    _bundler
                                )
                                .toVacancyEdit(
                                    actionId = R.id.addVacancyFragment,
                                    vacancy = vacancy
                                )
                        }
                    }

                    ClickType.CheckPause -> {
                        vacancy.checkIn?.let {
                            if (it.isTimerActive) {
                                firebaseAnalyticManager.sendEventWithId(
                                    AnalyticsConstants.Event.Main.vacancyOnCheckOutClick,
                                    vacancy.identity
                                )
                                metricManager.sendSimpleEvent(
                                    AnalyticsConstants.Event.Main.vacancyOnCheckOutClick,
                                    Param(
                                        param = vacancy.identity,
                                        paramType = AnalyticsConstants.Params.id
                                    )
                                )

                                _showCheckOutDialog().show()
                            } else {
                                firebaseAnalyticManager.sendEventWithId(
                                    AnalyticsConstants.Event.Main.vacancyOnCheckInClick,
                                    vacancy.identity
                                )
                                metricManager.sendSimpleEvent(
                                    AnalyticsConstants.Event.Main.vacancyOnCheckInClick,
                                    Param(
                                        param = vacancy.identity,
                                        paramType = AnalyticsConstants.Params.id
                                    )
                                )
                                _checkIn(CheckInMode.CheckIn)
                            }
                        }
                    }

                    ClickType.Map -> {
                        firebaseAnalyticManager.sendEventWithId(
                            AnalyticsConstants.Event.Main.vacancyOnMapClick,
                            vacancy.identity
                        )
                        metricManager.sendSimpleEvent(AnalyticsConstants.Event.Main.vacancyOnMapClick)

                        vacancy.address?.let { location ->
                            MapFragment
                                .navigate(
                                    findNavController(),
                                    _bundler
                                )
                                .toMap(
                                    R.id.main__vacancy__to__main__map,
                                    location
                                )
                        }
                    }

                    ClickType.Share -> {
                        firebaseAnalyticManager.sendEventWithId(
                            AnalyticsConstants.Event.Main.vacancyOnShareClick,
                            vacancy.identity
                        )

                        metricManager.sendSimpleEvent(
                            AnalyticsConstants.Event.Main.vacancyOnShareClick,
                            Param(
                                param = vacancy.identity,
                                paramType = AnalyticsConstants.Params.id
                            )
                        )

                        tryStartActivityBy {
                            it.send(MimeType.Text.plain)
                                .text(vacancy.url.toString())
                        }
                    }
                }
            }.scoped()

        _state
            .map {
                it is VacancyState.Displaying
                    && ((it.vacancyDataState is VacancyDataStateReloading && !it.vacancyDataState.isBackground)
                    || (it.vacancyRequestState is GetVacancyRequestDataStateReloading && !it.vacancyRequestState.isBackground))
            }
            .distinctUntilChanged()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _swipeRefreshLayout.isRefreshing = it
            }
            .scoped()

        _state
            .ofSubtype<VacancyState.Displaying>()
            .map {
                (it.vacancyDataState is VacancyDataStateLoaded || it.vacancyDataState is VacancyDataStateReloading)
            }
            .distinctUntilChanged()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.mainVacancyContent.isGoneVisible = it
                binding.skeleton.mainVacancySkeletonContainer.isGoneVisible = !it
                binding.skeleton.mainVacancySkeletonShimmer.isShimmerAnimated = !it
            }
            .scoped()

        AuthorizationFragment
            .onResult(
                this@VacancyFragment,
                _requestCodeAuthorization,
                _bundler
            )
            .subscribeBy {
                _state
                    .sendWhen<VacancyState, VacancyState.Displaying> {
                        refresh()
                    }
            }
            .scoped()

        AuthorizationStubFragment
            .onResultRx(
                this@VacancyFragment,
                _requestCodeAuthorizationStub,
                _bundler
            )
            .subscribeBy {
                AuthorizationFragment
                    .navigate(
                        findNavController(),
                        _bundler
                    )
                    .toAuthorizationForResult(
                        R.id.main__vacancy__to__main__authorization,
                        _requestCodeAuthorization
                    )
            }
            .scoped()

        VacancyRequestFragment
            .onResult(
                this@VacancyFragment,
                _requestCodeVacancyRequest,
                _bundler
            )
            .filter { it == VacancyRequestResult.Success }
            .subscribeBy {
                _state
                    .sendWhen<VacancyState, VacancyState.Displaying> {
                        refresh()
                    }
            }
            .scoped()

        VacancyRequestRejectingFragment
            .onResult(
                this@VacancyFragment,
                _requestCodeVacancyRequestRejecting,
                _bundler
            )
            .filter { it == VacancyRequestRejectingResult.Success }
            .subscribeBy {
                _state
                    .sendWhen<VacancyState, VacancyState.Displaying> {
                        refresh()
                    }
            }
            .scoped()

        _state
            .handleActionExecution<
                VacancyState,
                VacancyState.CheckInProcessing,
                CheckInRequest>(
                toTaskState = { it.taskState },
                task = {
                    if (it.isActive) {
                        _api
                            .vacancy()
                            .checkOutByVacancyAsync(it.vacancyId, it.checkInBody)
                            .logWarning(logger) { "Failed to checkOut" }
                    } else {
                        _api
                            .vacancy()
                            .checkInByVacancyAsync(it.vacancyId, it.checkInBody)
                            .logWarning(logger) { "Failed to checkIn" }
                    }
                },
                updateState = { updateTaskState(it) }
            )
            .scoped()

        _state
            .ofSubtype<VacancyState.Displaying>()
            .observeOn(RxSchedulers.computation())
            .map { it.locationPermissionState }
            .ofSubtype<LocationPermissionState.Process>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                val locationManager =
                    requireContext().getSystemService(Context.LOCATION_SERVICE) as LocationManager
                val criteria = Criteria()
                criteria.accuracy = Criteria.ACCURACY_FINE
                criteria.powerRequirement = Criteria.POWER_HIGH

                if (ActivityCompat.checkSelfPermission(
                        requireContext(),
                        Manifest.permission.ACCESS_FINE_LOCATION
                    ) == PackageManager.PERMISSION_GRANTED
                    && ActivityCompat.checkSelfPermission(
                        requireContext(),
                        Manifest.permission.ACCESS_COARSE_LOCATION
                    ) == PackageManager.PERMISSION_GRANTED
                ) {
                    if (locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) || locationManager.isProviderEnabled(
                            LocationManager.NETWORK_PROVIDER
                        )
                    ) {
                        locationManager.requestSingleUpdate(
                            criteria,
                            object : LocationListener {
                                override fun onLocationChanged(location: Location) {
                                    _state.sendWhen<VacancyState, VacancyState.Displaying> {
                                        updateLocationPermissionsState(checkInMode = it.checkInMode) {
                                            when (this) {
                                                is LocationPermissionState.Process -> {
                                                    get(
                                                        GeoPoint.Coordinates(
                                                            location.latitude,
                                                            location.longitude
                                                        )
                                                    )
                                                }

                                                else -> this
                                            }
                                        }
                                    }
                                }

                                override fun onProviderEnabled(provider: String) {
                                }

                                override fun onProviderDisabled(provider: String) {
                                }

                                override fun onStatusChanged(
                                    provider: String?,
                                    status: Int,
                                    extras: Bundle?
                                ) {
                                }
                            },
                            null
                        )
                    } else {
                        when (it.checkInMode) {
                            CheckInMode.CheckIn -> {
                                _messageDisplay.showMessage(R.string.main__vacancy___check_in__error.rString)

                                _state.sendWhen<VacancyState, VacancyState.Displaying> {
                                    updateLocationPermissionsState(it.checkInMode) {
                                        when (this) {
                                            is LocationPermissionState.Process -> {
                                                error()
                                            }

                                            else -> this
                                        }
                                    }
                                }
                            }

                            CheckInMode.CheckOut -> {
                                val location =
                                    locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER)
                                if (location != null) {
                                    _state.sendWhen<VacancyState, VacancyState.Displaying> {
                                        updateLocationPermissionsState(checkInMode = it.checkInMode) {
                                            when (this) {
                                                is LocationPermissionState.Process -> {
                                                    get(
                                                        GeoPoint.Coordinates(
                                                            location.latitude,
                                                            location.longitude
                                                        )
                                                    )
                                                }

                                                else -> this
                                            }
                                        }
                                    }
                                } else {
                                    _state.sendWhen<VacancyState, VacancyState.Displaying> {
                                        updateLocationPermissionsState(checkInMode = it.checkInMode) {
                                            when (this) {
                                                is LocationPermissionState.Process -> {
                                                    error()
                                                }

                                                else -> this
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .scoped()

        _handleLocationPermissions().scoped()
    }

    private fun _bindVacancy(
        vacancy: Vacancy
    ) {

        binding.mainVacancyTitle.text = vacancy.title

        binding.mainVacancyPeriod.text = _displayFormat.dateAndTimeIntervalFormatter()
            .format(vacancy.period)

        // Показать переход к карте, только если есть адрес при оффлайн-типе
        val shouldShowOpenMapButton: Boolean =
            if (vacancy.isOnline) false else vacancy.address != null
        // Скрыть контейнер адреса, только если адреса нет при офлайн-типе
        val shouldShowAddress = vacancy.isOnline || vacancy.address != null

        binding.mainVacancyLocationOpenMap.isGoneVisible = shouldShowOpenMapButton
        binding.mainVacancyLocationLogo.isGoneVisible = shouldShowAddress
        binding.mainVacancyLocation.apply {
            isGoneVisible = shouldShowAddress
            text =
                if (vacancy.isOnline) getString(R.string.main__vacancy___is_online) else vacancy.address?.title
        }

        binding.mainVacancyEvent.apply {
            isGoneVisible = vacancy.event != null
            text = vacancy.event?.title
        }
        binding.mainVacancyOpenEvent.isGoneVisible = vacancy.event != null
        binding.mainVacancyEventLogo.isGoneVisible = vacancy.event != null

        binding.mainVacancyOrganizer.text = vacancy.organizer?.name

        binding.mainVacancyDescription.setHtmlText(vacancy.description)
        binding.mainVacancyDescription.requestLayout()
        binding.mainVacancyDescription.movementMethod = LinkMovementMethod.getInstance()

        binding.mainVacancyDescription?.postDelayed({
            binding.mainVacancyDescriptionExpand?.isGoneVisible =
                (binding.mainVacancyDescription?.lineCount
                    ?: 0) > App.descriptionNonExpandedLineCount
        }, 200)

        val noVacancyLimit = vacancy.openCount != -1

        binding.mainVacancyAvailableJobsCount.isGoneVisible = noVacancyLimit
        binding.mainVacancyAvailableJobsLogo.isGoneVisible = noVacancyLimit

        binding.mainVacancyAvailableJobsCount.text = when (vacancy.openCount) {
            -1 -> null
            0 -> getString(R.string.main__vacancy___no_available_jobs)
            else -> requireContext().resources.getQuantityString(
                R.plurals.main__vacancy___available_jobs__count,
                vacancy.openCount,
                vacancy.openCount,
            )
        }

        binding.mainVacancyDescriptionExpand.setOnClickListener {
            binding.mainVacancyDescription.maxLines = Int.MAX_VALUE
            binding.mainVacancyDescriptionExpand.gone()
        }

        images
            .load(vacancy.organizer?.icon)
            .circleCrop()
            .into(binding.mainVacancyOrganizerLogo)

        vacancy.organizer?.rating?.let { rating ->
            binding.mainVacancyOrganizerRating.text =
                _displayFormat.ratingFormatter().format(rating)
        }
        binding.mainVacancyOrganizerRating.isGoneVisible = vacancy.organizer?.rating != null

        binding.mainVacancyRequirementsTitle.isGoneVisible = vacancy.requirements.isNotEmpty()
        binding.mainVacancyConditionsTitle.isGoneVisible = vacancy.conditions.isNotEmpty()

        binding.mainVacancyDescription.isGoneVisible = vacancy.description.isNotEmpty()
        binding.mainVacancyDescriptionTitle.isGoneVisible = vacancy.description.isNotEmpty()

        binding.mainVacancyTasksTitle.isGoneVisible = vacancy.volunteerTasks.isNotEmpty()

        _categoriesAdapter.submitData(vacancy.categories)
        _requirementAdapter.submitData(vacancy.previewRequirements)
        _conditionsAdapter.submitData(vacancy.conditions)
        _tasksAdapter.submitList(vacancy.volunteerTasks.mapPersistent { it.title })
    }

    private fun _bindVacancyBottomUiPart(
        vacancy: Vacancy,
        vacancyRequest: VacancyRequest,
        requestButtonType: RequestButtonType,
        statusText: String
    ) {
        binding.mainVacancyApplyForVacancy.apply {
            isEnabled = !requestButtonType.isDisabledButtonType()
                || vacancyRequest.requirements.firstOrNull { it.type == "esia-confirmed" && !it.satisfy } != null
                || !_accountManager.hasUserAccount()
            text = when (requestButtonType) {
                RequestButtonType.CancelDisabled,
                RequestButtonType.Cancel -> getString(R.string.main__vacancy___reject)

                RequestButtonType.ReserveDisabled,
                RequestButtonType.Reserve -> getString(R.string.main__vacancy___visit_event_reserve)

                RequestButtonType.Visit -> getString(R.string.main__vacancy___visit_event_participant)
                RequestButtonType.CreateDisabled,
                RequestButtonType.Create -> getString(R.string.main__vacancy___apply_for_vacancy)

                RequestButtonType.VisitDisabled -> getString(R.string.main__vacancy___visit_event_participant)
            }
        }

        when (vacancy.requestStatus) {
            ru.dobro.domain.overview.VacancyRequestStatus.Accepted -> binding.mainVacancyAcceptedCount.text =
                if (requestButtonType.isDisabledButtonType()) {
                    "Организатор одобрил вашу заявку. Вы не можете отказаться от участия в добром деле"
                } else {
                    "Организатор одобрил вашу заявку. Вы можете отказаться от участия в добром деле"
                }

            ru.dobro.domain.overview.VacancyRequestStatus.InReserve -> binding.mainVacancyAcceptedCount.text =
                "Организатор добавил вашу заявку в резерв. Вы можете отказаться от участия в добром деле"

            else -> {
                binding.mainVacancyAcceptedCount.setHtmlText(statusText)
                when {
                    requestButtonType == RequestButtonType.Cancel -> binding.mainVacancyAcceptedCount.text =
                        getString(R.string.main__vacancy___can_be_rejected)

                    requestButtonType == RequestButtonType.CreateDisabled -> binding.mainVacancyAcceptedCount.setHtmlText(
                        statusText
                    )

                    requestButtonType == RequestButtonType.CancelDisabled -> binding.mainVacancyAcceptedCount.setHtmlText(
                        statusText
                    )

                    requestButtonType == RequestButtonType.ReserveDisabled -> binding.mainVacancyAcceptedCount.setHtmlText(
                        statusText
                    )

                    vacancy.recruitmentInProgress -> when (vacancy.openCount) {
                        0 -> binding.mainVacancyAcceptedCount.text =
                            getString(R.string.main__vacancy___can_visit_reserve)

                        else -> (if (vacancy.submittedVolunteerCount > 0) {
                            binding.mainVacancyAcceptedCount.text =
                                requireContext().resources.getQuantityString(
                                    R.plurals.main__vacancy___accepted_request_count,
                                    vacancy.submittedVolunteerCount,
                                    vacancy.submittedVolunteerCount
                                )
                        } else {
                            if (vacancy.requestButtonHelp.isNotBlank()) {
                                binding.mainVacancyAcceptedCount.setHtmlText(
                                    vacancy.requestButtonHelp
                                )
                            } else {
                                binding.mainVacancyAcceptedCount.text = getString(
                                    R.string.main__vacancy___be_first_accepted
                                )
                            }
                        })
                    }

                    else -> binding.mainVacancyAcceptedCount.text =
                        getString(R.string.main__vacancy___no_recruitment)
                }
            }
        }
    }

    private fun _checkIn(checkInMode: CheckInMode) {
        _state.sendWhen<VacancyState, VacancyState.Displaying> {
            updateLocationPermissionsState(checkInMode = checkInMode) {
                when (this) {
                    is LocationPermissionState.Idle -> checkPermissionsAndOpenIfGranted(
                        checkInMode = checkInMode,
                        checkPermissionManager = checkPermissionManager
                    )

                    is LocationPermissionState.PermissionsCheck -> updatePermissionsState { resetExplained() }
                    is LocationPermissionState.Ready -> start(checkInMode)
                    else -> this
                }
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            _locationCheckInRequestCode ->
                _state.sendWhen<VacancyState, VacancyState.Displaying> {
                    updateLocationPermissionsState(checkInMode = CheckInMode.CheckIn) {
                        if (this is LocationPermissionState.PermissionsCheck) {
                            updatePermissionsState {
                                handleResponse(
                                    checkPermissionManager,
                                    permissions,
                                    grantResults
                                )
                            }
                        } else {
                            this
                        }
                    }
                }

            _locationCheckOutRequestCode ->
                _state.sendWhen<VacancyState, VacancyState.Displaying> {
                    updateLocationPermissionsState(checkInMode = CheckInMode.CheckOut) {
                        if (this is LocationPermissionState.PermissionsCheck) {
                            updatePermissionsState {
                                handleResponse(
                                    checkPermissionManager,
                                    permissions,
                                    grantResults
                                )
                            }
                        } else {
                            this
                        }
                    }
                }
        }
    }

    @CheckReturnValue
    private fun _handleLocationPermissions(): Disposable {
        var permissionDialog: Dialog? = null

        fun _dismissPermissionDialog() {
            val dialog: Dialog? = permissionDialog
            if (dialog !== null) {
                dialog.dismiss()
            }
            permissionDialog = null
        }

        return _state
            .subscribeBy {
                if (it is VacancyState.Displaying && it.locationPermissionState is LocationPermissionState.PermissionsCheck) {
                    val locationPermissionState = it.locationPermissionState
                    val permissionsState: PermissionsState.NotAllGranted =
                        locationPermissionState.permissionsState

                    if (permissionsState.isAllDeniedExplained()) {
                        permissionsState.requestDeniedOnce(
                            checkPermissionManager,
                            _locationCheckInRequestCode
                        )

                        _dismissPermissionDialog()
                    } else {
                        if (permissionDialog == null) {
                            val dialog: AlertDialog = requireContext().dialog {
                                it
                                    .message(R.string.main__vacancy___check_in__permission_explain.rText)
                                    .okButton { _dismissPermissionDialog() }
                                    .cancelButton { _dismissPermissionDialog() }
                                    .cancellable()
                            }

                            dialog.setOnDismissListener {
                                locationPermissionState.updatePermissionsState { markAllExplained() }
                            }

                            dialog.show()

                            permissionDialog = dialog
                        }
                    }
                } else {
                    _dismissPermissionDialog()
                }
            }
    }

    @SuppressLint("InflateParams")
    private fun _showCheckOutDialog() =
        MaterialAlertDialogBuilder(requireContext(), R.style.Application_MaterialAlertDialog_Remove)
            .setTitle(R.string.main__vacancy___check_out__title)
            .setMessage(R.string.main__vacancy___check_out__message)
            .setPositiveButton(R.string.main__vacancy___check_out) { dialog, _ ->
                _checkIn(CheckInMode.CheckOut)
                dialog.dismiss()
            }
            .setNegativeButton(R.string.application___cancel) { dialog, _ ->
                dialog.dismiss()
            }
}
