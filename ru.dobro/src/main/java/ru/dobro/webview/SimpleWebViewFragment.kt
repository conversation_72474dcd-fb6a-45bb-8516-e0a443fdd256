package ru.dobro.webview

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import androidx.activity.OnBackPressedCallback
import androidx.navigation.fragment.findNavController
import common.library.android.widget.gone
import common.library.android.widget.visible
import org.kodein.di.generic.instance
import ru.dobro.api.di.AuthorizationHandler
import ru.dobro.core.BaseFragment
import ru.dobro.databinding.CommonWebViewBinding
import javax.annotation.CheckReturnValue

class SimpleWebViewFragment : BaseFragment() {
    override val screenName: String get() = "SimpleWebViewFragment"

    private val _authorizationHandler: AuthorizationHandler by instance()

    private lateinit var binding: CommonWebViewBinding

    private var targetUrl: String? = null

    @CheckReturnValue
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = CommonWebViewBinding.inflate(inflater)
        return binding.root
    }

    @SuppressLint("SetJavaScriptEnabled")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        requireActivity().onBackPressedDispatcher.addCallback(
            viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    if (binding.commonWebView.canGoBack()) {
                        binding.commonWebView.goBack()
                    } else {
                        findNavController().navigateUp()
                    }
                }
            })

        targetUrl = arguments?.getString("URL")

        val token = _authorizationHandler.getAccessToken()

        val cookieManager = android.webkit.CookieManager.getInstance()
        cookieManager.setAcceptCookie(true)

        val domain = "https://dobro.ru"
        token?.let {
            cookieManager.setCookie(domain, "dobro_access_token=${it.asString()}")
        }

        binding.commonWebView.apply {
            this.setDownloadListener { url, _, _, _, _ ->
                startActivity(Intent(Intent.ACTION_VIEW).apply {
                    data = Uri.parse(url)
                })
            }
            webViewClient = WebViewClient()
            isVerticalScrollBarEnabled = false
            settings.javaScriptEnabled = true
            settings.loadsImagesAutomatically = true
            settings.domStorageEnabled = true

            clearCache(true)
            clearSslPreferences()

            val url = targetUrl.toString().plus("?utm_source=mobile_app")
            loadUrl(url)
        }
    }

    private inner class WebViewClient : android.webkit.WebViewClient() {

        override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
            super.onPageStarted(view, url, favicon)
            setRefreshing(shouldRefresh = true)
        }

        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            if (url == "https://edu.dobro.ru/?logout=yes") {
                view?.loadUrl("https://edu.dobro.ru/personal/get_lid/")
            }
            setRefreshing(shouldRefresh = false)
        }

        override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
            return if (!(url?.contains("client_id") ?: false)) {
                when (url?.split("/")?.dropLast(1)?.last()) {
                    "courses", "calendar", "materials", "contacts", "dobro.ru", "journal.dobro.ru" -> true
                    "personal" -> {
                        view?.loadUrl(targetUrl.toString())
                        false
                    }

                    else -> {
                        false
                    }
                }
            } else {
                false
            }
        }
    }

    private fun setRefreshing(shouldRefresh: Boolean) {
        if (shouldRefresh) {
            binding.commonWebViewProgress.visible()
        } else {
            binding.commonWebViewProgress.gone()
        }
    }
}
