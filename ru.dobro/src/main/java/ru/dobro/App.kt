package ru.dobro

import com.google.firebase.analytics.FirebaseAnalytics
import com.vk.id.VKID
import com.yandex.mapkit.MapKitFactory
import common.library.android.BasicApplication
import common.library.android.auth.di.authModule
import common.library.android.image.internalization.ImageInternalizer
import common.library.android.image.internalization.JpegImageInternalizer
import common.library.android.logging.addAndroid
import common.library.android.message.MessageDisplay
import common.library.android.message.ToastMessageDisplay
import common.library.core.logging.LoggerFactory
import common.library.core.logging.di.logger
import common.library.core.network.NetworkMonitor
import common.library.core.percent.absolutePercent
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.provider
import org.kodein.di.generic.singleton
import ru.dobro.api.Server
import ru.dobro.api.di.ApiSerializationProviderTag
import ru.dobro.api.di.AuthorizationHandler
import ru.dobro.api.di.dbModule
import ru.dobro.api.di.mockMainApiModule
import ru.dobro.api.di.mockTargetedHelpApiModule
import ru.dobro.api.di.realMainApiModule
import ru.dobro.api.di.realTargetedHelpApiModule
import ru.dobro.api.repository.AuthRepositoryImpl
import ru.dobro.api.request_handler.ApiRequestHandler
import ru.dobro.core.account.AccountAuthenticator
import ru.dobro.core.account.AccountManager
import ru.dobro.core.account.AndroidAccountManager
import ru.dobro.core.account.RetryAccountAuthorizationHandler
import ru.dobro.core.analytic.AnalyticManager
import ru.dobro.core.analytic.FirebaseAnalyticManager
import ru.dobro.core.analytic.MockAnalyticManager
import ru.dobro.core.data.bindAppParamsBundler
import ru.dobro.core.download.FileDownloader
import ru.dobro.core.download.FileDownloaderImpl
import ru.dobro.core.format.ContextDisplayFormat
import ru.dobro.core.format.DisplayFormat
import ru.dobro.core.format.FormatContext
import ru.dobro.core.metric.MetricManager
import ru.dobro.core.metric.MockMetricManager
import ru.dobro.core.metric.YandexMetricManager
import ru.dobro.core.notifications.NotificationsManager
import ru.dobro.core.notifications.OneSignalNotificationsManager
import ru.dobro.core.settings.Environment
import ru.dobro.core.settings.Settings
import ru.dobro.core.settings.SharedPreferencesSettings
import ru.dobro.core.settings.UserMockSettings
import ru.dobro.core.settings.UserSettings
import ru.dobro.core.settings.UserSharedPreferencesSettings
import ru.dobro.domain.repository.AuthRepository
import ru.dobro.domain.repository.PostsRepository
import ru.dobro.domain.repository.ProfileRepository
import ru.dobro.main.task_report.TaskReportFragment
import ru.dobro.main.vacancy_request.VacancyRequestFragment
import java.time.Duration
import ru.dobro.api.repository.PostsRepository as PostsRepositoryImpl
import ru.dobro.api.repository.ProfileRepository as ProfileRepositoryImpl

class App : BasicApplication(
    applicationId = BuildConfig.APPLICATION_ID,
    logging = {
        if (instance<Environment>().isLoggable) {
            it.addAndroid()
        } else {
            it
        }
    },
    strictMode = false,
    isDebugBuild = BuildConfig.DEBUG,
    dependency = {
        val context = it.applicationContext

        val settings: Settings = SharedPreferencesSettings(
            context.getSharedPreferences("settings", MODE_PRIVATE)
        )

        val environment: Environment = settings.environment.getOrDefault()

        bind<Environment>() with instance(environment)
        if (environment.api is Environment.Api.Real) {
            bind<Server>() with singleton { environment.api.server }
        }

        bind<RetryAccountAuthorizationHandler>() with singleton {
            RetryAccountAuthorizationHandler(
                _accountManager = instance(),
                _daDataApiKey = environment.daDataApiId
            )
        }

        bind<AuthorizationHandler>() with provider { instance<RetryAccountAuthorizationHandler>() }

        import(dbModule)

        val userAgent =
            "Dobro/${BuildConfig.VERSION_NAME} (${BuildConfig.APPLICATION_ID}; build:${BuildConfig.VERSION_CODE}; Android ${android.os.Build.VERSION.RELEASE})"
        import(
            if (environment.api is Environment.Api.Real) {
                realMainApiModule(
                    authorizationProvider = { instance() },
                    serverProvider = { instance() },
                    loggerProvider = {
                        instance<LoggerFactory>()
                            .createScope("main-api")
                            .createLogger("real")
                    },
                    userAgent = userAgent,
                    serviceToken = environment.serviceToken,
                    isDebugBuild = BuildConfig.DEBUG,
                    applicationContext = it
                )
            } else {
                mockMainApiModule(
                    loggerProvider = {
                        instance<LoggerFactory>()
                            .createScope("main-api")
                            .createLogger("mock")
                    }
                )
            }
        )

        import(
            if (environment.api is Environment.Api.Real) {
                realTargetedHelpApiModule(
                    authorizationProvider = { instance() },
                    serverProvider = { instance() },
                    loggerProvider = {
                        instance<LoggerFactory>()
                            .createScope("targeted_help-api")
                            .createLogger("real")
                    },
                    userAgent = userAgent,
                    serviceToken = environment.serviceToken,
                    isDebugBuild = BuildConfig.DEBUG,
                    applicationContext = context
                )
            } else {
                mockTargetedHelpApiModule(
                    loggerProvider = {
                        instance<LoggerFactory>()
                            .createScope("targeted_help-api")
                            .createLogger("mock")
                    }
                )
            }
        )

        import(authModule { AccountAuthenticator(context) })

        bindAppParamsBundler()

        bind<MessageDisplay>() with singleton { ToastMessageDisplay(context) }

        bind<FileDownloader>() with singleton { FileDownloaderImpl(
            context = context,
            authorizationHandler = instance()
        ) }

        bind<Settings>() with singleton { settings }
        bind<Settings.EnvironmentSettings>() with singleton { instance<Settings>().environment }

        if (environment.api is Environment.Api.Real) {
            bind<UserSettings>() with singleton {
                UserSharedPreferencesSettings(
                    context.getSharedPreferences("settings", MODE_PRIVATE),
                    instance(tag = ApiSerializationProviderTag)
                )
            }
        } else {
            bind<UserSettings>() with singleton {
                UserMockSettings(
                    context.getSharedPreferences("settings", MODE_PRIVATE)
                )
            }
        }

        bind<AccountManager>() with singleton {
            AndroidAccountManager(
                _native = instance(),
                _notificationsManager = instance(),
                _bundler = instance(),
                _logger = logger<AccountManager>(),
                _userSettings = instance(),
                _metricManager = instance()
            )
        }

        bind<ApiRequestHandler>() with singleton { ApiRequestHandler(instance(tag = ApiSerializationProviderTag)) }

        bind<AuthRepository>() with singleton { AuthRepositoryImpl(instance(), instance()) }
        bind<PostsRepository>() with singleton { PostsRepositoryImpl(instance(), instance()) }
        bind<ProfileRepository>() with singleton { ProfileRepositoryImpl(instance(), instance()) }

        bind<NotificationsManager>() with singleton {
            OneSignalNotificationsManager(it, instance())
        }

        bind<DisplayFormat>() with provider {
            ContextDisplayFormat(FormatContext())
        }

        if (environment.api is Environment.Api.Real) {
            bind<AnalyticManager>() with singleton {
                FirebaseAnalyticManager(
                    FirebaseAnalytics.getInstance(instance()),
                    instance(tag = ApiSerializationProviderTag)
                )
            }
        } else {
            bind<AnalyticManager>() with singleton {
                MockAnalyticManager(
                    logger<MockAnalyticManager>()
                )
            }
        }

        bind<ImageInternalizer>() with singleton {
            JpegImageInternalizer(
                context,
                90.absolutePercent
            )
        }

        if (environment.api is Environment.Api.Real) {
            bind<MetricManager>() with singleton {
                YandexMetricManager(
                    it,
                    instance(tag = ApiSerializationProviderTag),
                    logger<YandexMetricManager>()
                )
            }
        } else {
            bind<MetricManager>() with singleton { MockMetricManager(logger<MockMetricManager>()) }
        }

        bind<NetworkMonitor>() with singleton { NetworkMonitor(instance()) }
    }
) {
    companion object {
        val tempFilesTimeToLive: Duration = Duration.ofDays(1)

        const val clicksForEnvironmentSwitcher: Int = 10

        const val maxTaskReportImagesCount: Int = 10

        const val nonExpandedSocialsCount: Int = 3

        const val authorizationExpandedSocialsSpanCount: Int = 2

        const val descriptionNonExpandedLineCount: Int = 6

        const val defaultPageSize = 30

        // Возраст дееспособности - если волонтер моложе, то требуются данные его опекуна
        const val majorityAge = 14

        // Минимальновозможный возраст волонтера
        const val minRegAge = 8
    }

    override fun onCreate() {
        super.onCreate()

        val accountManager: AccountManager by instance()
        accountManager.enforceConsistency()

        TaskReportFragment.tempFiles.deleteAll(this)
        VacancyRequestFragment.tempFiles.deleteAll(this)

        MapKitFactory.setApiKey(getString(R.string.mapkit_apikey))

        val metricManager: MetricManager by instance()
        metricManager.activate()

        val accountInfo = accountManager.getInfo()
        if (accountInfo == null) {
            accountManager.removeAccount()
            return
        }

        VKID(this)
    }
}
