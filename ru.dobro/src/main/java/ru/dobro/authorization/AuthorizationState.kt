package ru.dobro.authorization

import common.library.android.input.InputField
import common.library.android.input.InputRequestState
import common.library.android.string.RString
import common.library.android.string.rString
import common.library.core.contract.ContractException
import common.library.core.email.Email
import common.library.core.state.task.TaskState
import ru.dobro.R
import ru.dobro.core.error.ErrorMessages
import ru.dobro.core.validation.EmailInputValidator
import javax.annotation.CheckReturnValue

sealed class AuthorizationState :
    InputRequestState<AuthorizationField, AuthorizationState> {
    object Idle : AuthorizationState() {
        @CheckReturnValue
        fun process(request: AuthorizationRequest?): AuthorizationState =
            if (request?.authorizationBySocialNetworkTaskParams != null) {
                AuthenticationBySocialNetwork(
                    authorizationBySocialNetworkTaskState = TaskState.Idle
                        .awaiting<AuthorizationBySocialNetworkTaskResult, AuthorizationBySocialNetworkTaskParams>()
                        .execute(
                            AuthorizationBySocialNetworkTaskParams(
                                remoteId = request.authorizationBySocialNetworkTaskParams.remoteId,
                                remoteToken = request.authorizationBySocialNetworkTaskParams.remoteToken,
                                socialType = request.authorizationBySocialNetworkTaskParams.socialType
                            )
                        )
                )
            } else {
                Input(
                    email = InputField.empty(),
                    password = InputField.empty()
                )
            }
    }

    data class Input(
        val email: InputField<Email>,
        val password: InputField<String>
    ) : AuthorizationState(),
        InputRequestState.Input<AuthorizationField, AuthorizationState> {

        override val isValid: Boolean
            get() = email is InputField.NonEmpty.Valid && password is InputField.NonEmpty.Valid

        override fun get(id: AuthorizationField): InputField<*> =
            when (id) {
                AuthorizationField.Email -> email

                AuthorizationField.Password -> password
            }

        @CheckReturnValue
        fun inputEmail(value: String): Input =
            copy(
                email = EmailInputValidator.validateInput(email, value)
            )

        @CheckReturnValue
        fun inputPassword(value: String): Input =
            if (value.isNotEmpty()) {
                copy(password = password.valid(value, value))
            } else {
                copy(
                    password = password.invalid(
                        value,
                        R.string.application___validation_error__empty.rString
                    )
                )
            }

        @CheckReturnValue
        override fun focus(id: AuthorizationField): AuthorizationState =
            when (id) {
                AuthorizationField.Email -> focusEmail()
                AuthorizationField.Password -> focusPassword()
            }

        @CheckReturnValue
        fun focusEmail(): Input = copy(email = email.markFocused())

        @CheckReturnValue
        fun focusPassword(): Input = copy(password = password.markFocused())

        @CheckReturnValue
        override fun processing(): Authentication {
            if (email !is InputField.NonEmpty.Valid
                || password !is InputField.NonEmpty.Valid
            ) {
                throw ContractException("Invalid input.")
            }

            return Authentication(
                _input = this,

                requestAuthState = TaskState.Idle
                    .awaiting<AuthorizationTaskResult, AuthorizationTaskParams>()
                    .execute(AuthorizationTaskParams(email.data, password.data))
            )
        }

        @CheckReturnValue
        override fun input(id: AuthorizationField, value: String): AuthorizationState =
            when (id) {
                AuthorizationField.Email -> inputEmail(value)
                AuthorizationField.Password -> inputPassword(value)
            }
    }

    data class Authentication(
        private val _input: Input,

        val requestAuthState: AuthorizationTask
    ) : AuthorizationState() {
        @CheckReturnValue
        fun updateTask(action: AuthorizationTask.() -> AuthorizationTask): AuthorizationState {
            return when (val state = requestAuthState.action()) {
                is TaskState.Idle.Completed.Success ->
                    Completed.Success(
                        result = state.result,
                        authType = "common"
                    )

                is TaskState.Idle.Completed.Fail ->
                    Completed.Failed(
                        _input = _input,

                        error = ErrorMessages.ofException(state.error),
                        errorCause = state.error
                    )

                else -> copy(requestAuthState = state)
            }
        }
    }

    data class AuthenticationBySocialNetwork(
        val authorizationBySocialNetworkTaskState: AuthorizationBySocialNetworkTaskState
    ) : AuthorizationState() {
        @CheckReturnValue
        fun updateTask(action: AuthorizationBySocialNetworkTaskState.() -> AuthorizationBySocialNetworkTaskState): AuthorizationState {
            return when (val state = authorizationBySocialNetworkTaskState.action()) {
                is TaskState.Idle.Completed.Success ->
                    Completed.Success(
                        result = state.result,
                        authType = state.params.socialType
                    )

                is TaskState.Idle.Completed.Fail ->
                    Completed.Failed(
                        _input = null,

                        error = ErrorMessages.ofException(state.error),
                        errorCause = state.error
                    )

                else -> copy(authorizationBySocialNetworkTaskState = state)
            }
        }
    }

    sealed class Completed : AuthorizationState() {
        data class Failed(
            private val _input: Input?,

            val error: RString?,
            val errorCause: Throwable?,
        ) : Completed() {
            @CheckReturnValue
            fun backToInput(): AuthorizationState = _input ?: Idle
        }

        data class Success(
            val result: AuthorizationTaskResult,
            val authType: String
        ) : Completed()
    }
}
