package ru.dobro.authorization

import android.view.ViewGroup
import android.widget.ImageView
import androidx.annotation.DrawableRes
import androidx.annotation.LayoutRes
import androidx.recyclerview.widget.RecyclerView
import common.library.android.inflateBy
import common.library.core.rx.invoke
import io.reactivex.Flowable
import io.reactivex.processors.PublishProcessor
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import retrofit2.http.Url
import ru.dobro.R

class SocialNetworksAdapter : RecyclerView.Adapter<SocialNetworksAdapter.ViewHolder>() {

    private val _onItemClick: PublishProcessor<Item> = PublishProcessor.create()

    val onItemClick: Flowable<Item> get() = _onItemClick.hide()

    private var _items: PersistentList<Item> = persistentListOf()

    fun setItems(items: PersistentList<Item>) {
        _items = items
        notifyDataSetChanged()
    }

    enum class ViewType {
        ExpandButton,
        SocialNetwork
    }

    override fun getItemCount(): Int = _items.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        when (holder) {
            is ViewHolder.SocialNetwork -> holder.bind(_items[position] as Item.SocialNetwork)
            is ViewHolder.Expand -> holder.bind(_items[position] as Item.ExpandButton)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder =
        when (common.library.core.enumValueOf<ViewType>(viewType)) {
            ViewType.ExpandButton -> ViewHolder.Expand(parent, _onItemClick)
            ViewType.SocialNetwork -> ViewHolder.SocialNetwork(parent, _onItemClick)
        }

    override fun getItemViewType(position: Int): Int =
        when (_items[position]) {
            is Item.ExpandButton -> ViewType.ExpandButton
            is Item.SocialNetwork -> ViewType.SocialNetwork
        }.ordinal

    sealed class Item {
        object ExpandButton : Item()

        data class SocialNetwork(
            @DrawableRes val iconResId: Int,
            @Url val path: String,
            val isEditable: Boolean = true
        ) : Item()
    }

    sealed class ViewHolder(
        container: ViewGroup,
        @LayoutRes layoutId: Int
    ) : RecyclerView.ViewHolder(
        container.context.inflateBy(layoutId, container)
    ) {
        class SocialNetwork(
            container: ViewGroup,
            private val _onClick: PublishProcessor<Item>
        ) : ViewHolder(container, R.layout.login__social_network) {

            fun bind(item: Item.SocialNetwork) {
                (itemView as ImageView).setImageResource(item.iconResId)

                itemView.setOnClickListener {
                    _onClick.invoke(item)
                }
            }
        }

        class Expand(
            container: ViewGroup,
            private val _onClick: PublishProcessor<Item>
        ) : ViewHolder(container, R.layout.login__social_network___expand) {
            fun bind(item: Item.ExpandButton) {
                itemView.setOnClickListener {
                    _onClick.invoke(item)
                }
            }
        }
    }

    companion object {
        val _socialNetworkList: PersistentList<Item> = persistentListOf(
            // TODO(убрали на время пока Meta запрещена. Не удалять, возможно, Meta еще будет доступна)
//            Item.SocialNetwork(
//                R.drawable.login__authorization___facebook,
//                "facebook"
//            ),
            Item.SocialNetwork(
                R.drawable.login__authorization___vk,
                "vkontakte"
            ),
            Item.SocialNetwork(
                R.drawable.login__authorization___ok,
                "odnoklassniki"
            ),
            // TODO (пока убрали)
//            Item.SocialNetwork(
//                R.drawable.login__authorization___google,
//                "google"
//            ),
            Item.SocialNetwork(
                R.drawable.login__authorization___esia,
                "esia"
            ),
            // TODO(Убрать отсюда при разблокировке Meta)
            Item.SocialNetwork(
                R.drawable.login__authorization___scos,
                "scos"
            ),
            Item.ExpandButton
        )

        val _socialNetworkExpandedList: PersistentList<Item> =
            persistentListOf(
                // TODO(убрали на время пока Meta запрещена. Не удалять, возможно, Meta еще будет доступна)
//                Item.SocialNetwork(
//                    R.drawable.login__authorization___facebook,
//                    "facebook"
//                ),
                Item.SocialNetwork(
                    R.drawable.login__authorization___vk,
                    "vkontakte"
                ),
                Item.SocialNetwork(
                    R.drawable.login__authorization___ok,
                    "odnoklassniki"
                ),
//                Item.SocialNetwork(
//                    R.drawable.login__authorization___google,
//                    "google"
//                ),
                Item.SocialNetwork(
                    R.drawable.login__authorization___esia,
                    "esia",
                    false
                ),
                Item.SocialNetwork(
                    R.drawable.login__authorization___scos,
                    "scos"
                ),
                Item.SocialNetwork(
                    R.drawable.login__authorization___mail_ru,
                    "mail_ru"
                ),
                Item.SocialNetwork(
                    R.drawable.login__authorization___yandex,
                    "yandex"
                ),
                Item.SocialNetwork(
                    R.drawable.login__authorization___leader,
                    "leader-id"
                ),
                Item.SocialNetwork(
                    R.drawable.login__authorization___rm,
                    "rosmol"
                )
            )

        val _socialNetworkExpandedListWithoutGoogle: PersistentList<Item> =
            persistentListOf(
                Item.SocialNetwork(
                    R.drawable.login__authorization___vk,
                    "vkontakte"
                ),
                Item.SocialNetwork(
                    R.drawable.login__authorization___ok,
                    "odnoklassniki"
                ),
                Item.SocialNetwork(
                    R.drawable.login__authorization___esia,
                    "esia",
                    false
                ),
                Item.SocialNetwork(
                    R.drawable.login__authorization___scos,
                    "scos"
                ),
                Item.SocialNetwork(
                    R.drawable.login__authorization___mail_ru,
                    "mail_ru"
                ),
                Item.SocialNetwork(
                    R.drawable.login__authorization___yandex,
                    "yandex"
                ),
                Item.SocialNetwork(
                    R.drawable.login__authorization___leader,
                    "leader-id"
                ),
                Item.SocialNetwork(
                    R.drawable.login__authorization___rm,
                    "rosmol"
                )
            )
    }
}
