package ru.dobro.authorization

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.CookieManager
import android.webkit.WebResourceRequest
import android.webkit.WebView
import androidx.activity.OnBackPressedCallback
import androidx.core.net.toUri
import androidx.navigation.fragment.findNavController
import common.library.android.widget.gone
import common.library.android.widget.visible
import io.reactivex.annotations.CheckReturnValue
import org.kodein.di.generic.instance
import ru.dobro.core.BaseFragment
import ru.dobro.core.settings.Environment
import ru.dobro.databinding.CommonWebViewBinding
import ru.dobro.main.MainActivity

class AuthorizationWebViewFragment : BaseFragment() {
    override val screenName: String get() = "WebView"
    val environment: Environment by instance()

    private lateinit var binding: CommonWebViewBinding

    @CheckReturnValue
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = CommonWebViewBinding.inflate(inflater)
        return binding.root
    }

    @SuppressLint("SetJavaScriptEnabled")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        val url = arguments?.getString("URL")

        requireActivity().onBackPressedDispatcher.addCallback(
            viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    if (binding.commonWebView.canGoBack()) {
                        binding.commonWebView.goBack()
                    } else {
                        findNavController().navigateUp()
                    }
                }
            })

        binding.commonWebView.apply {
            this.setDownloadListener { url, _, _, _, _ ->
                startActivity(Intent(Intent.ACTION_VIEW).apply {
                    data = Uri.parse(url)
                })
            }
            webViewClient = WebViewClient()
            isVerticalScrollBarEnabled = false
            settings.javaScriptEnabled = true
            settings.loadsImagesAutomatically = true
            settings.domStorageEnabled = true
            CookieManager.getInstance().removeAllCookies(null)
            CookieManager.getInstance().flush()
            clearCache(true)
            clearFormData()
            clearHistory()
            clearSslPreferences()
            settings.userAgentString =
                "Chrome/******** Mobile" // Не убирать иначе не будет работать вход чз Google

            url?.let { loadUrl(it) }
        }
    }

    private inner class WebViewClient : android.webkit.WebViewClient() {
        override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
            super.onPageStarted(view, url, favicon)
            setRefreshing(shouldRefresh = true)
        }

        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            setRefreshing(shouldRefresh = false)
        }

        @Deprecated("")
        override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
            if (url == null) {
                return false
            }
            return handleUri(url)
        }

        override fun shouldOverrideUrlLoading(
            view: WebView?,
            request: WebResourceRequest
        ): Boolean {
            val url = request.url.toString()
            return handleUri(url)
        }

        private fun handleUri(url: String): Boolean = when {
            url.contains("app://") || url.contains("app://") -> {
                CookieManager.getInstance().removeAllCookies(null)
                CookieManager.getInstance().flush()
                binding.commonWebView.clearCache(true)
                binding.commonWebView.clearFormData()
                binding.commonWebView.clearHistory()
                binding.commonWebView.clearSslPreferences()
                val intent = Intent(requireContext(), MainActivity::class.java)
                intent.data = url.toUri()
                startActivity(intent)
                true
            }

            else -> false
        }
    }

    private fun setRefreshing(shouldRefresh: Boolean) {
        if (shouldRefresh) {
            binding.commonWebViewProgress.visible()
        } else {
            binding.commonWebViewProgress.gone()
        }
    }
}
