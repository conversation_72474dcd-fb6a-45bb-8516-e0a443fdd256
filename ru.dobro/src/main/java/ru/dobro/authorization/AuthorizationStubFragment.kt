package ru.dobro.authorization

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.Fragment
import androidx.fragment.app.clearFragmentResult
import androidx.fragment.app.setFragmentResult
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import common.library.android.awaitFragmentResult
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.onFragmentResult
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.widget.onClick
import common.library.core.rx.RxSchedulers
import io.reactivex.Observable
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.flow.Flow
import org.kodein.di.generic.instance
import ru.dobro.R
import ru.dobro.core.BaseDialogFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.databinding.AuthorizationStubBinding
import javax.annotation.CheckReturnValue

class AuthorizationStubFragment : BaseDialogFragment(), HasCustomToolbar {
    private lateinit var binding: AuthorizationStubBinding
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        HasCustomToolbar.CustomToolbar.None

    private val _bundler: Bundler by instance()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = AuthorizationStubBinding.inflate(inflater)
        return binding.root
    }

    init {
        setStyle(STYLE_NORMAL, R.style.Application_Theme)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)

        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        return dialog
    }

    override val screenName: String = AnalyticsConstants.Screen.Login.authorizationStub

    companion object {
        class NavigationContext(
            private val _navigation: NavController,
            private val _bundler: Bundler
        ) {
            fun toAuthorizationStub(
                requestKey: String,
            ) {
                _navigation.navigate(
                    R.id.authorizationStubFragment,
                    requestKey.intoBundle(_bundler)
                )
            }
        }

        @CheckReturnValue
        fun navigate(
            navigation: NavController,
            bundler: Bundler
        ): NavigationContext = NavigationContext(navigation, bundler)

        @CheckReturnValue
        fun onResultRx(
            fragment: Fragment,
            resultKey: String,
            bundler: Bundler
        ): Observable<AuthorizationStubResult> =
            fragment.onFragmentResult(resultKey, bundler)

        @CheckReturnValue
        fun onResult(
            fragment: Fragment,
            resultKey: String,
            bundler: Bundler
        ): Flow<AuthorizationStubResult> =
            fragment.awaitFragmentResult(resultKey, bundler)
    }

    object AuthorizationStubResult

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        val requestCode: String? = arguments?.getParams(_bundler)

        if (requestCode === null) {
            logger.warning { "Invalid request $requestCode" }

            findNavController().navigateUp()

            return
        }

        clearFragmentResult(requestCode)

        binding.authorizationStubClose
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.authorizationStubOnCloseClick)
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.authorizationStubOnCloseClick)

                findNavController().navigateUp()
            }
            .scoped()

        binding.authorizationStubPerform
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.authorizationStubOnPerformClick)
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.authorizationStubOnPerformClick)

                setFragmentResult(requestCode, AuthorizationStubResult.intoBundle(_bundler))
                findNavController().navigateUp()
            }
            .scoped()
    }
}
