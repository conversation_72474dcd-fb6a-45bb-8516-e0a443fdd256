package ru.dobro.authorization

import common.library.core.state.task.TaskState
import ru.dobro.api.overview.OverviewApi

data class AuthorizeBySocialNetworkRequestParams(
    val remoteId: String,
    val remoteToken: String?,
    val socialType: String
)

typealias AuthorizationBySocialNetworkTaskParams = AuthorizeBySocialNetworkRequestParams
typealias AuthorizationBySocialNetworkTaskResult = OverviewApi.AuthorizationResponse
typealias AuthorizationBySocialNetworkTaskState = TaskState<AuthorizationTaskResult, AuthorizeBySocialNetworkRequestParams>
