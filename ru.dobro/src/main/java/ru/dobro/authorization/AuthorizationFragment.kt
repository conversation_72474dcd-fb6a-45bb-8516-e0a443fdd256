package ru.dobro.authorization

import android.os.Bundle
import android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.IdRes
import androidx.fragment.app.Fragment
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import com.vk.id.AccessToken
import com.vk.id.VKID
import com.vk.id.VKIDAuthFail
import common.library.android.coroutines.repeatOnResume
import common.library.android.coroutines.subscribeToFlow
import common.library.android.getSupportColor
import common.library.android.input.InputFieldDescriptor
import common.library.android.input.setupInputFields
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.message.MessageDisplay
import common.library.android.onFragmentResult
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.resource.withOnStartResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.soft_input.SoftInputMode
import common.library.android.string.span.spannedString
import common.library.android.widget.enableSoftInputMode
import common.library.android.widget.gone
import common.library.android.widget.onClick
import common.library.android.widget.visible
import common.library.core.Characters
import common.library.core.lazyGet
import common.library.core.logging.logError
import common.library.core.rx.RxSchedulers
import common.library.core.rx.ofSubtype
import common.library.core.rx.onBackpressureBufferLast
import common.library.core.state.task.TaskState
import common.library.core.variable.Agent
import common.library.core.variable.agent
import common.library.core.variable.sendWhen
import io.reactivex.Flowable
import io.reactivex.Observable
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.rxkotlin.withLatestFrom
import kotlinx.collections.immutable.persistentListOf
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.App
import ru.dobro.BuildConfig
import ru.dobro.R
import ru.dobro.api.AccountDisabledException
import ru.dobro.api.DobroApi
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.error.ErrorHandler
import ru.dobro.core.settings.Environment.Api
import ru.dobro.core.settings.Settings
import ru.dobro.databinding.AuthorizationBinding
import ru.dobro.environment_switcher.EnvironmentSwitcherFragment
import ru.dobro.main.MainActivity
import ru.dobro.reset_password.ResetPasswordFragment
import javax.annotation.CheckReturnValue

class AuthorizationFragment : BaseFragment({
    bind<Agent<AuthorizationState>>() with singleton { agent(AuthorizationState.Idle) }
    bind<SocialNetworksAdapter>() with singleton { SocialNetworksAdapter() }
}), HasCustomToolbar {

    private lateinit var binding: AuthorizationBinding
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        HasCustomToolbar.CustomToolbar.None

    companion object {
        @CheckReturnValue
        fun navigate(
            navigator: NavController,
            bundler: Bundler
        ): NavigationContext = NavigationContext(navigator, bundler)

        class NavigationContext(
            private val _navigator: NavController,
            private val _bundler: Bundler
        ) {
            @CheckReturnValue
            fun toAuthorizationForResult(
                @IdRes actionId: Int,
                resultKey: String
            ) {
                _navigator.navigate(
                    actionId,
                    AuthorizationRequest(
                        resultKey = resultKey
                    ).intoBundle(_bundler)
                )
            }

            @CheckReturnValue
            fun toAuthorizationBySocialNetwork(
                @IdRes actionId: Int,
                remoteId: String,
                type: String,
                resultKey: String,
                remoteToken: String?
            ) {
                _navigator.navigate(
                    actionId,
                    AuthorizationRequest(
                        authorizationBySocialNetworkTaskParams = AuthorizationBySocialNetworkTaskParams(
                            remoteId = remoteId,
                            socialType = type,
                            remoteToken = remoteToken
                        ),
                        resultKey = resultKey,
                    ).intoBundle(_bundler),
                    NavOptions.Builder()
                        .setPopUpTo(R.id.authorization, false)
                        .setLaunchSingleTop(true)
                        .build()
                )
            }
        }

        @CheckReturnValue
        fun onResult(
            fragment: Fragment,
            resultKey: String,
            bundler: Bundler
        ): Observable<AuthorizationResult> = fragment.onFragmentResult(resultKey, bundler)
    }

    object AuthorizationResult

    private val _bundler: Bundler by instance()

    private val _api: DobroApi by instance()

    private val _settings: Settings by instance()

    private val _messageDisplay: MessageDisplay by instance()

    private val _errorHandler: ErrorHandler by instance()

    private val _state: Agent<AuthorizationState> by instance()

    private val _socialNetworksAdapter: SocialNetworksAdapter by lazyGet { SocialNetworksAdapter() }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = AuthorizationBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onStart() = withOnStartResourcesScope {
        super.onStart()

        activity?.window?.enableSoftInputMode(SoftInputMode.AdjustResize)?.scoped()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.authorizationSocialNetworkContainer.adapter =
            _socialNetworksAdapter.apply {
                setItems(SocialNetworksAdapter._socialNetworkExpandedList)
            }
        (binding.authorizationSocialNetworkContainer.layoutManager as GridLayoutManager).spanCount =
            App.authorizationExpandedSocialsSpanCount
    }

    override val screenName: String = AnalyticsConstants.Screen.Login.authorization

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        val authorizationRequest: AuthorizationRequest? = arguments?.getParams(_bundler)

        binding.authorizationPerform
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.authorizationPerformClick)
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.authorizationPerformClick)
            }
            .scoped()

        binding.authorizationResetPassword
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                ResetPasswordFragment
                    .navigate(findNavController())
                    .toResetPassword(R.id.authorization__to__reset_password)
            }
            .scoped()

        binding.authorizationClose
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.authorizationOnCloseClick)
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.authorizationOnCloseClick)

                findNavController().navigateUp()
            }
            .scoped()

        if (BuildConfig.DEBUG) {
            Flowable
                .zip(
                    Flowable.range(0, Int.MAX_VALUE / 4),
                    binding.authorizationTitle.onClick
                ) { index, _ -> index }
                .subscribeOn(RxSchedulers.computation())
                .map { it / App.clicksForEnvironmentSwitcher }
                .distinctUntilChanged()
                .skip(1)
                .throttleUserInput()
                .observeOn(RxSchedulers.main())
                .subscribeBy { EnvironmentSwitcherFragment.show(childFragmentManager) }
                .scoped()
        }

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<AuthorizationState.Idle>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state
                    .sendWhen<AuthorizationState, AuthorizationState.Idle> {
                        process(request = authorizationRequest)
                    }
            }
            .scoped()

        // Restore input values.
        _state
            .observeOn(RxSchedulers.computation())
            .distinctUntilChanged { authorizationState -> authorizationState::class }
            .ofSubtype<AuthorizationState.Input>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.authorizationEmail.setText(it.email.tryGetValue().orNull())
                binding.authorizationPassword.setText(it.password.tryGetValue().orNull())
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<AuthorizationState.Input>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _setSpan()
            }
            .scoped()

        _state
            .setupInputFields(
                persistentListOf(
                    InputFieldDescriptor(
                        AuthorizationField.Email,
                        binding.authorizationEmail,
                        binding.authorizationEmailContainer,
                        false
                    ),
                    InputFieldDescriptor(
                        AuthorizationField.Password,
                        binding.authorizationPassword,
                        binding.authorizationPasswordContainer,
                        false
                    )
                ),
                binding.authorizationPerform
            )
            .scoped()

        // Disable input for non input states.
        _state
            .observeOn(RxSchedulers.computation())
            .map { it is AuthorizationState.Input && it.isValid }
            .distinctUntilChanged()
            .onBackpressureBufferLast(RxSchedulers.main())
            .subscribeBy {
                binding.authorizationPerform.isEnabled = it
            }
            .scoped()

        _socialNetworksAdapter
            .onItemClick
            .withLatestFrom(
                _state
                    .ofSubtype<AuthorizationState.Input>()
            )
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy { (item, _) ->
                when (item) {
                    is SocialNetworksAdapter.Item.SocialNetwork -> {
                        firebaseAnalyticManager.sendSimpleEvent("${AnalyticsConstants.Event.Login.authorizationSocialClick}_${item.path}_click")
                        metricManager.sendSimpleEvent("${AnalyticsConstants.Event.Login.authorizationSocialClick}_${item.path}_click")

                        if (item.path == "vkontakte") {
                            binding.authorizationPerform.gone()
                            binding.progress.visible()
                            VKID(requireContext()).authorize(
                                this@AuthorizationFragment,
                                object : VKID.AuthCallback {
                                    override fun onFail(fail: VKIDAuthFail) {
                                        binding.authorizationPerform.visible()
                                        binding.progress.gone()
                                    }

                                    override fun onSuccess(accessToken: AccessToken) {
                                        binding.progress.gone()
                                        authorizationVKCompleted(
                                            context = requireContext(),
                                            api = _api,
                                            accessToken = accessToken,
                                            messageDisplay = _messageDisplay,
                                            errorHandler = _errorHandler
                                        )
                                    }
                                })
                        } else {
                            val api = _settings.environment.getOrDefault().api
                            if (api is Api.Real) {
                                val url = api.server.buildSocialNetworkAuthorizationUri(item.path)
                                findNavController().navigate(
                                    R.id.authorization__to__authorizationWebView,
                                    Bundle().apply {
                                        putString("URL", url.toString())
                                        putString("title", "Авторизация")
                                    }
                                )
                            }
                        }
                    }

                    is SocialNetworksAdapter.Item.ExpandButton -> {
                        val listWithoutExpandButton =
                            SocialNetworksAdapter._socialNetworkExpandedList

                        _socialNetworksAdapter.setItems(listWithoutExpandButton)
                        (binding.authorizationSocialNetworkContainer.layoutManager as GridLayoutManager).spanCount =
                            App.authorizationExpandedSocialsSpanCount
                    }
                }
            }
            .scoped()

        // Perform authorization.
        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<AuthorizationState.Authentication>()
            .map { it.requestAuthState }
            .ofSubtype<TaskState.InProgress<AuthorizationTaskResult, AuthorizationTaskParams>>()
            .concatMapCompletable {
                _api
                    .overview()
                    .authorizeAsync(
                        email = it.params.email,
                        password = it.params.password,
                        clientId = getString(R.string.client_id),
                        clientSecret = getString(R.string.client_secret),
                    )
                    .doOnSuccess {
                        _state.sendWhen<AuthorizationState, AuthorizationState.Authentication> {
                            updateTask {
                                if (this is TaskState.InProgress) {
                                    success(it)
                                } else {
                                    this
                                }
                            }
                        }
                    }
                    .doOnError {
                        _state.sendWhen<AuthorizationState, AuthorizationState.Authentication> {
                            updateTask {
                                if (this is TaskState.InProgress) {
                                    fail(it)
                                } else {
                                    this
                                }
                            }
                        }
                    }
                    .logError(logger) { "Failed to perform email authorization." }
                    .ignoreElement()
                    .onErrorComplete()
            }
            .subscribe()
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<AuthorizationState.AuthenticationBySocialNetwork>()
            .map { it.authorizationBySocialNetworkTaskState }
            .ofSubtype<TaskState.InProgress<AuthorizationBySocialNetworkTaskResult, AuthorizationBySocialNetworkTaskParams>>()
            .concatMapCompletable {
                _api
                    .overview()
                    .authorizeBySocialNetworkAsync(
                        clientId = getString(R.string.client_id),
                        clientSecret = getString(R.string.client_secret),
                        remoteId = it.params.remoteId,
                        socialType = it.params.socialType,
                        remoteToken = it.params.remoteToken
                    )
                    .doOnSuccess {
                        _state.sendWhen<AuthorizationState, AuthorizationState.AuthenticationBySocialNetwork> {
                            updateTask {
                                if (this is TaskState.InProgress) {
                                    success(it)
                                } else {
                                    this
                                }
                            }
                        }
                    }
                    .doOnError {
                        _state.sendWhen<AuthorizationState, AuthorizationState.AuthenticationBySocialNetwork> {
                            updateTask {
                                if (this is TaskState.InProgress) {
                                    fail(it)
                                } else {
                                    this
                                }
                            }
                        }
                    }
                    .logError(logger) { "Failed to perform social network authorization." }
                    .ignoreElement()
                    .onErrorComplete()
            }
            .subscribe()
            .scoped()

        viewLifecycleOwner.repeatOnResume {
            _state
                .observe<AuthorizationState.Completed>()
                .subscribeToFlow { state ->
                    when (state) {
                        is AuthorizationState.Completed.Success -> {
                            authorizationCompletedSuccessfully(
                                authResponse = state.result,
                                param = state.authType,
                                onCompleteEvent = {
                                    (requireActivity() as MainActivity).scheduleBottomNavigationAllTabsRefresh()
                                    findNavController().navigateUp()
                                },
                                mainActivity = (activity as? MainActivity)
                            )
                        }

                        is AuthorizationState.Completed.Failed -> {
                            when (state.errorCause) {
                                is AccountDisabledException -> {
                                    findNavController().navigate(
                                        R.id.authorization__to__account_disabled
                                    )
                                }

                                else -> {
                                    state.error?.let(_messageDisplay::showMessage)
                                }
                            }

                            _state.sendWhen<AuthorizationState, AuthorizationState.Completed.Failed> {
                                backToInput()
                            }
                        }
                    }
                }
        }
    }

    private fun _setSpan() {
        context?.let { context ->
            val clickablePart =
                context.getString(R.string.authorization___subtitle__clickable_part)
            val notClickablePart =
                context.getString(R.string.authorization___subtitle__not_clickable_part)

            val subtitleSpannedString = requireContext().spannedString {
                it
                    .append(notClickablePart)
                    .append(Characters.nonBreakingSpace)
                    .append(clickablePart)
            }

            val subtitleClickableSpan: ClickableSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    findNavController().navigate(R.id.authorization__to_registration)
                    firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.authorizationOnRegistrationClick)
                    metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.authorizationOnRegistrationClick)
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.isUnderlineText = false
                    ds.color =
                        context.getSupportColor(R.color.application___color__primary)
                }
            }

            subtitleSpannedString.setSpan(
                subtitleClickableSpan,
                notClickablePart.length + 1,
                subtitleSpannedString.length,
                SPAN_EXCLUSIVE_EXCLUSIVE
            )
            binding.authorizationSubtitle.apply {
                movementMethod = LinkMovementMethod.getInstance()
                text = subtitleSpannedString
            }
        }
    }
}
