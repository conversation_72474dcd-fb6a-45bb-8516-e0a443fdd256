package ru.dobro.login

import kotlinx.collections.immutable.PersistentSet
import kotlinx.collections.immutable.persistentSetOf
import ru.dobro.R
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.ScaffoldActivity
import javax.annotation.CheckReturnValue

class LoginActivity : ScaffoldActivity(
    R.id.login___navigation_host_container,
    contentId = R.layout.login___content
) {
    override val defaultCustomToolbar: HasCustomToolbar = object : HasCustomToolbar {
        @CheckReturnValue
        override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
            HasCustomToolbar.CustomToolbar.None
    }

    override val topLevelDestinationIds: PersistentSet<Int> = persistentSetOf(
        R.id.login__onboarding,
        R.id.authorization
    )
}
