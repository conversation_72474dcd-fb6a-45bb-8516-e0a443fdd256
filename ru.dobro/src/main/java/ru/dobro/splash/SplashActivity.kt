package ru.dobro.splash

import android.os.Bundle
import android.view.View
import common.library.android.BasicActivity
import common.library.android.intent.startActivity
import common.library.android.resource.withOnCreateResourcesScope
import common.library.core.rx.RxSchedulers
import common.library.core.rx.signal
import common.library.mobile.services.InAppUpdatesProvider
import common.library.mobile.services.InAppUpdatesProviderImpl
import common.library.mobile.services.models.RemoteStoreAlias
import io.reactivex.rxkotlin.Singles
import io.reactivex.rxkotlin.subscribeBy
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.BuildConfig
import ru.dobro.R
import ru.dobro.core.account.AccountManager
import ru.dobro.core.analytic.AnalyticManager
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.metric.MetricManager
import ru.dobro.core.settings.Settings
import ru.dobro.login.LoginActivity
import ru.dobro.main.MainActivity
import java.util.concurrent.TimeUnit

class SplashActivity : BasicActivity({
    bind<InAppUpdatesProvider>() with singleton { InAppUpdatesProviderImpl(it) }
}) {
    private val _accountManager: AccountManager by instance()

    private val _settings: Settings by instance()

    private val _firebaseAnalyticManager: AnalyticManager by instance()

    private val _metricManager: MetricManager by instance()

    private val _inAppUpdatesProvider: InAppUpdatesProvider by instance()

    override fun onCreate(savedInstanceState: Bundle?) = withOnCreateResourcesScope {
        super.onCreate(savedInstanceState)

        val view = window.decorView
        view.systemUiVisibility = (View.SYSTEM_UI_FLAG_FULLSCREEN
            or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN)

        setContentView(R.layout.splash)

        _firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Screen.showSplashScreen)
        _metricManager.sendSimpleEvent(AnalyticsConstants.Screen.showSplashScreen)

        if (_settings.isFirstInstall() && !BuildConfig.DEBUG) {
            when (_inAppUpdatesProvider.getRemoteStoreAlias()) {
                RemoteStoreAlias.GooglePlay -> _metricManager.sendSimpleEvent(
                    AnalyticsConstants.Event.Common.Instalation.googlePlayInstall
                )

                RemoteStoreAlias.RuStore -> _metricManager.sendSimpleEvent(
                    AnalyticsConstants.Event.Common.Instalation.ruStoreInstall
                )

                RemoteStoreAlias.AppGallery -> _metricManager.sendSimpleEvent(
                    AnalyticsConstants.Event.Common.Instalation.appGalleryInstall
                )

                else -> {}
            }
        }
        _settings.setWasInstalled()

        Singles
            .signal()
            .delay(if (BuildConfig.DEBUG) 100 else 3000, TimeUnit.MILLISECONDS)
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                val accountInfo = _accountManager.getInfo()

                if (accountInfo == null) {
                    _accountManager.removeAccount()
                }

                _settings.incrementLaunchesCount()

                if (_settings.isFirstLaunch()) {
                    startActivity<LoginActivity>()
                } else {
                    startActivity<MainActivity>()
                }
            }
            .scoped()
    }
}
