package ru.dobro.registration_social_network

import common.library.core.email.Email
import common.library.core.location.GeoPoint
import common.library.core.state.task.TaskActionState
import ru.dobro.domain.Location
import java.time.ZonedDateTime

data class SocialNetworkRegistrationRequestParams(
    val name: String,
    val surname: String,
    val birthdate: ZonedDateTime,
    val email: Email?,
    val country: Location,
    val city: Location?,
    val remoteAuth: RemoteAuthParams,
    val settlement: GeoPoint.Coordinates?,
    val isReceiveNewsletters: Boolean,
    val isUnderage: Boolean
) {
    data class RemoteAuthParams(
        val remoteId: String,
        val remoteToken: String?,
        val type: String,
        val emailConfirmed: <PERSON><PERSON><PERSON>
    )
}

typealias SocialNetworkRegistrationTaskParams = SocialNetworkRegistrationRequestParams
typealias SocialNetworkRegistrationTaskState = TaskActionState<SocialNetworkRegistrationTaskParams>
