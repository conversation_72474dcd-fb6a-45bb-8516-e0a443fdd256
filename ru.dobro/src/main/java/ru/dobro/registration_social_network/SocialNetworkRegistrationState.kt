package ru.dobro.registration_social_network

import arrow.core.toOption
import common.library.android.input.InputElement
import common.library.android.input.InputField
import common.library.android.input.InputRequestState
import common.library.android.input.OriginalInput
import common.library.android.string.RString
import common.library.core.EqualityComparators
import common.library.core.contract.ContractException
import common.library.core.email.Email
import common.library.core.location.GeoPoint
import common.library.core.state.task.TaskActionState
import ru.dobro.App
import ru.dobro.core.error.ErrorMessages
import ru.dobro.core.settings.UserSettings
import ru.dobro.core.validation.EmailInputValidator
import ru.dobro.core.validation.PersonNameInputValidator
import ru.dobro.core.validation.PersonSurnameInputValidator
import ru.dobro.domain.Location
import java.time.ZonedDateTime
import javax.annotation.CheckReturnValue

sealed class SocialNetworkRegistrationState :
    InputRequestState<SocialNetworkRegistrationField, SocialNetworkRegistrationState> {
    data object Idle : SocialNetworkRegistrationState() {
        @CheckReturnValue
        fun requestInput(
            request: SocialNetworkRegistrationRequest,
            userSettings: UserSettings
        ): Input =
            Input(
                email = request.email?.let { email ->
                    EmailInputValidator.validateInput(InputField.empty(), email)
                } ?: InputField.empty(),
                name = InputField
                    .empty<String>(
                        OriginalInput.some(
                            request.firstName,
                            EqualityComparators.value()
                        ),
                        OriginalInput.some(
                            request.firstName,
                            EqualityComparators.value()
                        )
                    )
                    .valid(request.firstName, request.firstName),
                surname = InputField
                    .empty<String>(
                        OriginalInput.some(
                            request.lastName,
                            EqualityComparators.value()
                        ),
                        OriginalInput.some(
                            request.lastName,
                            EqualityComparators.value()
                        )
                    )
                    .valid(request.lastName, request.lastName),
                birthday = InputElement
                    .ofOriginal(
                        request.birthDate.toOption(),
                        EqualityComparators.value()
                    ),
                isReceiveNewsletters = true,
                _type = request.type,
                _remoteId = request.remoteId,
                _settlement = userSettings.getSettlementLocations()?.coordinates,
                isNameEditable = !(request.type == "esia" && request.firstName.isNotEmpty()),
                isSurnameEditable = !(request.type == "esia" && request.lastName.isNotEmpty()),
                isEmailEditable = !(request.type == "esia" && request.email.isNullOrEmpty().not()),
                isBirthdayEditable = !(request.type == "esia" && request.birthDate != null),
                remoteToken = request.remoteToken
            )
    }

    data class Input(
        val isNameEditable: Boolean,
        val isSurnameEditable: Boolean,
        val isEmailEditable: Boolean,
        val isBirthdayEditable: Boolean,
        val name: InputField<String>,
        val surname: InputField<String>,
        val birthday: InputElement<ZonedDateTime>,
        val email: InputField<Email>,
        val country: Location = Location.restoreRussiaCountry(),
        val city: Location? = null,
        val isReceiveNewsletters: Boolean,
        val remoteToken: String?,
        private val _type: String,
        private val _remoteId: String,
        private val _settlement: GeoPoint.Coordinates?
    ) : SocialNetworkRegistrationState(),
        InputRequestState.Input<SocialNetworkRegistrationField, SocialNetworkRegistrationState> {

        override val isValid: Boolean
            get() = email is InputField.NonEmpty.Valid
                && name is InputField.NonEmpty.Valid
                && surname is InputField.NonEmpty.Valid
                && birthday is InputElement.NonEmpty.Valid
                && city != null

        override fun get(id: SocialNetworkRegistrationField): InputField<*> =
            when (id) {
                SocialNetworkRegistrationField.Name -> name
                SocialNetworkRegistrationField.Surname -> surname
                SocialNetworkRegistrationField.Email -> email
            }

        @CheckReturnValue
        fun inputEmail(value: String): Input =
            copy(
                email = EmailInputValidator.validateInput(email, value)
            )

        @CheckReturnValue
        fun inputCountry(county: Location): Input =
            copy(country = county)

        @CheckReturnValue
        fun inputCity(city: Location): Input =
            copy(city = city)

        @CheckReturnValue
        fun inputName(value: String): Input =
            copy(
                name = PersonNameInputValidator.validateInput(name, value)
            )

        @CheckReturnValue
        fun inputSurname(value: String): Input =
            copy(
                surname = PersonSurnameInputValidator.validateInput(surname, value)
            )

        @CheckReturnValue
        fun inputBirthday(value: ZonedDateTime): Input =
            copy(
                birthday = birthday.valid(value)
            )

        fun checkReceiveNewsletters(): Input =
            copy(isReceiveNewsletters = isReceiveNewsletters.not())

        @CheckReturnValue
        override fun focus(id: SocialNetworkRegistrationField): SocialNetworkRegistrationState =
            when (id) {
                SocialNetworkRegistrationField.Email -> focusEmail()
                SocialNetworkRegistrationField.Name -> focusName()
                SocialNetworkRegistrationField.Surname -> focusSurname()
            }

        @CheckReturnValue
        fun focusEmail(): Input = copy(email = email.markFocused())

        @CheckReturnValue
        fun focusName(): Input = copy(name = name.markFocused())

        @CheckReturnValue
        fun focusSurname(): Input = copy(surname = surname.markFocused())

        @CheckReturnValue
        fun getParams(): SocialNetworkRegistrationTaskParams {
            if (email !is InputField.NonEmpty.Valid
                || name !is InputField.NonEmpty.Valid
                || surname !is InputField.NonEmpty.Valid
                || birthday !is InputElement.NonEmpty.Valid

            ) {
                throw ContractException("Invalid input.")
            }

            return SocialNetworkRegistrationTaskParams(
                name.data,
                surname.data,
                birthday.data,
                email.data,
                country = country,
                city = city,
                remoteAuth = SocialNetworkRegistrationRequestParams.RemoteAuthParams(
                    type = _type,
                    remoteId = _remoteId,
                    remoteToken = remoteToken,
                    emailConfirmed = true
                ),
                settlement = _settlement,
                isReceiveNewsletters = isReceiveNewsletters,
                isUnderage = isUnderage()
            )
        }

        @CheckReturnValue
        fun isUnderage(): Boolean = birthday.tryGetValidData()
            .orNull()
            ?.isAfter(ZonedDateTime.now().minusYears(App.majorityAge.toLong())) == true

        @CheckReturnValue
        override fun processing(): Registration {
            if (email !is InputField.NonEmpty.Valid
                || name !is InputField.NonEmpty.Valid
                || surname !is InputField.NonEmpty.Valid
                || birthday !is InputElement.NonEmpty.Valid

            ) {
                throw ContractException("Invalid input.")
            }

            return Registration(
                _input = this,

                socialNetworkRegistrationTask = TaskActionState.Idle
                    .awaiting<SocialNetworkRegistrationTaskParams>()
                    .execute(getParams())
            )
        }

        @CheckReturnValue
        override fun input(
            id: SocialNetworkRegistrationField,
            value: String
        ): SocialNetworkRegistrationState =
            when (id) {
                SocialNetworkRegistrationField.Email -> inputEmail(value)
                SocialNetworkRegistrationField.Name -> inputName(value)
                SocialNetworkRegistrationField.Surname -> inputSurname(value)
            }
    }

    data class Registration(
        private val _input: Input,

        val socialNetworkRegistrationTask: SocialNetworkRegistrationTaskState
    ) : SocialNetworkRegistrationState() {
        @CheckReturnValue
        fun updateTask(action: SocialNetworkRegistrationTaskState.() -> SocialNetworkRegistrationTaskState): SocialNetworkRegistrationState {
            if (_input.email !is InputField.NonEmpty.Valid
                || _input.name !is InputField.NonEmpty.Valid
                || _input.surname !is InputField.NonEmpty.Valid
                || _input.birthday !is InputElement.NonEmpty.Valid
            ) {
                throw ContractException("Invalid input.")
            }

            return when (val state = socialNetworkRegistrationTask.action()) {
                is TaskActionState.Idle.Completed.Success ->
                    Completed.Success(
                        remoteId = state.params.remoteAuth.remoteId,
                        remoteToken = state.params.remoteAuth.remoteToken,
                        type = state.params.remoteAuth.type,
                        _input = _input
                    )

                is TaskActionState.Idle.Completed.Fail ->
                    Completed.Failed(
                        input = _input,
                        error = ErrorMessages.ofException(state.error)
                    )

                else -> copy(socialNetworkRegistrationTask = state)
            }
        }
    }

    sealed class Completed : SocialNetworkRegistrationState() {
        data class Failed(
            val input: Input,
            val error: RString?
        ) : Completed() {
            @CheckReturnValue
            fun backToInput(): Input = input
        }

        data class Success(
            val remoteId: String,
            val remoteToken: String?,
            val type: String,
            private val _input: Input
        ) : Completed() {
            @CheckReturnValue
            fun backToInput(): Input = _input
        }
    }
}
