package ru.dobro.registration_social_network

import android.net.Uri
import android.os.Bundle
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import androidx.annotation.IdRes
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.MaterialDatePicker
import common.library.android.coroutines.repeatOnResume
import common.library.android.coroutines.safeRun
import common.library.android.coroutines.subscribeToFlow
import common.library.android.getSupportColor
import common.library.android.input.InputFieldDescriptor
import common.library.android.input.setupInputFields
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.intent.newTask
import common.library.android.intent.tryStartActivityBy
import common.library.android.intent.view
import common.library.android.message.MessageDisplay
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.string.r
import common.library.android.string.span.spannedString
import common.library.android.widget.isGoneVisible
import common.library.android.widget.onClick
import common.library.core.Characters
import common.library.core.collection.ifSome
import common.library.core.collection.orEmptySet
import common.library.core.rx.RxSchedulers
import common.library.core.rx.invoke
import common.library.core.rx.ofSubtype
import common.library.core.rx.onBackpressureBufferLast
import common.library.core.state.task.handleActionExecution
import common.library.core.variable.Agent
import common.library.core.variable.agent
import common.library.core.variable.sendWhen
import io.reactivex.Single
import io.reactivex.processors.PublishProcessor
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.rxkotlin.withLatestFrom
import kotlinx.collections.immutable.persistentListOf
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.R
import ru.dobro.address_selector.AddressSelectorFragment
import ru.dobro.address_selector.Country
import ru.dobro.api.DobroApi
import ru.dobro.api.overview.createUserFromSocialNetworkDataAsync
import ru.dobro.authorization.SocialNetworksAdapter
import ru.dobro.authorization.authorizationCompletedSuccessfully
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.RequestCodes
import ru.dobro.core.account.AccountManager
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.format.DisplayFormat
import ru.dobro.core.settings.Settings
import ru.dobro.core.settings.UserSettings
import ru.dobro.core.validation.DatePickerRangeValidator
import ru.dobro.databinding.SocialNetworkRegistrationBinding
import ru.dobro.domain.SettlementExtended
import ru.dobro.main.MainActivity
import ru.dobro.registration.parents_or_guardian.UnderageRegistrationFragment
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.ZonedDateTime
import javax.annotation.CheckReturnValue

class SocialNetworkRegistrationFragment : BaseFragment({
    bind<Agent<SocialNetworkRegistrationState>>() with singleton {
        agent(
            SocialNetworkRegistrationState.Idle
        )
    }
    bind<SocialNetworksAdapter>() with singleton { SocialNetworksAdapter() }
}), HasCustomToolbar {

    private lateinit var binding: SocialNetworkRegistrationBinding
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        HasCustomToolbar.CustomToolbar.None

    companion object {
        @CheckReturnValue
        fun navigate(
            navigator: NavController,
            bundler: Bundler
        ): NavigationContext = NavigationContext(navigator, bundler)

        class NavigationContext(
            private val _navigator: NavController,
            private val _bundler: Bundler
        ) {
            @CheckReturnValue
            fun toRegistration(
                @IdRes actionId: Int,
                firstName: String,
                lastName: String,
                email: String?,
                birthDate: ZonedDateTime?,
                emailConfirmed: Boolean,
                remoteId: String,
                type: String,
                remoteToken: String?
            ) {
                _navigator.navigate(
                    actionId,
                    SocialNetworkRegistrationRequest(
                        firstName = firstName,
                        lastName = lastName,
                        email = email,
                        birthDate = birthDate,
                        remoteId = remoteId,
                        type = type,
                        emailConfirmed = emailConfirmed,
                        remoteToken = remoteToken
                    ).intoBundle(_bundler),
                    NavOptions.Builder()
                        .setPopUpTo(R.id.authorization, false)
                        .setLaunchSingleTop(true)
                        .build()
                )
            }
        }
    }

    private val _bundler: Bundler by instance()

    private val _api: DobroApi by instance()

    private val _userSettings: UserSettings by instance()

    private val _settings: Settings by instance()

    private val _messageDisplay: MessageDisplay by instance()

    private val _displayFormatter: DisplayFormat by instance()

    private val _accountManager: AccountManager by instance()

    private val _state: Agent<SocialNetworkRegistrationState> by instance()

    private val _onRegistrationConditionsClick: PublishProcessor<Unit> = PublishProcessor.create()
    private val _onRegistrationAgreementClick: PublishProcessor<Unit> = PublishProcessor.create()

    private val _agreement: Uri get() = Uri.parse(getString(R.string.application___agreement))
    private val _consentOfPersonalDataProcessing: Uri get() = Uri.parse(getString(R.string.application___consent_of_personal_data_processing))

    private val _requestCodeSelectCity: String =
        RequestCodes.scopedBy<SocialNetworkRegistrationFragment>("select_city")

    private val _requestCodeSelectCountry: String =
        RequestCodes.scopedBy<SocialNetworkRegistrationFragment>("select_country")

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = SocialNetworkRegistrationBinding.inflate(inflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.socialNetworkRegistrationCountry.setOnClickListener {
            onCountrySelect()
        }
        binding.socialNetworkRegistrationCity.setOnClickListener {
            onCitySelect()
        }
    }

    override val screenName: String = AnalyticsConstants.Screen.Login.registrationBySocialNetwork

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        val request: SocialNetworkRegistrationRequest? = arguments?.getParams(_bundler)

        if (request === null) {
            logger.warning { "Invalid socialNetworkRequest $request" }

            findNavController().navigateUp()

            return
        }

        binding.socialNetworkRegistrationPerform
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.socialRegistrationOnPerformClick)
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.socialRegistrationOnPerformClick)
            }
            .scoped()

        binding.socialNetworkRegistrationClose
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.socialRegistrationOnCloseClick)
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.socialRegistrationOnCloseClick)

                findNavController().popBackStack(R.id.authorization, false)
                (requireActivity() as? MainActivity)?.apply {
                    scheduleBottomNavigationAllTabsRefresh()
                    setBottomNavigationSelectedItemId(R.id.main__overview)
                }
            }
            .scoped()

        _setTopSpan()

        _setBottomSpan()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<SocialNetworkRegistrationState.Input>()
            .observeOn(RxSchedulers.main())
            .subscribeBy { state ->
                binding.socialNetworkRegistrationUsername.isEnabled =
                    state.isNameEditable
                binding.socialNetworkRegistrationUserSurname.isEnabled =
                    state.isSurnameEditable
                binding.socialNetworkRegistrationEmail.isEnabled =
                    state.isEmailEditable
                binding.socialNetworkRegistrationUserBirthday.isEnabled =
                    state.isBirthdayEditable
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<SocialNetworkRegistrationState.Input>()
            .map { it.isReceiveNewsletters }
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.socialNetworkRegistrationSubscribeCheckBox.isChecked = it
            }
            .scoped()

        _onRegistrationConditionsClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.socialRegistrationOnConditionsClick)
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.socialRegistrationOnConditionsClick)

                tryStartActivityBy {
                    it
                        .view(_consentOfPersonalDataProcessing)
                        .newTask()
                }
            }
            .scoped()

        _onRegistrationAgreementClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.registrationOnAgreementClick)
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.registrationOnAgreementClick)

                tryStartActivityBy {
                    it
                        .view(_agreement)
                        .newTask()
                }
            }
            .scoped()

        binding.socialNetworkRegistrationSubscribeContainer
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state.sendWhen<SocialNetworkRegistrationState, SocialNetworkRegistrationState.Input> {
                    checkReceiveNewsletters()
                }
            }
            .scoped()

        binding.socialNetworkRegistrationUserBirthday
            .onClick
            .withLatestFrom(
                _state
                    .ofSubtype<SocialNetworkRegistrationState.Input>()
            )
            .observeOn(RxSchedulers.computation())
            .filter { it.second.isBirthdayEditable }
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy { (_, state) ->
                val constraintsBuilder = CalendarConstraints.Builder()
                constraintsBuilder.setEnd(ZonedDateTime.now().toInstant().toEpochMilli())
                constraintsBuilder.setValidator(
                    DatePickerRangeValidator(
                        LocalDate.MIN,
                        LocalDate.now()
                    )
                )
                val builder: MaterialDatePicker.Builder<Long> =
                    MaterialDatePicker.Builder.datePicker()
                builder.setCalendarConstraints(constraintsBuilder.build())
                state.birthday.tryGetData().ifSome {
                    builder.setSelection(
                        it.toLocalDate().atStartOfDay(ZoneOffset.UTC).toInstant()
                            .toEpochMilli()
                    )
                }
                val picker: MaterialDatePicker<Long> = builder.build()
                picker.addOnPositiveButtonClickListener {
                    val instant: Instant = Instant.ofEpochMilli(it)
                    val date = instant.atZone(ZoneOffset.UTC)
                    binding.socialNetworkRegistrationUserBirthday.setText(
                        _displayFormatter.dateZonedFormatter().format(date)
                    )

                    _state.sendWhen<SocialNetworkRegistrationState, SocialNetworkRegistrationState.Input> {
                        inputBirthday(date)
                    }
                }
                picker.show(requireActivity().supportFragmentManager, picker.toString())
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<SocialNetworkRegistrationState.Idle>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state
                    .sendWhen<SocialNetworkRegistrationState, SocialNetworkRegistrationState.Idle> {
                        requestInput(request, _userSettings)
                    }
            }
            .scoped()

        // Restore input values.
        _state
            .observeOn(RxSchedulers.computation())
            .distinctUntilChanged { registrationState -> registrationState::class }
            .ofSubtype<SocialNetworkRegistrationState.Input>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.socialNetworkRegistrationEmail.setText(it.email.tryGetValue().orNull())

                binding.socialNetworkRegistrationUserSurname.setText(
                    it.surname.tryGetValue().orNull()
                )

                binding.socialNetworkRegistrationUsername.setText(it.name.tryGetValue().orNull())

                it.birthday.tryGetValidData().mapNotNull {
                    binding.socialNetworkRegistrationUserBirthday.setText(
                        _displayFormatter.dateZonedFormatter().format(it)
                    )
                }
            }
            .scoped()

        _state
            .ofSubtype<SocialNetworkRegistrationState.Input>()
            .observeOn(RxSchedulers.computation())
            .distinctUntilChanged { input -> input.city }
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.socialNetworkRegistrationCity.setText(
                    it.city?.settlement ?: it.city?.cityAlternative ?: ""
                )
            }
            .scoped()

        _state
            .ofSubtype<SocialNetworkRegistrationState.Input>()
            .observeOn(RxSchedulers.computation())
            .distinctUntilChanged { registrationState -> registrationState.country }
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.socialNetworkRegistrationCountry.setText(it.country.country ?: "")
            }
            .scoped()

        _state
            .setupInputFields(
                persistentListOf(
                    InputFieldDescriptor(
                        SocialNetworkRegistrationField.Email,
                        binding.socialNetworkRegistrationEmail,
                        binding.socialNetworkRegistrationEmailContainer,
                        false
                    ),
                    InputFieldDescriptor(
                        SocialNetworkRegistrationField.Name,
                        binding.socialNetworkRegistrationUsername,
                        binding.socialNetworkRegistrationUsernameContainer,
                        false
                    ), InputFieldDescriptor(
                        SocialNetworkRegistrationField.Surname,
                        binding.socialNetworkRegistrationUserSurname,
                        binding.socialNetworkRegistrationUserSurnameContainer,
                        false
                    )
                ),
                binding.socialNetworkRegistrationPerform
            )
            .scoped()

        // Disable input for non input states.
        _state
            .observeOn(RxSchedulers.computation())
            .map { it is SocialNetworkRegistrationState.Input && it.isValid }
            .distinctUntilChanged()
            .onBackpressureBufferLast(RxSchedulers.main())
            .subscribeBy {
                binding.socialNetworkRegistrationPerform.isEnabled = it
            }
            .scoped()

        _state
            .handleActionExecution<
                SocialNetworkRegistrationState,
                SocialNetworkRegistrationState.Registration,
                SocialNetworkRegistrationTaskParams>(
                toTaskState = {
                    it.socialNetworkRegistrationTask
                },
                task = { params ->
                    (if (params.settlement == null) {
                        _api.overview()
                            .getCurrentSettlementByIpAsync()
                            .doOnError {
                                _messageDisplay.takeIf { !params.isUnderage }
                                    ?.showMessage("Не удалось загрузить данные".r)
                            }
                            .map { it.coordinates }
                    } else {
                        Single.just(params.settlement)
                    }).flatMapCompletable {
                        _api.overview().createUserFromSocialNetworkDataAsync(
                            firstName = params.name,
                            lastName = params.surname,
                            email = params.email.toString(),
                            birthdate = params.birthdate,
                            settlement = params.city?.coordinates ?: it,
                            settlementExtended = SettlementExtended.restore(
                                title = params.city?.title,
                                country = params.city?.countryISO,
                                municipality = params.city?.municipality,
                                municipalityCode = params.city?.municipalityCode,
                                settlement = params.city?.cityAlternative,
                                flat = params.city?.flat,
                                house = params.city?.house,
                                street = params.city?.street,
                                region = params.city?.region?.take(2),
                                x = params.city?.coordinates?.longitude,
                                y = params.city?.coordinates?.latitude
                            ),
                            emailConfirmed = params.remoteAuth.emailConfirmed,
                            remoteId = params.remoteAuth.remoteId,
                            socialType = params.remoteAuth.type,
                            categories = _userSettings.getCategories().orEmptySet(),
                            hasMedicalHistory = _userSettings.getHasMedicalHistory(),
                            hasDriverLicense = _userSettings.getHasDriverLicence(),
                            receiveNewsletters = params.isReceiveNewsletters
                        ).doOnError {
                            _messageDisplay.takeIf { !params.isUnderage }
                                ?.showMessage("Не удалось загрузить данные".r)
                        }
                    }
                },
                updateState = { updateTask(it) }
            ).scoped()

        viewLifecycleOwner.repeatOnResume {
            _state
                .observe<SocialNetworkRegistrationState.Completed>()
                .subscribeToFlow { state ->
                    when (state) {
                        is SocialNetworkRegistrationState.Completed.Success -> {
                            firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.registrationSuccess)
                            metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.registrationSuccess)

                            auth(state)
                        }

                        is SocialNetworkRegistrationState.Completed.Failed -> {
                            if (context?.let { state.error?.asString(it) } == "Не указаны данные родительского согласия") {
                                if (state.input.isUnderage()) {
                                    UnderageRegistrationFragment
                                        .navigate(
                                            findNavController(),
                                            _bundler
                                        )
                                        .toUnderageSocialNetworkRegistration(
                                            R.id.main__social_network_registration__to__registration__underage_registration,
                                            state.input.getParams()
                                        )
                                }
                            } else {
                                state.error?.let(_messageDisplay::showMessage)
                            }

                            _state.sendWhen<SocialNetworkRegistrationState, SocialNetworkRegistrationState.Completed.Failed> {
                                backToInput()
                            }
                        }
                    }
                }
        }

        AddressSelectorFragment
            .onResult(
                this@SocialNetworkRegistrationFragment,
                _requestCodeSelectCity,
                _bundler
            )
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state.sendWhen<SocialNetworkRegistrationState, SocialNetworkRegistrationState.Input> {
                    inputCity(it)
                }
            }
            .scoped()

        AddressSelectorFragment
            .onResult(
                this@SocialNetworkRegistrationFragment,
                _requestCodeSelectCountry,
                _bundler
            )
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state.sendWhen<SocialNetworkRegistrationState, SocialNetworkRegistrationState.Input> {
                    inputCountry(it)
                }
            }
            .scoped()
    }

    private fun onCountrySelect() {
        AddressSelectorFragment.createCountry(
            requestKey = _requestCodeSelectCountry
        )
            .show(parentFragmentManager, null)
    }

    private fun onCitySelect() {
        AddressSelectorFragment.createSettlement(
            requestKey = _requestCodeSelectCity,
            country = Country.All
        ).show(parentFragmentManager, null)
    }

    private fun _setTopSpan() {
        context?.let { context ->
            val clickablePart =
                context.getString(R.string.registration___authorize__clickable_part)
            val notClickablePart =
                context.getString(R.string.registration___authorize__not_clickable_part)

            val subtitleSpannedString = requireContext().spannedString {
                it
                    .append(notClickablePart)
                    .append(Characters.nonBreakingSpace)
                    .append(clickablePart)
            }

            val subtitleClickableSpan: ClickableSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    findNavController().navigate(R.id.main__social_network_registration__to__authorization)

                    metricManager.sendSimpleEvent(AnalyticsConstants.Event.Login.socialRegistrationOnLoginClick)
                    firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Login.socialRegistrationOnLoginClick)
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.isUnderlineText = false
                    ds.color =
                        context.getSupportColor(R.color.application___color__primary)
                }
            }

            subtitleSpannedString.setSpan(
                subtitleClickableSpan,
                notClickablePart.length + 1,
                subtitleSpannedString.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            binding.socialNetworkRegistrationAuthorize.apply {
                movementMethod = LinkMovementMethod.getInstance()
                text = subtitleSpannedString
            }
        }
    }

    private fun _setBottomSpan() {
        val subtitleSpannedString = requireContext().spannedString {
            it
                .append(R.string.registration__bottom_span_1_part)
                .append(Characters.nonBreakingSpace)
                .append(R.string.registration__bottom_span_2_part)
                .spans {
                    object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            _onRegistrationAgreementClick.invoke()
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            ds.isUnderlineText = false
                            ds.color =
                                requireContext().getSupportColor(R.color.application___color__primary)
                        }
                    }
                }
                .append(Characters.nonBreakingSpace)
                .append(R.string.registration__bottom_span_3_part)
                .append(Characters.nonBreakingSpace)
                .append(R.string.registration__bottom_span_4_part)
                .spans {
                    object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            _onRegistrationConditionsClick.invoke()
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            ds.isUnderlineText = false
                            ds.color =
                                requireContext().getSupportColor(R.color.application___color__primary)
                        }
                    }
                }
        }

        binding.socialNetworkRegistrationSpan.apply {
            movementMethod = LinkMovementMethod.getInstance()
            text = subtitleSpannedString
        }
    }

    private suspend fun auth(state: SocialNetworkRegistrationState.Completed.Success) {
        binding.progress.isVisible(true)

        safeRun(
            onError = {
                findNavController().popBackStack()
                findNavController().popBackStack()
                binding.progress.isVisible(false)
            }
        ) {
            val result = _api.overview().authorizeBySocialNetwork(
                clientId = getString(R.string.client_id),
                clientSecret = getString(R.string.client_secret),
                remoteId = state.remoteId,
                socialType = state.type,
                remoteToken = state.remoteToken
            )

            authorizationCompletedSuccessfully(
                authResponse = result,
                onCompleteEvent = {
                    binding.progress.isVisible(false)
                    findNavController().popBackStack()
                    findNavController().popBackStack()
                    (requireActivity() as MainActivity).scheduleBottomNavigationAllTabsRefresh()
                },
                mainActivity = (activity as? MainActivity)
            )
        }
    }

    private fun ProgressBar.isVisible(isVisible: Boolean) {
        listOf(
            binding.socialNetworkRegistrationClose,
            binding.socialNetworkRegistrationPerform,
        ).forEach { it.isEnabled = !isVisible }
        binding.progress.isGoneVisible = isVisible
    }
}
