package ru.dobro.common

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import androidx.activity.OnBackPressedCallback
import androidx.navigation.fragment.findNavController
import common.library.android.widget.gone
import common.library.android.widget.visible
import ru.dobro.core.BaseFragment
import ru.dobro.databinding.CommonWebViewBinding
import javax.annotation.CheckReturnValue

class WebViewFragment : BaseFragment() {
    override val screenName: String get() = "WebView"

    private lateinit var binding: CommonWebViewBinding

    private var targetUrl: String? = null

    @CheckReturnValue
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = CommonWebViewBinding.inflate(inflater)
        return binding.root
    }

    @SuppressLint("SetJavaScriptEnabled")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        requireActivity().onBackPressedDispatcher.addCallback(
            viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    if (binding.commonWebView.canGoBack()) {
                        binding.commonWebView.goBack()
                    } else {
                        findNavController().navigateUp()
                    }
                }
            })

        targetUrl = arguments?.getString("URL")

        binding.commonWebView.apply {
            this.setDownloadListener { url, _, _, _, _ ->
                startActivity(Intent(Intent.ACTION_VIEW).apply {
                    data = Uri.parse(url)
                })
            }
            webViewClient = WebViewClient()
            isVerticalScrollBarEnabled = false
            settings.javaScriptEnabled = true
            settings.loadsImagesAutomatically = true
            settings.domStorageEnabled = true

//            clearCache(true)
//            WebStorage.getInstance().deleteAllData()
//            val cookieManager: CookieManager = CookieManager.getInstance()
//            cookieManager.removeAllCookies(null)
//            cookieManager.flush()
            clearCache(true)
//            clearFormData()
//            clearHistory()
            clearSslPreferences()
            settings.userAgentString =
                "Chrome/******** Mobile"
//            loadUrl("https://edu.dobro.ru/personal/get_lid/")
            val url = targetUrl.toString()
                .plus("?utm_source=mobile_app&utm_medium=organic&utm_campaign=university_courses_in_app")
            loadUrl(url)
        }
    }

    private inner class WebViewClient : android.webkit.WebViewClient() {

        override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
            super.onPageStarted(view, url, favicon)
            setRefreshing(shouldRefresh = true)
        }

        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            if (url == "https://edu.dobro.ru/?logout=yes") {
                view?.loadUrl("https://edu.dobro.ru/personal/get_lid/")
            }
            setRefreshing(shouldRefresh = false)
        }

        override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
            return if (!(url?.contains("client_id") ?: false)) {
                when (url?.split("/")?.dropLast(1)?.last()) {
                    "courses", "calendar", "materials", "contacts", "dobro.ru", "journal.dobro.ru" -> true
                    "personal" -> {
                        view?.loadUrl(targetUrl.toString())
                        false
                    }

                    else -> {
                        false
                    }
                }
            } else {
                false
            }
        }
    }

    private fun setRefreshing(shouldRefresh: Boolean) {
        if (shouldRefresh) {
            binding.commonWebViewProgress.visible()
        } else {
            binding.commonWebViewProgress.gone()
        }
    }
}
