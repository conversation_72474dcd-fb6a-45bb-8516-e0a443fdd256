package ru.dobro.environment_switcher

import android.Manifest
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import com.google.android.material.snackbar.Snackbar
import common.library.android.BasicDialogFragment
import common.library.android.Processes
import common.library.android.accountManager
import common.library.android.removeAccountsExplicitly
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.widget.onCheckedChange
import common.library.android.widget.onClick
import common.library.android.widget.visible
import common.library.core.collection.toPersistentList
import common.library.core.rx.RxSchedulers
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.schedulers.Schedulers
import org.kodein.di.generic.instance
import ru.dobro.BuildConfig
import ru.dobro.api.DobroApi
import ru.dobro.core.account.AccountManager
import ru.dobro.core.account.UserAccountType
import ru.dobro.core.settings.Environment
import ru.dobro.core.settings.Settings
import ru.dobro.core.validation.Ui.makeAccent
import ru.dobro.core.validation.Ui.makeDefault
import ru.dobro.databinding.EnvironmentSwitcherBinding
import ru.dobro.domain.AccessToken
import ru.dobro.domain.UserId
import ru.dobro.logs.LogInternalWriter
import java.util.concurrent.TimeUnit
import javax.annotation.CheckReturnValue

class EnvironmentSwitcherFragment : BasicDialogFragment() {
    companion object {
        fun show(fragmentManager: FragmentManager, tag: String? = null) {
            EnvironmentSwitcherFragment().show(fragmentManager, tag)
        }
    }

    private lateinit var binding: EnvironmentSwitcherBinding

    private val _settings: Settings by instance()

    private val _accountManager: AccountManager by instance()

    private val _api: DobroApi by instance()

    private val _adapter: EnvironmentAdapter = EnvironmentAdapter()

    private val writeExternalStoragePermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) {
            val isSuccess = LogInternalWriter.writeLogsToFile(requireActivity())
            if (isSuccess) {
                Snackbar.make(
                    binding.root,
                    "Логи сохранены в /Android/data/ru.dobro/files/Documents/dobro/logs",
                    6000
                ).show()
            } else {
                Snackbar.make(binding.root, "Не удалось сохранить логи", 2000).show()
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setStyle(STYLE_NORMAL, androidx.appcompat.R.style.Theme_AppCompat_Dialog_Alert)
    }

    @CheckReturnValue
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = EnvironmentSwitcherBinding.inflate(inflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.environmentSwitcherSelector.adapter = _adapter

        binding.authToken.addTextChangedListener {
            binding.authTokenContainer.makeDefault(binding.authToken)
        }

        if (BuildConfig.DEBUG) {
            binding.writeLogs.visible()
        }
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        val current: Environment = _settings.environment.getOrDefault()

        val isCleartextMode = _settings.isCleartextMode()

        binding.environmentSwitcherCleartextMode.isChecked = isCleartextMode
        binding.environmentSwitcherCleartextMode
            .onCheckedChange
            .throttleUserInput()
            .map { it.isChecked }
            .observeOn(RxSchedulers.main())
            .subscribeBy { isChecked ->
                _settings.setCleartextMode(isChecked)

                _adapter.submitList(
                    Environment.getAllPredefined(isChecked)
                        .asSequence()
                        .map { EnvironmentAdapter.Item(it, it == current) }
                        .toPersistentList()
                )
            }
            .scoped()

        _adapter.submitList(
            Environment.getAllPredefined(isCleartextMode)
                .asSequence()
                .map { EnvironmentAdapter.Item(it, it == current) }
                .toPersistentList()
        )

        _adapter
            .onClick
            // Игнорируем нажатия некоторый интервал времени, чтобы после открытия диалога случайно не выбрать окружение.
            .skip(1, TimeUnit.SECONDS)
            .take(1)
            .doOnNext {
                _settings.environment.set(it)

                requireContext().accountManager.removeAccountsExplicitly(UserAccountType)
            }
            .delay(500, TimeUnit.MILLISECONDS)
            .doOnNext { Processes.killMyself() }
            .subscribe()
            .scoped()

        binding.applyToken
            .onClick
            .observeOn(AndroidSchedulers.mainThread())
            .subscribeBy {
                val token = binding.authToken.text?.toString()
                if (!token.isNullOrEmpty()) {
                    fun createAccByToken(token: String, userId: UserId) {
                        _accountManager.mockAccessToken(
                            AccessToken.restore(token),
                            userId
                        )
                        Processes.killMyself()
                    }

                    val validToken = token.substringAfter("Bearer").trim()
                    _accountManager.removeAccount()
                    _api.profile().getUserMe("Bearer $validToken")
                        .observeOn(RxSchedulers.main())
                        .subscribeBy({
                            createAccByToken(validToken, UserId.restore(-1))
                        }, {
                            createAccByToken(validToken, it.identity)
                        })
                        .scoped()
                } else {
                    binding.authTokenContainer.makeAccent(
                        binding.authToken,
                        "Укажите токен пользователя"
                    )
                }
            }
            .scoped()

        binding.writeLogs
            .onClick
            .subscribeOn(Schedulers.computation())
            .throttleUserInput()
            .observeOn(AndroidSchedulers.mainThread())
            .subscribeBy {
                writeExternalStoragePermissionLauncher.launch(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
            .scoped()
    }

    override fun onStart() {
        super.onStart()

        dialog?.window?.apply {
            setLayout(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT,
            )
        }
    }
}
