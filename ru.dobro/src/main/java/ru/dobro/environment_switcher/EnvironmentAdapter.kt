package ru.dobro.environment_switcher

import android.net.Uri
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import common.library.android.string.r
import common.library.android.string.rString
import common.library.android.string.span.spannedString
import common.library.android.widget.recycler_view.getRxAsyncDiffer
import common.library.core.Characters
import common.library.core.EqualityComparable
import common.library.core.EqualityComparators
import common.library.core.EqualityContentComparable
import common.library.core.rx.invoke
import io.reactivex.Flowable
import io.reactivex.processors.PublishProcessor
import org.reactivestreams.Subscriber
import ru.dobro.R
import ru.dobro.core.settings.Environment
import ru.dobro.databinding.EnvironmentSwitcherItemBinding
import java.util.Objects
import javax.annotation.CheckReturnValue

class EnvironmentAdapter : ListAdapter<EnvironmentAdapter.Item, EnvironmentAdapter.ViewHolder>(
    getRxAsyncDiffer(
        identityComparator = EqualityComparators.natural(),
        contentComparator = EqualityComparators.content()
    )
) {
    private val _onClick: PublishProcessor<Environment> = PublishProcessor.create()

    val onClick: Flowable<Environment> get() = _onClick.hide()

    @CheckReturnValue
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder =
        ViewHolder(
            EnvironmentSwitcherItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            ), _onClick
        )

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class ViewHolder(
        private val binding: EnvironmentSwitcherItemBinding,
        private val _onClick: Subscriber<Environment>
    ) : RecyclerView.ViewHolder(
        binding.root
    ) {
        fun bind(item: Item) {
            val environment: Environment = item.environment
            itemView.apply {
                setOnClickListener { _onClick(environment) }

                val color = context.getColor(
                    if (item.isSelected) {
                        com.google.android.material.R.color.design_default_color_error
                    } else {
                        R.color.black
                    }
                )
                binding.environmentSwitcherItemName.setTextColor(color)
                binding.environmentSwitcherItemName.text = environment.name.asString(context)
                binding.environmentSwitcherItemDescription.text = context.spannedString {
                    it
                        .append(R.string.environment_switcher___main_server_label)
                        .styleBold()
                        .append(Characters.lf)
                        .append(
                            when (val api = environment.api) {
                                Environment.Api.Mock -> R.string.environment_switcher___no_server.rString
                                is Environment.Api.Real -> Uri.Builder()
                                    .scheme(api.server.scheme.asString())
                                    .run {
                                        encodedAuthority(api.server.mainHost)
                                    }
                                    .appendEncodedPath(api.server.mainPath)
                                    .build()
                                    .toString()
                                    .r
                            }
                        )
                        .append(Characters.lf)
                        .append(R.string.environment_switcher___targeted_help_server_label)
                        .styleBold()
                        .append(Characters.lf)
                        .append(
                            when (val api = environment.api) {
                                Environment.Api.Mock -> R.string.environment_switcher___no_server.rString
                                is Environment.Api.Real -> Uri.Builder()
                                    .scheme(api.server.scheme.asString())
                                    .run {
                                        encodedAuthority(api.server.targetedHelpHost)
                                    }
                                    .appendEncodedPath(api.server.targetedHelpPath)
                                    .build()
                                    .toString()
                                    .r
                            }
                        )
                        .append(Characters.lf)
                        .append(R.string.environment_switcher___notifications)
                        .styleBold()
                        .append(Characters.lf)
                        .append(
                            when (val clientId = environment.notificationsClientId) {
                                null -> R.string.environment_switcher___no_server.rString
                                else -> clientId.key.r
                            }
                        )
                }
            }
        }
    }

    data class Item(
        val environment: Environment,
        val isSelected: Boolean
    ) : EqualityComparable<Item>,
        EqualityContentComparable<Item> {
        @CheckReturnValue
        override fun equalTo(other: Item?): Boolean {
            if (other !is Item) {
                return false
            }

            return environment == other.environment
        }

        @CheckReturnValue
        override fun equals(other: Any?): Boolean {
            if (other !is Item) {
                return false
            }

            return equalTo(other)
        }

        @CheckReturnValue
        override fun hashCode(): Int = environment.hashCode()

        @CheckReturnValue
        override fun contentEqualTo(other: Item?): Boolean {
            if (other !is Item) {
                return false
            }

            return environment === other.environment && isSelected == other.isSelected
        }

        @CheckReturnValue
        override fun contentHashCode(): Int = Objects.hash(environment, isSelected)
    }
}
