package ru.dobro.categories_selector

import common.library.core.state.task.TaskActionState
import kotlinx.collections.immutable.PersistentSet
import ru.dobro.domain.CategoryId
import ru.dobro.domain.OrganizationId
import ru.dobro.domain.UserId

data class SaveCategoriesRequest(
    val categories: PersistentSet<CategoryId>,
    val hasDriverLicense: Boolean,
    val hasMedicalHistory: <PERSON><PERSON>an,
    val userId: UserId
)

data class SaveCategoriesOrganizationRequest(
    val categories: PersistentSet<CategoryId>,
    val organizationId: OrganizationId
)

typealias SaveCategoriesAndHelpTypesState = TaskActionState<SaveCategoriesRequest>
typealias SaveCategoriesAndHelpTypesStateIdle = TaskActionState.Idle<SaveCategoriesRequest>
typealias SaveCategoriesAndHelpTypesStateIdleCompletedSuccess = TaskActionState.Idle.Completed.Success<SaveCategoriesRequest>
typealias SaveCategoriesAndHelpTypesStateIdleCompletedFail = TaskActionState.Idle.Completed.Fail<SaveCategoriesRequest>

typealias SaveCategoriesAndHelpTypesOrganizationState = TaskActionState<SaveCategoriesOrganizationRequest>
typealias SaveCategoriesAndHelpTypesOrganizationStateIdle = TaskActionState.Idle<SaveCategoriesOrganizationRequest>
typealias SaveCategoriesAndHelpTypesOrganizationStateIdleCompletedSuccess = TaskActionState.Idle.Completed.Success<SaveCategoriesOrganizationRequest>
typealias SaveCategoriesAndHelpTypesOrganizationStateIdleCompletedFail = TaskActionState.Idle.Completed.Fail<SaveCategoriesOrganizationRequest>
