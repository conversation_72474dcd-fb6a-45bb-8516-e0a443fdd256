package ru.dobro.categories_selector

import android.content.res.ColorStateList
import android.content.res.Resources
import android.net.Uri
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.updatePadding
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import common.library.android.widget.gone
import common.library.android.widget.recycler_view.getRxAsyncDiffer
import common.library.android.widget.scope
import common.library.core.EqualityComparable
import common.library.core.EqualityComparators
import common.library.core.EqualityContentComparable
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import ru.dobro.R
import ru.dobro.databinding.CommonCategoriesItemBinding
import ru.dobro.domain.Category
import ru.dobro.images
import java.util.Objects
import javax.annotation.CheckReturnValue

interface OnCategoryClickHandler {
    fun onCategoryClicked(category: Category)
}

class CategoriesAdapter : ListAdapter<CategoriesAdapter.CategoryItem, CategoriesAdapter.ViewHolder>(
    getRxAsyncDiffer(
        EqualityComparators.natural(),
        EqualityComparators.content()
    )
) {
    private var listener: OnCategoryClickHandler? = null
    private val _onItemClick: MutableSharedFlow<CategoryItem> = MutableSharedFlow()

    val onItemClick: Flow<CategoryItem> get() = _onItemClick

    fun setOnCategoryListener(listener: OnCategoryClickHandler) {
        this.listener = listener
    }

    @CheckReturnValue
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            CommonCategoriesItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            ), _onItemClick
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    data class CategoryItem(
        val category: Category,
        val isChecked: Boolean
    ) : EqualityComparable<CategoryItem>, EqualityContentComparable<CategoryItem> {
        @CheckReturnValue
        override fun equalTo(other: CategoryItem?): Boolean {
            if (other !is CategoryItem) {
                return false
            }

            return category == other.category
        }

        @CheckReturnValue
        override fun equals(other: Any?): Boolean {
            if (other !is CategoryItem) {
                return false
            }

            return equalTo(other)
        }

        @CheckReturnValue
        override fun hashCode(): Int = category.hashCode()

        @CheckReturnValue
        override fun contentEqualTo(other: CategoryItem?): Boolean {
            if (other !is CategoryItem) {
                return false
            }

            return category == other.category
                && isChecked == other.isChecked
        }

        @CheckReturnValue
        override fun contentHashCode(): Int = Objects.hash(category, isChecked)
    }

    inner class ViewHolder(
        private val binding: CommonCategoriesItemBinding,
        private val onClick: MutableSharedFlow<CategoryItem>,
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: CategoryItem) {
            itemView.apply {
                setOnClickListener {
                    binding.scope?.launch {
                        onClick.emit(item)
                    }
                    listener?.onCategoryClicked(item.category)
                }

                binding.title.text = item.category.title

                binding.title.setTextColor(
                    if (item.isChecked) {
                        item.category.color.toInt()
                    } else {
                        ContextCompat.getColor(
                            context,
                            R.color.black
                        )
                    }
                )

                if (item.category == Category.restoreAllCategory()) {
                    binding.icon.setImageResource(R.drawable.ic_all_categories)
                } else {
                    images.load(
                        if (item.isChecked) {
                            item.category.icon
                        } else {
                            item.category.iconGray
                        }
                    ).into(binding.icon)
                }

                binding.root.backgroundTintList =
                    if (item.isChecked) {
                        ColorStateList.valueOf(item.category.color.withAlpha(0x19u).toInt())
                    } else {
                        ColorStateList.valueOf(context.getColor(R.color.application___color__shade))
                    }

                if ((item.category.iconGray == Uri.EMPTY || item.category.icon == Uri.EMPTY) && item.category != Category.restoreAllCategory()) {
                    binding.icon.gone()
                    binding.container.updatePadding(left = 12.toDp)
                }
            }
        }

        private val Number.toDp
            get() = TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP,
                this.toFloat(),
                Resources.getSystem().displayMetrics
            ).toInt()
    }
}
