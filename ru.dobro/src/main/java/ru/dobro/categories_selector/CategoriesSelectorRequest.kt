package ru.dobro.categories_selector

import kotlinx.collections.immutable.PersistentSet
import ru.dobro.domain.Category
import ru.dobro.domain.OrganizationId
import ru.dobro.domain.UserId

sealed class CategoriesSelectorRequest {
    abstract val selectedCategories: PersistentSet<Category>
    abstract val hasMedicalHistory: Boolean
    abstract val hasDriverLicence: Boolean
    abstract val requestKey: String

    class Authorized(
        override val selectedCategories: PersistentSet<Category>,
        override val hasMedicalHistory: <PERSON>olean,
        override val hasDriverLicence: Boolean,
        override val requestKey: String,
        val userId: UserId
    ) : CategoriesSelectorRequest()

    class AuthorizedOrganization(
        override val selectedCategories: PersistentSet<Category>,
        override val hasMedicalHistory: Boolean,
        override val hasDriverLicence: Boolean,
        override val requestKey: String,
        val organizationId: OrganizationId
    ) : CategoriesSelectorRequest()

    class Default(
        override val selectedCategories: PersistentSet<Category>,
        override val hasMedicalHistory: <PERSON>olean,
        override val hasDriverLicence: Boolean,
        override val requestKey: String
    ) : CategoriesSelectorRequest()
}
