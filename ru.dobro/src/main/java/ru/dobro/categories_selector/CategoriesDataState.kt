package ru.dobro.categories_selector

import common.library.core.state.load.LoadDataBySelectorState
import common.library.core.state.load.LoadDataPlainState
import kotlinx.collections.immutable.PersistentSet
import ru.dobro.domain.Category
import ru.dobro.domain.OrganizationType
import ru.dobro.domain.Tag
import ru.dobro.domain.vacancy.VacancyRequirements

typealias CategoriesDataState = LoadDataPlainState<PersistentSet<Category>>
typealias CategoriesDataStateIdle = LoadDataPlainState.Idle<PersistentSet<Category>>
typealias CategoriesDataStateIdleLoading = LoadDataPlainState.InProgress<PersistentSet<Category>>
typealias CategoriesDataStateIdleLoaded = LoadDataPlainState.Idle.Loaded<PersistentSet<Category>>
typealias CategoriesDataStateIdleFailed = LoadDataPlainState.Idle.Failed<PersistentSet<Category>>

typealias OrganizationTypesDataState = LoadDataPlainState<PersistentSet<OrganizationType>>
typealias OrganizationTypesDataStateIdle = LoadDataPlainState.Idle<PersistentSet<OrganizationType>>
typealias OrganizationTypesDataStateIdleLoading = LoadDataPlainState.InProgress<PersistentSet<OrganizationType>>
typealias OrganizationTypesDataStateIdleLoaded = LoadDataPlainState.Idle.Loaded<PersistentSet<OrganizationType>>
typealias OrganizationTypesDataStateIdleFailed = LoadDataPlainState.Idle.Failed<PersistentSet<OrganizationType>>

typealias TagsDataState = LoadDataPlainState<PersistentSet<Tag>>
typealias TagsDataStateIdle = LoadDataPlainState.Idle<PersistentSet<Tag>>
typealias TagsDataStateIdleLoading = LoadDataPlainState.InProgress<PersistentSet<Tag>>
typealias TagsDataStateIdleLoaded = LoadDataPlainState.Idle.Loaded<PersistentSet<Tag>>
typealias TagsDataStateIdleFailed = LoadDataPlainState.Idle.Failed<PersistentSet<Tag>>

typealias RequirementsDataState = LoadDataBySelectorState<VacancyRequirements, String>
typealias RequirementsDataStateIdle = LoadDataBySelectorState.Idle<VacancyRequirements, String>
typealias RequirementsDataStateIdleEmpty = LoadDataBySelectorState.Idle.Empty<VacancyRequirements, String>
typealias RequirementsDataStateIdleLoading = LoadDataBySelectorState.InProgress<VacancyRequirements, String>
typealias RequirementsDataStateIdleReloading = LoadDataBySelectorState.InProgress.Reloading<VacancyRequirements, String>
typealias RequirementsDataStateIdleLoaded = LoadDataBySelectorState.Idle.Loaded<VacancyRequirements, String>
typealias RequirementsDataStateIdleFailed = LoadDataBySelectorState.Idle.Failed<VacancyRequirements, String>
