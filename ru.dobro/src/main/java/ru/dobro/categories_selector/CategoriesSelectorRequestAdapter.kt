package ru.dobro.categories_selector

import com.google.gson.GsonBuilder
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonElement
import com.google.gson.JsonParseException
import com.google.gson.JsonSerializationContext
import common.library.serialization.gson.adapter.SealedClassJsonAdapter
import common.library.serialization.gson.adapter.registerTypeHierarchyAdapter
import common.library.serialization.gson.deserializeTypedRequired
import common.library.serialization.gson.serializeTyped
import kotlinx.collections.immutable.PersistentSet
import ru.dobro.domain.Category
import ru.dobro.domain.OrganizationId
import ru.dobro.domain.UserId
import java.lang.reflect.Type
import javax.annotation.CheckReturnValue
import kotlin.reflect.KClass

fun GsonBuilder.registerCategoriesSelectorRequestAdapter(): GsonBuilder =
    registerTypeHierarchyAdapter(CategoriesSelectorRequestAdapter)

object CategoriesSelectorRequestAdapter :
    SealedClassJsonAdapter<CategoriesSelectorRequest>(CategoriesSelectorRequest::class) {
    private class DefaultProxy(
        val categories: PersistentSet<Category>,
        val hasMedicalHistory: Boolean,
        val hasDriverLicence: Boolean,
        val requestCode: String
    )

    private class AuthorizedProxy(
        val categories: PersistentSet<Category>,
        val hasMedicalHistory: Boolean,
        val hasDriverLicence: Boolean,
        val requestCode: String,
        val userId: UserId
    )

    private class AuthorizedOrganizationProxy(
        val categories: PersistentSet<Category>,
        val hasMedicalHistory: Boolean,
        val hasDriverLicence: Boolean,
        val requestCode: String,
        val organizationId: OrganizationId
    )

    @CheckReturnValue
    override fun serializeInstance(
        src: CategoriesSelectorRequest,
        typeOfSrc: Type,
        context: JsonSerializationContext
    ): JsonElement = when (src) {
        is CategoriesSelectorRequest.Authorized -> context.serializeTyped(
            AuthorizedProxy(
                categories = src.selectedCategories,
                hasDriverLicence = src.hasDriverLicence,
                hasMedicalHistory = src.hasMedicalHistory,
                requestCode = src.requestKey,
                userId = src.userId
            )
        )

        is CategoriesSelectorRequest.Default -> context.serializeTyped(
            DefaultProxy(
                categories = src.selectedCategories,
                hasDriverLicence = src.hasDriverLicence,
                hasMedicalHistory = src.hasMedicalHistory,
                requestCode = src.requestKey
            )
        )

        is CategoriesSelectorRequest.AuthorizedOrganization -> context.serializeTyped(
            AuthorizedOrganizationProxy(
                categories = src.selectedCategories,
                hasDriverLicence = src.hasDriverLicence,
                hasMedicalHistory = src.hasMedicalHistory,
                requestCode = src.requestKey,
                organizationId = src.organizationId
            )
        )
    }

    @CheckReturnValue
    override fun deserializeInstance(
        json: JsonElement,
        typeOfT: Type,
        type: KClass<out CategoriesSelectorRequest>,
        context: JsonDeserializationContext
    ): CategoriesSelectorRequest =
        when (type) {
            CategoriesSelectorRequest.Authorized::class -> {
                val proxy: AuthorizedProxy = context.deserializeTypedRequired(json)

                CategoriesSelectorRequest.Authorized(
                    selectedCategories = proxy.categories,
                    hasDriverLicence = proxy.hasDriverLicence,
                    hasMedicalHistory = proxy.hasMedicalHistory,
                    requestKey = proxy.requestCode,
                    userId = proxy.userId
                )
            }

            CategoriesSelectorRequest.AuthorizedOrganization::class -> {
                val proxy: AuthorizedOrganizationProxy = context.deserializeTypedRequired(json)

                CategoriesSelectorRequest.AuthorizedOrganization(
                    selectedCategories = proxy.categories,
                    hasDriverLicence = proxy.hasDriverLicence,
                    hasMedicalHistory = proxy.hasMedicalHistory,
                    requestKey = proxy.requestCode,
                    organizationId = proxy.organizationId
                )
            }

            CategoriesSelectorRequest.Default::class -> {
                val proxy: DefaultProxy = context.deserializeTypedRequired(json)

                CategoriesSelectorRequest.Default(
                    selectedCategories = proxy.categories,
                    hasDriverLicence = proxy.hasDriverLicence,
                    hasMedicalHistory = proxy.hasMedicalHistory,
                    requestKey = proxy.requestCode
                )
            }

            else -> throw JsonParseException("Unknown type: `$type`.")
        }
}
