package ru.dobro.categories_selector

import common.library.core.state.load.LoadDataPlainState
import kotlinx.collections.immutable.PersistentSet
import ru.dobro.domain.HelpType

typealias HelpTypesDataState = LoadDataPlainState<PersistentSet<HelpType>>
typealias HelpTypesDataStateIdle = LoadDataPlainState.Idle<PersistentSet<HelpType>>
typealias HelpTypesDataStateIdleLoaded = LoadDataPlainState.Idle.Loaded<PersistentSet<HelpType>>
typealias HelpTypesDataStateIdleFailed = LoadDataPlainState.Idle.Failed<PersistentSet<HelpType>>
