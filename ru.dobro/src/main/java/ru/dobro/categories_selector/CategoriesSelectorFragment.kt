package ru.dobro.categories_selector

import android.app.Dialog
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.Fragment
import androidx.fragment.app.clearFragmentResult
import androidx.fragment.app.setFragmentResult
import arrow.core.toOption
import com.google.android.flexbox.AlignItems
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.message.MessageDisplay
import common.library.android.onFragmentResult
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleErrorMessages
import common.library.android.rx.throttleUserInput
import common.library.android.string.r
import common.library.android.widget.isGoneVisible
import common.library.android.widget.onClick
import common.library.core.collection.ifSome
import common.library.core.lazyGet
import common.library.core.logging.logWarning
import common.library.core.orFalse
import common.library.core.rx.RxSchedulers
import common.library.core.rx.ofSubtype
import common.library.core.state.load.LoadDataPlainState
import common.library.core.state.load.handlePlainLoading
import common.library.core.state.task.TaskActionState
import common.library.core.state.task.handleActionExecution
import common.library.core.variable.Agent
import common.library.core.variable.agent
import common.library.core.variable.sendWhen
import io.reactivex.Observable
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.rxkotlin.withLatestFrom
import kotlinx.collections.immutable.PersistentSet
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.rx2.asFlowable
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.R
import ru.dobro.api.DobroApi
import ru.dobro.api.overview.OverviewApi
import ru.dobro.core.BaseBottomSheetDialogFragment
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.error.ErrorMessages
import ru.dobro.core.settings.UserSettings
import ru.dobro.databinding.CategoriesSelectorBinding
import ru.dobro.domain.Category
import ru.dobro.domain.OrganizationId
import ru.dobro.domain.UserId
import javax.annotation.CheckReturnValue

class CategoriesSelectorFragment : BaseBottomSheetDialogFragment({
    bind<Agent<CategoriesSelectorState>>() with singleton { agent(CategoriesSelectorState.Idle) }
}) {
    private lateinit var binding: CategoriesSelectorBinding

    private val _state: Agent<CategoriesSelectorState> by instance()

    private val _bundler: Bundler by instance()

    private val _api: DobroApi by instance()

    private val _userSettings: UserSettings by instance()

    private val _messageDisplay: MessageDisplay by instance()

    private val _categoriesAdapter: CategoriesAdapter by lazyGet { CategoriesAdapter() }

    companion object {
        @CheckReturnValue
        fun createDefault(
            selectedCategories: PersistentSet<Category>,
            hasMedicalHistory: Boolean,
            hasDriverLicence: Boolean,
            requestKey: String,
            bundler: Bundler
        ): CategoriesSelectorFragment = CategoriesSelectorFragment().apply {
            arguments = CategoriesSelectorRequest.Default(
                selectedCategories = selectedCategories,
                hasMedicalHistory = hasMedicalHistory,
                hasDriverLicence = hasDriverLicence,
                requestKey = requestKey
            ).intoBundle<CategoriesSelectorRequest>(bundler)
        }

        @CheckReturnValue
        fun createAuthorized(
            selectedCategories: PersistentSet<Category>,
            hasMedicalHistory: Boolean,
            hasDriverLicence: Boolean,
            requestKey: String,
            bundler: Bundler,
            userId: UserId
        ): CategoriesSelectorFragment = CategoriesSelectorFragment().apply {
            arguments = CategoriesSelectorRequest.Authorized(
                selectedCategories = selectedCategories,
                hasMedicalHistory = hasMedicalHistory,
                hasDriverLicence = hasDriverLicence,
                requestKey = requestKey,
                userId = userId
            ).intoBundle<CategoriesSelectorRequest>(bundler)
        }

        @CheckReturnValue
        fun createAuthorized(
            selectedCategories: PersistentSet<Category>,
            requestKey: String,
            bundler: Bundler,
            organizationId: OrganizationId,
        ): CategoriesSelectorFragment = CategoriesSelectorFragment().apply {
            arguments = CategoriesSelectorRequest.AuthorizedOrganization(
                selectedCategories = selectedCategories,
                hasMedicalHistory = false,
                hasDriverLicence = false,
                requestKey = requestKey,
                organizationId = organizationId
            ).intoBundle<CategoriesSelectorRequest>(bundler)
        }

        @CheckReturnValue
        fun onResult(
            receiver: Fragment,
            requestKey: String,
            bundler: Bundler
        ): Observable<CategoriesSelectorResult> =
            receiver.onFragmentResult(requestKey, bundler)
    }

    object CategoriesSelectorResult

    init {
        setStyle(STYLE_NORMAL, R.style.Application_BottomSheet)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        dialog.setOnShowListener {
            (it as BottomSheetDialog).findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
                ?.let { sheetView ->
                    sheetView.layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
                    it.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                }
        }
        dialog.behavior.skipCollapsed = true
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = CategoriesSelectorBinding.inflate(inflater)
        return binding.root
    }

    override val screenName: String = AnalyticsConstants.Screen.Common.categoriesSelector

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val layoutManager = FlexboxLayoutManager(requireContext())
        layoutManager.flexWrap = FlexWrap.WRAP
        layoutManager.flexDirection = FlexDirection.ROW
        layoutManager.justifyContent = JustifyContent.SPACE_EVENLY
        layoutManager.alignItems = AlignItems.FLEX_START

        binding.categoriesSelectorCategoriesList.layoutManager = layoutManager
        binding.categoriesSelectorCategoriesList.adapter = _categoriesAdapter
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        val request: CategoriesSelectorRequest? = arguments?.getParams(_bundler)
        if (request === null) {
            logger.warning { "Illegal params." }

            dismiss()
            return
        }

        clearFragmentResult(request.requestKey)

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<CategoriesSelectorState.Idle>()
            .subscribeBy {
                _state.sendWhen<CategoriesSelectorState, CategoriesSelectorState.Idle> {
                    requestDataLoading(request)
                }
            }
            .scoped()

        _state
            .map {
                ((it is CategoriesSelectorState.DataLoading && it.categoriesDataState is LoadDataPlainState.InProgress)
                    || (it is CategoriesSelectorState.SaveProcessing && it.taskState is TaskActionState.InProgress))
            }
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.categoriesSelectorProgress.isGoneVisible = it
            }
            .scoped()

        _state.sendWhen<CategoriesSelectorState, CategoriesSelectorState.DataLoading> {
            retryAll()
        }

        _state
            .handlePlainLoading<
                CategoriesSelectorState,
                CategoriesSelectorState.DataLoading,
                PersistentSet<Category>>(
                toTaskState = { it.categoriesDataState },
                task = {
                    _api.overview()
                        .getCategoriesAsync()
                        .doOnError { _messageDisplay.showMessage("Не удалось загрузить данные".r) }
                        .logWarning(logger) { "Failed to load categories." }
                },
                updateState = { updateCategoriesDataState(it) }
            )
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .distinctUntilChanged { categoriesSelectorState -> categoriesSelectorState::class }
            .ofSubtype<CategoriesSelectorState.DataLoading>()
            .map {
                ErrorMessages
                    .ofException(
                        (it.categoriesDataState as? LoadDataPlainState.Idle.Failed)?.error
                    )
                    .toOption()
            }
            .doOnNext {
                _state.sendWhen<CategoriesSelectorState, CategoriesSelectorState.DataLoading> {
                    handleAll()
                }
            }
            .throttleErrorMessages()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                it.ifSome {
                    dismiss()
                    _messageDisplay::showMessage
                }
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<CategoriesSelectorState.Choice>()
            .distinctUntilChanged { choice -> choice.chosenCategories }
            .observeOn(RxSchedulers.main())
            .subscribeBy { state ->
                _categoriesAdapter.submitList(state.allCategories.map {
                    CategoriesAdapter.CategoryItem(
                        category = it,
                        isChecked = state.chosenCategories.contains(it)
                    )
                }.toPersistentList())
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<CategoriesSelectorState.Choice>()
            .distinctUntilChanged { choice -> choice.hasMedicalHistory }
            .map { it.hasMedicalHistory }
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.categoriesSelectorMedicalHistoryCheck.isChecked =
                    it.tryGetData().orNull().orFalse()
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<CategoriesSelectorState.Choice>()
            .distinctUntilChanged { choice -> choice.hasDriverLicence }
            .map { it.hasDriverLicence }
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.categoriesSelectorDriverLicenceCheck.isChecked =
                    it.tryGetData().orNull().orFalse()
            }
            .scoped()

        _categoriesAdapter
            .onItemClick
            .asFlowable()
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state.sendWhen<CategoriesSelectorState, CategoriesSelectorState.Choice> {
                    if (it.isChecked) {
                        removeCategory(it.category)
                    } else {
                        addCategory(it.category)
                    }
                }
            }
            .scoped()

        binding.categoriesSelectorSave
            .onClick
            .withLatestFrom(
                _state
                    .observeOn(RxSchedulers.computation())
                    .ofSubtype<CategoriesSelectorState.Choice>()
            )
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy { (_, state) ->
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Common.categoriesSelectorSaveClick)

                when (val selectorRequest = state.request) {
                    is CategoriesSelectorRequest.Default -> {
                        metricManager.sendSimpleEvent(AnalyticsConstants.Event.Common.categoriesSelectorSaveClick)

                        _userSettings.setCategories(state.chosenCategories)
                        _userSettings.setHasDriverLicence(
                            state.hasDriverLicence.tryGetData().orNull().orFalse()
                        )
                        _userSettings.setHasMedicalHistory(
                            state.hasMedicalHistory.tryGetData().orNull().orFalse()
                        )

                        setFragmentResult(
                            selectorRequest.requestKey,
                            CategoriesSelectorResult.intoBundle(_bundler)
                        )
                        dismiss()
                    }

                    is CategoriesSelectorRequest.Authorized -> {
                        _userSettings.setCategories(state.chosenCategories)
                        _userSettings.setHasDriverLicence(
                            state.hasDriverLicence.tryGetData().orNull().orFalse()
                        )
                        _userSettings.setHasMedicalHistory(
                            state.hasMedicalHistory.tryGetData().orNull().orFalse()
                        )

                        _state.sendWhen<CategoriesSelectorState, CategoriesSelectorState.Choice> {
                            saveProcessing(userId = selectorRequest.userId)
                        }
                    }

                    is CategoriesSelectorRequest.AuthorizedOrganization -> {
                        _state.sendWhen<CategoriesSelectorState, CategoriesSelectorState.Choice> {
                            saveProcessing(organizationId = selectorRequest.organizationId)
                        }
                    }
                }
            }
            .scoped()

        _state
            .handleActionExecution<
                CategoriesSelectorState,
                CategoriesSelectorState.SaveProcessing,
                SaveCategoriesRequest>(
                toTaskState = { it.taskState },
                task = {
                    _api
                        .overview()
                        .saveCategoriesAsync(
                            userId = it.userId,
                            request = OverviewApi.SaveCategoriesRequest(
                                categories = it.categories,
                                hasDriverLicense = it.hasDriverLicense,
                                hasMedicalHistory = it.hasMedicalHistory
                            )
                        )
                        .doOnError { _messageDisplay.showMessage("Не удалось загрузить данные".r) }
                },
                updateState = { updateTaskState(it) }
            )
            .scoped()

        _state
            .handleActionExecution<
                CategoriesSelectorState,
                CategoriesSelectorState.SaveOrganizationProcessing,
                SaveCategoriesOrganizationRequest>(
                toTaskState = { it.taskState },
                task = {
                    _api
                        .overview()
                        .saveCategoriesAsync(
                            organizationId = it.organizationId,
                            request = OverviewApi.SaveCategoriesRequest(
                                categories = it.categories,
                                hasDriverLicense = null,
                                hasMedicalHistory = null
                            )
                        )
                        .doOnError { _messageDisplay.showMessage("Не удалось загрузить данные".r) }
                },
                updateState = { updateTaskState(it) }
            )
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<CategoriesSelectorState.Completed>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                when (it) {
                    is CategoriesSelectorState.Completed.Success -> {
                        setFragmentResult(
                            request.requestKey,
                            CategoriesSelectorResult.intoBundle(_bundler)
                        )
                        dismiss()
                    }

                    is CategoriesSelectorState.Completed.Failed -> {
                        if (it.error !== null) {
                            _messageDisplay.showMessage(it.error)
                        }

                        _state.sendWhen<CategoriesSelectorState, CategoriesSelectorState.Completed.Failed> {
                            backToInput()
                        }
                    }

                    is CategoriesSelectorState.Completed.LoadingFailed -> {
                        Handler(Looper.getMainLooper()).postDelayed({
                            if (it.error !== null) {
                                _messageDisplay.showMessage(it.error)
                            }
                            dismiss()
                        }, 300)
                    }
                }
            }
            .scoped()

        binding.categoriesSelectorMedicalHistoryContainer
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state.sendWhen<CategoriesSelectorState, CategoriesSelectorState.Choice> {
                    checkMedicalHistory()
                }
            }
            .scoped()

        binding.categoriesSelectorDriverLicenceContainer
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state.sendWhen<CategoriesSelectorState, CategoriesSelectorState.Choice> {
                    checkDriverLicence()
                }
            }
            .scoped()
    }
}
