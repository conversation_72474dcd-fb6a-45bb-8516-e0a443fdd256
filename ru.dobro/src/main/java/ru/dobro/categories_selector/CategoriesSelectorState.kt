package ru.dobro.categories_selector

import arrow.core.some
import common.library.android.input.InputElement
import common.library.android.string.RString
import common.library.core.EqualityComparators
import common.library.core.collection.mapPersistent
import common.library.core.orFalse
import common.library.core.state.load.LoadDataPlainState
import kotlinx.collections.immutable.PersistentSet
import ru.dobro.core.error.ErrorMessages
import ru.dobro.domain.Category
import ru.dobro.domain.OrganizationId
import ru.dobro.domain.UserId
import javax.annotation.CheckReturnValue

sealed class CategoriesSelectorState {
    object Idle : CategoriesSelectorState() {
        @CheckReturnValue
        fun requestDataLoading(request: CategoriesSelectorRequest): DataLoading =
            DataLoading(
                categoriesDataState = CategoriesDataStateIdle
                    .empty<PersistentSet<Category>>()
                    .load(),
                _request = request
            )
    }

    data class Choice(
        val allCategories: PersistentSet<Category>,
        val chosenCategories: PersistentSet<Category>,
        val hasMedicalHistory: InputElement<Boolean>,
        val hasDriverLicence: InputElement<Boolean>,
        val request: CategoriesSelectorRequest
    ) : CategoriesSelectorState() {
        @CheckReturnValue
        fun addCategory(item: Category): Choice =
            copy(
                chosenCategories = chosenCategories.add(item)
            )

        @CheckReturnValue
        fun removeCategory(item: Category): Choice =
            copy(
                chosenCategories = chosenCategories.remove(item)
            )

        @CheckReturnValue
        fun checkMedicalHistory(): Choice =
            copy(
                hasMedicalHistory = when (hasMedicalHistory) {
                    is InputElement.Empty -> hasMedicalHistory.valid(false)
                    is InputElement.NonEmpty -> hasMedicalHistory.valid(hasMedicalHistory.data.not())
                }
            )

        @CheckReturnValue
        fun checkDriverLicence(): Choice =
            copy(
                hasDriverLicence = when (hasDriverLicence) {
                    is InputElement.Empty -> hasDriverLicence.valid(false)
                    is InputElement.NonEmpty -> hasDriverLicence.valid(hasDriverLicence.data.not())
                }
            )

        @CheckReturnValue
        fun saveProcessing(userId: UserId): CategoriesSelectorState =
            SaveProcessing(
                _choice = this,
                taskState = SaveCategoriesAndHelpTypesStateIdle
                    .awaiting<SaveCategoriesRequest>()
                    .execute(
                        SaveCategoriesRequest(
                            userId = userId,
                            categories = chosenCategories.mapPersistent { it.identity },
                            hasMedicalHistory = hasMedicalHistory.tryGetData().orNull().orFalse(),
                            hasDriverLicense = hasDriverLicence.tryGetData().orNull().orFalse()
                        )
                    )
            )

        @CheckReturnValue
        fun saveProcessing(organizationId: OrganizationId): CategoriesSelectorState =
            SaveOrganizationProcessing(
                _choice = this,
                taskState = SaveCategoriesAndHelpTypesOrganizationStateIdle
                    .awaiting<SaveCategoriesOrganizationRequest>()
                    .execute(
                        SaveCategoriesOrganizationRequest(
                            categories = chosenCategories.mapPersistent { it.identity },
                            organizationId = organizationId
                        )
                    )
            )
    }

    data class DataLoading(
        val categoriesDataState: CategoriesDataState,
        private val _request: CategoriesSelectorRequest
    ) : CategoriesSelectorState() {
        @CheckReturnValue
        fun handleAll(): CategoriesSelectorState =
            copy(
                categoriesDataState = when (categoriesDataState) {
                    is CategoriesDataStateIdleFailed -> categoriesDataState.handle()
                    else -> categoriesDataState
                }
            )

        @CheckReturnValue
        fun retryAll(): CategoriesSelectorState =
            copy(
                categoriesDataState = when (categoriesDataState) {
                    is LoadDataPlainState.Idle.Empty -> categoriesDataState.load()
                    is CategoriesDataStateIdleFailed -> categoriesDataState.retry()
                    else -> categoriesDataState
                }
            )

        @CheckReturnValue
        fun updateCategoriesDataState(action: CategoriesDataState.() -> CategoriesDataState): CategoriesSelectorState {
            val state = categoriesDataState.action()

            return when {
                state is CategoriesDataStateIdleLoaded -> {
                    Choice(
                        allCategories = state.data,
                        chosenCategories = _request.selectedCategories,
                        hasMedicalHistory = InputElement.ofOriginal(
                                _request.hasMedicalHistory.some(),
                                EqualityComparators.value()
                        ),
                        hasDriverLicence = InputElement.ofOriginal(
                            _request.hasDriverLicence.some(),
                            EqualityComparators.value()
                        ),
                        request = _request
                    )
                }
                state is CategoriesDataStateIdleFailed -> {
                    Completed.LoadingFailed(ErrorMessages.ofException(state.error))
                }
                else -> {
                    copy(categoriesDataState = state)
                }
            }
        }
    }

    data class SaveProcessing(
        private val _choice: Choice,
        val taskState: SaveCategoriesAndHelpTypesState
    ) : CategoriesSelectorState() {
        @CheckReturnValue
        fun updateTaskState(action: SaveCategoriesAndHelpTypesState.() -> SaveCategoriesAndHelpTypesState): CategoriesSelectorState =
            when (val state: SaveCategoriesAndHelpTypesState = taskState.action()) {
                is SaveCategoriesAndHelpTypesStateIdleCompletedSuccess -> Completed.Success
                is SaveCategoriesAndHelpTypesStateIdleCompletedFail -> Completed.Failed(
                    _choice = _choice,
                    error = ErrorMessages.ofException(state.error)
                )
                else -> copy(taskState = state)
            }
    }

    data class SaveOrganizationProcessing(
        private val _choice: Choice,
        val taskState: SaveCategoriesAndHelpTypesOrganizationState
    ) : CategoriesSelectorState() {
        @CheckReturnValue
        fun updateTaskState(action: SaveCategoriesAndHelpTypesOrganizationState.() -> SaveCategoriesAndHelpTypesOrganizationState): CategoriesSelectorState =
            when (val state: SaveCategoriesAndHelpTypesOrganizationState = taskState.action()) {
                is SaveCategoriesAndHelpTypesOrganizationStateIdleCompletedSuccess -> Completed.Success
                is SaveCategoriesAndHelpTypesOrganizationStateIdleCompletedFail -> Completed.Failed(
                    _choice = _choice,
                    error = ErrorMessages.ofException(state.error)
                )
                else -> copy(taskState = state)
            }
    }

    sealed class Completed : CategoriesSelectorState() {
        object Success : Completed()

        data class Failed(
            private val _choice: Choice,

            val error: RString?
        ) : Completed() {
            @CheckReturnValue
            fun backToInput(): Choice = _choice
        }

        data class LoadingFailed(
            val error: RString?
        ) : Completed()
    }
}
