package ru.dobro.image_picker

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.IdRes
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.fragment.app.Fragment
import androidx.fragment.app.clearFragmentResult
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import com.google.android.material.appbar.AppBarLayout
import common.library.android.awaitFragmentResult
import common.library.android.dialog.dialog
import common.library.android.dialog.okButton
import common.library.android.image.internalization.ImageInternalizer
import common.library.android.inflateBy
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.intent.pick
import common.library.android.intent.startActivityForResultBy
import common.library.android.message.MessageDisplay
import common.library.android.onFragmentResult
import common.library.android.permissions.CheckPermissionFragmentManager
import common.library.android.permissions.PermissionsState
import common.library.android.permissions.checkPermissionManager
import common.library.android.resource.withOnCreateResourcesScope
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.setFragmentResult
import common.library.android.string.rString
import common.library.android.string.rText
import common.library.android.widget.gone
import common.library.android.widget.isGoneVisible
import common.library.android.widget.onClick
import common.library.android.widget.visible
import common.library.core.data.FileExtension
import common.library.core.data.MimeType
import common.library.core.images.getBitmapAsFile
import common.library.core.logging.logWarning
import common.library.core.rx.RxSchedulers
import common.library.core.rx.ofSubtype
import common.library.core.variable.Agent
import common.library.core.variable.agent
import common.library.core.variable.sendWhen
import io.reactivex.Flowable
import io.reactivex.Observable
import io.reactivex.Single
import io.reactivex.disposables.Disposable
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.rxkotlin.withLatestFrom
import kotlinx.coroutines.flow.Flow
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.App
import ru.dobro.BuildConfig
import ru.dobro.R
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.RequestCodes
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.data.TempFiles
import ru.dobro.core.error.UserVisibleException
import ru.dobro.core.window.setStatusbarColor
import ru.dobro.databinding.ImagePickerBinding
import ru.dobro.image_picker.GalleryState.Selection.select
import ru.dobro.image_picker.crop_image_view.CropImageView
import ru.dobro.images
import java.io.File
import javax.annotation.CheckReturnValue

/**
 * Supported params: [ImagePickerRequest].
 */
class ImagePickerFragment : BaseFragment({
    bind<Agent<ImagePickerState>>() with singleton { agent(ImagePickerState.Idle) }
}), HasCustomToolbar {
    private lateinit var binding: ImagePickerBinding

    companion object {
        @CheckReturnValue
        fun navigate(
            navigator: NavController,
            bundler: Bundler
        ): NavigationContext = NavigationContext(navigator, bundler)

        class NavigationContext(
            private val _navigation: NavController,
            private val _bundler: Bundler
        ) {
            @CheckReturnValue
            fun toGalleryPickerForResult(
                requestKey: String,
                @IdRes actionId: Int,
                cropMode: CropMode
            ) {
                _toPickerForResult(
                    requestKey = requestKey,

                    mode = ImagePickerMode.Gallery,
                    cropMode = cropMode,

                    actionId = actionId
                )
            }

            @CheckReturnValue
            fun toPhotoCaptureForResult(
                requestKey: String,
                @IdRes actionId: Int,
                cropMode: CropMode
            ) {
                _toPickerForResult(
                    requestKey = requestKey,

                    mode = ImagePickerMode.Photo,
                    cropMode = cropMode,

                    actionId = actionId
                )
            }

            private fun _toPickerForResult(
                requestKey: String,
                @IdRes actionId: Int,

                mode: ImagePickerMode,
                cropMode: CropMode
            ) {
                _navigation.navigate(
                    actionId, ImagePickerRequest(
                        requestKey = requestKey,
                        mode = mode,
                        cropMode = cropMode,
                    ).intoBundle(_bundler)
                )
            }
        }

        @CheckReturnValue
        fun onResult(
            fragment: Fragment,
            resultKey: String,
            bundler: Bundler
        ): Observable<ImagePickerResult> = fragment.onFragmentResult(resultKey, bundler)

        @CheckReturnValue
        fun awaitResult(
            fragment: Fragment,
            resultKey: String,
            bundler: Bundler
        ): Flow<ImagePickerResult> = fragment.awaitFragmentResult(resultKey, bundler)

        private val _cameraPermissionsRequestCode: Int = RequestCodes.permission()
        private val _galleryPermissionsRequestCode: Int = RequestCodes.permission()
        private val _galleryRequestCode: Int = RequestCodes.result()
        private val _cameraRequestCode: Int = RequestCodes.result()
        private val _permissionsFromSettingsRequestCode: Int = RequestCodes.result()

        val tempFiles: TempFiles = TempFiles.forType<ImagePickerFragment>()

        @CheckReturnValue
        private fun _createResultFile(context: Context, extension: FileExtension): File {
            return tempFiles.create(context, "result__", extension)
        }

        @CheckReturnValue
        private fun Agent<ImagePickerState>._galleryState(): Flowable<GalleryState> = Flowable
            .mergeArray(
                observeOn(RxSchedulers.computation())
                    .ofSubtype<ImagePickerState.Selection.FromGallery>()
                    .map { it.galleryState },
                observeOn(RxSchedulers.computation())
                    .ofSubtype<ImagePickerState.Selection.FromCamera>()
                    .map { it.cameraState }
            )

        @CheckReturnValue
        private fun ImagePickerState.Selection._tryUpdateGalleryState(action: GalleryState.() -> GalleryState): ImagePickerState =
            when (this) {
                is ImagePickerState.Selection.FromGallery -> updateGalleryState(action)
                is ImagePickerState.Selection.FromCamera -> updateCameraState(action)
            }
    }

    private val _state: Agent<ImagePickerState> by instance()

    private val _messageDisplay: MessageDisplay by instance()

    private val _bundler: Bundler by instance()

    private val _imageInternalizer: ImageInternalizer by instance()

    private lateinit var _imageUri: Uri

    @CheckReturnValue
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar = object :
        HasCustomToolbar.CustomToolbar.Layout(HasCustomToolbar.CustomToolbarStyle.OverlapContent) {
        @CheckReturnValue
        override fun onCreateView(container: ViewGroup): View =
            container.context.inflateBy(R.layout.image_picker___toolbar, container)

        @CheckReturnValue
        override fun getAppBarLayout(view: View): AppBarLayout? = null

        @CheckReturnValue
        override fun getToolbar(view: View): Toolbar =
            view.findViewById(R.id.image_picker___toolbar)
    }

    override fun onStart() {
        super.onStart()
        activity?.window?.setStatusbarColor(false, R.color.black)
    }

    override fun onStop() {
        super.onStop()
        activity?.window?.setStatusbarColor(true, R.color.application___color_general_background)
    }

    @CheckReturnValue
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = ImagePickerBinding.inflate(inflater)
        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) = withOnCreateResourcesScope {
        super.onCreate(savedInstanceState)

        tempFiles
            .deleteOldAsync(requireContext(), App.tempFilesTimeToLive)
            .subscribe()
            .scoped()

        _state
            .ofSubtype<ImagePickerState.Selection.FromGallery>()
            .map { it.galleryState }
            .observeOn(RxSchedulers.computation())
            .ofSubtype<GalleryState.Selection>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                startActivityForResultBy(_galleryRequestCode) {
                    it.pick().withData(MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
                }
            }
            .scoped()

        _state
            .ofSubtype<ImagePickerState.Selection.FromCamera>()
            .map { it.cameraState }
            .observeOn(RxSchedulers.computation())
            .ofSubtype<GalleryState.Selection>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                startActivityForResultBy(_cameraRequestCode) {
                    val image: File = File.createTempFile(
                        "tempImage",
                        ".jpg",
                        requireContext().cacheDir
                    )

                    _imageUri = FileProvider.getUriForFile(
                        requireContext(),
                        BuildConfig.APPLICATION_ID + ".provider",
                        image
                    )
                    it.implicit(MediaStore.ACTION_IMAGE_CAPTURE).withUri(
                        MediaStore.EXTRA_OUTPUT,
                        _imageUri
                    )
                }
            }
            .scoped()

        _state
            ._galleryState()
            .observeOn(RxSchedulers.computation())
            .ofSubtype<GalleryState.Internalization>()
            .concatMapCompletable {
                val file: File = _createResultFile(requireContext(), FileExtension.jpeg)

                Single
                    .fromCallable {
                        when (
                            _imageInternalizer.internalize(
                                it.selectedImage,
                                file,
                                MimeType.Image.jpeg
                            )
                        ) {
                            ImageInternalizer.Error.InvalidInputSource,
                            ImageInternalizer.Error.UnsupportedInputFormat,
                            ImageInternalizer.Error.UnsupportedOutputFormat -> throw UserVisibleException(
                                R.string.application___error__unsupported_file_format.rString
                            )

                            null -> file
                        }
                    }
                    .subscribeOn(RxSchedulers.io())
                    .observeOn(RxSchedulers.computation())
                    .logWarning(logger) { "Failed to save selected image into internal storage." }
                    .doOnSuccess {
                        _state.sendWhen<ImagePickerState, ImagePickerState.Selection> {
                            _tryUpdateGalleryState {
                                if (this is GalleryState.Internalization) {
                                    success(it)
                                } else {
                                    this
                                }
                            }
                        }
                    }
                    .doOnError {
                        _state.sendWhen<ImagePickerState, ImagePickerState.Selection> {
                            _tryUpdateGalleryState {
                                if (this is GalleryState.Internalization) {
                                    fail(it)
                                } else {
                                    this
                                }
                            }
                        }

                        file.delete()
                    }
                    .ignoreElement()
                    .onErrorComplete()
            }
            .subscribe()
            .scoped()

        setupUploadImage().scoped()
    }

    override val screenName: String = AnalyticsConstants.Screen.Common.imagePicker

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        val request: ImagePickerRequest? = arguments?.getParams(_bundler)
        if (request === null) {
            logger.warning { "Illegal image picker params." }

            findNavController().navigateUp()

            return
        }

        if (request.cropMode == CropMode.Ratio3To4) {
            binding.imagePickerImageOther.setCropMode(CropImageView.CropMode.Ratio3x4)
        }

        clearFragmentResult(request.requestKey)

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<ImagePickerState.Edit>()
            .map { it.fileForEdit }
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.imagePickerMessage.isGoneVisible = true
                binding.imagePickerSave.isGoneVisible = true
                when (request.cropMode) {
                    CropMode.Avatar -> {
                        binding.imagePickerImageCircle.visible()
                        binding.imagePickerImageOther.gone()
                        binding.imagePickerImageNone.gone()
                        images.load(it)
                            .into(binding.imagePickerImageCircle)

                        binding.imagePickerMessage.visible()
                        binding.imagePickerMessage.text = getString(R.string.image_picker___message)
                        binding.imagePickerImageCircle.setActionListener(object : ActionListener {
                            override fun onActionBegin() {
                                binding.imagePickerSave.isEnabled = false
                            }

                            override fun onActionEnd() {
                                binding.imagePickerSave.isEnabled = true
                            }
                        })
                    }

                    CropMode.Other, CropMode.Ratio3To4 -> {
                        binding.imagePickerMessage.visible()
                        binding.imagePickerMessage.text =
                            getString(R.string.image_picker___message_variant)
                        binding.imagePickerImageOther.visible()
                        binding.imagePickerImageCircle.gone()
                        binding.imagePickerImageNone.gone()
                        images.load(it)
                            .into(binding.imagePickerImageOther)
                    }

                    CropMode.None -> {
                        binding.imagePickerMessage.gone()
                        binding.imagePickerImageOther.gone()
                        binding.imagePickerImageCircle.gone()
                        binding.imagePickerImageNone.visible()
                        images.load(it)
                            .into(binding.imagePickerImageNone)
                    }
                }
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .map { it is ImagePickerState.UploadProcessing }
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.imagePickerProgress.isGoneVisible = it
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<ImagePickerState.Idle>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state.sendWhen<ImagePickerState, ImagePickerState.Idle> {
                    val context = context
                    if (context != null) {
                        select(
                            checkPermissionManager = checkPermissionManager,
                            mode = request.mode
                        )
                    } else {
                        this
                    }
                }
            }
            .scoped()

        _handleCameraPermissions().scoped()
        _handleGalleryPermissions().scoped()

        _state
            .toObservable()
            .observeOn(RxSchedulers.computation())
            .ofSubtype<ImagePickerState.Completed>()
            .observeOn(RxSchedulers.io())
            .doOnNext { it.deleteTempFiles() }
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                when (it) {
                    is ImagePickerState.Completed.Success -> {
                        when (it) {
                            is ImagePickerState.Completed.Success.Cancelled -> findNavController().navigateUp()
                            is ImagePickerState.Completed.Success.Uploaded -> {
                                setFragmentResult<ImagePickerResult>(
                                    resultKey = request.requestKey,
                                    result = ImagePickerResult(it.file),
                                    bundler = _bundler
                                )
                                findNavController().navigateUp()
                            }

                            else -> {
                            }
                        }
                    }

                    is ImagePickerState.Completed.Fail -> {
                        if (it.error !== null) {
                            _messageDisplay.showMessage(it.error)
                        }

                        _state.sendWhen<ImagePickerState, ImagePickerState.Completed.Fail> {
                            reset()
                        }
                    }
                }
            }
            .scoped()

        binding.imagePickerSave
            .onClick
            .withLatestFrom(
                _state
                    .observeOn(RxSchedulers.computation())
                    .ofSubtype<ImagePickerState.Edit>()
                    .map { it.fileForEdit }
            )
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy { (_, file) ->
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Common.imagePickerSaveClick)
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Common.imagePickerSaveClick)

                _state.sendWhen<ImagePickerState, ImagePickerState.Edit> {
                    when (request.cropMode) {
                        CropMode.Avatar -> success(
                            binding.imagePickerImageCircle.getCroppedImage(
                                file
                            )
                        )

                        CropMode.Other -> success(
                            binding.imagePickerImageOther.getCroppedBitmapAsFile(
                                file
                            )
                        )

                        CropMode.Ratio3To4 -> success(
                            binding.imagePickerImageOther.getCroppedBitmapAsFile(
                                file
                            )
                        )

                        CropMode.None -> success(binding.imagePickerImageNone.getBitmapAsFile(file))
                    }
                }
            }
            .scoped()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            _cameraPermissionsRequestCode ->
                _state.sendWhen<ImagePickerState, ImagePickerState.Selection> {
                    _tryUpdateGalleryState {
                        if (this is GalleryState.PermissionsCheck) {
                            updatePermissionsState {
                                handleResponse(
                                    checkPermissionManager,
                                    permissions,
                                    grantResults
                                )
                            }
                        } else {
                            this
                        }
                    }
                }

            _galleryPermissionsRequestCode ->
                _state.sendWhen<ImagePickerState, ImagePickerState.Selection> {
                    _tryUpdateGalleryState {
                        if (this is GalleryState.PermissionsCheck) {
                            updatePermissionsState {
                                handleResponse(
                                    checkPermissionManager,
                                    permissions,
                                    grantResults
                                )
                            }
                        } else {
                            this
                        }
                    }
                }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            _galleryRequestCode -> {
                val selectedFile: Uri? = if (resultCode == Activity.RESULT_OK) {
                    data?.data
                } else {
                    null
                }

                _state.sendWhen<ImagePickerState, ImagePickerState.Selection> {
                    if (selectedFile !== null) {
                        _tryUpdateGalleryState {
                            if (this is GalleryState.Selection) {
                                select(selectedFile)
                            } else {
                                this
                            }
                        }
                    } else {
                        when (this) {
                            // В режиме запуска выбора из галереи отказ от выбора медиа сразу отменяет работу всего компонента.
                            is ImagePickerState.Selection.FromGallery -> cancel()
                            is ImagePickerState.Selection.FromCamera -> cancel()
                        }
                    }
                }
            }

            _cameraRequestCode -> {
                val selectedFile: Uri? = if (resultCode == Activity.RESULT_OK) {
                    _imageUri
                } else {
                    null
                }

                _state.sendWhen<ImagePickerState, ImagePickerState.Selection> {
                    if (selectedFile !== null) {
                        _tryUpdateGalleryState {
                            if (this is GalleryState.Selection) {
                                select(selectedFile)
                            } else {
                                this
                            }
                        }
                    } else {
                        when (this) {
                            // В режиме запуска выбора из галереи отказ от выбора медиа сразу отменяет работу всего компонента.
                            is ImagePickerState.Selection.FromGallery -> cancel()
                            is ImagePickerState.Selection.FromCamera -> cancel()
                        }
                    }
                }
            }

            _permissionsFromSettingsRequestCode -> {
                _state.sendWhen<ImagePickerState, ImagePickerState.Selection> {
                    _tryUpdateGalleryState {
                        if (this is GalleryState.PermissionsCheck) {
                            updatePermissionsState {
                                handleResponse(
                                    checkPermissionManager,
                                    arrayOf(GalleryState.Idle.galleryPermission),
                                    intArrayOf(
                                        ContextCompat.checkSelfPermission(
                                            requireActivity(),
                                            GalleryState.Idle.galleryPermission
                                        )
                                    )
                                )
                            }
                        } else {
                            this
                        }
                    }
                }
            }
        }
    }

    @CheckReturnValue
    private fun _handleCameraPermissions(): Disposable {
        var permissionDialog: Dialog? = null

        fun _dismissPermissionDialog() {
            val dialog: Dialog? = permissionDialog
            if (dialog !== null) {
                dialog.dismiss()
            }
            permissionDialog = null
        }

        _dismissPermissionDialog()
        return _state
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                if (it is ImagePickerState.Selection.FromCamera && it.cameraState is GalleryState.PermissionsCheck) {
                    val cameraState = it.cameraState
                    val permissionsState: PermissionsState.NotAllGranted =
                        cameraState.permissionsState

                    if (permissionsState.isAllDeniedExplained()) {
                        permissionsState.requestDeniedOnce(
                            checkPermissionManager,
                            _cameraPermissionsRequestCode
                        )

                        _dismissPermissionDialog()
                    } else {
                        if (permissionDialog == null) {
                            val dialog: AlertDialog = requireContext().dialog {
                                it
                                    .message(R.string.image_picker__permission_explain.rText)
                                    .okButton {
                                        val rationale =
                                            CheckPermissionFragmentManager(this).shouldExplain(
                                                GalleryState.Idle.galleryPermission
                                            )
                                        if (rationale) {
                                            _dismissPermissionDialog()

                                            permissionsState.requestDenied(
                                                checkPermissionManager,
                                                _cameraPermissionsRequestCode
                                            )
                                        } else {
                                            _dismissPermissionDialog()

                                            startActivityForResult(
                                                Intent(
                                                    Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                                                    Uri.fromParts(
                                                        "package",
                                                        requireActivity().packageName,
                                                        null
                                                    )
                                                ),
                                                _permissionsFromSettingsRequestCode
                                            )
                                        }
                                    }
                            }

                            dialog.setOnDismissListener {
                                cameraState.updatePermissionsState { markAllExplained() }
                            }

                            dialog.show()

                            permissionDialog = dialog
                        }
                    }
                } else {
                    _dismissPermissionDialog()
                }
            }
    }

    @CheckReturnValue
    private fun _handleGalleryPermissions(): Disposable {
        var permissionDialog: Dialog? = null

        fun _dismissPermissionDialog() {
            val dialog: Dialog? = permissionDialog
            if (dialog !== null) {
                dialog.dismiss()
            }
            permissionDialog = null
        }

        _dismissPermissionDialog()
        return _state
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                if (it is ImagePickerState.Selection.FromGallery && it.galleryState is GalleryState.PermissionsCheck) {
                    val galleryState = it.galleryState
                    val permissionsState: PermissionsState.NotAllGranted =
                        galleryState.permissionsState

                    if (permissionsState.isAllDeniedExplained()) {
                        permissionsState.requestDeniedOnce(
                            checkPermissionManager,
                            _galleryPermissionsRequestCode
                        )

                        _dismissPermissionDialog()
                    } else {
                        if (permissionDialog == null) {
                            val dialog: AlertDialog = requireContext().dialog {
                                it
                                    .message(R.string.image_picker__permission_explain.rText)
                                    .okButton {
                                        val rationale =
                                            CheckPermissionFragmentManager(this).shouldExplain(
                                                GalleryState.Idle.galleryPermission
                                            )
                                        if (rationale) {
                                            _dismissPermissionDialog()

                                            permissionsState.requestDenied(
                                                checkPermissionManager,
                                                _galleryPermissionsRequestCode
                                            )
                                        } else {
                                            _dismissPermissionDialog()

                                            startActivityForResult(
                                                Intent(
                                                    Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                                                    Uri.fromParts(
                                                        "package",
                                                        requireActivity().packageName,
                                                        null
                                                    )
                                                ),
                                                _permissionsFromSettingsRequestCode
                                            )
                                        }
                                    }
                            }

                            dialog.setOnDismissListener {
                                galleryState.updatePermissionsState { markAllExplained() }
                            }

                            dialog.show()

                            permissionDialog = dialog
                        }
                    }
                } else {
                    _dismissPermissionDialog()
                }
            }
    }
}
