package ru.dobro.image_picker

import common.library.core.data.FileExtension
import common.library.core.logging.Logger
import common.library.core.logging.di.logger
import common.library.core.logging.logWarning
import common.library.core.resource.withResourcesScope
import common.library.core.rx.RxSchedulers
import common.library.core.rx.ofSubtype
import common.library.core.state.task.TaskState
import common.library.core.variable.Agent
import common.library.core.variable.sendWhen
import org.kodein.di.KodeinAware
import org.kodein.di.generic.instance
import ru.dobro.api.DobroApi
import ru.dobro.api.overview.uploadImageAsync
import javax.annotation.CheckReturnValue

@CheckReturnValue
fun KodeinAware.setupUploadImage(): AutoCloseable = withResourcesScope {
    val state: Agent<ImagePickerState> by instance()

    val api: DobroApi by instance()

    val logger: Logger by logger("upload-image")

    state
        .toObservable()
        .observeOn(RxSchedulers.computation())
        .ofSubtype<ImagePickerState.UploadProcessing>()
        .map { it.state }
        .ofSubtype<UploadImageStateInProgress>()
        .concatMapCompletable { uploadState ->
            api.overview().uploadImageAsync(uploadState.params, FileExtension.jpeg)
                .doOnSuccess {
                    state.sendWhen<ImagePickerState, ImagePickerState.UploadProcessing> {
                        updateTask {
                            if (this is TaskState.InProgress) {
                                success(it)
                            } else {
                                this
                            }
                        }
                    }
                }
                .doOnError {
                    state.sendWhen<ImagePickerState, ImagePickerState.UploadProcessing> {
                        updateTask {
                            if (this is TaskState.InProgress) {
                                fail(it)
                            } else {
                                this
                            }
                        }
                    }
                }
                .logWarning(logger) { "Failed to upload image." }
                .ignoreElement()
                .onErrorComplete()
        }
        .subscribe()
        .scoped()
}
