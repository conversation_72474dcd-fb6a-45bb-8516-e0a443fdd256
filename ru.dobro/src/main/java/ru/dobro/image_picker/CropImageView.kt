package ru.dobro.image_picker

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.GestureDetector.SimpleOnGestureListener
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.ScaleGestureDetector.SimpleOnScaleGestureListener
import androidx.annotation.IntRange
import androidx.appcompat.widget.AppCompatImageView
import common.library.android.dimension.DimensionContext
import common.library.android.dimension.dp
import ru.dobro.R
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.lang.ref.WeakReference
import kotlin.math.max
import kotlin.math.min
import kotlin.math.pow
import kotlin.math.roundToInt
import kotlin.math.sqrt

class CropImageView : AppCompatImageView {
    companion object {
        /**
         * @return - maximum scale value for current image and crop ratio
         */
        private const val _maxScale = 3f
        private const val _rectCornerPointsCoordinates = 8
        private const val _animationCropDuration = 500

        /**
         * Gets a float array of the 2D coordinates representing a rectangles
         * corners.
         * The order of the corners in the float array is:
         * 0------->1
         * ^        |
         * |        |
         * |        v
         * 3<-------2
         *
         * @param r the rectangle to get the corners of
         * @return the float array of corners (8 floats)
         */
        private fun getCornersFromRect(r: RectF): FloatArray {
            return floatArrayOf(
                r.left, r.top,
                r.right, r.top,
                r.right, r.bottom,
                r.left, r.bottom
            )
        }

        /**
         * Takes an array of 2D coordinates representing corners and returns the
         * smallest rectangle containing those coordinates.
         *
         * @param array array of 2D coordinates
         * @return smallest rectangle containing coordinates
         */
        private fun trapToRect(array: FloatArray): RectF {
            val r = RectF(
                Float.POSITIVE_INFINITY,
                Float.POSITIVE_INFINITY,
                Float.NEGATIVE_INFINITY,
                Float.NEGATIVE_INFINITY
            )
            var i = 1
            while (i < array.size) {
                val x = (array[i - 1] * 10).roundToInt() / 10f
                val y = (array[i] * 10).roundToInt() / 10f
                r.left = if ((x < r.left)) x else r.left
                r.top = if ((y < r.top)) y else r.top
                r.right = if ((x > r.right)) x else r.right
                r.bottom = if ((y > r.bottom)) y else r.bottom
                i += 2
            }
            r.sort()
            return r
        }
    }

    /**
     * Интерфейс для отслеживания события максимального приближения
     */
    interface OnMaximumScaleListener {
        fun onMaximumScale()
    }

    private val _initialImageRect = RectF()

    // Runnable для анимации
    private var _wrapCropBoundsRunnable: Runnable? = null

    /**
     * Массив для сохранения текущих координат изображения
     *
     *
     * 0------->1
     * ^        |
     * |        |
     * |        v
     * 3<-------2
     */
    private val _currentImageCorners = FloatArray(_rectCornerPointsCoordinates)
    private lateinit var _initialImageCorners: FloatArray
    private var _onMaximumScaleListener: OnMaximumScaleListener? = null
    private var _onAnimationEndListener: WrapCropBoundsRunnable.OnAnimationListener? = null
    private var _actionListener: ActionListener? = null
    private var _viewWidth = 0f
    private var _viewHeight = 0f
    private var _frameWidth = 300f
    private var _frameHeight = 300f
    private var _initScale = 1f
    private val _viewMatrix = Matrix()
    val frameRect = RectF()
    private val _viewRect = RectF()
    private var _gestureDetector: GestureDetector? = null
    private var _scaleGestureDetector: ScaleGestureDetector? = null

    constructor(context: Context?) : super(context!!) {
        _init()
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(
        context!!, attrs
    ) {
        _init()
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context!!, attrs, defStyleAttr
    ) {
        _init()
    }

    private fun _init() {
        scaleType = ScaleType.MATRIX
        _gestureDetector = GestureDetector(context, GestureListener())
        _scaleGestureDetector = ScaleGestureDetector(context, ScaleListener())
    }

    /**
     * If it's ACTION_DOWN event - user touches the screen and all current animation must be canceled.
     * If it's ACTION_UP event - user removed all fingers from the screen and current image position must be corrected.
     * If there are more than 2 fingers - update focal point coordinates.
     * Pass the event to the gesture detectors if those are enabled.
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        val drawable = drawable
        return if (drawable != null) {
            if (event.action and MotionEvent.ACTION_MASK == MotionEvent.ACTION_DOWN) {
                cancelAllAnimations()

                _actionListener?.onActionBegin()
            }
            _gestureDetector?.onTouchEvent(event)
            _scaleGestureDetector?.onTouchEvent(event)
            if (event.action and MotionEvent.ACTION_MASK == MotionEvent.ACTION_UP) {
                checkActionEnd()

                _actionListener?.onActionEnd()
            }
            true
        } else {
            false
        }
    }

    /**
     * Метод для установки слушателя событий жестов
     *
     * @param actionListener слушатель события жестов
     */
    fun setActionListener(actionListener: ActionListener) {
        this._actionListener = actionListener
    }

    // К од для отрисовки вспомогательных линий
    private val _backgroundPaint: Paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val _tempPaint: Paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val _transparentPaint: Paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val _strokePaint: Paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val _dimensionContext = DimensionContext(context)
    private val _porterDuffXfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val drawable = drawable
        if (drawable != null) {
            val bitmap: Bitmap =
                Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            val newCanvas = Canvas(bitmap)

            _backgroundPaint.color = context.getColor(common.library.android.R.color.translucent)
            newCanvas.drawRect(_viewRect, _backgroundPaint)

            _strokePaint.style = Paint.Style.STROKE
            _strokePaint.color = context.getColor(R.color.white)
            _strokePaint.strokeWidth = _dimensionContext.run { 1.dp.toPX() }
            canvas.drawCircle(
                frameRect.centerX(),
                frameRect.centerY(),
                _frameWidth / 2,
                _strokePaint
            )

            _transparentPaint.color = context.getColor(common.library.android.R.color.transparent)
            _transparentPaint.xfermode = _porterDuffXfermode
            newCanvas.drawCircle(
                frameRect.centerX(),
                frameRect.centerY(),
                _frameWidth / 2,
                _transparentPaint
            )

            canvas.drawBitmap(bitmap, 0f, 0f, _tempPaint)
        }
    }

    fun getCroppedImage(file: File): File {
        val viewScale = max(
            _viewWidth / getImageWidthPx(),
            _viewHeight / getImageHeightPx()
        ) * _initScale

        val resizedBitmap = Bitmap.createScaledBitmap(
            (drawable as BitmapDrawable).bitmap,
            (drawable.intrinsicWidth / viewScale).toInt(),
            (drawable.intrinsicHeight / viewScale).toInt(), false
        )

        val bitmap = Bitmap.createBitmap(
            resizedBitmap,
            (frameRect.centerX() - _frameWidth / 2 - _getXOffset()).toInt()
                .coerceIn(0, resizedBitmap.width - _frameWidth.toInt()),
            (frameRect.centerY() - _frameHeight / 2 - _getYOffset()).toInt()
                .coerceIn(0, resizedBitmap.height - _frameHeight.toInt()),
            (_frameWidth).toInt(),
            (_frameHeight).toInt(),
            null,
            false
        )

        val byteArrayOutputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, byteArrayOutputStream)

        val fileOutputStream = FileOutputStream(file)
        fileOutputStream.write(byteArrayOutputStream.toByteArray())
        fileOutputStream.flush()
        fileOutputStream.close()

        return file
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        if (_frameWidth != 0f && _frameHeight != 0f) {
            val widthSize = MeasureSpec.getSize(widthMeasureSpec)
            val heightSize = MeasureSpec.getSize(heightMeasureSpec)

            // Заисываем квадрат рамки
            _viewWidth = widthSize.toFloat()
            _viewHeight = heightSize.toFloat()
            frameRect[widthSize / 2 - _frameWidth / 2, heightSize / 2 - _frameHeight / 2, widthSize / 2 + _frameWidth / 2] =
                heightSize / 2 + _frameHeight / 2
            _viewRect[0f, 0f, _viewWidth] = _viewHeight

            setMeasuredDimension(widthSize, heightSize)
            val drawable = drawable
            drawable?.let { placeDrawable(it) }
        } else {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        }
    }

    override fun setImageDrawable(drawable: Drawable?) {
        super.setImageDrawable(drawable)
        drawable?.let { placeDrawable(it) }
    }

    private fun placeDrawable(drawable: Drawable) {
        _initialImageRect[0f, 0f, drawable.intrinsicWidth.toFloat()] =
            drawable.intrinsicHeight.toFloat()
        _initialImageCorners = getCornersFromRect(_initialImageRect)

        for (i in _currentImageCorners.indices) {
            _currentImageCorners[i] = 0f
        }
        _viewMatrix.reset()

        val viewScale = max(
            _viewWidth / drawable.intrinsicWidth,
            _viewHeight / drawable.intrinsicHeight
        ) * _initScale

        _viewMatrix.setTranslate(_imageCenterX - _viewRect.centerX(), 0f)

        _viewMatrix.postScale(
            viewScale,
            viewScale,
            _imageCenterX - _viewRect.centerX(),
            _imageCenterY - _viewRect.centerY()
        )
        // Апдейтим текущие углы изображения, т.к. по ним вычисляются getImageCenterX() и getImageCenterY()
        _viewMatrix.mapPoints(_currentImageCorners, _initialImageCorners)
        _viewMatrix.mapPoints(_currentImageCorners, _initialImageCorners)
        imageMatrix = _viewMatrix
    }

    /**
     * Метод для получения центра изображения по оси X
     */
    private val _imageCenterX: Float
        get() = (_currentImageCorners[0] + _currentImageCorners[2] + _currentImageCorners[4] + _currentImageCorners[6]) / 4

    /**
     * Метод для получения центра изображения по оси Y
     */
    private val _imageCenterY: Float
        get() = (_currentImageCorners[1] + _currentImageCorners[3] + _currentImageCorners[5] + _currentImageCorners[7]) / 4

    /**
     * Метод для получения смещения изображения по оси X
     */
    private fun _getXOffset(): Float {
        return (_imageCenterX - getImageWidthPx() / 2)
    }

    /**
     * Метод для получения смещения изображения по Y
     */
    private fun _getYOffset(): Float {
        return (_imageCenterY - getImageHeightPx() / 2)
    }

    /**
     * Метод получения ширины изображения в px
     */
    private fun getImageWidthPx(): Float {
        return sqrt(
            (_currentImageCorners[2] - _currentImageCorners[0]).toDouble().pow(2.0)
                + (_currentImageCorners[3] - _currentImageCorners[1]).toDouble().pow(2.0)
        )
            .toFloat()
    }

    /**
     * Метод получения высоты изображения в px
     */
    private fun getImageHeightPx(): Float {
        return sqrt(
            ((_currentImageCorners[4] - _currentImageCorners[2]).toDouble().pow(2.0)
                + (_currentImageCorners[5] - _currentImageCorners[3]).toDouble().pow(2.0))
        )
            .toFloat()
    }

    /**
     * This method translates current image.
     *
     * @param dx - horizontal shift
     * @param dy - vertical shift
     */
    private fun _postTranslate(dx: Float, dy: Float) {
        if (dx != 0f || dy != 0f) {
            _viewMatrix.set(imageMatrix)
            _viewMatrix.postTranslate(dx, dy)
            _viewMatrix.mapPoints(_currentImageCorners, _initialImageCorners)
            imageMatrix = _viewMatrix
        }
    }

    /**
     * This method scales current image.
     *
     * @param ds - scale value
     * @param px - scale center X
     * @param py - scale center Y
     */
    private fun _postScale(ds: Float, px: Float, py: Float) {
        if (ds != 0f) {
            if (ds > 1 && _currentScale * ds <= _maxScale || ds < 1) {
                if (ds * getImageWidthPx() > _frameWidth && ds * getImageHeightPx() > _frameHeight) {
                    _viewMatrix.set(imageMatrix)
                    _viewMatrix.postScale(ds, ds, px, py)
                    _viewMatrix.mapPoints(_currentImageCorners, _initialImageCorners)
                    imageMatrix = _viewMatrix
                }
            } else if (_currentScale * ds > _maxScale) {
                if (_onMaximumScaleListener != null) {
                    _onMaximumScaleListener!!.onMaximumScale()
                }
            }
        }
    }

    /**
     * @return - current image scale value.
     * [1.0f - for original image, 2.0f - for 200% scaled image, etc.]
     */
    private val _currentScale: Float
        get() = _getMatrixScale(imageMatrix)

    /**
     * This method calculates scale value for given Matrix object.
     */
    private fun _getMatrixScale(matrix: Matrix): Float {
        return sqrt(
            _getMatrixValue(matrix, Matrix.MSCALE_X).toDouble().pow(2.0)
                + _getMatrixValue(matrix, Matrix.MSKEW_Y).toDouble().pow(2.0)
        )
            .toFloat()
    }

    /**
     * This method returns Matrix value for given index.
     *
     * @param matrix     - valid Matrix object
     * @param valueIndex - index of needed value. See [Matrix.MSCALE_X] and others.
     * @return - matrix value for index
     */
    private fun _getMatrixValue(
        matrix: Matrix,
        @IntRange(from = 0, to = 9) valueIndex: Int
    ): Float {
        val matrixValues = FloatArray(9)
        matrix.getValues(matrixValues)
        return matrixValues[valueIndex]
    }

    /**
     * This method scales image up for given value related to given coords (x, y).
     */
    fun zoomInImage(scale: Float, centerX: Float, centerY: Float) {
        if (scale <= _maxScale) {
            _postScale(scale / _currentScale, centerX, centerY)
        }
    }

    /**
     * If image doesn't fill the crop bounds it must be translated and scaled properly to fill those.
     *
     *
     * Therefore this method calculates delta X, Y and scale values and passes them to the
     *
     *
     * Scale value must be calculated only if image won't fill the crop bounds after it's translated to the
     * crop bounds rectangle center. Using temporary variables this method checks this case.
     */
    @Suppress("MaxLineLength")
    private fun _setImageToWrapCropBounds(animate: Boolean) {
        val drawable = drawable
        val outerImageRect = trapToRect(_currentImageCorners)
        if (drawable != null && !outerImageRect.contains(frameRect)) {
            val currentX = _imageCenterX
            val currentY = _imageCenterY
            val currentScale = _currentScale
            var deltaX = frameRect.centerX() - currentX
            var deltaY = frameRect.centerY() - currentY
            var deltaScale = 0f
            _viewMatrix.reset()
            _viewMatrix.setTranslate(deltaX, deltaY)
            val tempCurrentImageCorners =
                _currentImageCorners.copyOf(_currentImageCorners.size)
            _viewMatrix.mapPoints(tempCurrentImageCorners)
            val tempOuterImageRect = trapToRect(tempCurrentImageCorners)
            var willImageWrapCropBoundsAfterTranslate = tempOuterImageRect.contains(frameRect)

            // при полном максимальном уменьшении появляется что-то типо погрешности, размер подстраивается по меньшей стороне,
            // но в алгоритме из-за разницы в неск пикселей фотка центрируется постоянно, даже при ручном переносе
            if (!willImageWrapCropBoundsAfterTranslate && (((frameRect.width() - tempOuterImageRect.width() >= 0) && (frameRect.width() - tempOuterImageRect.width() < 10) && (frameRect.height() < tempOuterImageRect.height())
                    || (frameRect.height() - tempOuterImageRect.height() > 0) && (frameRect.height() - tempOuterImageRect.height() < 10) && (frameRect.width() < tempOuterImageRect.width())))
            ) {
                willImageWrapCropBoundsAfterTranslate = true
            }
            if (willImageWrapCropBoundsAfterTranslate) {
                val imageIndents = calculateIndents()
                deltaX = -(imageIndents[0] + imageIndents[2])
                deltaY = -(imageIndents[1] + imageIndents[3])
            } else {
                deltaScale = max(
                    (frameRect.width()) / (tempOuterImageRect.width()),
                    (frameRect.height()) / (tempOuterImageRect.height())
                )
                deltaScale = deltaScale * currentScale - currentScale
            }
            if (animate) {
                post(
                    WrapCropBoundsRunnable(
                        this,
                        _animationCropDuration.toLong(),
                        currentX,
                        currentY,
                        deltaX,
                        deltaY,
                        currentScale,
                        deltaScale,
                        willImageWrapCropBoundsAfterTranslate,
                        _onAnimationEndListener
                    ).also {
                        _wrapCropBoundsRunnable = it
                    })
            } else {
                _postTranslate(deltaX, deltaY)
                if (!willImageWrapCropBoundsAfterTranslate) {
                    zoomInImage(currentScale + deltaScale, frameRect.centerX(), frameRect.centerY())
                }
            }
        }
    }

    private fun calculateIndents(): FloatArray {
        val imageRect = trapToRect(_currentImageCorners)
        val deltaLeft = imageRect.left - frameRect.left
        val deltaTop = imageRect.top - frameRect.top
        val deltaRight = imageRect.right - frameRect.right
        val deltaBottom = imageRect.bottom - frameRect.bottom
        val indents = FloatArray(4)
        indents[0] = if ((deltaLeft > 0)) deltaLeft else 0f
        indents[1] = if ((deltaTop > 0)) deltaTop else 0f
        indents[2] = if ((deltaRight < 0)) deltaRight else 0f
        indents[3] = if ((deltaBottom < 0)) deltaBottom else 0f
        return indents
    }

    /**
     * This method cancels all current Runnable objects that represent animations.
     */
    private fun cancelAllAnimations() {
        removeCallbacks(_wrapCropBoundsRunnable)
    }

    private fun checkActionEnd() {
        _setImageToWrapCropBounds(true)
    }

    /**
     * This Runnable is used to animate an image so it fills the crop bounds entirely.
     * Given values are interpolated during the animation time.
     */
    class WrapCropBoundsRunnable internal constructor(
        cropImageView: CropImageView,
        durationMs: Long,
        oldX: Float,
        oldY: Float,
        centerDiffX: Float,
        centerDiffY: Float,
        oldScale: Float,
        deltaScale: Float,
        willBeImageInBoundsAfterTranslate: Boolean,
        onAnimationEndListener: OnAnimationListener?
    ) :
        Runnable {
        interface OnAnimationListener {
            fun onAnimationEnd()
        }

        private val _cropImageView: WeakReference<CropImageView> = WeakReference(cropImageView)
        private val _durationMs: Long = durationMs
        private val _startTime: Long = System.currentTimeMillis()
        private val _oldX: Float = oldX
        private val _oldY: Float = oldY
        private val _centerDiffX: Float = centerDiffX
        private val _centerDiffY: Float = centerDiffY
        private val _oldScale: Float = oldScale
        private val _deltaScale: Float = deltaScale
        private val _willBeImageInBoundsAfterTranslate: Boolean = willBeImageInBoundsAfterTranslate
        private val _onAnimationEndListener: OnAnimationListener? = onAnimationEndListener
        override fun run() {
            val cropImageView = _cropImageView.get() ?: return
            val now = System.currentTimeMillis()
            val currentMs = min(_durationMs, now - _startTime).toFloat()
            val newX = easeOut(currentMs, _centerDiffX, _durationMs.toFloat())
            val newY = easeOut(currentMs, _centerDiffY, _durationMs.toFloat())
            val newScale = easeInOut(currentMs, _deltaScale, _durationMs.toFloat())
            if (currentMs < _durationMs) {
                cropImageView._postTranslate(
                    newX - (cropImageView._imageCenterX - _oldX),
                    newY - (cropImageView._imageCenterY - _oldY)
                )
                if (!_willBeImageInBoundsAfterTranslate) {
                    cropImageView.zoomInImage(
                        _oldScale + newScale,
                        cropImageView.frameRect.centerX(),
                        cropImageView.frameRect.centerY()
                    )
                }
                if (!trapToRect(cropImageView._currentImageCorners).contains(cropImageView.frameRect)) {
                    cropImageView.post(this)
                } else {
                    _onAnimationEndListener?.onAnimationEnd()
                }
            } else {
                _onAnimationEndListener?.onAnimationEnd()
            }
        }

        private fun easeOut(time: Float, end: Float, duration: Float): Float {
            val newTime = time / duration - 1.0f
            return end * (newTime * newTime * newTime + 1.0f)
        }

        private fun easeInOut(time: Float, end: Float, duration: Float): Float {
            var newTime = time
            return if (duration / 2.0f.let { newTime /= it; newTime } < 1.0f) {
                end / 2.0f * newTime * newTime * newTime
            } else {
                end / 2.0f * (2.0f.let { newTime -= it; newTime } * newTime * newTime + 2.0f)
            }
        }
    }

    /**
     * Реализация класса для перехвата событий жеста скролла
     */
    private inner class GestureListener : SimpleOnGestureListener() {
        override fun onScroll(
            e1: MotionEvent?,
            e2: MotionEvent,
            distanceX: Float,
            distanceY: Float
        ): Boolean {
            // Получаем расстояние скролла по осям
            val roundedDistanceX = round(distanceX)
            val roundedDistanceY = round(distanceY)
            // Смещаем изображение
            _postTranslate(-roundedDistanceX, -roundedDistanceY)
            return super.onScroll(e1, e2, distanceX, distanceY)
        }
    }

    /**
     * Реализация класса для перехвата событий жеста приближения
     */
    private inner class ScaleListener : SimpleOnScaleGestureListener() {
        override fun onScale(detector: ScaleGestureDetector): Boolean {
            val roundedScaleFactor = round(detector.scaleFactor)
            _postScale(roundedScaleFactor, detector.focusX, detector.focusY)
            return true
        }
    }

    /**
     * Метод для округления float
     *
     * @param value исходное значение
     * @return значение с округлением до 2-х знаков после запятой
     */
    private fun round(value: Float): Float {
        return (value * 100.00f).roundToInt() / 100.00f
    }
}
