package ru.dobro.image_picker

import common.library.android.permissions.CheckPermissionManager
import common.library.android.string.RString
import common.library.core.state.task.TaskState
import kotlinx.collections.immutable.PersistentSet
import kotlinx.collections.immutable.persistentSetOf
import ru.dobro.core.error.ErrorMessages
import java.io.File
import javax.annotation.CheckReturnValue
import ru.dobro.domain.File as DomainFile

sealed class ImagePickerState {
    /**
     * Состояние простоя в ожидании параметров запуска.
     */
    data object Idle : ImagePickerState() {
        @CheckReturnValue
        fun select(
            checkPermissionManager: CheckPermissionManager,
            mode: ImagePickerMode
        ): Selection = when (mode) {
            ImagePickerMode.Photo -> Selection.FromCamera(
                mode = mode,
                cameraState = GalleryState.Idle.checkPermissionsAndOpenIfGranted(
                    checkPermissionManager = checkPermissionManager
                )
            )

            ImagePickerMode.Gallery -> Selection.FromGallery(
                mode = mode,
                galleryState = GalleryState.Idle.checkPermissionsAndOpenIfGranted(
                    checkPermissionManager = checkPermissionManager
                )
            )
        }
    }

    /**
     * Состояние, когда пользователь выбирает медиа из какого-то источнкика.
     */
    sealed class Selection : ImagePickerState() {
        abstract val mode: ImagePickerMode

        @CheckReturnValue
        protected fun success(file: File): ImagePickerState =
            Edit(
                fileForEdit = file,

                mode = mode
            )

        @CheckReturnValue
        protected fun fail(error: RString?): ImagePickerState = Completed.Fail(
            error = error,
            tempFiles = persistentSetOf()
        )

        @CheckReturnValue
        fun cancel(): ImagePickerState = Completed.Success.Cancelled(persistentSetOf())

        /**
         * Состояние, когда пользователь выбирает медиа из галереи.
         */
        data class FromGallery(
            override val mode: ImagePickerMode,
            val galleryState: GalleryState
        ) : Selection() {
            @CheckReturnValue
            fun updateGalleryState(
                action: GalleryState.() -> GalleryState
            ): ImagePickerState =
                when (val state = galleryState.action()) {
                    is GalleryState.Completed.Success -> success(state.file)
                    is GalleryState.Completed.Fail -> fail(state.error)
                    else -> copy(galleryState = state)
                }
        }

        /**
         * Состояние, когда пользователь выбирает медиа с помощью режима камеры.
         */
        data class FromCamera(
            override val mode: ImagePickerMode,
            val cameraState: GalleryState
        ) : Selection() {
            @CheckReturnValue
            fun updateCameraState(action: GalleryState.() -> GalleryState): ImagePickerState =
                when (val state: GalleryState = cameraState.action()) {
                    is GalleryState.Completed.Success -> success(state.file)
                    is GalleryState.Completed.Fail -> fail(state.error)
                    else -> copy(cameraState = state)
                }
        }
    }

    /**
     * Состояние, когда пользователь редактирует медиа.
     */
    data class Edit(
        val fileForEdit: File,
        val mode: ImagePickerMode
    ) : ImagePickerState() {
        @CheckReturnValue
        fun success(editedFile: File): ImagePickerState =
            UploadProcessing(
                state = TaskState.Idle
                    .awaiting<DomainFile, File>()
                    .execute(editedFile),

                mode = mode,

                tempFiles = persistentSetOf(fileForEdit, editedFile)
            )

        @CheckReturnValue
        fun fail(): Completed.Fail = Completed.Fail(
            error = null,
            tempFiles = persistentSetOf(fileForEdit)
        )
    }

    /**
     * Состояние, когда выбранное и, возможно, отредактированное медиа в процессе загрузки на сервер.
     */
    data class UploadProcessing(
        val state: UploadImageState,

        val mode: ImagePickerMode,

        private val tempFiles: PersistentSet<File>
    ) : ImagePickerState() {
        @CheckReturnValue
        fun updateTask(action: UploadImageState.() -> UploadImageState): ImagePickerState {
            return when (val state = state.action()) {
                is TaskState.Idle.Completed.Success ->
                    Completed.Success.Uploaded(
                        file = state.result,
                        tempFiles = tempFiles
                            .add(state.params)
                    )

                is TaskState.Idle.Completed.Fail -> Completed.Fail(
                    error = ErrorMessages.ofException(state.error),

                    tempFiles = tempFiles
                        .add(state.params)
                )

                else -> copy(state = state)
            }
        }
    }

    sealed class Completed : ImagePickerState() {
        /**
         * Список файлов, которые были созданы в процессе подготовки медиа и больше не нужны.
         */
        abstract val tempFiles: PersistentSet<File>

        fun deleteTempFiles() {
            for (file in tempFiles) {
                file.delete()
            }
        }

        sealed class Success : Completed() {
            class Uploaded(
                val file: DomainFile,
                override val tempFiles: PersistentSet<File>
            ) : Success()

            data class Cancelled(
                override val tempFiles: PersistentSet<File>
            ) : Success()
        }

        data class Fail(
            val error: RString?,

            override val tempFiles: PersistentSet<File>
        ) : Completed() {
            @CheckReturnValue
            fun reset(): Idle = Idle
        }
    }
}
