package ru.dobro.image_picker

import android.Manifest
import android.net.Uri
import android.os.Build
import common.library.android.permissions.CheckPermissionManager
import common.library.android.permissions.Permission
import common.library.android.permissions.PermissionsState
import common.library.android.string.RString
import common.library.android.string.rString
import kotlinx.collections.immutable.persistentSetOf
import ru.dobro.R
import java.io.File
import java.io.IOException
import javax.annotation.CheckReturnValue

sealed class GalleryState {
    data object Idle : GalleryState() {
        @CheckReturnValue
        fun checkPermissionsAndOpenIfGranted(checkPermissionManager: CheckPermissionManager): GalleryState {
            val permissionsState: PermissionsState =
                PermissionsState.checkSelf(
                    checkPermissionManager,
                    persistentSetOf(
                        galleryPermission
                    )
                )

            return when (permissionsState) {
                is PermissionsState.NotAllGranted -> PermissionsCheck(
                    permissionsState = permissionsState
                )

                is PermissionsState.AllGranted -> Selection
            }
        }

        val galleryPermission: Permission =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                Manifest.permission.READ_MEDIA_IMAGES
            } else {
                Manifest.permission.READ_EXTERNAL_STORAGE
            }
    }

    /**
     * Состояние проверки разрешений доступа к галерее.
     */
    data class PermissionsCheck(
        val permissionsState: PermissionsState.NotAllGranted
    ) : GalleryState() {
        @CheckReturnValue
        fun updatePermissionsState(
            action: PermissionsState.NotAllGranted.() -> PermissionsState
        ): GalleryState =
            when (val state = permissionsState.action()) {
                is PermissionsState.NotAllGranted -> copy(permissionsState = state)
                is PermissionsState.AllGranted -> Selection
            }
    }

    /**
     * Состояние, в котором можено безопасно открыть галерею, т.к. необходимые разрешения выданы.
     */
    object Ready : GalleryState() {
        @CheckReturnValue
        fun open(): Selection = Selection
    }

    /**
     * Состояние выбора изображения, в этом состоянии галерея открыта и ожадиется результат выбора.
     */
    object Selection : GalleryState() {
        @CheckReturnValue
        fun select(image: Uri): Internalization = Internalization(image)

        /**
         * Пользователь отказался выбирать файл с помощью галереи.
         */
        @CheckReturnValue
        fun cancel(): Completed =
            Completed.Fail(RString.res(R.string.application___error__file_not_selected, null))
    }

    /**
     * Состояние копирования выбранного файла во внутреннее хранилище для дальнейшей обработки.
     */
    data class Internalization(
        val selectedImage: Uri
    ) : GalleryState() {
        @CheckReturnValue
        fun success(file: File): Completed.Success = Completed.Success(file)

        @CheckReturnValue
        fun fail(error: Throwable): Completed.Fail = Completed.Fail(
            when (error) {
                is IOException -> R.string.image_picker___error__io
                else -> R.string.application___error__unsupported_file_format
            }.rString
        )
    }

    sealed class Completed : GalleryState() {
        data class Success(
            val file: File
        ) : Completed()

        data class Fail(
            val error: RString
        ) : Completed()
    }
}
