package ru.dobro.address_selector

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import ru.dobro.address_selector.AddressSelectorMode.Address
import ru.dobro.address_selector.AddressSelectorMode.Country
import ru.dobro.address_selector.AddressSelectorMode.Settlement
import ru.dobro.address_selector.Country.All
import ru.dobro.address_selector.Country.Special
import ru.dobro.domain.Location

/**
 * [requestCode] - код для отлавливания в фрагменте
 * [mode] - вариация экрана
 * [country] - ограничение результатов по стране
 **/
@Parcelize
sealed class AddressSelectorRequest(
    open val requestCode: String,
) : Parcelable {
    data class Country(
        override val requestCode: String,
    ) : AddressSelectorRequest(requestCode)

    data class Settlement(
        override val requestCode: String,
        val country: ru.dobro.address_selector.Country = ru.dobro.address_selector.Country.All,
    ) : AddressSelectorRequest(requestCode)

    data class Address(
        override val requestCode: String,
        val country: ru.dobro.address_selector.Country = ru.dobro.address_selector.Country.All,
    ) : AddressSelectorRequest(requestCode)

    data class Street(
        override val requestCode: String,
        val country: ru.dobro.address_selector.Country = ru.dobro.address_selector.Country.All,
        val settlement: Location?
    ) : AddressSelectorRequest(requestCode)

    data class House(
        override val requestCode: String,
        val settlement: Location?, // settlement кидаем потому что может не быть улицы
        val street: Location?
    ) : AddressSelectorRequest(requestCode)
}

/**
 * [All] - выводить адреса для всех стран
 * [Special] - выводить адреса только для страны [Special.special]
 **/
@Parcelize
sealed class Country(val value: String) : Parcelable {
    data class Special(val special: String) : ru.dobro.address_selector.Country(special)
    object All : ru.dobro.address_selector.Country("*")
}

/**
 * [Country] - выбор страны
 * [Settlement] - выбор города
 * [Address] - выбор адреса
 **/
enum class AddressSelectorMode {
    Country,
    Settlement,
    Address
}

/**
 * [CountryName] - перечисление используемых напрямую стран
 **/
enum class CountryName(val title: String) {
    Russia("Россия")
}
