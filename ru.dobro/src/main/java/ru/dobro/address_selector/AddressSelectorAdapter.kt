package ru.dobro.address_selector

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import common.library.android.widget.recycler_view.getRxAsyncDiffer
import common.library.core.EqualityComparators
import common.library.core.rx.invoke
import io.reactivex.Flowable
import io.reactivex.processors.PublishProcessor
import org.reactivestreams.Subscriber
import ru.dobro.api.global.AddressResponse
import ru.dobro.databinding.AddressSelectorItemBinding

class AddressSelectorAdapter : ListAdapter<AddressResponse, AddressSelectorAdapter.ViewHolder>(
    getRxAsyncDiffer(
        EqualityComparators.natural(),
        EqualityComparators.content()
    )
) {
    private val _onItemClick: PublishProcessor<AddressResponse> = PublishProcessor.create()

    val onItemClick: Flowable<AddressResponse> get() = _onItemClick.hide()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            AddressSelectorItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            ), _onItemClick
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class ViewHolder(
        private val binding: AddressSelectorItemBinding,
        private val _onClick: Subscriber<AddressResponse>
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: AddressResponse) {
            itemView.apply {
                binding.addressSelectorItemTitle.text = item.name
                setOnClickListener { _onClick(item) }
            }
        }
    }
}
