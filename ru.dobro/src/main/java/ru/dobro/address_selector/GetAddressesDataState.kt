package ru.dobro.address_selector

import common.library.core.location.GeoPoint
import common.library.core.state.load.LoadDataBySelectorState
import kotlinx.collections.immutable.PersistentList
import ru.dobro.api.global.AddressResponse
import ru.dobro.api.overview.OverviewApi

data class UserGeoCoordinates(
    val coordinates: GeoPoint.Coordinates,
) {
    override fun equals(other: Any?): <PERSON>olean {
        return super.equals(other)
    }

    override fun hashCode(): Int {
        return super.hashCode()
    }
}

@Suppress("MaxLineLength")
typealias GetAddressesState = LoadDataBySelectorState<PersistentList<AddressResponse>, Pair<String, PersistentList<OverviewApi.AddressSuggestionBody.Location>>>

@Suppress("MaxLineLength")
typealias GetAddressStateLoaded = LoadDataBySelectorState.Idle.Loaded<PersistentList<AddressResponse>, Pair<String, PersistentList<OverviewApi.AddressSuggestionBody.Location>>>

@Suppress("MaxLineLength")
typealias GetAddressStateFailed = LoadDataBySelectorState.Idle.Failed<PersistentList<AddressResponse>, Pair<String, PersistentList<OverviewApi.AddressSuggestionBody.Location>>>

@Suppress("MaxLineLength")
typealias GetAddressLocationState = LoadDataBySelectorState<PersistentList<AddressResponse>, UserGeoCoordinates>

@Suppress("MaxLineLength")
typealias GetAddressLocationStateLoaded = LoadDataBySelectorState.Idle.Loaded<PersistentList<AddressResponse>, UserGeoCoordinates>

@Suppress("MaxLineLength")
typealias GetAddressLocationStateFailed = LoadDataBySelectorState.Idle.Failed<PersistentList<AddressResponse>, UserGeoCoordinates>
