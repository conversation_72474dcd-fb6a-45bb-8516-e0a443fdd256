package ru.dobro.address_selector

import android.Manifest
import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AlertDialog
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.clearFragmentResult
import androidx.fragment.app.setFragmentResult
import arrow.core.Some
import arrow.core.toOption
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import common.library.android.dialog.cancelButton
import common.library.android.dialog.dialog
import common.library.android.dialog.okButton
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.intoBundle
import common.library.android.message.MessageDisplay
import common.library.android.onFragmentResult
import common.library.android.permissions.CheckPermissionFragmentManager
import common.library.android.permissions.PermissionsState
import common.library.android.permissions.checkPermissionManager
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleErrorMessages
import common.library.android.rx.throttleUserInput
import common.library.android.soft_input.Ime
import common.library.android.string.RString
import common.library.android.string.r
import common.library.android.string.rString
import common.library.android.string.rText
import common.library.android.widget.onClick
import common.library.android.widget.onTextChanged
import common.library.core.lazyGet
import common.library.core.location.GeoPoint
import common.library.core.location.UserLocationProvider
import common.library.core.logging.logWarning
import common.library.core.orTrue
import common.library.core.rx.RxSchedulers
import common.library.core.rx.ofSubtype
import common.library.core.state.load.LoadDataBySelectorState
import common.library.core.state.load.handleLoadingBySelector
import common.library.core.state.load.loadSingleBySelector
import common.library.core.variable.Agent
import common.library.core.variable.agent
import common.library.core.variable.sendWhen
import io.reactivex.Observable
import io.reactivex.Single
import io.reactivex.disposables.Disposable
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.R
import ru.dobro.api.DobroApi
import ru.dobro.api.global.AddressResponse
import ru.dobro.api.overview.OverviewApi
import ru.dobro.api.overview.getAddressSuggestionLocationDataAsync
import ru.dobro.api.overview.getAddressSuggestionQueryDataAsync
import ru.dobro.api.overview.getAllCountriesAsync
import ru.dobro.api.overview.getHouseSuggestionQueryDataAsync
import ru.dobro.api.overview.getSettlementSuggestionQueryDataAsync
import ru.dobro.api.overview.getStreetSuggestionQueryDataAsync
import ru.dobro.core.BaseBottomSheetDialogFragment
import ru.dobro.core.RequestCodes
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.error.ErrorHandler
import ru.dobro.core.error.ErrorMessages
import ru.dobro.core.settings.UserSettings
import ru.dobro.databinding.AddressSelectorBinding
import ru.dobro.databinding.LoginOnboardingSettlementDialogBinding
import ru.dobro.domain.Location
import ru.dobro.login.onboarding.LocationPermissionState
import ru.dobro.login.onboarding.LocationPermissionState.Idle.checkPermissionsAndOpenIfGranted
import ru.dobro.login.onboarding.LocationPermissionState.Process.error
import ru.dobro.login.onboarding.LocationPermissionState.Process.get
import ru.dobro.login.onboarding.LocationPermissionState.Ready.start
import java.util.Locale
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeoutException
import javax.annotation.CheckReturnValue

class AddressSelectorFragment(override val screenName: String = AnalyticsConstants.Screen.Common.settlementSelector) :
    BaseBottomSheetDialogFragment({
        bind<Agent<AddressSelectorState>>() with singleton { agent(AddressSelectorState.Idle) }
        bind<Agent<GetAddressesState>>() with singleton { agent(LoadDataBySelectorState.Idle.empty()) }
    }) {

    private lateinit var binding: AddressSelectorBinding
    private val _state: Agent<AddressSelectorState> by instance()
    private val _addressesState: Agent<GetAddressesState> by instance()
    private val _bundler: Bundler by instance()
    private val _api: DobroApi by instance()
    private val _userLocationProvider: UserLocationProvider by instance()
    private val _userSettings: UserSettings by instance()
    private val _messageDisplay: MessageDisplay by instance()
    private val _errorHandler: ErrorHandler by instance()
    private val _adapter: AddressSelectorAdapter by lazyGet { AddressSelectorAdapter() }
    private var countries: List<AddressResponse>? = null
    private var request: AddressSelectorRequest? = null
    private val _locationRequestCode: Int = RequestCodes.permission()
    private val _locationPermissionFromSettingsRequestCode: Int = RequestCodes.result()
    private val observingCode = "request"
    private val shouldAskLocationPermissionCode =
        "${this.javaClass}_shouldAskLocationPermissionCode"
    private var wasSearchQueryTriggered = false

    companion object {

        @CheckReturnValue
        fun createCountry(
            requestKey: String,
        ): AddressSelectorFragment = AddressSelectorFragment().apply {
            arguments = bundleOf(
                observingCode to AddressSelectorRequest.Country(
                    requestCode = requestKey
                )
            )
        }

        @CheckReturnValue
        fun createSettlement(
            requestKey: String,
            country: Country = Country.All,
            shouldAskLocationPermission: Boolean = true
        ): AddressSelectorFragment = AddressSelectorFragment().apply {
            arguments = bundleOf(
                observingCode to AddressSelectorRequest.Settlement(
                    requestCode = requestKey,
                    country = country
                ),
                shouldAskLocationPermissionCode to shouldAskLocationPermission
            )
        }

        @CheckReturnValue
        fun createAddress(
            requestKey: String,
            country: Country = Country.All
        ): AddressSelectorFragment = AddressSelectorFragment().apply {
            arguments = bundleOf(
                observingCode to AddressSelectorRequest.Address(
                    requestCode = requestKey,
                    country = country
                )
            )
        }

        @CheckReturnValue
        fun createStreet(
            requestKey: String,
            country: Country = Country.All,
            settlement: Location?
        ): AddressSelectorFragment = AddressSelectorFragment().apply {
            arguments = bundleOf(
                observingCode to AddressSelectorRequest.Street(
                    requestCode = requestKey,
                    country = country,
                    settlement = settlement
                )
            )
        }

        @CheckReturnValue
        fun createHouse(
            requestKey: String,
            settlement: Location?,
            street: Location?
        ): AddressSelectorFragment = AddressSelectorFragment().apply {
            arguments = bundleOf(
                observingCode to AddressSelectorRequest.House(
                    requestCode = requestKey,
                    street = street,
                    settlement = if (street?.fiasId == null) settlement else null
                )
            )
        }

        @CheckReturnValue
        fun onResult(
            receiver: Fragment,
            requestKey: String,
            bundler: Bundler
        ): Observable<Location> =
            receiver.onFragmentResult(requestKey, bundler)
    }

    init {
        setStyle(STYLE_NORMAL, R.style.Application_BottomSheet)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        dialog.setOnShowListener {
            (it as BottomSheetDialog).findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
                ?.let { sheetView ->
                    sheetView.minimumHeight = resources.displayMetrics.heightPixels
                    sheetView.layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
                    it.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                }
        }
        dialog.behavior.skipCollapsed = true
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = AddressSelectorBinding.inflate(inflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.addressSelectorList.adapter = _adapter

        request = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getParcelable(observingCode, AddressSelectorRequest::class.java)
        } else {
            arguments?.getParcelable(observingCode)
        }
        if (request === null) {
            logger.warning { "Illegal params." }

            dismiss()
            return
        }
        if (request !is AddressSelectorRequest.Settlement) {
            binding.addressSelectorSearch.requestFocus()
            Ime.show().withDelay(300).to(binding.addressSelectorSearch)
        }
        onAddressChanged("")
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        clearFragmentResult(request!!.requestCode)

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<AddressSelectorState.Idle>()
            .subscribeBy {
                _state.sendWhen<AddressSelectorState, AddressSelectorState.Idle> {
                    requestChoice()
                }
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .delay(200, TimeUnit.MILLISECONDS)
            .filter { request is AddressSelectorRequest.Settlement }
            .ofSubtype<AddressSelectorState.Idle>()
            .subscribeBy {
                _state.sendWhen<AddressSelectorState, AddressSelectorState.Choice> {
                    updateLocationPermissionsState {
                        when (this) {
                            is LocationPermissionState.Idle -> {
                                checkPermissionsAndOpenIfGranted(checkPermissionManager)
                            }

                            is LocationPermissionState.PermissionsCheck -> {
                                updatePermissionsState { resetExplained() }
                            }

                            is LocationPermissionState.Ready -> {
                                start()
                            }

                            else -> {
                                this
                            }
                        }
                    }
                }
            }
            .scoped()

        when (request!!) {
            is AddressSelectorRequest.Country -> {
                binding.addressSelectorTitle.text =
                    getString(R.string.address_selector___country__input__hint)
                binding.addressSelectorSearchContainer.hint =
                    getString(R.string.address_selector___country__input__hint)
            }

            is AddressSelectorRequest.Settlement -> {
                binding.addressSelectorTitle.text =
                    getString(R.string.address_selector___settlement__title)
                binding.addressSelectorSearchContainer.hint =
                    getString(R.string.address_selector___settlement__input__hint)
            }

            is AddressSelectorRequest.Address -> {
                binding.addressSelectorTitle.text =
                    getString(R.string.address_selector___actual_address__title)
                binding.addressSelectorSearchContainer.hint =
                    getString(R.string.address_selector___actual_address__input__hint)
            }

            is AddressSelectorRequest.Street -> {
                binding.addressSelectorTitle.text = getString(R.string.address_selector___street)
                binding.addressSelectorSearchContainer.hint =
                    getString(R.string.address_selector___street_hint)
            }

            is AddressSelectorRequest.House -> {
                binding.addressSelectorTitle.text = getString(R.string.address_selector___house)
                binding.addressSelectorSearchContainer.hint =
                    getString(R.string.address_selector___house_hint)
            }
        }

        binding.addressSelectorSearch
            .onTextChanged
            .observeOn(RxSchedulers.computation())
            .debounce(300, TimeUnit.MILLISECONDS)
            .distinctUntilChanged()
            .observeOn(RxSchedulers.main())
            .subscribeBy { input ->
                onAddressChanged(input.text.toString())
                wasSearchQueryTriggered = true
            }.scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<AddressSelectorState.Choice>()
            .distinctUntilChanged()
            .observeOn(RxSchedulers.main())
            .subscribeBy { state ->
                if (!binding.addressSelectorSearch.isFocused) {
                    binding.addressSelectorSearch.setText(state.searchQuery)
                }
                if (request!! is AddressSelectorRequest.Country) {
                    if (countries == null && state.items.isNotEmpty()) {
                        countries = state.items
                    }
                }
                if (request!! is AddressSelectorRequest.Settlement &&
                    binding.addressSelectorSearch.text.isNullOrBlank() &&
                    (request as? AddressSelectorRequest.Settlement)?.country?.value == CountryName.Russia.title ||
                    (request as? AddressSelectorRequest.Settlement)?.country == Country.All
                ) {
                    val list = Location.restorePopularCities().map {
                        AddressResponse(
                            name = it.title.removePrefix("Россия, "),
                            fullName = it.title,
                            country = it.country,
                            countryISO = it.countryISO,
                            municipality = it.municipality,
                            settlementCode = it.settlementCode,
                            municipalityCode = it.municipalityCode,
                            cityAlternative = it.cityAlternative,
                            flat = it.flat,
                            house = it.house,
                            street = it.street,
                            region = it.region,
                            coordinates = it.coordinates,
                            settlement = it.settlement,
                            fiasId = it.fiasId,
                            cityFiasId = it.cityFiasId,
                            streetFiasId = it.streetFiasId,
                            regionKladrId = it.region
                        )
                    }
                    _adapter.submitList(list)
                } else if (request!! is AddressSelectorRequest.Settlement) {
                    _adapter.submitList(state.items.map {
                        it.copy(name = it.name)
                    })
                } else {
                    _adapter.submitList(state.items)
                }
            }
            .scoped()

        binding.addressSelectorSearchContainer.setEndIconOnClickListener {
            binding.addressSelectorSearch.setText("")

            _addressesState.send {
                reset()
            }
        }

        _addressesState
            .loadSingleBySelector {
                when (val request = request!!) {
                    is AddressSelectorRequest.Country -> if (countries == null) {
                        _api.overview()
                            .getAllCountriesAsync()
                            .doOnError { _messageDisplay.showMessage("Не удалось загрузить данные".r) }
                            .logWarning(logger) { "Ошибка поиска страны." }
                    } else {
                        Single.just(countries?.toPersistentList() ?: persistentListOf())
                    }

                    is AddressSelectorRequest.Settlement -> _api.overview()
                        .getSettlementSuggestionQueryDataAsync(it.first, it.second)
                        .doOnError { _messageDisplay.showMessage("Не удалось загрузить данные".r) }
                        .logWarning(logger) { "Ошибка поиска города." }

                    is AddressSelectorRequest.Address -> _api.overview()
                        .getAddressSuggestionQueryDataAsync(it.first, it.second)
                        .doOnError { _messageDisplay.showMessage("Не удалось загрузить данные".r) }
                        .logWarning(logger) { "Ошибка поиска адреса." }

                    is AddressSelectorRequest.Street -> _api.overview()
                        .getStreetSuggestionQueryDataAsync(
                            it.first,
                            request.settlement?.settlementCode
                        )
                        .doOnError { _messageDisplay.showMessage("Не удалось загрузить данные".r) }
                        .logWarning(logger) { "Ошибка поиска улицы." }

                    is AddressSelectorRequest.House -> _api.overview()
                        .getHouseSuggestionQueryDataAsync(
                            it.first,
                            request.street?.settlementCode,
                            request.street?.street
                        )
                        .doOnError { _messageDisplay.showMessage("Не удалось загрузить данные".r) }
                        .logWarning(logger) { "Ошибка поиска дома." }
                }
            }
            .scoped()

        _addressesState
            .observeOn(RxSchedulers.computation())
            .ofSubtype<GetAddressStateLoaded>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _state.sendWhen<AddressSelectorState, AddressSelectorState.Choice> {
                    setItems(countries?.let { _ ->
                        it.data.filter { address ->
                            address.name.lowercase(Locale.getDefault())
                                .contains(it.selector.first.lowercase(Locale.getDefault()))
                        }.toPersistentList()
                    } ?: it.data, it.selector.first)
                }
                _addressesState.send {
                    reset()
                }
            }
            .scoped()

        _addressesState
            .observeOn(RxSchedulers.computation())
            .ofSubtype<GetAddressStateFailed>()
            .map { ErrorMessages.ofException(_errorHandler.ofException(it.error)).toOption() }
            .doOnNext { _addressesState.sendWhen<GetAddressesState, GetAddressStateFailed> { handle() } }
            .ofSubtype<Some<RString>>()
            .throttleErrorMessages()
            .observeOn(RxSchedulers.main())
            .subscribeBy { _messageDisplay.showMessage(it.t) }
            .scoped()

        binding.addressSelectorClose
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                dismiss()
            }
            .scoped()

        _adapter
            .onItemClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy { addressResponse: AddressResponse ->
                finishAddressInput(
                    Location.restore(
                        title = addressResponse.name,
                        rawTitle = addressResponse.name,
                        shortName = addressResponse.cityAlternative ?: addressResponse.name,
                        settlement = addressResponse.settlement ?: addressResponse.name,
                        settlementCode = addressResponse.settlementCode,
                        country = addressResponse.country,
                        countryISO = addressResponse.countryISO,
                        municipality = addressResponse.municipality,
                        municipalityCode = addressResponse.municipalityCode,
                        cityAlternative = addressResponse.cityAlternative,
                        region = addressResponse.regionKladrId ?: addressResponse.region,
                        flat = addressResponse.flat,
                        street = addressResponse.street,
                        house = addressResponse.house,
                        coordinates = addressResponse.coordinates,
                        fiasId = addressResponse.fiasId,
                        cityFiasId = addressResponse.cityFiasId,
                        streetFiasId = addressResponse.streetFiasId
                    )
                )
            }
            .scoped()

        _state
            .handleLoadingBySelector<
                AddressSelectorState,
                AddressSelectorState.GetSettlementProcessing,
                PersistentList<AddressResponse>,
                UserGeoCoordinates>(
                toTaskState = { it.state },
                task = {
                    _api
                        .overview()
                        .getAddressSuggestionLocationDataAsync(it.coordinates)
                        .doOnError { _messageDisplay.showMessage("Не удалось загрузить данные".r) }
                        .logWarning(logger) { "Ошибка поиска адреса." }
                },
                updateState = { updateTask(it) }
            )
            .scoped()

        _state
            .ofSubtype<AddressSelectorState.Choice>()
            .observeOn(RxSchedulers.computation())
            .map { it.locationPermissionState }
            .ofSubtype<LocationPermissionState.Process>()
            .filter {
                ActivityCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.ACCESS_FINE_LOCATION
                ) == PackageManager.PERMISSION_GRANTED
                    && ActivityCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.ACCESS_COARSE_LOCATION
                ) == PackageManager.PERMISSION_GRANTED
            }
            .observeOn(RxSchedulers.main())
            .flatMap { _userLocationProvider.requestCurrentLocation() }
            .observeOn(RxSchedulers.main())
            .subscribeBy { locationRequestResult ->
                fun onFailure(error: Throwable) {
                    when (error) {
                        is TimeoutException -> {
                            _messageDisplay.showMessage(R.string.login__onboarding___location__auto__timeout.rString)
                        }

                        else -> {
                            _messageDisplay.showMessage(R.string.login__onboarding___location__auto__provider_error.rString)
                        }
                    }

                    _state.sendWhen<AddressSelectorState, AddressSelectorState.Choice> {
                        updateLocationPermissionsState {
                            when (this) {
                                is LocationPermissionState.Process -> {
                                    error()
                                }

                                else -> this
                            }
                        }
                    }
                }
                locationRequestResult.fold(
                    onSuccess = { location ->
                        location.first?.let {
                            updateLocationPermissionState(it)
                        } ?: onFailure(TimeoutException())
                    },
                    onFailure = { error ->
                        onFailure(error)
                    }
                )
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<AddressSelectorState.Completed>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                when (it) {
                    is AddressSelectorState.Completed.Success -> {
                        _showSettlementCheckDialog(it.settlementLocation)

                        _state.sendWhen<AddressSelectorState, AddressSelectorState.Completed.Success> {
                            backToChoice()
                        }
                    }

                    is AddressSelectorState.Completed.Failed -> {
                        if (it.error !== null) {
                            _messageDisplay.showMessage(it.error)
                        }

                        _state.sendWhen<AddressSelectorState, AddressSelectorState.Completed.Failed> {
                            backToChoice()
                        }
                    }
                }
            }
            .scoped()

        val shouldAskLocationPermission =
            arguments?.getBoolean(shouldAskLocationPermissionCode).orTrue()
        if (shouldAskLocationPermission) {
            _handleLocationPermissions().scoped()
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            _locationRequestCode ->
                _state.sendWhen<AddressSelectorState, AddressSelectorState.Choice> {
                    updateLocationPermissionsState {
                        if (this is LocationPermissionState.PermissionsCheck) {
                            updatePermissionsState {
                                handleResponse(
                                    checkPermissionManager,
                                    permissions,
                                    grantResults
                                )
                            }
                        } else {
                            this
                        }
                    }
                }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            _locationPermissionFromSettingsRequestCode ->
                _state.sendWhen<AddressSelectorState, AddressSelectorState.Choice> {
                    updateLocationPermissionsState {
                        if (this is LocationPermissionState.PermissionsCheck) {
                            updatePermissionsState {
                                handleResponse(
                                    checkPermissionManager,
                                    LocationPermissionState.Idle.locationPermissions.toTypedArray(),
                                    LocationPermissionState.Idle.locationPermissions.map {
                                        ContextCompat.checkSelfPermission(requireActivity(), it)
                                    }.toIntArray()
                                )
                            }
                        } else {
                            this
                        }
                    }
                }
        }
    }

    private fun finishAddressInput(location: Location) {
        firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Common.settlementSelectorAddressClick)
        metricManager.sendSimpleEvent(AnalyticsConstants.Event.Common.settlementSelectorAddressClick)

        parentFragment?.fragmentManager?.let {
            setFragmentResult(
                request!!.requestCode,
                location.intoBundle(_bundler)
            )
        }

        dismiss()
    }

    private fun onAddressChanged(text: String) {
        _addressesState.send {
            val country = when (val request = request!!) {
                is AddressSelectorRequest.Country -> Country.All.value
                is AddressSelectorRequest.Address -> request.country.value
                is AddressSelectorRequest.House -> request.street?.country
                is AddressSelectorRequest.Settlement -> request.country.value
                is AddressSelectorRequest.Street -> request.country.value
            }
            reset().load(
                text to persistentListOf(
                    OverviewApi.AddressSuggestionBody.Location(
                        country ?: "*"
                    )
                )
            )
        }
    }

    @CheckReturnValue
    private fun _handleLocationPermissions(): Disposable {
        var permissionDialog: Dialog? = null

        fun _dismissPermissionDialog() {
            val dialog: Dialog? = permissionDialog
            if (dialog !== null) {
                dialog.dismiss()
            }
            permissionDialog = null
        }

        return _state
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                if (it is AddressSelectorState.Choice && it.locationPermissionState is LocationPermissionState.PermissionsCheck
                    && it.searchQuery.isEmpty() && wasSearchQueryTriggered.not()
                ) {
                    val locationPermissionState = it.locationPermissionState
                    val permissionsState: PermissionsState.NotAllGranted =
                        locationPermissionState.permissionsState

                    if (permissionsState.isAllDeniedExplained()) {
                        permissionsState.requestDeniedOnce(
                            checkPermissionManager,
                            _locationRequestCode
                        )

                        _dismissPermissionDialog()
                    } else {
                        if (permissionDialog == null) {
                            val dialog: AlertDialog = requireContext().dialog {
                                it
                                    .message(R.string.location__dialog_text.rText)
                                    .okButton {
                                        val rationale =
                                            LocationPermissionState.Idle.locationPermissions.map { permission ->
                                                CheckPermissionFragmentManager(this).shouldExplain(
                                                    permission
                                                )
                                            }.any { equals(false) }

                                        if (rationale) {
                                            _dismissPermissionDialog()

                                            permissionsState.requestDenied(
                                                checkPermissionManager,
                                                _locationRequestCode
                                            )
                                        } else {
                                            _dismissPermissionDialog()

                                            startActivityForResult(
                                                Intent(
                                                    Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                                                    Uri.fromParts(
                                                        "package",
                                                        requireActivity().packageName,
                                                        null
                                                    )
                                                ),
                                                _locationPermissionFromSettingsRequestCode
                                            )
                                        }
                                    }
                                    .cancelButton { _dismissPermissionDialog() }
                                    .cancellable()
                            }

                            dialog.setOnDismissListener {
                                locationPermissionState.updatePermissionsState { markAllExplained() }
                            }

                            dialog.show()

                            permissionDialog = dialog
                        }
                    }
                } else {
                    _dismissPermissionDialog()
                }
            }
    }

    private fun updateLocationPermissionState(location: android.location.Location) {
        try {
            _state.sendWhen<AddressSelectorState, AddressSelectorState.Choice> {
                updateLocationPermissionsState {
                    when (this) {
                        is LocationPermissionState.Process -> {
                            get(
                                GeoPoint.Coordinates(
                                    location.latitude,
                                    location.longitude
                                )
                            )
                        }

                        else -> this
                    }
                }
            }
        } catch (_: Exception) {
        }
    }

    @SuppressLint("InflateParams")
    private fun _showSettlementCheckDialog(location: Location) {
        val dialog = MaterialAlertDialogBuilder(requireContext()).create()
        val binding =
            LoginOnboardingSettlementDialogBinding.inflate(layoutInflater, this.binding.root, false)
        binding.apply {
            loginOnboardingSettlementDialogConfirm.setOnClickListener {
                _userSettings.setSettlementLocations(location)

                finishAddressInput(location)

                dialog.dismiss()
            }
            loginOnboardingSettlementDialogCancel.setOnClickListener {
                <EMAIL> {
                    it.requestFocus()
                    Ime.show().withDelay(300).to(it)
                }

                dialog.dismiss()
            }

            loginOnboardingSettlementDialogTitle.text =
                getString(R.string.login__onboarding___location__auto__title, location.settlement)
        }
        dialog.setView(binding.root)
        return dialog.show()
    }
}
