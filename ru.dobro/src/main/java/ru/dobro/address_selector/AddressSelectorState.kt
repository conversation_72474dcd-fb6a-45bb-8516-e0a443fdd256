package ru.dobro.address_selector

import common.library.android.string.RString
import common.library.android.string.rString
import common.library.core.location.GeoPoint
import common.library.core.state.load.LoadDataBySelectorState
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import ru.dobro.R
import ru.dobro.api.global.AddressResponse
import ru.dobro.core.error.ErrorMessages
import ru.dobro.domain.Location
import ru.dobro.login.onboarding.LocationPermissionState
import javax.annotation.CheckReturnValue

sealed class AddressSelectorState {
    data object Idle : AddressSelectorState() {
        @CheckReturnValue
        fun requestChoice() = Choice(
            items = persistentListOf(),
            searchQuery = "",
            locationPermissionState = LocationPermissionState.Idle
        )
    }

    data class Choice(
        val items: PersistentList<AddressResponse>,
        val searchQuery: String,
        val locationPermissionState: LocationPermissionState,
        val cachedSettlementLocation: Location? = null,
        val cachedUserCoordinates: GeoPoint.Coordinates? = null,
    ) : AddressSelectorState() {
        @CheckReturnValue
        fun setItems(items: PersistentList<AddressResponse>, query: String) =
            copy(
                items = items,
                searchQuery = query
            )

        @CheckReturnValue
        fun reset() =
            copy(
                items = persistentListOf(),
                searchQuery = ""
            )

        @CheckReturnValue
        fun updateLocationPermissionsState(
            action: LocationPermissionState.() -> LocationPermissionState
        ): AddressSelectorState {
            return when (val state = locationPermissionState.action()) {
                is LocationPermissionState.Success ->
                    if (cachedUserCoordinates == state.coordinates && cachedSettlementLocation != null) {
                        Completed.Success(
                            _input = this,
                            settlementLocation = cachedSettlementLocation,
                            userCoordinates = cachedUserCoordinates
                        )
                    } else {
                        GetSettlementProcessing(
                            state = LoadDataBySelectorState.Idle
                                .empty<PersistentList<AddressResponse>, UserGeoCoordinates>()
                                .load(UserGeoCoordinates(state.coordinates)),
                            _input = this
                        )
                    }

                else -> copy(locationPermissionState = state)
            }
        }
    }

    data class GetSettlementProcessing(
        val state: GetAddressLocationState,

        private val _input: Choice
    ) : AddressSelectorState() {
        @CheckReturnValue
        fun updateTask(action: GetAddressLocationState.() -> GetAddressLocationState): AddressSelectorState {
            return when (val state = state.action()) {
                is GetAddressLocationStateLoaded ->
                    if (state.data.isNotEmpty()) {
                        Completed.Success(
                            settlementLocation = state.data.first().let {
                                Location.restore(
                                    title = it.name,
                                    rawTitle = it.name,
                                    shortName = it.cityAlternative ?: it.name,
                                    settlement = it.settlement ?: it.name,
                                    settlementCode = it.settlementCode,
                                    country = it.country,
                                    countryISO = it.countryISO,
                                    municipality = it.municipality,
                                    municipalityCode = it.municipalityCode,
                                    cityAlternative = it.cityAlternative,
                                    region = it.regionKladrId ?: it.region,
                                    flat = it.flat,
                                    street = it.street,
                                    house = it.house,
                                    coordinates = it.coordinates,
                                    fiasId = it.fiasId,
                                    cityFiasId = it.cityFiasId,
                                    streetFiasId = it.streetFiasId
                                )
                            },
                            userCoordinates = state.selector.coordinates,
                            _input = _input
                        )
                    } else {
                        Completed.Failed(
                            _input = _input,

                            error = R.string.login__onboarding___location__auto__error.rString
                        )
                    }

                is GetAddressLocationStateFailed -> Completed.Failed(
                    _input = _input,

                    error = ErrorMessages.ofException(state.error)
                )

                else -> copy(state = state)
            }
        }
    }

    sealed class Completed : AddressSelectorState() {
        data class Success(
            private val _input: Choice,
            val settlementLocation: Location,
            val userCoordinates: GeoPoint.Coordinates,
        ) : Completed() {
            @CheckReturnValue
            fun backToChoice(): Choice =
                _input.copy(
                    locationPermissionState = LocationPermissionState.Ready,
                    cachedSettlementLocation = settlementLocation,
                    cachedUserCoordinates = userCoordinates
                )
        }

        data class Failed(
            private val _input: Choice,

            val error: RString?
        ) : Completed() {
            @CheckReturnValue
            fun backToChoice(): Choice =
                _input.copy(locationPermissionState = LocationPermissionState.Ready)
        }
    }
}
