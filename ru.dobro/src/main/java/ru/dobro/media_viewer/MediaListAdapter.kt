package ru.dobro.media_viewer

import android.net.Uri
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import common.library.android.widget.recycler_view.getRxAsyncDiffer
import common.library.core.EqualityComparators
import common.library.core.rx.invoke
import io.reactivex.Flowable
import io.reactivex.processors.PublishProcessor
import org.reactivestreams.Subscriber
import ru.dobro.databinding.MediaViewerListItemBinding
import ru.dobro.images
import javax.annotation.CheckReturnValue

class MediaListAdapter : ListAdapter<Uri, MediaListAdapter.ViewHolder>(
    getRxAsyncDiffer(
        EqualityComparators.value(),
        EqualityComparators.value()
    )
) {
    private val _onItemClick: PublishProcessor<Int> = PublishProcessor.create()

    val onItemClick: Flowable<Int> get() = _onItemClick.hide()

    @CheckReturnValue
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            binding = MediaViewerListItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            ),

            _onItemClick = _onItemClick
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position), position)
    }

    class ViewHolder(
        private val binding: MediaViewerListItemBinding,
        private val _onItemClick: Subscriber<Int>,
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: Uri, position: Int) {
            binding.apply {
                root.setOnClickListener { _onItemClick(position) }

                images.load(item)
                    .transition(DrawableTransitionOptions.withCrossFade())
                    .centerCrop()
                    .into(mediaViewerListItem)
            }
        }
    }
}
