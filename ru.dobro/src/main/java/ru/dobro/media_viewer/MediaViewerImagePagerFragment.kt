package ru.dobro.media_viewer

import android.Manifest
import android.content.ContentResolver
import android.content.ContentUris
import android.content.ContentValues
import android.content.Intent
import android.graphics.Bitmap
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.Toolbar
import androidx.navigation.fragment.findNavController
import com.bumptech.glide.Glide
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.textview.MaterialTextView
import common.library.android.inflateBy
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.message.MessageDisplay
import common.library.android.onPageChanged
import common.library.android.permissions.checkPermissionManager
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.string.RString
import common.library.android.widget.onClick
import common.library.core.lazyGet
import common.library.core.rx.RxSchedulers
import common.library.core.rx.invoke
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.processors.PublishProcessor
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.reactive.asFlow
import org.kodein.di.generic.instance
import ru.dobro.R
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomDynamicToolbar
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.databinding.MediaViewerImagePagerBinding
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream
import java.util.Locale
import java.util.concurrent.TimeUnit
import javax.annotation.CheckReturnValue

/**
 * Required param [MediaViewerPagerRequest]
 */
class MediaViewerImagePagerFragment : BaseFragment(), HasCustomDynamicToolbar<Int> {
    private val _mediaAdapter: MediaImageAdapter by lazyGet { MediaImageAdapter() }
    private lateinit var binding: MediaViewerImagePagerBinding

    private val _bundler: Bundler by instance()

    private val _messageDisplay: MessageDisplay by instance()

    private val _onPageChanged: PublishProcessor<Int> = PublishProcessor.create()

    private val _writeExternalStoragePermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (!isGranted) {
                if (!checkPermissionManager.shouldExplain(Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
                    val snackbar = Snackbar.make(
                        binding.root,
                        R.string.media_viewer___images_pager__permission_error,
                        3000
                    )
                    snackbar.setAction(R.string.media_viewer___images_pager__permission_action) {
                        startActivity(
                            Intent(
                                Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                                Uri.fromParts(
                                    "package",
                                    requireActivity().packageName,
                                    null
                                )
                            )
                        )
                    }
                    snackbar.show()
                }
            }
        }

    override val screenName: String
        get() = AnalyticsConstants.Screen.Common.mediaViewerImagesPager

    @CheckReturnValue
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar = object :
        HasCustomToolbar.CustomToolbar.Layout() {
        @CheckReturnValue
        override fun onCreateView(container: ViewGroup): View =
            container.context.inflateBy(R.layout.media_viewer___pager__toolbar, container)

        @CheckReturnValue
        override fun getAppBarLayout(view: View): AppBarLayout? {
            return view.findViewById(R.id.media_viewer___app_bar)
        }

        @CheckReturnValue
        override fun getToolbar(view: View): Toolbar {
            return view.findViewById(R.id.media_viewer___toolbar)
        }
    }

    @CheckReturnValue
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = MediaViewerImagePagerBinding.inflate(inflater)
        return binding.root
    }

    var request: MediaViewerPagerRequest? = null
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        request = arguments?.getParams(_bundler)
        if (request === null) {
            logger.debug { "Invalid request." }

            findNavController().navigateUp()

            return
        }
        binding.mediaViewerImagePager.adapter = _mediaAdapter
        _mediaAdapter.submitList(request?.files)
        when (request?.type) {
            ContentType.CERTIFICATES -> {
                binding.mediaViewerImagePagerDownload.text = getString(
                    R.string.media_viewer___images_pager__download,
                    ContentType.CERTIFICATES.readableName.lowercase(Locale.getDefault())
                )
            }

            null -> {
                binding.mediaViewerImagePagerDownload.visibility = View.GONE
            }
        }

        Observable.just("")
            .delay(100, TimeUnit.MILLISECONDS)
            .subscribeOn(RxSchedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribeBy {
                // todo (костыль) скролл на позицию, если убрать задержку -> всегда встает на первый элемент
                binding.mediaViewerImagePager?.currentItem = request?.position ?: 0
            }.scopedByCreate()
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        binding.mediaViewerImagePager
            .onPageChanged()
            .startWith(binding.mediaViewerImagePager.currentItem)
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                _onPageChanged.invoke(it)
            }
            .scoped()

        binding.mediaViewerImagePagerDownload
            .onClick
            .throttleUserInput()
            .observeOn(RxSchedulers.io())
            .subscribeBy {
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                    if (!checkPermissionManager.isGranted(Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
                        _writeExternalStoragePermissionLauncher.launch(Manifest.permission.WRITE_EXTERNAL_STORAGE)
                        return@subscribeBy
                    }
                }

                val file = request?.files?.get(binding.mediaViewerImagePager.currentItem)
                try {
                    request?.type?.let { requestType ->
                        saveImageOnDevice(
                            file.toString(), requestType.readableName,
                            Glide.with(requireActivity())
                                .asBitmap()
                                .load(file)
                                .placeholder(android.R.drawable.progress_indeterminate_horizontal) // need placeholder to avoid issue like glide annotations
                                .error(android.R.drawable.stat_notify_error) // need error to avoid issue like glide annotations
                                .submit()
                                .get()
                        )
                    }
                } catch (e: Exception) {
                    _messageDisplay.showMessage(
                        RString.res(
                            R.string.media_viewer___images_pager__download_error,
                            null
                        )
                    )
                }
            }
            .scoped()
    }

    override fun onUpdateCustomToolbar(event: Int, appBar: AppBarLayout?, toolbar: Toolbar) {
        val request: MediaViewerPagerRequest? = arguments?.getParams(_bundler)
        if (appBar != null && request != null) {
            appBar.findViewById<MaterialTextView>(R.id.media_viewer___toolbar__title).text =
                getString(
                    R.string.media_viewer___images_pager__count,
                    event + 1,
                    request.files.size
                )
        }
    }

    override fun onCustomToolbarChanged(): Flow<Int> =
        _onPageChanged
            .asFlow()
            .distinctUntilChanged()

    private fun saveImageOnDevice(fileName: String, fileType: String, image: Bitmap): String? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            downloadFile(fileName, fileType, image)
        } else {
            downloadFileLegacy(fileName, fileType, image)
        }
    }

    private fun downloadFileLegacy(fileName: String, fileType: String, image: Bitmap): String? {
        var savedImagePath: String? = null
        val imageFileName = "${fileType.dropLast(1)}_${fileName.split("/").last()}"
        val storageDir = File(
            Environment.getExternalStorageDirectory().toString()
                + "/${Environment.DIRECTORY_PICTURES}"
                + "/Добро/$fileType"
        )
        var success = true
        if (!storageDir.exists()) {
            success = storageDir.mkdirs()
        }
        if (success) {
            val imageFile = File(storageDir, imageFileName)
            savedImagePath = imageFile.getAbsolutePath()
            try {
                val fOut: OutputStream = FileOutputStream(imageFile)
                image.compress(Bitmap.CompressFormat.JPEG, 100, fOut)
                fOut.close()
            } catch (e: Exception) {
                Log.e("DownloadFile", e.stackTraceToString())
            }

            // Add the image to the system gallery
            notifyGallery(savedImagePath)
            savedImagePath?.let {
                _messageDisplay.showMessage(
                    RString.res(
                        R.string.main__profile___certificates__download_message,
                        persistentListOf(fileType)
                    )
                )
            }
        } else {
            _messageDisplay.showMessage(
                RString.res(
                    R.string.media_viewer___images_pager__download_error,
                    null
                )
            )
        }
        return savedImagePath
    }

    private fun downloadFile(fileName: String, fileType: String, image: Bitmap): String? {
        var savedImagePath: String? = null
        val imageFileName = "${fileType.dropLast(1)}_${fileName.split("/").last()}"
        val storageDir = File(
            Environment.DIRECTORY_PICTURES,
            "Добро/$fileType"
        )

        val contentValues = ContentValues().apply {
            put(MediaStore.Images.Media.DISPLAY_NAME, imageFileName)
            put(MediaStore.Images.Media.MIME_TYPE, "image/*")
            put(MediaStore.Images.Media.RELATIVE_PATH, storageDir.path)
        }

        val resolver = requireContext().contentResolver

        deleteFileCopiesIfExist(imageFileName, resolver)

        val imageUri = createNewFile(resolver, contentValues)

        if (imageUri != null) {
            try {
                val outputStream = resolver.openOutputStream(imageUri, "wt") ?: return null
                image.compress(Bitmap.CompressFormat.JPEG, 100, outputStream)
                outputStream.close()
                savedImagePath = getRealPathFromURI(imageUri)

                notifyGallery(imageUri.path)
                savedImagePath?.let {
                    _messageDisplay.showMessage(
                        RString.res(
                            R.string.main__profile___certificates__download_message,
                            persistentListOf(fileType)
                        )
                    )
                }
            } catch (e: Exception) {
                Log.e("DownloadFile", e.stackTraceToString())
            }
        } else {
            _messageDisplay.showMessage(
                RString.res(
                    R.string.media_viewer___images_pager__download_error,
                    null
                )
            )
        }

        return savedImagePath
    }

    private fun createNewFile(
        resolver: ContentResolver,
        contentValues: ContentValues
    ): Uri? {
        return resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
    }

    private fun deleteFileCopiesIfExist(imageFileName: String, resolver: ContentResolver) {
        val existingFileUri =
            MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)
        val selection = "${MediaStore.Images.Media.DISPLAY_NAME} = ?"
        val selectionArgs = arrayOf(imageFileName)
        val existingFileCursor =
            resolver.query(existingFileUri, null, selection, selectionArgs, null)

        if (existingFileCursor != null && existingFileCursor.count > 0) {
            while (existingFileCursor.moveToNext()) {
                val idColumnIndex = existingFileCursor.getColumnIndex(MediaStore.Images.Media._ID)
                val fileId = existingFileCursor.getString(idColumnIndex).toLong()
                val deleteUri = ContentUris.withAppendedId(existingFileUri, fileId)
                resolver.delete(deleteUri, null, null)
            }
            existingFileCursor.close()
        }
    }

    private fun getRealPathFromURI(uri: Uri): String? {
        val cursor = requireContext().contentResolver.query(uri, null, null, null, null)
        cursor?.use {
            val index = cursor.getColumnIndex(MediaStore.Images.Media.DATA)
            if (index != -1 && cursor.moveToFirst()) {
                return cursor.getString(index)
            }
        }
        return null
    }

    private fun notifyGallery(imagePath: String?) {
        imagePath?.let { path ->
            val file = File(path)
            if (file.exists()) {
                MediaScannerConnection.scanFile(
                    requireContext(),
                    arrayOf(file.toString()),
                    null
                ) { _, _ ->
                    // No need.
                }
            }
        }
    }
}
