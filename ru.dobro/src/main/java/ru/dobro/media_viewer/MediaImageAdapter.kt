package ru.dobro.media_viewer

import android.net.Uri
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import common.library.android.widget.recycler_view.getRxAsyncDiffer
import common.library.core.EqualityComparators
import ru.dobro.databinding.MediaViewerImageItemBinding
import ru.dobro.images
import javax.annotation.CheckReturnValue

class MediaImageAdapter : ListAdapter<Uri, MediaImageAdapter.ViewHolder>(
    getRxAsyncDiffer(
        EqualityComparators.value(),
        EqualityComparators.value()
    )
) {
    @CheckReturnValue
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            MediaViewerImageItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class ViewHolder(
        private val binding: MediaViewerImageItemBinding
    ) : RecyclerView.ViewHolder(
        binding.root
    ) {
        fun bind(item: Uri) {
            binding.apply {
                images.load(item)
                    .transition(DrawableTransitionOptions.withCrossFade())
                    .into(mediaViewerImageItem)
            }
        }
    }
}
