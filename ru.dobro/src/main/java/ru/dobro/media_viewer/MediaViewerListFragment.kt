package ru.dobro.media_viewer

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.IdRes
import androidx.appcompat.widget.Toolbar
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.textview.MaterialTextView
import common.library.android.dimension.dp
import common.library.android.inflateBy
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.intent.newTask
import common.library.android.intent.tryStartActivityBy
import common.library.android.intent.view
import common.library.android.message.MessageDisplay
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.widget.recycler_view.addSpacingItemDecoration
import common.library.core.collection.mapPersistent
import common.library.core.lazyGet
import common.library.core.rx.RxSchedulers
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.collections.immutable.PersistentList
import org.kodein.di.generic.instance
import ru.dobro.R
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.databinding.MediaViewerListBinding
import ru.dobro.domain.File
import ru.dobro.domain.VideoFile
import javax.annotation.CheckReturnValue

/**
 * Required param [MediaViewerRequest]
 */
class MediaViewerListFragment : BaseFragment(), HasCustomToolbar {
    private lateinit var binding: MediaViewerListBinding

    private val _mediaAdapter: MediaListAdapter by lazyGet { MediaListAdapter() }

    private val _messageDisplay: MessageDisplay by instance()

    private val _bundler: Bundler by instance()

    override val screenName: String
        get() = AnalyticsConstants.Screen.Common.mediaViewerList

    companion object {
        class NavigationContext(
            private val _navigation: NavController,
            private val _bundler: Bundler
        ) {
            fun toImages(@IdRes actionId: Int, photos: PersistentList<File>) {
                to(
                    actionId,
                    MediaViewerRequest.Images(photos)
                )
            }

            fun toVideos(
                @IdRes actionId: Int,
                videos: PersistentList<VideoFile>
            ) {
                to(
                    actionId,
                    MediaViewerRequest.Videos(videos)
                )
            }

            fun to(
                @IdRes actionId: Int,
                request: MediaViewerRequest
            ) {
                _navigation.navigate(
                    actionId,
                    request.intoBundle<MediaViewerRequest>(_bundler)
                )
            }
        }

        @CheckReturnValue
        fun navigate(
            navigation: NavController,
            bundler: Bundler
        ): NavigationContext = NavigationContext(navigation, bundler)
    }

    @CheckReturnValue
    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar = object :
        HasCustomToolbar.CustomToolbar.Layout() {
        @CheckReturnValue
        override fun onCreateView(container: ViewGroup): View =
            container.context.inflateBy(R.layout.media_viewer___toolbar, container)

        @CheckReturnValue
        override fun getAppBarLayout(view: View): AppBarLayout? {
            return view.findViewById(R.id.media_viewer___app_bar)
        }

        @CheckReturnValue
        override fun getToolbar(view: View): Toolbar {
            when (arguments?.getParams(_bundler) as? MediaViewerRequest) {
                is MediaViewerRequest.Images -> view.findViewById<MaterialTextView>(R.id.media_viewer___toolbar__title).text =
                    getString(R.string.media_viewer___images)

                is MediaViewerRequest.Videos -> view.findViewById<MaterialTextView>(R.id.media_viewer___toolbar__title).text =
                    getString(R.string.media_viewer___videos)

                null -> {}
            }

            return view.findViewById(R.id.media_viewer___toolbar)
        }
    }

    @CheckReturnValue
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = MediaViewerListBinding.inflate(inflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.mediaViewerList.adapter = _mediaAdapter
        binding.mediaViewerList.addSpacingItemDecoration { it.spacing(2.dp) }
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        val request: MediaViewerRequest? = arguments?.getParams(_bundler)

        if (request === null) {
            logger.debug { "Invalid request." }

            findNavController().navigateUp()

            return
        }

        when (request) {
            is MediaViewerRequest.Images -> {
                (binding.mediaViewerList.layoutManager as GridLayoutManager).spanCount = 3
                _mediaAdapter.submitList(request.images.mapPersistent { it.uri })
                binding.mediaViewerListCount.text = requireContext().resources.getQuantityString(
                    R.plurals.media_viewer___photos__count,
                    request.images.size,
                    request.images.size
                )
            }

            is MediaViewerRequest.Videos -> {
                (binding.mediaViewerList.layoutManager as GridLayoutManager).spanCount = 2
                _mediaAdapter.submitList(request.videos.mapPersistent { it.previewUri })
                binding.mediaViewerListCount.text =
                    getString(R.string.media_viewer___videos__count, request.videos.size)
            }
        }

        _mediaAdapter
            .onItemClick
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy { position ->
                firebaseAnalyticManager.sendSimpleEvent(AnalyticsConstants.Event.Common.mediaViewerOnMediaClick)
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Common.mediaViewerOnMediaClick)

                when (request) {
                    is MediaViewerRequest.Images -> {
                        findNavController().navigate(
                            R.id.media_viewer___list__to__media_viewer___image_pager,
                            MediaViewerPagerRequest(
                                position = position,
                                files = request.images.mapPersistent { it.uri }
                            ).intoBundle(_bundler))
                    }

                    is MediaViewerRequest.Videos -> {
                        tryStartActivityBy {
                            it.view(request.videos[position].uri)
                                .newTask()
                        }
                    }
                }
            }
            .scoped()
    }
}
