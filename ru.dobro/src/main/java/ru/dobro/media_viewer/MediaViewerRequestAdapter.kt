package ru.dobro.media_viewer

import com.google.gson.GsonBuilder
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonElement
import com.google.gson.JsonParseException
import com.google.gson.JsonSerializationContext
import common.library.serialization.gson.adapter.SealedClassJsonAdapter
import common.library.serialization.gson.adapter.registerTypeHierarchyAdapter
import common.library.serialization.gson.deserializeTypedRequired
import common.library.serialization.gson.serializeTyped
import kotlinx.collections.immutable.PersistentList
import ru.dobro.domain.File
import ru.dobro.domain.VideoFile
import java.lang.reflect.Type
import javax.annotation.CheckReturnValue
import kotlin.reflect.KClass

fun GsonBuilder.registerMediaViewerRequestAdapter(): GsonBuilder =
    registerTypeHierarchyAdapter(MediaViewerRequestAdapter)

object MediaViewerRequestAdapter :
    SealedClassJsonAdapter<MediaViewerRequest>(MediaViewerRequest::class) {
    @CheckReturnValue
    override fun serializeInstance(
        src: MediaViewerRequest,
        typeOfSrc: Type,
        context: JsonSerializationContext
    ): JsonElement = when (src) {
        is MediaViewerRequest.Images -> context.serializeTyped<PersistentList<File>>(src.images)
        is MediaViewerRequest.Videos -> context.serializeTyped<PersistentList<VideoFile>>(src.videos)
    }

    @CheckReturnValue
    override fun deserializeInstance(
        json: JsonElement,
        typeOfT: Type,
        type: KClass<out MediaViewerRequest>,
        context: JsonDeserializationContext
    ): MediaViewerRequest =
        when (type) {
            MediaViewerRequest.Images::class -> {
                MediaViewerRequest.Images(
                    images = context.deserializeTypedRequired(json)
                )
            }

            MediaViewerRequest.Videos::class -> {
                MediaViewerRequest.Videos(
                    videos = context.deserializeTypedRequired(json)
                )
            }

            else -> throw JsonParseException("Unknown type: `$type`.")
        }
}
