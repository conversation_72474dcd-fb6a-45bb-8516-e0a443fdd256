package ru.dobro.app_update

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.DialogFragment
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.setFragmentResult
import common.library.android.widget.onClick
import common.library.core.rx.RxSchedulers
import common.library.mobile.services.InAppUpdatesProvider
import common.library.mobile.services.models.RemoteStoreAlias
import io.reactivex.rxkotlin.subscribeBy
import org.kodein.di.generic.instance
import ru.dobro.R
import ru.dobro.core.BaseDialogFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.metric.Param
import ru.dobro.databinding.FragmentAppUpdateRequestBinding
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.annotation.CheckReturnValue

class AppUpdateRequestFragment : BaseDialogFragment(), HasCustomToolbar {
    override val screenName: String = AnalyticsConstants.Screen.Main.appUpdateRequest
    private lateinit var binding: FragmentAppUpdateRequestBinding

    companion object {
        @CheckReturnValue
        fun create(
            alias: RemoteStoreAlias,
            resultCode: String,
            bundler: Bundler
        ): AppUpdateRequestFragment = AppUpdateRequestFragment().apply {
            arguments = RequestToAppUpdate(alias, resultCode).intoBundle(bundler)
        }
    }

    private val _bundler: Bundler by instance()

    private val _inAppUpdatesProvider: InAppUpdatesProvider by instance()

    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        HasCustomToolbar.CustomToolbar.None

    init {
        setStyle(DialogFragment.STYLE_NORMAL, R.style.Application_Theme)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)

        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        isCancelable = false
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentAppUpdateRequestBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val request: RequestToAppUpdate? = arguments?.getParams(_bundler)

        if (request === null) {
            logger.warning { "Invalid request to AppUpdateFragment" }

            dismiss()

            return
        }

        binding.title.text = when (request.alias) {
            RemoteStoreAlias.GooglePlay -> getString(R.string.app_update_request___go_to_googleplay)
            RemoteStoreAlias.RuStore -> getString(R.string.app_update_request___go_to_rustore)
            RemoteStoreAlias.AppGallery -> getString(R.string.app_update_request___go_to_appgallery)
        }
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        val request: RequestToAppUpdate? = arguments?.getParams(_bundler)

        if (request === null) {
            logger.warning { "Invalid request to AppUpdateFragment" }

            dismiss()

            return
        }

        setFragmentResult(request.resultCode, true, _bundler)

        val dateFormat = SimpleDateFormat("dd.MM.yyyy_HH.mm.ss", Locale.getDefault())
        val formattedDateFormat = dateFormat.format(Date())
        val market = when (request.alias) {
            RemoteStoreAlias.GooglePlay -> "google_play"
            RemoteStoreAlias.RuStore -> "ru_store"
            RemoteStoreAlias.AppGallery -> "app_gallery"
        }
        metricManager.sendSimpleEvent(
            eventName = AnalyticsConstants.Screen.showScreen + AnalyticsConstants.Screen.Main.appUpdateRequest,
            Param(
                param = formattedDateFormat,
                paramType = "time"
            ),
            Param(
                param = market,
                paramType = "market"
            )
        )

        binding.update
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Common.updateAppUpdateRequestClick)

                _inAppUpdatesProvider.launchStore()
            }
            .scoped()

        binding.dismiss
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                metricManager.sendSimpleEvent(AnalyticsConstants.Event.Common.dismissAppUpdateRequestClick)

                dismiss()
            }
            .scoped()
    }
}
