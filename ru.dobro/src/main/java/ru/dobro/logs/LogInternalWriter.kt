package ru.dobro.logs

import android.content.Context
import android.os.Environment
import android.util.Log
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

object LogInternalWriter {

    /**
     * Записывает все последние логи с logcat в internal storage в папку **./Android/data/ru.dobro/files/Documents/dobro/logs**
     */
    fun writeLogsToFile(context: Context): Boolean {
        try {
            val sdf = SimpleDateFormat("dd.MM.yyyy_HH.mm.ss", Locale.getDefault())
            val currentDateAndTime: String = sdf.format(Date()).plus(".txt")
            val folderName = "dobro"
            val uri =
                context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)
            val folder = File(uri, folderName)
            if (!folder.exists()) {
                folder.mkdir()
            }
            val logFolder =
                File(uri, "$folderName${File.separator}${"logs"}")
            if (!logFolder.exists()) {
                logFolder.mkdir()
            }
            val filesInLogFolder = logFolder.listFiles()
            if ((filesInLogFolder?.size ?: 0) >= 10) {
                val dropFilesCount = filesInLogFolder?.size?.minus(10 - 1) ?: 0
                val filesToRemove =
                    filesInLogFolder?.toMutableList()?.dropLast(10 - dropFilesCount)
                filesToRemove?.forEach {
                    it.deleteRecursively()
                }
            }
            val filename = File(
                uri,
                "$folderName${File.separator}${"logs"}${File.separator}$currentDateAndTime"
            )
            filename.createNewFile()
            val cmd = "logcat -f ${filename.absolutePath}"
            Runtime.getRuntime().exec(cmd)
            return true
        } catch (e: Exception) {
            Log.e("LogInternalWriter", "Can`t write logs in file due to Exception: $e")
        }
        return false
    }
}
