package ru.dobro.reset_password

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.IdRes
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import arrow.core.Some
import arrow.core.toOption
import common.library.android.input.InputFieldDescriptor
import common.library.android.input.setupInputFields
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.message.MessageDisplay
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.resource.withOnStartResourcesScope
import common.library.android.rx.throttleErrorMessages
import common.library.android.rx.throttleUserInput
import common.library.android.soft_input.SoftInputMode
import common.library.android.string.RString
import common.library.android.string.r
import common.library.android.widget.enableSoftInputMode
import common.library.android.widget.onClick
import common.library.android.widget.wrapIntoSwipeRefresh
import common.library.core.rx.RxSchedulers
import common.library.core.rx.ofSubtype
import common.library.core.rx.onBackpressureBufferLast
import common.library.core.state.task.handleActionExecution
import common.library.core.variable.Agent
import common.library.core.variable.agent
import common.library.core.variable.sendWhen
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.collections.immutable.persistentListOf
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.api.DobroApi
import ru.dobro.api.overview.setNewPasswordAsync
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.core.error.ErrorHandler
import ru.dobro.core.error.ErrorMessages
import ru.dobro.databinding.MainNewPasswordBinding
import ru.dobro.domain.UserId
import javax.annotation.CheckReturnValue

class NewPasswordFragment : BaseFragment({
    bind<Agent<NewPasswordState>>() with singleton { agent(NewPasswordState.Idle) }
}), HasCustomToolbar {
    private lateinit var binding: MainNewPasswordBinding

    companion object {
        class NavigationContext(
            private val _navigator: NavController,
            private val _bundler: Bundler
        ) {
            @CheckReturnValue
            fun toNewPassword(
                @IdRes actionId: Int,
                token: String,
                userId: UserId
            ) {
                _navigator.navigate(
                    actionId,
                    NewPasswordRequest(
                        token = token,
                        userId = userId
                    ).intoBundle(_bundler)
                )
            }
        }

        @CheckReturnValue
        fun navigate(
            navigation: NavController,
            bundler: Bundler
        ): NavigationContext = NavigationContext(navigation, bundler)
    }

    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        HasCustomToolbar.CustomToolbar.None

    override val screenName: String = AnalyticsConstants.Screen.Main.newPassword

    private val _api: DobroApi by instance()

    private val _state: Agent<NewPasswordState> by instance()

    private val _messageDisplay: MessageDisplay by instance()

    private val _errorHandler: ErrorHandler by instance()

    private val _bundler: Bundler by instance()

    private lateinit var _swipeRefreshLayout: SwipeRefreshLayout

    override fun onStart() = withOnStartResourcesScope {
        super.onStart()

        activity?.window?.enableSoftInputMode(SoftInputMode.AdjustResize)?.scoped()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = MainNewPasswordBinding.inflate(inflater)
        _swipeRefreshLayout = binding.root
            .wrapIntoSwipeRefresh()
            .apply {
                isEnabled = false
            }

        return _swipeRefreshLayout
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        _swipeRefreshLayout.isEnabled = false

        val request: NewPasswordRequest? = arguments?.getParams(_bundler)

        if (request === null) {
            logger.warning { "Invalid request to NewPasswordFragment" }

            findNavController().navigateUp()

            return
        }

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<NewPasswordState.Idle>()
            .subscribeBy {
                _state.sendWhen<NewPasswordState, NewPasswordState.Idle> {
                    requestInput()
                }
            }
            .scoped()

        binding.mainNewPasswordReturn
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                findNavController().navigateUp()
            }
            .scoped()

        _state
            .setupInputFields(
                persistentListOf(
                    InputFieldDescriptor(
                        NewPasswordField.Password,
                        binding.mainNewPasswordPassword,
                        binding.mainNewPasswordPasswordContainer,
                        false
                    ),
                    InputFieldDescriptor(
                        NewPasswordField.PasswordConfirmation,
                        binding.mainNewPasswordPasswordOnfirmation,
                        binding.mainNewPasswordPasswordOnfirmationContainer,
                        false
                    )
                ),
                binding.mainNewPasswordPerform
            )
            .scoped()

        // Disable input for non input states.
        _state
            .observeOn(RxSchedulers.computation())
            .map { it is NewPasswordState.Input && it.isValid }
            .distinctUntilChanged()
            .onBackpressureBufferLast(RxSchedulers.main())
            .subscribeBy {
                binding.mainNewPasswordPerform.isEnabled = it
            }
            .scoped()

        _state
            .handleActionExecution<
                NewPasswordState,
                NewPasswordState.Processing,
                NewPasswordTaskParams
                >(
                toTaskState = { it.newPasswordState },
                task = {
                    _api
                        .overview().setNewPasswordAsync(
                            token = request.token,
                            password = it.password,
                            userId = request.userId
                        )
                        .doOnError { _messageDisplay.showMessage("Не удалось загрузить данные".r) }
                },
                updateState = { updateTaskState(it) }
            )

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<NewPasswordState.Completed.Success>()
            .subscribeBy {
                findNavController().navigateUp()
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .map { it is NewPasswordState.Processing }
            .observeOn(RxSchedulers.main())
            .subscribeBy { isProcessingState ->
                _swipeRefreshLayout.isRefreshing = isProcessingState
            }
            .scoped()

        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<NewPasswordState.Completed.Failed>()
            .map { ErrorMessages.ofException(_errorHandler.ofException(it.error)).toOption() }
            .ofSubtype<Some<RString>>()
            .throttleErrorMessages()
            .doOnNext {
                _messageDisplay.showMessage(it.t)
            }
            .subscribeBy {
                _state.sendWhen<NewPasswordState, NewPasswordState.Completed.Failed> {
                    backToInput()
                }
            }
            .scoped()
    }
}
