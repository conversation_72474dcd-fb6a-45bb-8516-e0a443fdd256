package ru.dobro.reset_password

import common.library.android.input.InputField
import common.library.android.input.InputRequestState
import common.library.core.contract.ContractException
import common.library.core.state.task.TaskActionState
import io.reactivex.annotations.CheckReturnValue
import ru.dobro.core.validation.PasswordInputValidator
import ru.dobro.core.validation.PasswordWithConfirmationInputValidator

sealed class NewPasswordState : InputRequestState<NewPasswordField, NewPasswordState> {
    object Idle : NewPasswordState() {
        @CheckReturnValue
        fun requestInput(): Input =
            Input(
                password = InputField.empty(),
                passwordConfirmation = InputField.empty()
            )
    }

    data class Input(
        val password: InputField<String>,
        val passwordConfirmation: InputField<String>
    ) : NewPasswordState(), InputRequestState.Input<NewPasswordField, NewPasswordState> {

        override val isValid: Boolean
            get() = password is InputField.NonEmpty.Valid && passwordConfirmation is InputField.NonEmpty.Valid

        override fun get(id: NewPasswordField): InputField<*> =
            when (id) {
                NewPasswordField.Password -> password
                NewPasswordField.PasswordConfirmation -> passwordConfirmation
            }

        @CheckReturnValue
        fun inputPassword(value: String): Input =
            copy(
                password = PasswordWithConfirmationInputValidator.validateInput(
                    password,
                    passwordConfirmation,
                    value
                ),
                passwordConfirmation = passwordConfirmation.tryGetValidData().fold({
                    PasswordInputValidator.validateInput(
                        passwordConfirmation,
                        value
                    )
                }, {
                    PasswordWithConfirmationInputValidator.validateInput(
                        passwordConfirmation,
                        password,
                        it
                    )
                })
            )

        @CheckReturnValue
        fun inputPasswordConfirmation(value: String): Input =
            copy(
                passwordConfirmation = PasswordWithConfirmationInputValidator.validateInput(
                    passwordConfirmation,
                    password,
                    value
                ),
                password = password.tryGetValidData().fold({
                    PasswordInputValidator.validateInput(
                        password,
                        value
                    )
                }, {
                    PasswordWithConfirmationInputValidator.validateInput(
                        passwordConfirmation,
                        password,
                        it
                    )
                }),
            )

        override fun focus(id: NewPasswordField): NewPasswordState =
            when (id) {
                NewPasswordField.Password -> focusPassword()
                NewPasswordField.PasswordConfirmation -> focusPasswordConfirmation()
            }

        @CheckReturnValue
        fun focusPassword(): Input = copy(password = password.markFocused())

        @CheckReturnValue
        fun focusPasswordConfirmation(): Input =
            copy(passwordConfirmation = passwordConfirmation.markFocused())

        @CheckReturnValue
        override fun input(id: NewPasswordField, value: String): NewPasswordState =
            when (id) {
                NewPasswordField.Password -> inputPassword(value)
                NewPasswordField.PasswordConfirmation -> inputPasswordConfirmation(value)
            }

        @CheckReturnValue
        override fun processing(): NewPasswordState {
            if (password !is InputField.NonEmpty.Valid
                || passwordConfirmation !is InputField.NonEmpty.Valid
                || password.data != passwordConfirmation.data
            ) {
                throw ContractException("Invalid input.")
            }

            return Processing(
                _input = this,

                newPasswordState = TaskActionState
                    .Idle
                    .awaiting<NewPasswordTaskParams>()
                    .execute(NewPasswordTaskParams(password = password.data))
            )
        }
    }

    data class Processing(
        private val _input: Input,

        val newPasswordState: NewPasswordTaskState
    ) : NewPasswordState() {
        @CheckReturnValue
        fun updateTaskState(action: NewPasswordTaskState.() -> NewPasswordTaskState): NewPasswordState {
            return when (val state = newPasswordState.action()) {
                is TaskActionState.Idle.Completed.Success<NewPasswordTaskParams> -> Completed.Success
                is TaskActionState.Idle.Completed.Fail<NewPasswordTaskParams> -> Completed.Failed(
                    _input, state.error
                )

                else -> copy(
                    _input = _input,
                    newPasswordState = state
                )
            }
        }
    }

    sealed class Completed : NewPasswordState() {
        data object Success : Completed()

        data class Failed(
            private val _input: Input,

            val error: Throwable?
        ) : Completed() {
            @CheckReturnValue
            fun backToInput(): Input = _input
        }
    }
}
