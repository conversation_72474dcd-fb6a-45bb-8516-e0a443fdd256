package ru.dobro.reset_password

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.IdRes
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import common.library.android.input.InputFieldDescriptor
import common.library.android.input.setupInputFields
import common.library.android.intent.bundler.Bundler
import common.library.android.message.MessageDisplay
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.resource.withOnStartResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.soft_input.SoftInputMode
import common.library.android.widget.enableSoftInputMode
import common.library.android.widget.onClick
import common.library.core.logging.logWarning
import common.library.core.rx.RxSchedulers
import common.library.core.rx.ofSubtype
import common.library.core.rx.onBackpressureBufferLast
import common.library.core.state.task.TaskActionState
import common.library.core.variable.Agent
import common.library.core.variable.agent
import common.library.core.variable.sendWhen
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.collections.immutable.persistentListOf
import org.kodein.di.generic.bind
import org.kodein.di.generic.instance
import org.kodein.di.generic.singleton
import ru.dobro.R
import ru.dobro.api.DobroApi
import ru.dobro.api.overview.resetPasswordAsync
import ru.dobro.check_email.CheckEmailFragment
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.databinding.MainResetPasswordBinding
import javax.annotation.CheckReturnValue

class ResetPasswordFragment : BaseFragment({
    bind<Agent<ResetPasswordState>>() with singleton { agent(ResetPasswordState.Idle) }
}), HasCustomToolbar {

    private lateinit var binding: MainResetPasswordBinding

    companion object {
        class NavigationContext(
            private val _navigator: NavController
        ) {
            @CheckReturnValue
            fun toResetPassword(
                @IdRes actionId: Int
            ) {
                _navigator.navigate(actionId)
            }
        }

        @CheckReturnValue
        fun navigate(
            navigation: NavController
        ): NavigationContext = NavigationContext(navigation)
    }

    private val _api: DobroApi by instance()

    private val _state: Agent<ResetPasswordState> by instance()

    private val _messageDisplay: MessageDisplay by instance()

    private val _bundler: Bundler by instance()

    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        HasCustomToolbar.CustomToolbar.None

    override fun onStart() = withOnStartResourcesScope {
        super.onStart()

        activity?.window?.enableSoftInputMode(SoftInputMode.AdjustResize)?.scoped()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = MainResetPasswordBinding.inflate(inflater)
        return binding.root
    }

    override val screenName: String = AnalyticsConstants.Screen.Main.resetPassword

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        binding.mainResetPasswordReturn
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                findNavController().navigateUp()
            }
            .scoped()

        _state
            .sendWhen<ResetPasswordState, ResetPasswordState.Idle> {
                requestInput()
            }

        // Restore input values.
        _state
            .observeOn(RxSchedulers.computation())
            .distinctUntilChanged { resetPasswordState -> resetPasswordState::class }
            .ofSubtype<ResetPasswordState.Input>()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                binding.mainResetPasswordEmail.setText(it.email.tryGetValue().orNull())
            }
            .scoped()

        _state
            .setupInputFields(
                persistentListOf(
                    InputFieldDescriptor(
                        ResetPasswordField.Email,
                        binding.mainResetPasswordEmail,
                        binding.mainResetPasswordEmailContainer,
                        false
                    )
                ),
                binding.mainResetPasswordPerform
            )
            .scoped()

        // Disable input for non input states.
        _state
            .observeOn(RxSchedulers.computation())
            .map { it is ResetPasswordState.Input && it.isValid }
            .distinctUntilChanged()
            .onBackpressureBufferLast(RxSchedulers.main())
            .subscribeBy {
                binding.mainResetPasswordPerform.isEnabled = it
            }
            .scoped()

        // Perform authorization.
        var isTimeoutException = false
        _state
            .observeOn(RxSchedulers.computation())
            .ofSubtype<ResetPasswordState.Processing>()
            .map { it.resetPasswordTaskState }
            .ofSubtype<TaskActionState.InProgress<ResetPasswordTaskParams>>()
            .concatMapCompletable {
                _api
                    .overview()
                    .resetPasswordAsync(it.params.email)
                    .doOnComplete {
                        _state.sendWhen<ResetPasswordState, ResetPasswordState.Processing> {
                            updateTask {
                                if (this is TaskActionState.InProgress) {
                                    success()
                                } else {
                                    this
                                }
                            }
                        }
                    }
                    .doOnError {
                        _state.sendWhen<ResetPasswordState, ResetPasswordState.Processing> {
                            val exceptionCode = it.message?.split(' ').orEmpty()
                            isTimeoutException =
                                exceptionCode.getOrElse(exceptionCode.size - 2) { 1 } == "429"
                            updateTask {
                                if (this is TaskActionState.InProgress) {
                                    fail(it)
                                } else {
                                    this
                                }
                            }
                        }
                    }
                    .logWarning(logger) { "Failed to reset password." }
                    .onErrorComplete()
            }
            .observeOn(RxSchedulers.main())
            .subscribe()
            .scoped()

        _state
            .ofSubtype<ResetPasswordState.Completed>()
            .subscribe { state ->
                when (state) {
                    is ResetPasswordState.Completed.Success -> {
                        CheckEmailFragment
                            .navigate(findNavController(), _bundler)
                            .toCheckEmail(R.id.reset_password___to__check_email)

                        _state.send {
                            reset()
                        }
                    }

                    is ResetPasswordState.Completed.Failed -> {
                        state.error?.let(_messageDisplay::showMessage)
                        _state.sendWhen<ResetPasswordState, ResetPasswordState.Completed.Failed> {
                            backToInput()
                        }
                    }
                }
            }
            .scoped()
    }
}
