package ru.dobro.reset_password

import common.library.android.input.InputField
import common.library.android.input.InputRequestState
import common.library.android.string.RString
import common.library.core.contract.ContractException
import common.library.core.email.Email
import common.library.core.state.task.TaskActionState
import io.reactivex.annotations.CheckReturnValue
import ru.dobro.core.error.ErrorMessages
import ru.dobro.core.validation.EmailInputValidator

sealed class ResetPasswordState : InputRequestState<ResetPasswordField, ResetPasswordState> {
    @CheckReturnValue
    fun reset(): Idle = Idle

    object Idle : ResetPasswordState() {
        @CheckReturnValue
        fun requestInput(): Input =
            Input(email = InputField.empty())
    }

    data class Input(
        val email: InputField<Email>
    ) : ResetPasswordState(), InputRequestState.Input<ResetPasswordField, ResetPasswordState> {
        override val isValid: <PERSON>ole<PERSON>
            get() = email is InputField.NonEmpty.Valid

        override fun get(id: ResetPasswordField): InputField<*> =
            when (id) {
                ResetPasswordField.Email -> email
            }

        @CheckReturnValue
        fun inputEmail(value: String): Input =
            copy(
                email = EmailInputValidator.validateInput(email, value)
            )

        override fun focus(id: ResetPasswordField): ResetPasswordState =
            when (id) {
                ResetPasswordField.Email -> focusEmail()
            }

        @CheckReturnValue
        fun focusEmail(): Input = copy(email = email.markFocused())

        @CheckReturnValue
        override fun input(id: ResetPasswordField, value: String): ResetPasswordState =
            when (id) {
                ResetPasswordField.Email -> inputEmail(value)
            }

        @CheckReturnValue
        override fun processing(): ResetPasswordState {
            if (email !is InputField.NonEmpty.Valid) {
                throw ContractException("Invalid input.")
            }

            return Processing(
                _input = this,

                resetPasswordTaskState = TaskActionState
                    .Idle
                    .awaiting<ResetPasswordRequestParams>()
                    .execute(ResetPasswordTaskParams(email = email.data))
            )
        }
    }

    data class Processing(
        private val _input: Input,

        val resetPasswordTaskState: ResetPasswordTaskState
    ) : ResetPasswordState() {
        fun updateTask(action: ResetPasswordTaskState.() -> ResetPasswordTaskState): ResetPasswordState {
            if (_input.email !is InputField.NonEmpty.Valid) {
                throw ContractException("Invalid input.")
            }

            return when (val state = resetPasswordTaskState.action()) {
                is TaskActionState.Idle.Completed.Success ->
                    Completed.Success

                is TaskActionState.Idle.Completed.Fail ->
                    Completed.Failed(
                        _input = _input,

                        error = ErrorMessages.ofException(state.error)
                    )

                else -> copy(resetPasswordTaskState = state)
            }
        }
    }

    sealed class Completed : ResetPasswordState() {
        data object Success : Completed()

        data class Failed(
            private val _input: Input,

            val error: RString?
        ) : Completed() {
            @CheckReturnValue
            fun backToInput(): Input = _input
        }
    }
}
