package ru.dobro.date_chooser

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.annotation.StringRes
import androidx.fragment.app.Fragment
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.prolificinteractive.materialcalendarview.CalendarDay
import com.prolificinteractive.materialcalendarview.DayViewDecorator
import com.prolificinteractive.materialcalendarview.DayViewFacade
import common.library.android.getSupportDrawable
import common.library.android.intent.bundler.Bundler
import common.library.android.intent.bundler.getParams
import common.library.android.intent.bundler.intoBundle
import common.library.android.onFragmentResult
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.setFragmentResult
import common.library.android.widget.onClick
import common.library.core.ifAllNotNull
import common.library.core.rx.RxSchedulers
import io.reactivex.Observable
import io.reactivex.rxkotlin.subscribeBy
import org.kodein.di.generic.instance
import ru.dobro.R
import ru.dobro.core.BaseBottomSheetDialogFragment
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.databinding.DateChooserBinding
import java.time.LocalDate
import javax.annotation.CheckReturnValue

// TODO add decorator when there is no min/max dates
class DatesChooserFragment : BaseBottomSheetDialogFragment() {
    override val screenName: String
        get() = AnalyticsConstants.Screen.Main.dateChooser

    private val _bundler: Bundler by instance()
    private lateinit var binding: DateChooserBinding

    companion object {
        fun createDateChooser(
            requestKey: String,
            bundler: Bundler,
            minDate: LocalDate?,
            maxDate: LocalDate?,
            @StringRes title: Int,
        ): DatesChooserFragment = DatesChooserFragment().apply {
            arguments = DateChooserRequest(
                minDate = minDate,
                title = title,
                resultKey = requestKey,
            ).intoBundle(bundler)
        }

        @CheckReturnValue
        fun onResult(
            receiver: Fragment,
            requestKey: String,
            bundler: Bundler
        ): Observable<DateChooserResult> =
            receiver.onFragmentResult(requestKey, bundler)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        dialog.setOnShowListener {
            (it as BottomSheetDialog).findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
                ?.let { sheetView ->
                    sheetView.layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
                    it.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                }
        }
        dialog.behavior.skipCollapsed = true
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = DateChooserBinding.inflate(inflater)
        return binding.root
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        val request: DateChooserRequest? = arguments?.getParams(_bundler)

        if (request === null) {
            logger.warning { "Illegal date chooser request." }

            dismiss()
            return
        }

        binding.dateChooserTitle.setText(request.title)

        val vacancyStart = request.minDate ?: LocalDate.now().minusYears(1)

        val vacancyStartMinusMonth = vacancyStart?.minusMonths(12) // отматываем на год назад

        // Чтобы календарь не казался абсолютно голым, юзер сможет пролистать на месяц вперед и месяц назад
        binding.dateChooserCalendar.state()
            .edit()
            .also { builder ->
                vacancyStartMinusMonth?.let { startDate ->
                    builder.setMinimumDate(
                        CalendarDay.from(
                            startDate.year,
                            startDate.monthValue,
                            startDate.dayOfMonth
                        )
                    )
                }
            }
            .commit()

        ifAllNotNull(vacancyStart, LocalDate.MAX) { start, end ->
            val limitedByTodayPeriod: ClosedRange<LocalDate> = (start..end)/*.limitByToday()*/

            // Добавление недоступных дней (Раскомментить чтобы заблокировать дни [1 из 2])
            val notInRangeDisabler: OutRangeDaysDisabler =
                OutRangeDaysDisabler(limitedByTodayPeriod)

            val inRangeDecorator: InRangeDayDecorator =
                InRangeDayDecorator(limitedByTodayPeriod)

            // Добавление недоступных дней (Раскомментить чтобы заблокировать дни [2 из 2])
            binding.dateChooserCalendar.addDecorators(notInRangeDisabler, inRangeDecorator)
            binding.dateChooserCalendar.addDecorators(inRangeDecorator)
        }

        binding.dateChooserClose
            .onClick
            .observeOn(RxSchedulers.io())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                dismiss()
            }
            .scoped()

        binding.dateChooserSubmit
            .onClick
            .observeOn(RxSchedulers.io())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                val selection = binding.dateChooserCalendar.selectedDates

                val firstDate: LocalDate? = selection.firstOrNull()?.toLocalDate()

                val result = DateChooserResult(
                    fromDate = firstDate
                )

                setFragmentResult(
                    resultKey = request.resultKey,
                    result,
                    _bundler
                )
                dismiss()
            }
            .scoped()
    }

    /**
     * Дизейблит дни, находящиеся вне указанного периода
     **/
    private inner class OutRangeDaysDisabler(
        private val _range: ClosedRange<LocalDate>
    ) : DayViewDecorator {
        override fun decorate(view: DayViewFacade) {
            view.setDaysDisabled(true)
        }

        override fun shouldDecorate(day: CalendarDay): Boolean {
            return !day.isInRange(_range)
        }
    }

    /**
     * Енейблит дни, входящие в указанный период
     * Ставит фон в соответствующем состоянии
     **/
    private inner class InRangeDayDecorator(
        private val _range: ClosedRange<LocalDate>
    ) : DayViewDecorator {
        override fun decorate(view: DayViewFacade) {
            view.setDaysDisabled(false)
            context?.getSupportDrawable(R.drawable.application___multipicker__calendar___day_in_range__decorator)
                ?.let {
                    view.setSelectionDrawable(it)
                }
        }

        override fun shouldDecorate(day: CalendarDay): Boolean {
            return day.isInRange(_range)
        }
    }

    /**
     * CalendarDay == кастомный класс из либы календаря profileInteractive
     **/
    private fun CalendarDay.isInRange(range: ClosedRange<LocalDate>): Boolean =
        isInRange(
            CalendarDay.from(
                range.start.year,
                range.start.month.value,
                range.start.dayOfMonth
            ),
            CalendarDay.from(
                range.endInclusive.year,
                range.endInclusive.month.value,
                range.endInclusive.dayOfMonth
            )
        )

    /**
     * CalendarDay == кастомный класс из либы календаря profileInteractive
     **/
    private fun CalendarDay.toLocalDate(): LocalDate = LocalDate.of(
        this.year,
        this.month,
        this.day
    )
}
