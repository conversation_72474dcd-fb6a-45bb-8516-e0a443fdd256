package ru.dobro

import android.app.Activity
import android.content.Context
import android.util.Log
import android.view.View
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.GlideBuilder
import com.bumptech.glide.annotation.GlideExtension
import com.bumptech.glide.annotation.GlideModule
import com.bumptech.glide.annotation.GlideOption
import com.bumptech.glide.load.engine.cache.InternalCacheDiskCacheFactory
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.module.AppGlideModule
import com.bumptech.glide.request.BaseRequestOptions
import com.bumptech.glide.request.RequestOptions
import common.library.core.data.InformationSize
import common.library.core.data.bytes

val Context.images get() = Glide.with(this)
val Activity.images get() = Glide.with(this)
val FragmentActivity.images get() = Glide.with(this)
val View.images get() = Glide.with(this)
val Fragment.images get() = Glide.with(this)
val RecyclerView.ViewHolder.images get() = Glide.with(itemView)

@GlideModule(glideName = "Images")
class ImagesModule : AppGlideModule() {
    override fun applyOptions(
        context: Context,
        builder: GlideBuilder
    ) {
        builder.setLogLevel(Log.ERROR)
        builder.setDiskCache(InternalCacheDiskCacheFactory(context, _diskCacheSize.bytes.toLong()))
    }

    override fun isManifestParsingEnabled(): Boolean {
        return false
    }

    companion object {
        private val _diskCacheSize: InformationSize = InformationSize.gigabyte
    }
}

@GlideExtension
object GlideExtensions {
    @GlideOption
    @JvmStatic
    fun roundCorners(options: BaseRequestOptions<*>, radiusPx: Int): BaseRequestOptions<*> =
        options.apply(RequestOptions().transform(RoundedCorners(radiusPx)))

    @GlideOption
    @JvmStatic
    fun centerCropWithRoundCorners(
        options: BaseRequestOptions<*>,
        radiusPx: Int
    ): BaseRequestOptions<*> =
        options.apply(
            RequestOptions().transform(
                CenterCrop(),
                RoundedCorners(radiusPx)
            )
        )
}
