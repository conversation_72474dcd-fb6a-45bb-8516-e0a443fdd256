package ru.dobro.check_new_email

import android.os.Bundle
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import common.library.android.getSupportColor
import common.library.android.intent.newTask
import common.library.android.intent.sendTo
import common.library.android.intent.tryRunEmailApp
import common.library.android.intent.tryStartActivityBy
import common.library.android.resource.withOnResumeResourcesScope
import common.library.android.rx.throttleUserInput
import common.library.android.string.span.spannedString
import common.library.android.widget.onClick
import common.library.core.Characters
import common.library.core.email.Email
import common.library.core.ifAllNotNull
import common.library.core.rx.RxSchedulers
import common.library.core.rx.invoke
import io.reactivex.processors.PublishProcessor
import io.reactivex.rxkotlin.subscribeBy
import ru.dobro.R
import ru.dobro.core.BaseFragment
import ru.dobro.core.HasCustomToolbar
import ru.dobro.core.analytic.AnalyticsConstants
import ru.dobro.databinding.FragmentCheckNewEmailBinding

class CheckNewEmailFragment : BaseFragment(), HasCustomToolbar {
    override val screenName: String = AnalyticsConstants.Screen.Login.checkNewEmail

    private lateinit var binding: FragmentCheckNewEmailBinding

    private val _onChooseAnotherEmailClick: PublishProcessor<Unit> = PublishProcessor.create()

    override fun onCreateCustomToolbar(): HasCustomToolbar.CustomToolbar =
        HasCustomToolbar.CustomToolbar.None

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = FragmentCheckNewEmailBinding.inflate(inflater)
        return binding.root
    }

    override fun onResume() = withOnResumeResourcesScope {
        super.onResume()

        binding.performOpenMail
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                tryRunEmailApp(getString(R.string.application___choose__title))
            }
            .scoped()

        binding.close
            .onClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                findNavController().navigateUp()
            }
            .scoped()

        _onChooseAnotherEmailClick
            .observeOn(RxSchedulers.computation())
            .throttleUserInput()
            .observeOn(RxSchedulers.main())
            .subscribeBy {
                tryStartActivityBy {
                    it
                        .sendTo(Email("info", "dobro.ru", Email.CaseSensitivity.None))
                        .newTask()
                }
            }
            .scoped()

        ifAllNotNull(
            context?.getString(R.string.check_new_email___email_didnt_received__not_clickable_part),
            context?.getString(R.string.check_new_email___email_didnt_received__clickable_part)
        ) { notClickablePart, clickablePart ->
            val spannedString = requireContext().spannedString {
                it
                    .append(notClickablePart)
                    .append(Characters.nonBreakingSpace)
                    .append(clickablePart)
            }

            val myClickableSpan: ClickableSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    _onChooseAnotherEmailClick.invoke()
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.isUnderlineText = false
                    ds.color =
                        requireContext().getSupportColor(R.color.application___color__primary)
                }
            }

            spannedString.setSpan(
                myClickableSpan,
                notClickablePart.length + 1,
                spannedString.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            binding.noEmailReceived.apply {
                movementMethod = LinkMovementMethod.getInstance()
                text = spannedString
            }
        }
    }
}
