<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="382dp"
    android:height="104dp"
    android:viewportWidth="382"
    android:viewportHeight="104">
  <path
      android:pathData="M18,0L364,0A18,18 0,0 1,382 18L382,86A18,18 0,0 1,364 104L18,104A18,18 0,0 1,0 86L0,18A18,18 0,0 1,18 0z"
      android:fillColor="#2B1263"/>
  <group>
    <clip-path
        android:pathData="M18,0L364,0A18,18 0,0 1,382 18L382,86A18,18 0,0 1,364 104L18,104A18,18 0,0 1,0 86L0,18A18,18 0,0 1,18 0z"/>
    <path
        android:pathData="M0,0h382v104h-382z"
        android:fillAlpha="0.8">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:centerX="375"
            android:centerY="100"
            android:gradientRadius="166.39"
            android:type="radial">
          <item android:offset="0" android:color="#915C59FF"/>
          <item android:offset="1" android:color="#FF2B1263"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M276,43m-125,0a125,125 0,1 1,250 0a125,125 0,1 1,-250 0"
        android:fillAlpha="0.6">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="364"
            android:startY="138"
            android:endX="261"
            android:endY="2.5"
            android:type="linear">
          <item android:offset="0" android:color="#FF5C59FF"/>
          <item android:offset="1" android:color="#FF2B1263"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M299.5,101.5m-27.5,0a27.5,27.5 0,1 1,55 0a27.5,27.5 0,1 1,-55 0"
        android:strokeAlpha="0.33"
        android:fillColor="#3649F1"
        android:fillAlpha="0.198"/>
  </group>
</vector>
