<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <string name="application___name">Добро.рф</string>
    <string name="application___skip">Пропустить</string>
    <string name="application___later">Позже</string>
    <string name="application___next">Далее</string>
    <string name="application___save">Сохранить</string>
    <string name="application___more">Еще</string>
    <string name="application___done">Готово</string>
    <string name="application___dismiss">Отказаться</string>
    <string name="application___settings">Настройки</string>
    <string name="application___edit">Редактировать</string>
    <string name="application___share">Поделиться</string>
    <string name="application___favorite">Избранное</string>
    <string name="application___close">Закрыть</string>
    <string name="application___ok">OK</string>
    <string name="application___cancel">Отмена</string>
    <string name="application___delete">Удалить</string>
    <string name="application___not_selected">Не выбран</string>
    <string name="application___search">Поиск</string>
    <string name="application___filter">Фильтр</string>
    <string name="application___clear">Сбросить</string>
    <string name="application___yes">Да</string>
    <string name="application___no">Нет</string>
    <string name="application___choose__title">Открыть в</string>

    <string name="application___share_text">Рекомендую “Добро.рф”</string>

    <string name="application___contacts">Контакты</string>
    <string name="application___main_information">Основная информация</string>
    <string name="application___description">Описание</string>
    <string name="application___analytics">Аналитика организации</string>
    <string name="application___network_analytics">Аналитика сети организаций</string>
    <string name="application___expand">Показать полностью</string>
    <string name="application___show_all">Смотреть всё</string>
    <string name="application___select_all">Выбрать всё</string>
    <string name="application___select">Выбрать</string>
    <string name="application___filters">Фильтры</string>

    <string name="application___error__no_connection">Отсутствует соединение с интернетом</string>
    <string name="application___error__unknown">Произошла непредвиденная ошибка</string>
    <string name="application___error__no_access">Отказано в доступе</string>
    <string name="application___error__unsupported_file_format">Неподдерживаемый формат файла</string>
    <string name="application___error__file_not_selected">Файл не выбран</string>

    <string name="application___validation__help">* Обязательное поле</string>
    <string name="application___validation_error__empty">Обязательное поле</string>
    <string name="application___validation_error__email__invalid">Неверный формат</string>

    <string name="application___validation_error__name__invalid_format">Поле может содержать только русские буквы, пробел и дефис</string>

    <string name="application___validation_error__name__too_short">Имя пользователя должно быть от 2 до 16 символов</string>

    <string name="application___validation_error__surname__invalid_format">Поле должно содержать только русские буквы, дефис и пробел</string>

    <string name="application___validation_error__surname__too_short">Фамилия пользователя должна содержать от 1 до 16 символов</string>

    <string name="application___validation_error__password__too_short">Пароль должен содержать не менее 6 символов</string>

    <string name="application___validation_error__phone__empty">Обязательное поле</string>
    <string name="application___validation_error__phone__invalid">Неверный формат</string>
    <string name="application___validation_error__phone__too_short">Номер должен быть в формате\n+7 (000) 000–00–00</string>

    <string name="application___validation_error__person_id__invalid_format">ИНН должен содержать только цифры</string>
    <string name="application___validation_error__person_id__invalid_length">дли ИНН должна быть 10 или 12 символов</string>
    <string name="application___validation_error__person_id__invalid_checksum">ИНН недействительный, неверная контрольная сумма</string>

    <string name="application___agreement">https://dobro.ru/volunteers/98503329/resume/print</string>
    <string name="application___consent_of_personal_data_processing">https://dobro.ru/docs/consentToTheProcessingOfPersonalDataProcessing.pdf</string>
    <string name="application___consent_of_personal_data_processing_underaged">https://dobro.ru/docs/consentToTheProcessingOfPersonalDataProcessingMinor.pdf</string>

    <string name="main__map">Карта</string>
    <string name="main__list">Список</string>

    <array name="application__months">
        <item>Январь</item>
        <item>Февраль</item>
        <item>Март</item>
        <item>Апрель</item>
        <item>Май</item>
        <item>Июнь</item>
        <item>Июль</item>
        <item>Август</item>
        <item>Сентябрь</item>
        <item>Октябрь</item>
        <item>Ноябрь</item>
        <item>Декабрь</item>
    </array>

    <plurals name="application__volunteer">
        <item quantity="zero">%1$d волонтеров</item>
        <item quantity="one">%1$d волонтер</item>
        <item quantity="two">%1$d волонтера</item>
        <item quantity="few">%1$d волонтера</item>
        <item quantity="many">%1$d волонтеров</item>
        <item quantity="other">%1$d волонтеров</item>
    </plurals>

    <plurals name="application__results">
        <item quantity="zero">%1$d результатов</item>
        <item quantity="one">%1$d результатат</item>
        <item quantity="two">%1$d результата</item>
        <item quantity="few">%1$d результата</item>
        <item quantity="many">%1$d результатов</item>
        <item quantity="other">%1$d результатов</item>
    </plurals>

    <plurals name="application__hours">
        <item quantity="zero">%1$d часов</item>
        <item quantity="one">%1$d час</item>
        <item quantity="two">%1$d часа</item>
        <item quantity="few">%1$d часа</item>
        <item quantity="many">%1$d часов</item>
        <item quantity="other">%1$d часов</item>
    </plurals>

    <plurals name="application__minutes">
        <item quantity="zero">%1$d минут</item>
        <item quantity="one">%1$d минута</item>
        <item quantity="two">%1$d минуты</item>
        <item quantity="few">%1$d минуты</item>
        <item quantity="many">%1$d минут</item>
        <item quantity="other">%1$d минут</item>
    </plurals>

    <plurals name="application__statuses">
        <item quantity="zero">%1$d заявок</item>
        <item quantity="one">%1$d заявку</item>
        <item quantity="two">%1$d заявки</item>
        <item quantity="few">%1$d заявки</item>
        <item quantity="many">%1$d заявок</item>
        <item quantity="other">%1$d заявки</item>
    </plurals>

    <array name="application__weekdays">
        <item>Пн</item>
        <item>Вт</item>
        <item>Ср</item>
        <item>Чт</item>
        <item>Пт</item>
        <item>Сб</item>
        <item>Вс</item>
    </array>

    <style name="Application.Theme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorAccent">@color/application___color__accent</item>
        <item name="colorPrimary">@color/application___color__primary</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="android:colorPrimaryDark">@color/application___color__primary</item>
        <item name="colorSecondary">@color/application___color__secondary</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="colorError">@color/application___color__accent</item>
        <item name="android:textColorSecondary">@color/application___color__primary</item>
        <item name="android:windowBackground">@color/application___color__shade</item>
        <item name="materialAlertDialogTheme">@style/Application.MaterialAlertDialog</item>
        <!--// возможно, надо вынести в отдельный тип-->
        <item name="actionMenuTextAppearance">@style/Application.Design.Subtitle2.TextAppearance
        </item>
        <item name="actionMenuTextColor">@color/application___accent_color__selector</item>
        <item name="android:statusBarColor">@color/application___color_general_background</item>
        <item name="android:windowLayoutInDisplayCutoutMode" tools:ignore="NewApi">shortEdges</item>
    </style>

    <style name="Application.Theme.Splash">
        <item name="android:windowBackground">@color/application___color__shade</item>
    </style>

    <color name="application___color__accent">#F94937</color>
    <color name="application___color__accent_background">#FAEBEB</color>
    <color name="application___color__accent_variant">#FF3B30</color>
    <color name="application___color__primary">#540575</color>
    <color name="application___color__primary_shade">#F3EEF5</color>
    <color name="application___color__primary__10">#1A540575</color>
    <color name="application___color__secondary">#2ECC71</color>
    <color name="application___color__secondary_variant">#34C759</color>
    <color name="application___color__tertiary">#FFBB12</color>
    <color name="application___color__orange">#EF9252</color>
    <color name="application___color__orange_variant">#FF6614</color>
    <color name="application___color__orange_variant_2">#FFE6D8</color>
    <color name="application___color__orange_44">#70FFCC80</color>
    <color name="application___color__violet">#C43484</color>
    <color name="application___color__main">#0C1014</color>
    <color name="application___color__shade">#F6F8FB</color>
    <color name="application___color__icons">#B4BBC6</color>
    <color name="application___color__icons_20">#33B4BBC6</color>
    <color name="application___color__icons_dark">#878787</color>
    <color name="application___color__gray__12">#1F767680</color>
    <color name="application___color__gray__60">#993C3C43</color>
    <color name="application___color__button__gray">#F4F4F5</color>
    <color name="application___color__button__gray__text">#86868A</color>
    <color name="application___color__button__gray__60">#3386868A</color>
    <color name="application___color__button__gray__15">#2686868A</color>
    <color name="application___color__clickable_text">#636364</color>
    <color name="application___color__indigo">#5856D6</color>
    <color name="application___color__green">#2DA771</color>
    <color name="application___color__green_variant">#1FAF93</color>
    <color name="application___color__yellow">#FFC801</color>
    <color name="application___color__honorary_donor">#FFF6E0</color>
    <color name="application___color__esia">#0D4CD3</color>
    <color name="application___color__blue">#0029FF</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <color name="white_80">#CCFFFFFF</color>
    <color name="application___color__search">#F2F2F7</color>
    <color name="application___color_general_background">#F7F8FC</color>
    <color name="onboarding_download_default">#E8E8F0</color>
    <color name="onboarding_download_selected">#E2E2FA</color>

    <!--Значение обязано соответствовать множителю в имени, чтобы выполнялся распределительный закон, т.е. application___dimen__xA + application___dimen__xB = application___dimen__x[A+B].-->
    <dimen name="application___dimen__x0">0dp</dimen>
    <dimen name="application___dimen__x1">4dp</dimen>
    <dimen name="application___dimen__x2">8dp</dimen>
    <dimen name="application___dimen__x3">12dp</dimen>
    <dimen name="application___dimen__x4">16dp</dimen>
    <dimen name="application___dimen__x5">20dp</dimen>
    <dimen name="application___dimen__x6">24dp</dimen>
    <dimen name="application___dimen__x7">28dp</dimen>
    <dimen name="application___dimen__x8">32dp</dimen>
    <dimen name="application___dimen__x9">36dp</dimen>
    <dimen name="application___dimen__x10">40dp</dimen>
    <dimen name="application___dimen__x11">44dp</dimen>
    <dimen name="application___dimen__x12">48dp</dimen>
    <dimen name="application___dimen__x13">52dp</dimen>
    <dimen name="application___dimen__x14">56dp</dimen>
    <dimen name="application___dimen__x15">60dp</dimen>
    <dimen name="application___dimen__x16">64dp</dimen>
    <dimen name="application___dimen__x17">68dp</dimen>
    <dimen name="application___dimen__x18">72dp</dimen>
    <dimen name="application___dimen__x19">76dp</dimen>
    <dimen name="application___dimen__x20">80dp</dimen>
    <dimen name="application___dimen__x21">84dp</dimen>
    <dimen name="application___dimen__x22">88dp</dimen>
    <dimen name="application___dimen__x23">92dp</dimen>
    <dimen name="application___dimen__x24">96dp</dimen>
    <dimen name="application___dimen__x25">100dp</dimen>
    <dimen name="application___dimen__x26">104dp</dimen>


    <dimen name="application___dimen__x50">200dp</dimen>


    <dimen name="application___dimen_scaled__x0">0sp</dimen>
    <dimen name="application___dimen_scaled__x1">4sp</dimen>
    <dimen name="application___dimen_scaled__x2">8sp</dimen>
    <dimen name="application___dimen_scaled__x3">12sp</dimen>
    <dimen name="application___dimen_scaled__x4">16sp</dimen>
    <dimen name="application___dimen_scaled__x5">20sp</dimen>
    <dimen name="application___dimen_scaled__x6">24sp</dimen>
    <dimen name="application___dimen_scaled__x7">28sp</dimen>
    <dimen name="application___dimen_scaled__x8">32sp</dimen>
    <dimen name="application___dimen_scaled__x9">36sp</dimen>
    <dimen name="application___dimen_scaled__x10">40sp</dimen>

    <style name="Application.Design.H1.TextAppearance" parent="TextAppearance.MaterialComponents.Headline1">
        <item name="android:fontFamily">@font/euclid_circular_b_semi_bold</item>
        <item name="fontFamily">@font/euclid_circular_b_semi_bold</item>
    </style>

    <style name="Application.Design.H2.TextAppearance" parent="TextAppearance.MaterialComponents.Headline2">
        <item name="android:fontFamily">@font/euclid_circular_b_semi_bold</item>
        <item name="fontFamily">@font/euclid_circular_b_semi_bold</item>
    </style>

    <style name="Application.Design.H3.TextAppearance" parent="TextAppearance.MaterialComponents.Headline3">
        <item name="android:fontFamily">@font/euclid_circular_b_semi_bold</item>
        <item name="fontFamily">@font/euclid_circular_b_semi_bold</item>
    </style>

    <style name="Application.Design.H4.TextAppearance" parent="TextAppearance.MaterialComponents.Headline4">
        <item name="android:fontFamily">@font/euclid_circular_b_semi_bold</item>
        <item name="fontFamily">@font/euclid_circular_b_semi_bold</item>
    </style>

    <style name="Application.Design.H5.TextAppearance" parent="TextAppearance.MaterialComponents.Headline5">
        <item name="android:fontFamily">@font/euclid_circular_b_semi_bold</item>
        <item name="fontFamily">@font/euclid_circular_b_semi_bold</item>
    </style>

    <style name="Application.Design.H5.TextAppearance.Bigger" parent="TextAppearance.MaterialComponents.Headline5">
        <item name="android:fontFamily">@font/euclid_circular_b_semi_bold</item>
        <item name="fontFamily">@font/euclid_circular_b_semi_bold</item>
        <item name="android:textSize">30sp</item>
    </style>

    <style name="Application.Design.H6.TextAppearance" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:fontFamily">@font/euclid_circular_b_semi_bold</item>
        <item name="fontFamily">@font/euclid_circular_b_semi_bold</item>
    </style>

    <style name="Application.Design.H7.TextAppearance" parent="">
        <item name="android:fontFamily">@font/euclid_circular_b_semi_bold</item>
        <item name="fontFamily">@font/euclid_circular_b_semi_bold</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="Application.Design.Body1.TextAppearance" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:fontFamily">@font/euclid_circular_b_regular</item>
        <item name="fontFamily">@font/euclid_circular_b_regular</item>
    </style>

    <style name="Application.Design.Body2.TextAppearance" parent="TextAppearance.MaterialComponents.Body2">
        <item name="android:fontFamily">@font/euclid_circular_b_regular</item>
        <item name="fontFamily">@font/euclid_circular_b_regular</item>
    </style>

    <style name="Application.Design.Subtitle2.TextAppearance" parent="TextAppearance.MaterialComponents.Subtitle2">
        <item name="android:fontFamily">@font/euclid_circular_b_regular</item>
        <item name="fontFamily">@font/euclid_circular_b_regular</item>
    </style>

    <style name="Application.Design.Semibold.TextAppearance" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:fontFamily">@font/euclid_circular_b_medium</item>
        <item name="fontFamily">@font/euclid_circular_b_medium</item>
        <item name="android:textSize">15sp</item>
    </style>

    <style name="Application.Design.Button.TextAppearance" parent="TextAppearance.MaterialComponents.Button">
        <item name="android:fontFamily">@font/euclid_circular_b_semi_bold</item>
        <item name="fontFamily">@font/euclid_circular_b_semi_bold</item>
        <item name="textAllCaps">false</item>
        <item name="android:letterSpacing">0.007</item>
    </style>

    <style name="Application.Design.Button.Small.TextAppearance" parent="TextAppearance.MaterialComponents.Button">
        <item name="android:fontFamily">@font/euclid_circular_b_regular</item>
        <item name="fontFamily">@font/euclid_circular_b_regular</item>
        <item name="textAllCaps">false</item>
        <item name="android:letterSpacing">0.041</item>
    </style>

    <style name="Application.Design.Caption.TextAppearance" parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:fontFamily">@font/euclid_circular_b_regular</item>
        <item name="fontFamily">@font/euclid_circular_b_regular</item>
    </style>

    <style name="Application.Design.H7.TextAppearance.Black">
        <item name="android:textColor">@color/application___color__main</item>
    </style>

    <style name="Application.Design.H7.TextAppearance.White">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="Application.Design.H7.TextAppearance.Green">
        <item name="android:textColor">@color/application___color__green</item>
    </style>

    <style name="Application.Design.H7.TextAppearance.Yellow">
        <item name="android:textColor">@color/application___color__yellow</item>
    </style>

    <style name="Application.Design.H7.TextAppearance.Gray">
        <item name="android:textColor">@color/application___color__icons</item>
    </style>

    <style name="Application.Design.H6.TextAppearance.Black">
        <item name="android:textColor">@color/application___color__main</item>
    </style>

    <style name="Application.Design.H6.TextAppearance.Primary">
        <item name="android:textColor">@color/application___color__primary</item>
    </style>

    <style name="Application.Design.H6.TextAppearance.White">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="Application.Design.H6.TextAppearance.Gray">
        <item name="android:textColor">@color/application___color__icons</item>
    </style>

    <style name="Application.Design.H5.TextAppearance.Black">
        <item name="android:textColor">@color/application___color__main</item>
    </style>

    <style name="Application.Design.H5.TextAppearance.White">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="Application.Design.H5.TextAppearance.Gray">
        <item name="android:textColor">@color/application___color__icons</item>
    </style>

    <style name="Application.Design.H5.TextAppearance.Primary">
        <item name="android:textColor">@color/application___color__primary</item>
    </style>

    <style name="Application.Design.H4.TextAppearance.Black">
        <item name="android:textColor">@color/application___color__main</item>
    </style>

    <style name="Application.Design.H4.TextAppearance.Gray">
        <item name="android:textColor">@color/application___color__icons</item>
    </style>

    <style name="Application.Design.Body1.TextAppearance.White">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="Application.Design.Body1.TextAppearance.Black">
        <item name="android:textColor">@color/application___color__main</item>
    </style>

    <style name="Application.Design.Body1.TextAppearance.Gray">
        <item name="android:textColor">@color/application___color__icons</item>
    </style>

    <style name="Application.Design.Body1.TextAppearance.DarkGray">
        <item name="android:textColor">@color/main__requests___icons</item>
    </style>

    <style name="Application.Design.Body1.TextAppearance.Primary">
        <item name="android:textColor">@color/application___color__primary</item>
    </style>

    <style name="Application.Design.Body1.TextAppearance.Red">
        <item name="android:textColor">@color/application___color__accent_variant</item>
    </style>

    <style name="Application.Design.Body2.TextAppearance.Black">
        <item name="android:textColor">@color/application___color__main</item>
    </style>

    <style name="Application.Design.Body2.TextAppearance.Primary">
        <item name="android:textColor">@color/application___color__primary</item>
    </style>

    <style name="Application.Design.Body2.TextAppearance.Gray">
        <item name="android:textColor">@color/application___color__icons</item>
    </style>

    <style name="Application.Design.Body2.TextAppearance.DarkGray">
        <item name="android:textColor">@color/main__requests___icons</item>
    </style>

    <style name="Application.Design.Body2.TextAppearance.White">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="Application.Design.BottomMenu.TextAppearance" parent="TextAppearance.MaterialComponents.Body2">
        <item name="android:textColor">@color/application___color__icons</item>
        <item name="android:textSize">10sp</item>
    </style>

    <style name="Application.Design.Button.TextAppearance.Black">
        <item name="android:textColor">@color/application___color__main</item>
    </style>

    <style name="Application.Design.Button.TextAppearance.Primary">
        <item name="android:textColor">@color/application___primary_color__selector</item>
    </style>

    <style name="Application.Design.Subtitle2.TextAppearance.Black">
        <item name="android:textColor">@color/application___color__main</item>
    </style>

    <style name="Application.Design.Subtitle2.TextAppearance.Gray">
        <item name="android:textColor">@color/application___color__icons</item>
    </style>

    <style name="Application.Design.Subtitle2.TextAppearance.Primary">
        <item name="android:textColor">@color/application___primary_color__selector</item>
    </style>

    <style name="Application.Design.Caption.TextAppearance.White">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="Application.Design.Caption.TextAppearance.Gray">
        <item name="android:textColor">@color/application___color__icons</item>
    </style>

    <style name="Application.Design.Caption.TextAppearance.Black">
        <item name="android:textColor">@color/application___color__main</item>
    </style>

    <style name="Application.Design.Caption.TextAppearance.Primary">
        <item name="android:textColor">@color/application___color__primary</item>
    </style>

    <style name="Application.Design.Input" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
        <item name="android:textColor">@color/application___color__main</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/application___dimen__x3</item>
        <item name="boxCornerRadiusBottomStart">@dimen/application___dimen__x3</item>
        <item name="boxCornerRadiusTopEnd">@dimen/application___dimen__x3</item>
        <item name="boxCornerRadiusTopStart">@dimen/application___dimen__x3</item>
        <item name="boxBackgroundColor">@android:color/white</item>
        <item name="boxStrokeWidth">0dp</item>
        <item name="android:textColorHint">@color/application___color__icons</item>
        <item name="hintTextColor">@color/application___color__icons</item>
        <item name="hintTextAppearance">@style/Application.Design.Body1.TextAppearance</item>
        <item name="errorTextAppearance">@style/Application.Design.Caption.TextAppearance</item>
        <item name="errorTextColor">@color/application___color__accent</item>
        <item name="android:textAppearance">@style/Application.Design.Body1.TextAppearance</item>
        <item name="startIconTint">@color/application___color__icons</item>
        <item name="endIconTint">@color/application___color__icons</item>
        <item name="helperTextTextAppearance">@style/Application.Design.Caption.TextAppearance
        </item>
        <item name="helperTextTextColor">@color/application___color__icons</item>
        <item name="materialThemeOverlay">
            @style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox
        </item>
    </style>

    <style name="Application.Design.Input.Empty" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
        <item name="android:textColor">@color/application___color__main</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/application___dimen__x3</item>
        <item name="boxCornerRadiusBottomStart">@dimen/application___dimen__x3</item>
        <item name="boxCornerRadiusTopEnd">@dimen/application___dimen__x3</item>
        <item name="boxCornerRadiusTopStart">@dimen/application___dimen__x3</item>
        <item name="boxBackgroundColor">@android:color/white</item>
        <item name="boxStrokeWidth">0dp</item>
        <item name="boxStrokeColor">@color/transparent</item>
        <item name="android:textColorHint">@color/application___color__icons</item>
        <item name="hintTextColor">@color/application___color__icons</item>
        <item name="hintTextAppearance">@style/Application.Design.Body1.TextAppearance</item>
        <item name="errorTextAppearance">@style/Application.Design.Caption.TextAppearance</item>
        <item name="errorTextColor">@color/application___color__accent</item>
        <item name="android:textAppearance">@style/Application.Design.Body1.TextAppearance</item>
        <item name="startIconTint">@color/application___color__icons</item>
        <item name="endIconTint">@color/application___color__icons</item>
        <item name="helperTextTextAppearance">@style/Application.Design.Caption.TextAppearance
        </item>
        <item name="helperTextTextColor">@color/application___color__icons</item>
    </style>

    <style name="Application.Design.InputFilled" parent="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense">
        <item name="android:layout_marginBottom">@dimen/application___dimen__x3</item>
        <item name="android:textColor">@color/application___color__main</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/application___dimen__x3</item>
        <item name="boxCornerRadiusBottomStart">@dimen/application___dimen__x3</item>
        <item name="boxCornerRadiusTopEnd">@dimen/application___dimen__x3</item>
        <item name="boxCornerRadiusTopStart">@dimen/application___dimen__x3</item>
        <item name="boxBackgroundColor">@color/white</item>
        <item name="boxStrokeWidth">0dp</item>
        <item name="android:textColorHint">@color/application___color__icons</item>
        <item name="hintTextColor">@color/application___color__icons</item>
        <item name="boxStrokeWidthFocused">0dp</item>
        <item name="errorTextAppearance">@style/Application.Design.Caption.TextAppearance</item>
        <item name="errorTextColor">@color/application___color__accent</item>
        <item name="android:textAppearance">@style/Application.Design.Body1.TextAppearance
        </item>
        <item name="startIconTint">@color/application___color__icons</item>
        <item name="endIconTint">@color/application___color__icons</item>
        <item name="helperTextTextAppearance">@style/Application.Design.Caption.TextAppearance
        </item>
        <item name="helperTextTextColor">@color/application___color__icons</item>
        <item name="counterTextAppearance">@style/Application.Design.Caption.TextAppearance</item>
        <item name="counterTextColor">@color/application___color__icons</item>
        <item name="materialThemeOverlay">
            @style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox
        </item>
    </style>

    <style name="Application.Design.InputFilled.Shade">
        <item name="boxBackgroundColor">@color/application___color__shade</item>
    </style>

    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox">
        <item name="editTextStyle">@style/Application.EditText</item>
    </style>

    <style name="Application.EditText" parent="@style/Widget.MaterialComponents.TextInputEditText.FilledBox">
        <item name="android:textAppearance">@style/Application.Design.Body1.TextAppearance</item>
    </style>

    <style name="Application.EditText.Clickable" parent="">
        <item name="android:focusable">false</item>
        <item name="android:focusableInTouchMode">false</item>
        <item name="android:cursorVisible">false</item>
        <item name="android:clickable">true</item>
        <item name="android:foreground">@drawable/application___ripple</item>
    </style>

    <style name="Application.Design.Button" parent="Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:paddingVertical">@dimen/application___dimen__x3</item>
        <item name="cornerRadius">@dimen/application___dimen__x3</item>
        <item name="iconGravity">textStart</item>

        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:insetLeft">0dp</item>
        <item name="android:insetRight">0dp</item>

        <item name="android:minHeight">48dp</item>
        <item name="android:minWidth">48dp</item>

        <item name="android:textAppearance">@style/Application.Design.Button.TextAppearance</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Application.Design.Button.Small" parent="Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:paddingVertical">@dimen/application___dimen__x2</item>
        <item name="cornerRadius">@dimen/application___dimen__x2</item>
        <item name="iconGravity">textStart</item>

        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:insetLeft">0dp</item>
        <item name="android:insetRight">0dp</item>

        <item name="android:minHeight">38dp</item>
        <item name="android:minWidth">48dp</item>

        <item name="android:textAppearance">@style/Application.Design.Button.Small.TextAppearance</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Application.Design.Button.Primary">
        <item name="android:backgroundTint">@color/application___primary_color__selector</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="Application.Design.Button.Primary.BackgroundTint">
        <item name="android:backgroundTint">@color/application___primary_color__selector_background</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="Application.Design.Button.Shade">
        <item name="android:backgroundTint">@color/application___color__shade</item>
        <item name="android:textColor">@color/application___color__primary</item>
        <item name="iconTint">@color/application___color__primary</item>
        <item name="android:paddingVertical">@dimen/application___dimen__x1</item>
        <item name="rippleColor">@color/application___color__icons</item>
        <item name="iconGravity">textTop</item>
    </style>

    <style name="Application.Design.Button.White">
        <item name="android:backgroundTint">@color/white</item>
        <item name="android:textColor">@color/application___color__primary</item>
        <item name="iconTint">@color/application___color__primary</item>
        <item name="android:paddingVertical">@dimen/application___dimen__x1</item>
        <item name="rippleColor">@color/application___color__icons</item>
        <item name="iconGravity">textTop</item>
    </style>

    <style name="Application.Design.Button.Gray">
        <item name="android:backgroundTint">@color/application___color__button__gray</item>
        <item name="android:textColor">@color/application___color__button__gray__text</item>
        <item name="iconTint">@color/application___color__primary</item>
        <item name="android:paddingVertical">@dimen/application___dimen__x1</item>
        <item name="rippleColor">@color/application___color__icons</item>
        <item name="iconGravity">textTop</item>
    </style>

    <style name="Application.Design.Button.Small.Gray">
        <item name="android:backgroundTint">@color/application___color__button__gray</item>
        <item name="android:textColor">@color/application___color__button__gray__text</item>
        <item name="iconTint">@color/application___color__primary</item>
        <item name="android:paddingVertical">@dimen/application___dimen__x1</item>
        <item name="android:paddingHorizontal">@dimen/application___dimen__x3</item>
        <item name="rippleColor">@color/application___color__icons</item>
        <item name="iconGravity">textTop</item>
    </style>

    <style name="Application.Design.Button.Small.Shade">
        <item name="android:backgroundTint">#E1E1EC</item>
        <item name="android:textColor">@color/application___color__primary</item>
        <item name="iconTint">@color/application___color__primary</item>
        <item name="android:paddingVertical">@dimen/application___dimen__x1</item>
        <item name="android:paddingHorizontal">@dimen/application___dimen__x3</item>
        <item name="rippleColor">@color/application___color__icons</item>
        <item name="iconGravity">textTop</item>
    </style>

    <style name="Application.Design.Button.Dismiss">
        <item name="android:backgroundTint">@color/white</item>
        <item name="android:textColor">@color/application___color__accent</item>
        <item name="rippleColor">@color/application___color__icons</item>
    </style>

    <style name="Application.Design.Button.OutlinedButton" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="cornerRadius">@dimen/application___dimen__x3</item>
        <item name="strokeColor">@color/application___color__primary</item>
        <item name="android:textAppearance">@style/Application.Design.Button.TextAppearance
        </item>
    </style>

    <style name="Application.Design.Button.OutlinedButton.Orange" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="cornerRadius">@dimen/application___dimen__x3</item>
        <item name="strokeColor">@color/application___color__orange</item>
        <item name="android:textAppearance">@style/Application.Design.Button.TextAppearance</item>
        <item name="android:textColor">@color/application___color__orange</item>
    </style>

    <style name="Application.Design.Button.OutlinedButton.Grey" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="cornerRadius">@dimen/application___dimen__x3</item>
        <item name="strokeColor">@color/application___color__icons</item>
        <item name="android:textAppearance">@style/Application.Design.Button.TextAppearance
        </item>
    </style>

    <style name="Application.Design.Button.Text" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:textAppearance">@style/Application.Design.Button.TextAppearance
        </item>
    </style>


    <style name="StatusCheckBoxText" parent="Application.CheckBox">
        <item name="android:textSize">15sp</item>
        <item name="android:background"> @drawable/application___background__circle</item>
        <item name="android:backgroundTint"> @color/application___color__button__gray__15</item>
    </style>

    <style name="Application.Design.Button.Text.Primary">
        <item name="android:textColor">@color/application___primary_color__selector</item>
    </style>

    <style name="Application.Design.Button.Text.Gray">
        <item name="android:textColor">@color/application___color__icons</item>
    </style>

    <style name="Application.Design.Button.TextIcon">
        <item name="android:backgroundTint">@color/application___color__shade</item>
        <item name="android:textColor">@color/application___color__primary</item>
        <item name="iconTint">@color/application___color__primary</item>
        <item name="android:gravity">start|center_vertical</item>
        <item name="iconGravity">start</item>
        <item name="rippleColor">@color/application___color__icons</item>
        <item name="android:paddingHorizontal">0dp</item>
        <item name="android:textAppearance">@style/Application.Design.Body1.TextAppearance
        </item>
    </style>

    <style name="Application.Design.Button.TextIcon.Orange">
        <item name="iconTint">@color/application___color__orange</item>
        <item name="android:textColor">@color/application___color__orange</item>
        <item name="android:textAppearance">@style/Application.Design.Body1.TextAppearance</item>
    </style>

    <style name="Application.Divider" parent="">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">@color/application___color__icons</item>
    </style>

    <style name="Application.Divider.Thin" parent="">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0.5dp</item>
        <item name="android:background">#C6C6C8</item>
    </style>

    <style name="Application.Divider.Wide" parent="">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/application___dimen__x2</item>
        <item name="android:background">@color/application___color__shade</item>
    </style>

    <style name="Application.Progress" parent="Widget.MaterialComponents.CircularProgressIndicator">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:indeterminate">true</item>
        <item name="android:visibility">gone</item>
        <item name="indicatorColor">@color/application___color__primary</item>
    </style>

    <style name="Application.Counter" parent="">
        <item name="android:includeFontPadding">false</item>

        <item name="android:textColor">@color/white</item>
        <item name="android:textAlignment">gravity</item>
        <item name="android:gravity">center</item>
        <item name="android:lines">1</item>
        <item name="android:ellipsize">none</item>

        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>

        <item name="android:textAppearance">
            @style/Application.Design.Subtitle2.TextAppearance
        </item>

        <item name="android:background">
            @drawable/application___background__circle
        </item>
        <item name="backgroundTint">@color/application___color__primary</item>
    </style>

    <style name="Application.Counter.Light" parent="Application.Counter">
        <item name="android:textColor">@color/application___color__primary</item>

        <item name="backgroundTint">#EAE5F6</item>
    </style>

    <style name="Application.Toolbar.Light.ThemeOverlay" parent="">
        <item name="colorControlNormal">@color/white</item>
    </style>

    <style name="Application.Switch" parent="Widget.AppCompat.CompoundButton.Switch">
        <item name="thumbTint">@color/application___switch__thumb_selector</item>
        <item name="trackTint">@color/application___switch__track_selector</item>
    </style>

    <attr name="application___bottom_sheet__background__color" format="color|reference"/>

    <style name="Application.BottomSheetStyle" parent="Widget.MaterialComponents.BottomSheet">
        <item name="backgroundTint">@null</item>

        <item name="android:background">@drawable/application___bottom_sheet__background</item>

        <item name="shapeAppearance">@null</item>
        <item name="shapeAppearanceOverlay">@null</item>

        <item name="android:windowIsFloating">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>

    <style name="Application.BottomSheet" parent="Theme.MaterialComponents.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/Application.BottomSheetStyle</item>

        <item name="application___bottom_sheet__background__color">@color/white</item>
    </style>

    <style name="Application.BottomSheet.Shade" parent="Theme.MaterialComponents.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/Application.BottomSheetStyle</item>

        <item name="application___bottom_sheet__background__color">@color/application___color__shade</item>
    </style>

    <style name="Application.CheckBox" parent="">
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>

        <item name="android:background">@null</item>
        <item name="android:foreground">@null</item>

        <item name="buttonTint">@color/application___checkbox__tint</item>
    </style>

    <style name="Application.MaterialAlertDialog" parent="ThemeOverlay.MaterialComponents.MaterialAlertDialog">
        <item name="materialAlertDialogTitleTextStyle">
            @style/Application.MaterialAlertDialog.Title
        </item>
        <item name="materialAlertDialogBodyTextStyle">
            @style/Application.Design.Body1.TextAppearance.Black
        </item>
        <item name="buttonBarPositiveButtonStyle">
            @style/Application.MaterialAlertDialog.ButtonStyle.Positive
        </item>
        <item name="buttonBarNegativeButtonStyle">
            @style/Application.MaterialAlertDialog.ButtonStyle.Positive
        </item>
    </style>

    <style name="Application.MaterialAlertDialog.Remove">
        <item name="buttonBarPositiveButtonStyle">
            @style/Application.MaterialAlertDialog.ButtonStyle.Remove
        </item>
    </style>

    <style name="Application.MaterialAlertDialog.Title" parent="@style/MaterialAlertDialog.MaterialComponents.Title.Text">
        <item name="android:textAppearance">@style/Application.Design.H6.TextAppearance.Black</item>
    </style>

    <style name="Application.MaterialAlertDialog.ButtonStyle" parent="@style/Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="textAllCaps">false</item>
        <item name="android:textAppearance">@style/Application.Design.Button.TextAppearance</item>
        <item name="android:paddingVertical">@dimen/application___dimen__x3</item>
        <item name="android:paddingHorizontal">@dimen/application___dimen__x3</item>
        <item name="cornerRadius">@dimen/application___dimen__x3</item>
    </style>

    <style name="Application.MaterialAlertDialog.ButtonStyle.Positive">
        <item name="android:textColor">@color/application___color__primary</item>
        <item name="backgroundTint">@color/white</item>
    </style>

    <style name="Application.MaterialAlertDialog.ButtonStyle.Remove">
        <item name="android:textColor">@color/application___color__accent</item>
        <item name="backgroundTint">@color/white</item>
    </style>

    <style name="RatingBar" parent="Theme.AppCompat">
        <item name="colorControlNormal">@color/application___color__icons</item>
        <item name="colorControlActivated">@color/application___color__tertiary</item>
    </style>

    <declare-styleable name="MaskedEditText">
        <attr name="mask" format="string"/>
        <attr name="allowed_chars" format="string"/>
        <attr name="denied_chars" format="string"/>
        <attr name="char_representation" format="string"/>
        <attr name="keep_hint" format="boolean"/>
    </declare-styleable>

    <style name="roundedImageView">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <style name="rounded12ImageView">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">12%</item>
    </style>

    <style name="rounded10ImageView">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">10%</item>
    </style>

    <style name="rounded9ImageView">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">9%</item>
    </style>

    <style name="roundedBottomImageView">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeBottomLeft">18dp</item>
        <item name="cornerSizeBottomRight">18dp</item>
    </style>

    <style name="Application.NumberPickerTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorControlNormal">@color/application___color__primary</item>
        <item name="android:textColorPrimary">@color/application___color__primary</item>
        <item name="android:textSize">17sp</item>
    </style>

    <style name="RoundedAlertTheme" parent="@style/Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:windowBackground">@drawable/alert_dialog_corner</item>
    </style>

</resources>