<?xml version="1.0" encoding="utf-8"?>
<navigation
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/login_navigation"
    app:startDestination="@id/login__onboarding">

    <fragment
        android:id="@+id/login__onboarding"
        android:name="ru.dobro.login.onboarding.OnboardingFragment"
        android:label=""
        tools:layout="@layout/login__onboarding">

        <action
            android:id="@+id/login__onboarding__to__authorization"
            app:destination="@+id/authorization"/>

    </fragment>

    <dialog
        android:id="@+id/unauthorizedErrorFragment"
        android:name="ru.dobro.errors.unauthorized.UnauthorizedErrorFragment"
        android:label=""
        tools:layout="@layout/fragment__unauthorized_error"/>
</navigation>