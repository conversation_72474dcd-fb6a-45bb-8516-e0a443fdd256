<?xml version="1.0" encoding="utf-8"?>
<navigation
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_navigation"
    app:startDestination="@id/stubFragment">

    <fragment
        android:id="@+id/stubFragment"
        android:name="ru.dobro.common.LoaderFullscreenFragment"
        android:label=""
        tools:layout="@layout/fragment_loader_fullscreen"/>

    <fragment
        android:id="@+id/main__overview"
        android:name="ru.dobro.main.OverviewFragment"
        android:label=""
        tools:layout="@layout/fragment_overview">

        <action
            android:id="@+id/action_main__overview_to_dobro_centers"
            app:destination="@id/main__dobro_center"/>

        <action
            android:id="@+id/main__overview__to__main__tasks"
            app:destination="@id/main__tasks"/>

        <action
            android:id="@+id/main__overview__to__main__requests"
            app:destination="@id/main__requests"/>

        <action
            android:id="@+id/main__overview__to__main__events"
            app:destination="@id/main__events"/>

        <action
            android:id="@+id/main__overview__to__main__event"
            app:destination="@+id/main__event"/>

        <action
            android:id="@+id/main__overview__to__main__organizations"
            app:destination="@id/main__organizations"/>

        <action
            android:id="@+id/main__overview__to__main__vacancies"
            app:destination="@id/main__vacancies"/>

        <action
            android:id="@+id/main__overview__to__main__vacancy"
            app:destination="@id/main__vacancy"/>

        <action
            android:id="@+id/main__overview__to__main__organization"
            app:destination="@+id/main__organization"/>
        <action
            android:id="@+id/action_main__overview_to_webView"
            app:destination="@id/webView"/>
        <action
            android:id="@+id/action_main__overview_to_allCoursesFragment"
            app:destination="@id/allCoursesFragment"/>
        <action
            android:id="@+id/action_main__overview_to_main__dobro_journal"
            app:destination="@id/main__dobro_journal"/>
        <action
            android:id="@+id/action_main__overview_to_webView2"
            app:destination="@id/webView"/>

        <action
            android:id="@+id/action_main__overview_to_crmEventFunctionsFragment"
            app:destination="@id/crmEventFunctionsFragment"/>
        <action
            android:id="@+id/action_main__overview_to_crmAcceptRejectApplicationFragment"
            app:destination="@id/crmAcceptRejectApplicationFragment"/>
        <action
            android:id="@+id/action_main__overview_to_crmSetApplicationHoursFragment"
            app:destination="@id/crmSetApplicationHoursFragment"/>
        <action
            android:id="@+id/action_main__overview_to_addEventFragment"
            app:destination="@id/addEventFragment"/>
        <action
            android:id="@+id/action_main__overview_to_main__event"
            app:destination="@id/main__event"/>
        <action
            android:id="@+id/action_main__overview_to_donorOnboardingFragment"
            app:destination="@+id/donorOnboardingFragment"/>
    </fragment>

    <fragment
        android:id="@+id/main__posts__or__dobro__center"
        android:name="ru.dobro.main.PostsOrDobroCenterFragment"
        android:label=""
        tools:layout="@layout/fragment_posts">

        <action
            android:id="@+id/action_posts_to_create_post"
            app:destination="@+id/create_post"/>

        <action
            android:id="@+id/main__posts__to__main__search"
            app:destination="@+id/main__search"/>

        <action
            android:id="@+id/action_main__dobro_center_to_main__dobro_center_details"
            app:destination="@id/main__dobro_center_details"/>

        <action
            android:id="@+id/action_main__dobro_center_to_authorization"
            app:destination="@id/authorization"/>

        <action
            android:id="@+id/action_main__dobro_center_to_main__dobro_center_map"
            app:destination="@id/main__dobro_center_map"/>

        <action
            android:id="@+id/action_main__dobro_center_to_events_map_fragment"
            app:destination="@id/eventsMapFragment"/>

        <action
            android:id="@+id/action_main__dobro_center_to__main__organization"
            app:destination="@id/main__organization"/>
    </fragment>

    <fragment
        android:id="@+id/posts_onboarding_fragment"
        android:name="ru.dobro.main.posts.onboarding.PostsOnboardingFragment"
        android:label=""
        tools:layout="@layout/fragment_posts_onboarding"/>

    <fragment
        android:id="@+id/main__dobro_journal"
        android:name="ru.dobro.main.home.journal.JournalFragment"
        android:label="Добро.Журнал"
        tools:layout="@layout/fragment_journal"/>

    <fragment
        android:id="@+id/main__dobro_center"
        android:name="ru.dobro.main.dobro_centers.DobroCenterFragment"
        android:label=""
        tools:layout="@layout/fragment_dobro_center">

        <action
            android:id="@+id/action_main__dobro_center_to_main__dobro_center_details"
            app:destination="@id/main__dobro_center_details"/>

        <action
            android:id="@+id/action_main__dobro_center_to_authorization"
            app:destination="@id/authorization"/>

        <action
            android:id="@+id/action_main__dobro_center_to_main__dobro_center_map"
            app:destination="@id/main__dobro_center_map"/>

        <action
            android:id="@+id/action_main__dobro_center_to_events_map_fragment"
            app:destination="@id/eventsMapFragment"/>

        <action
            android:id="@+id/action_main__dobro_center_to__main__organization"
            app:destination="@id/main__organization"/>


    </fragment>

    <fragment
        android:id="@+id/main__dobro_center_map"
        android:name="ru.dobro.main.dobro_centers.map.ServiceMapFragment"
        android:label=""
        tools:layout="@layout/fragment_service_map">

        <action
            android:id="@+id/main__dobro_center_map_to_main__dobro_center_success"
            app:destination="@id/main__dobro_center_success"
            app:popUpTo="@+id/main__dobro_center"
            app:popUpToInclusive="true"/>

    </fragment>

    <dialog
        android:id="@+id/main__dobro_center_map_confirm"
        android:name="ru.dobro.main.dobro_centers.map.dialog.ServiceMapConfirmDialog"
        android:label=""
        tools:layout="@layout/dialog_service_map_confirm" />

    <fragment
        android:id="@+id/main__dobro_center_success"
        android:name="ru.dobro.main.dobro_centers.map.ServiceRequestSuccessFragment"
        android:label=""
        tools:layout="@layout/fragment_service_request_success">

        <action
            android:id="@+id/action_main__dobro_center_details_to_main__dobro_center"
            app:destination="@id/main__dobro_center"
            app:popUpTo="@+id/main__dobro_center"
            app:popUpToInclusive="true"/>

    </fragment>

    <fragment
        android:id="@+id/main__dobro_center_details"
        android:name="ru.dobro.main.dobro_centers.details.DobroCenterDetailsFragment"
        android:label=""
        tools:layout="@layout/fragment_dobro_center_details">

        <action
            android:id="@+id/action_main__dobro_center_details_to_main__dobro_center_sign_up"
            app:destination="@id/main__dobro_center_sign_up"/>

    </fragment>

    <fragment
        android:id="@+id/main__dobro_center_sign_up"
        android:name="ru.dobro.main.dobro_centers.details.sign_up_service.DobroCenterSignUpFragment"
        android:label="Запись на сервис"
        tools:layout="@layout/fragment_dobro_center_sign_up">

        <action
            android:id="@+id/main__dobro_center_sign_up_to_main__dobro_center_map"
            app:destination="@id/main__dobro_center_map"/>

    </fragment>

    <fragment
        android:id="@+id/crmEventFunctionsFragment"
        android:name="ru.dobro.main.crm.expanded.CrmEventFunctionsFragment"
        android:label=" "
        tools:layout="@layout/main__crm__functions">
        <action
            android:id="@+id/action_crmEventFunctionsFragment_to_crmAcceptRejectApplicationFragment"
            app:destination="@id/crmAcceptRejectApplicationFragment"/>
    </fragment>

    <fragment
        android:id="@+id/crmAcceptRejectApplicationFragment"
        android:name="ru.dobro.main.crm.accept_reject.CrmAcceptRejectApplicationFragment"
        android:label=" "
        tools:layout="@layout/main__crm__accept_reject_applicants">
        <action
            android:id="@+id/action_crmAcceptRejectApplicationFragment_to_crmAcceptRejectSearchApplicationFragment"
            app:destination="@id/crmAcceptRejectSearchApplicationFragment"/>
        <action
            android:id="@+id/action_crmAcceptRejectApplicationFragment_to_crmVolunteerRequestFragment"
            app:destination="@id/crmVolunteerRequestFragment"/>
    </fragment>

    <fragment
        android:id="@+id/crmSetApplicationHoursFragment"
        android:name="ru.dobro.main.crm.set_hours.CrmSetApplicationHoursFragment"
        android:label=" "
        tools:layout="@layout/fragment_crm_set_hours">
        <action
            android:id="@+id/action_crmSetApplicationHoursFragment_to_crmVolunteerRequestFragment"
            app:destination="@id/crmVolunteerRequestFragment"/>
        <action
            android:id="@+id/action_crmSetApplicationHoursFragment_to_crmSetApplicationHoursExceptionFragment"
            app:destination="@id/crmSetApplicationHoursExceptionFragment"/>
        <action
            android:id="@+id/action_crmSetApplicationHoursFragment_to_CrmSetUnsetApplicationsHoursFragment"
            app:destination="@id/CrmSetUnsetApplicationsHoursFragment"/>
        <action
            android:id="@+id/crmSetApplicationHoursExceptionFragment_to_CrmSetUnsetApplicationsHoursFragment"
            app:destination="@id/CrmSetUnsetApplicationsHoursFragment"
            app:popUpTo="@+id/crmSetApplicationHoursFragment"/>
    </fragment>

    <dialog
        android:id="@+id/crmCantChangeStatusDialogFragment"
        android:name="ru.dobro.main.crm.set_hours.CrmCantChangeStatusDialogFragment"
        android:label=""
        tools:layout="@layout/dialog_cant_change_status"/>

    <fragment
        android:id="@+id/CrmSetUnsetApplicationsHoursFragment"
        android:name="ru.dobro.main.crm.set_hours.exception.CrmSetUnsetApplicationsHoursFragment"
        android:label=""
        tools:layout="@layout/main__crm___set_time_variant_item"/>

    <fragment
        android:id="@+id/crmSetApplicationHoursExceptionFragment"
        android:name="ru.dobro.main.crm.set_hours.exception.CrmSetApplicationHoursExceptionFragment"
        android:label=""
        tools:layout="@layout/main__crm_set_hours_exception"></fragment>

    <fragment
        android:id="@+id/crmAcceptRejectSearchApplicationFragment"
        android:name="ru.dobro.main.crm.accept_reject.search.CrmAcceptRejectSearchApplicationFragment"
        android:label="Поиск"
        tools:layout="@layout/main__crm__accept_reject_search_fragment">
        <action
            android:id="@+id/action_crmAcceptRejectSearchApplicationFragment_to_crmVolunteerRequestFragment"
            app:destination="@id/crmVolunteerRequestFragment"/>
    </fragment>

    <fragment
        android:id="@+id/crmVolunteerRequestFragment"
        android:name="ru.dobro.main.crm.request.CrmVolunteerRequestFragment"
        android:label="Заявка"
        tools:layout="@layout/main__crm_volunteer_request__fragment">
        <action
            android:id="@+id/action_crmVolunteerRequestFragment_to_main__event"
            app:destination="@id/main__event"/>
        <action
            android:id="@+id/action_crmVolunteerRequestFragment_to_trophyInfoFragment"
            app:destination="@id/trophyInfoFragment"/>
        <action
            android:id="@+id/action_crmVolunteerRequestFragment_to_main__profile___volunteer_book"
            app:destination="@id/main__profile___volunteer_book"/>
    </fragment>

    <fragment
        android:id="@+id/main__search"
        android:name="ru.dobro.main.search.SearchFragment"
        android:label="Поиск">

        <action
            android:id="@+id/main__search__to__events_search_map"
            app:destination="@+id/main__events_search_map"/>

    </fragment>

    <fragment
        android:id="@+id/main__notifications"
        android:name="ru.dobro.main.notifications.NotificationsFragment"
        android:label="@string/main___bottom_navigation__notifications"
        tools:layout="@layout/main__notifications">

        <action
            android:id="@+id/main__notifications__to__main__notifications_settings"
            app:destination="@id/main__notifications_settings"/>

        <action
            android:id="@+id/main__notification__to__main__authorization"
            app:destination="@id/authorization"
            app:enterAnim="@anim/core___fade__none__to__full"
            app:exitAnim="@anim/core___fade__full__to__none"/>

        <action
            android:id="@+id/main__notifications__to__main__vacancy"
            app:destination="@id/main__vacancy"/>

        <action
            android:id="@+id/main__notifications__to__main__event"
            app:destination="@+id/main__event"/>


    </fragment>

    <fragment
        android:id="@+id/main__profile_me"
        android:name="ru.dobro.main.profile.ProfileMeFragment"
        android:label=""
        tools:layout="@layout/main__profile_me">
        <action
            android:id="@+id/action_profileMeFragment_to_addEventFragment"
            app:destination="@id/addEventFragment"/>
        <action
            android:id="@+id/action_profileMeFragment_to__main__events"
            app:destination="@+id/main__events"/>
        <action
            android:id="@+id/action_profileMeFragment_to__main__event"
            app:destination="@id/main__event"/>
        <action
            android:id="@+id/profileMeFragment_to_friendsFragment"
            app:destination="@id/friendsFragment"/>
        <action
            android:id="@+id/profileMeFragment_to_myOrganizationsFragment"
            app:destination="@id/myOrganizationsFragment"/>
        <action
            android:id="@+id/main__profile_me__to__main__requests"
            app:destination="@id/main__requests"/>
        <action
            android:id="@+id/main__profile_me__to__main__profile___volunteer_book"
            app:destination="@id/main__profile___volunteer_book"/>

        <action
            android:id="@+id/main__profile_me__to__main__authorization"
            app:destination="@id/authorization"
            app:enterAnim="@anim/core___fade__none__to__full"
            app:exitAnim="@anim/core___fade__full__to__none"/>

        <action
            android:id="@+id/main__profile_me__to__main__profile_edit"
            app:destination="@id/main__profile_edit"/>

        <action
            android:id="@+id/main__profile_me__to__main__profile_me"
            app:destination="@id/main__profile_me"
            app:popUpTo="@id/main_navigation"
            app:popUpToInclusive="false"/>

        <action
            android:id="@+id/main__profile_me__to__main__profile___favorites"
            app:destination="@id/main__profile___favorites"/>

        <action
            android:id="@+id/action_main__profile_to__main__profile___comments"
            app:destination="@id/commentsFragment"/>
        <action
            android:id="@+id/action_main__profile_to__my_posts"
            app:destination="@+id/my_posts"/>
        <action
            android:id="@+id/action_main__profile_me_to_trophyInfoFragment"
            app:destination="@id/trophyInfoFragment"/>
        <action
            android:id="@+id/action_main__profile_me_to_achievementInfoFragment"
            app:destination="@id/achievementInfoFragment"/>
        <action
            android:id="@+id/action_main__profile_me_to_media_viewer___image_pager"
            app:destination="@id/media_viewer___image_pager"/>
        <action
            android:id="@+id/action_main__profile_me_to_profileMeOrganizationFragment"
            app:destination="@id/profileMeOrganizationFragment"
            app:popUpTo="@+id/main_navigation"
            app:popUpToInclusive="false"/>
        <action
            android:id="@+id/action_main__profile_me_to_becomeOrganizerFragment"
            app:destination="@id/becomeOrganizerFragment"/>
        <action
            android:id="@+id/action_main__profile_me_to_editOrganizationFragment"
            app:destination="@id/editOrganizationFragment"/>
        <action
            android:id="@+id/action_main__profile_me_to_esiaWebViewFragment"
            app:destination="@id/esiaWebViewFragment"/>
    </fragment>

    <action android:id="@+id/to_public_profile_fragment"
        app:destination="@id/public_profile"/>

    <fragment
        android:id="@+id/my_posts"
        android:name="ru.dobro.main.posts.myPosts.MyPostsFragment"
        android:label="Моя лента">

        <action
            android:id="@+id/action_my_posts_to_create_post"
            app:destination="@+id/create_post"/>
    </fragment>

    <fragment
        android:id="@+id/create_post"
        android:name="ru.dobro.main.posts.createPost.CreatePostFragment"
        android:label="">

        <action
            android:id="@+id/action_create_post_to_image_picker"
            app:destination="@id/image_picker"/>
    </fragment>

    <fragment
        android:id="@+id/main__posts"
        android:name="ru.dobro.main.posts.PostsFragment"
        android:label="">

    </fragment>

    <fragment
        android:id="@+id/public_profile"
        android:name="ru.dobro.main.profile.ProfileFragment"
        android:label=""
        tools:layout="@layout/fragment_public_profile">

        <action
            android:id="@+id/action_public_profile_to__main__events"
            app:destination="@+id/main__events"/>
        <action
            android:id="@+id/main__profile__to__main__profile___volunteer_book"
            app:destination="@id/main__profile___volunteer_book"/>
    </fragment>

    <action
        android:id="@+id/action__to__public_profile"
        app:destination="@id/public_profile"/>

    <fragment
        android:id="@+id/friendsFragment"
        android:name="ru.dobro.main.profile.friends.FriendsFragment"
        android:label=""
        tools:layout="@layout/fragment_my_friends"/>

    <fragment
        android:id="@+id/myOrganizationsFragment"
        android:name="ru.dobro.main.organizations.MyOrganizationsFragment"
        android:label=""
        tools:layout="@layout/fragment_my_organizations"/>

    <fragment
        android:id="@+id/commentsFragment"
        android:name="ru.dobro.main.profile.comments.CommentsFragment"
        android:label="@string/main__profile___comments"
        tools:layout="@layout/main__profile___comments">
        <action
            android:id="@+id/action_commentsFragment_to_main__vacancy"
            app:destination="@id/main__vacancy"/>
    </fragment>

    <fragment
        android:id="@+id/main__profile___volunteer_book"
        android:name="ru.dobro.main.profile.VolunteerBookFragment"
        android:label="@string/main__profile___volunteer_book"
        tools:layout="@layout/main__profile___volunteer_book">

        <action
            android:id="@+id/main__profile___volunteer_book__to__main__profile___qr_code"
            app:destination="@id/main__profile___qr_code"/>

        <action
            android:id="@+id/main__profile___volunteer_book__to__main__vacancies"
            app:destination="@id/main__vacancies"/>

        <action
            android:id="@+id/main__profile___volunteer_book__to__main__organization"
            app:destination="@id/main__organization"/>
    </fragment>

    <fragment
        android:id="@+id/main__profile___favorites"
        android:name="ru.dobro.main.profile.FavoritesPagerFragment"
        android:label="@string/main__profile___favorites"
        tools:layout="@layout/main__profile___vacancy_favorites">

        <action
            android:id="@+id/main__profile___favorites__to__main__vacancy"
            app:destination="@id/main__vacancy"/>

        <action
            android:id="@+id/main__profile___favorites__to__main__event"
            app:destination="@id/main__event"/>
    </fragment>

    <fragment
        android:id="@+id/main__profile___qr_code"
        android:name="ru.dobro.main.profile.QrCodeFragment"
        android:label=""
        tools:layout="@layout/main__profile___qr_code">

    </fragment>

    <fragment
        android:id="@+id/main__profile_edit"
        android:name="ru.dobro.main.profile_edit.ProfileEditFragment"
        android:label="@string/main__profile_edit"
        tools:layout="@layout/main__profile_edit">

        <action
            android:id="@+id/main__profile_edit__to__main__profile_edit___password_edit"
            app:destination="@id/main__profile_edit___password_edit"/>

        <action
            android:id="@+id/main__profile_edit__to__main__profile_edit___passport_edit"
            app:destination="@id/main__profile_edit___passport_edit"/>

        <action
            android:id="@+id/main__profile_edit__to__image_picker"
            app:destination="@id/image_picker"/>

        <action
            android:id="@+id/main__profile_edit__to__main__profile_me"
            app:destination="@id/main__profile_me"
            app:popUpTo="@id/main_navigation"/>

        <action
            android:id="@+id/main__profile_edit__to__change_email"
            app:destination="@id/change_email"/>
        <action
            android:id="@+id/action_main__profile_edit_to_profileUnremovableFragment"
            app:destination="@id/profileUnremovableFragment"/>
    </fragment>

    <fragment
        android:id="@+id/change_email"
        android:name="ru.dobro.change_email.ChangeEmailFragment"
        android:label=""
        tools:layout="@layout/fragment_change_email">

        <action
            android:id="@+id/change_email__to__check_new_email"
            app:destination="@+id/check_new_email"
            app:popUpTo="@id/main__profile_me"/>
    </fragment>

    <fragment
        android:id="@+id/check_new_email"
        android:name="ru.dobro.check_new_email.CheckNewEmailFragment"
        android:label=""
        tools:layout="@layout/fragment_check_new_email"/>

    <fragment
        android:id="@+id/main__profile_edit___password_edit"
        android:name="ru.dobro.main.profile_edit.PasswordEditFragment"
        android:label="@string/main__profile_edit___password__change"
        tools:layout="@layout/main__profile_edit__password_edit">

        <action
            android:id="@+id/main__profile_edit___password_edit__to__reset_password"
            app:destination="@+id/reset_password"
            app:enterAnim="@anim/core___fade__none__to__full"/>
    </fragment>

    <fragment
        android:id="@+id/main__profile_edit___passport_edit"
        android:name="ru.dobro.main.profile_edit.PassportEditFragment"
        android:label="@string/main__profile_edit___passport_edit__title"
        tools:layout="@layout/main__profile_edit__passport_edit">

        <action
            android:id="@+id/action_main__profile_edit___passport_edit_to_pickAddressFragment"
            app:destination="@id/pickAddressFragment"/>

    </fragment>

    <dialog
        android:id="@+id/profileUnremovableFragment"
        android:name="ru.dobro.main.profile_edit.ProfileUnremovableFragment"
        android:label=""/>

    <fragment
        android:id="@+id/image_picker"
        android:name="ru.dobro.image_picker.ImagePickerFragment"
        android:label="@string/image_picker___title"
        tools:layout="@layout/image_picker">

    </fragment>

    <fragment
        android:id="@+id/media_viewer___list"
        android:name="ru.dobro.media_viewer.MediaViewerListFragment"
        android:label=""
        tools:layout="@layout/media_viewer___list">

        <action
            android:id="@+id/media_viewer___list__to__media_viewer___image_pager"
            app:destination="@+id/media_viewer___image_pager"/>
    </fragment>

    <fragment
        android:id="@+id/media_viewer___image_pager"
        android:name="ru.dobro.media_viewer.MediaViewerImagePagerFragment"
        android:label=""
        tools:layout="@layout/media_viewer___image__item">

    </fragment>

    <fragment
        android:id="@+id/main__notifications_settings"
        android:name="ru.dobro.main.notifications_settings.NotificationsSettingsFragment"
        android:label="@string/main__notifications_settings___title"
        tools:layout="@layout/fragment_notifications_settings">

    </fragment>

    <fragment
        android:id="@+id/main__events"
        android:name="ru.dobro.main.events.EventsFragment"
        android:label=""
        tools:layout="@layout/main__events">

        <action
            android:id="@+id/main__events__to__main__event"
            app:destination="@+id/main__event"/>

        <action
            android:id="@+id/main__events__to__main__events_filter"
            app:destination="@+id/main__events_filter"/>
        <action
            android:id="@+id/main__events__to__main_events_search_filter"
            app:destination="@id/main__events_search_filter"/>
        <action
            android:id="@+id/main__events__to__events_map_fragment"
            app:destination="@id/eventsMapFragment"/>
    </fragment>

    <fragment
        android:id="@+id/eventsMapFragment"
        android:name="ru.dobro.main.events.events_map.EventsMapFragment"
        android:label=" "
        tools:layout="@layout/fragment_events_map">
        <action
            android:id="@+id/eventsMapFragment__to__main__event"
            app:destination="@+id/main__event"/>
        <action
            android:id="@+id/eventsMapFragment__to__multipleEventsFragment"
            app:destination="@+id/multipleEventsFragment"/>
        <action
            android:id="@+id/eventsMapFragment__to_main__dobro_center"
            app:destination="@id/main__dobro_center"
            app:popUpTo="@+id/main__dobro_center"
            app:popUpToInclusive="true"/>
    </fragment>

    <fragment
        android:id="@+id/multipleEventsFragment"
        android:name="ru.dobro.main.events.events_map.multipleEvents.MultipleEventsFragment"
        android:label=" "
        tools:layout="@layout/fragment_multiple_events">
        <action
            android:id="@+id/multipleEventsFragment__to__main__event"
            app:destination="@+id/main__event"/>
    </fragment>

    <fragment
        android:id="@+id/main__events_filter"
        android:name="ru.dobro.main.events.EventsFilterFragment"
        android:label="@string/application___filters"
        tools:layout="@layout/common___filter">

        <action
            android:id="@+id/main__events_filter__to__main__events"
            app:destination="@+id/main__events"
            app:popUpTo="@id/main__events"
            app:popUpToInclusive="true"/>
    </fragment>

    <fragment
        android:id="@+id/main__events_search_filter"
        android:name="ru.dobro.main.events.events_search.EventsSearchFilterFragment"
        android:label="@string/application___filters">

        <action
            android:id="@+id/main__events_search_filter__to__search_empty"
            app:destination="@id/search_empty"/>
    </fragment>

    <fragment
        android:id="@+id/main__events_search_map"
        android:name="ru.dobro.main.events.events_search.EventsSearchMapFragment"
        android:label="">

        <action
            android:id="@+id/main__events_search_map_to_multipleEventsFragment"
            app:destination="@+id/main__multiple_events_search_map"/>

        <action
            android:id="@+id/main__events_search_map_to_main__event"
            app:destination="@+id/main__event"/>
    </fragment>

    <fragment
        android:id="@+id/main__multiple_events_search_map"
        android:name="ru.dobro.main.events.events_search.MultipleEventsSearchMapFragment"
        android:label="">

        <action
            android:id="@+id/multipleSearchMapEventsFragment__to__main__event"
            app:destination="@+id/main__event"/>

    </fragment>

    <fragment
        android:id="@+id/main__organizations"
        android:name="ru.dobro.main.organizations.OrganizationsPagerFragment"
        android:label="@string/main__organizations___title"
        tools:layout="@layout/main__notifications">

        <action
            android:id="@+id/main__organizations__to__main__organization"
            app:destination="@+id/main__organization"/>

        <action
            android:id="@+id/main__organizations__to__main__vacancies"
            app:destination="@+id/main__vacancies"/>

        <action
            android:id="@+id/main__organizations__to__main__organizations_filter"
            app:destination="@+id/main__organizations_filter"/>

        <action
            android:id="@+id/main__organizations__to__main__authorization"
            app:destination="@id/authorization"
            app:enterAnim="@anim/core___fade__none__to__full"
            app:exitAnim="@anim/core___fade__full__to__none"/>

    </fragment>

    <fragment
        android:id="@+id/main__organizers___search"
        android:name="ru.dobro.main.organizers.OrganizersSearchFragment"/>

    <fragment
        android:id="@+id/main__organizers_search_filter"
        android:name="ru.dobro.main.organizers.OrganizersSearchFilterFragment"
        android:label="@string/application___filters">

        <action
            android:id="@+id/main__organizers_search_filter__to__search_empty"
            app:destination="@id/search_empty"/>
    </fragment>

    <fragment
        android:id="@+id/main__volunteers_search_filter"
        android:name="ru.dobro.main.search.volunteers.VolunteersSearchFilterFragment"
        android:label="@string/application___filters"/>

    <fragment
        android:id="@+id/main__organizations_filter"
        android:name="ru.dobro.main.organizations.OrganizationsFilterFragment"
        android:label="@string/application___filters"
        tools:layout="@layout/common___filter">

    </fragment>

    <fragment
        android:id="@+id/main__organization"
        android:name="ru.dobro.main.organization.OrganizationFragment"
        android:label=""
        tools:layout="@layout/main__organization">

        <action
            android:id="@+id/main__organization__to__main__event"
            app:destination="@+id/main__event"/>

        <action
            android:id="@+id/main__organization__to__main__events"
            app:destination="@+id/main__events"/>

        <action
            android:id="@+id/main__organization__to__media_viewer___list"
            app:destination="@+id/media_viewer___list"/>

        <action
            android:id="@+id/main__organization__to__main__authorization"
            app:destination="@id/authorization"
            app:enterAnim="@anim/core___fade__none__to__full"
            app:exitAnim="@anim/core___fade__full__to__none"/>

        <action
            android:id="@+id/main__organization__to__main__vacancy"
            app:destination="@+id/main__vacancy"/>

        <action
            android:id="@+id/main__organization__to__main__vacancies"
            app:destination="@+id/main__vacancies"/>
        <action
            android:id="@+id/action_main__organization_to_organizationsAllReviews"
            app:destination="@id/organizationsAllReviews"/>

    </fragment>

    <fragment
        android:id="@+id/main__event"
        android:name="ru.dobro.main.event.EventFragment"
        android:label=""
        tools:layout="@layout/fragment_event">
        <action
            android:id="@+id/main__event__to_addVacancyFragment"
            app:destination="@id/addVacancyFragment"/>
        <action
            android:id="@+id/main__event__to_addVariantFragment"
            app:destination="@id/addVariantFragment"/>
        <action
            android:id="@+id/main__event__to__main__map"
            app:destination="@id/main__map"/>

        <action
            android:id="@+id/main__event__to__authorization"
            app:destination="@id/authorization"/>
        <action
            android:id="@+id/main__event__to__main__vacancy"
            app:destination="@+id/main__vacancy"/>
        <action
            android:id="@+id/main__event__to__main__organization"
            app:destination="@id/main__organization"/>
    </fragment>

    <fragment
        android:id="@+id/main__vacancies"
        android:name="ru.dobro.main.vacancies.VacanciesFragment"
        android:label=""
        tools:layout="@layout/main__vacancies">

        <action
            android:id="@+id/main__vacancies__to__main__vacancy"
            app:destination="@+id/main__vacancy"/>

        <action
            android:id="@+id/main__vacancies__to__main__vacancies_filter"
            app:destination="@+id/main__vacancies_filter"/>
    </fragment>

    <fragment
        android:id="@+id/main__vacancies_filter"
        android:name="ru.dobro.main.vacancies.VacanciesFilterFragment"
        android:label="@string/application___filters"
        tools:layout="@layout/common___filter">

        <action
            android:id="@+id/main__vacancies_filter__to__main__vacancies"
            app:destination="@+id/main__vacancies"
            app:popUpTo="@id/main__vacancies"
            app:popUpToInclusive="true"/>
    </fragment>

    <fragment
        android:id="@+id/main__vacancies_search_filter"
        android:name="ru.dobro.main.vacancies.vacancies_search.VacanciesSearchFilterFragment"
        android:label="@string/application___filters">

        <action
            android:id="@+id/main__vacancies_search_filter__to__search_empty"
            app:destination="@id/search_empty"/>
    </fragment>
    <fragment
        android:id="@+id/main__vacancy"
        android:name="ru.dobro.main.vacancy.VacancyFragment"
        android:label=""
        tools:layout="@layout/main__vacancy">

        <action
            android:id="@+id/main__vacancy__to__main__vacancy___request"
            app:destination="@+id/main__vacancy___request"/>

        <action
            android:id="@+id/main__vacancy__to__main__vacancy___request_reject"
            app:destination="@+id/main__vacancy___request_reject"/>

        <action
            android:id="@+id/main__vacancy__to__main__organization"
            app:destination="@+id/main__organization"/>

        <action
            android:id="@+id/main__vacancy__to__main__authorization"
            app:destination="@+id/authorization"/>

        <action
            android:id="@+id/main__vacancy__to__main__map"
            app:destination="@+id/main__map"/>
        <action
            android:id="@+id/action_main__vacancy_to_esiaRequestFragment"
            app:destination="@id/esiaRequestFragment"/>
    </fragment>

    <fragment
        android:id="@+id/search_empty"
        android:name="ru.dobro.main.search.empty.SearchEmptyFragment"
        android:label=""
        tools:layout="@layout/fragment_search_empty" />

    <fragment
        android:id="@+id/main__vacancy___request"
        android:name="ru.dobro.main.vacancy_request.VacancyRequestFragment"
        android:label="Заявка на функцию"
        tools:layout="@layout/main__vacancy_request">

        <action
            android:id="@+id/main__vacancy___request_to_pickAddressFragment"
            app:destination="@id/pickAddressFragment"/>

        <action
            android:id="@+id/main__vacancy___request_to_vacancyRequestAcceptedFragment"
            app:destination="@id/vacancyRequestAcceptedFragment"/>

    </fragment>

    <fragment
        android:id="@+id/main__vacancy___request_reject"
        android:name="ru.dobro.main.vacancy_request_reject.VacancyRequestRejectingFragment"
        android:label="Заявка на вакансию"
        tools:layout="@layout/main__vacancy_request">

    </fragment>

    <fragment
        android:id="@+id/main__tasks"
        android:name="ru.dobro.main.tasks.TasksFragment"
        android:label="@string/main__home___event_type__targeted_help"
        tools:layout="@layout/main__tasks">

        <action
            android:id="@+id/main__tasks__to__main__task"
            app:destination="@id/main__task"/>

    </fragment>

    <fragment
        android:id="@+id/main__requests"
        android:name="ru.dobro.main.requests.RequestsFragment"
        android:label="@string/main__requests___title"
        tools:layout="@layout/main__requests">

        <action
            android:id="@+id/main__requests__to__main__task"
            app:destination="@id/main__task"/>

        <action
            android:id="@+id/main__requests__to__main__vacancy"
            app:destination="@id/main__vacancy"/>

        <action
            android:id="@+id/main__requests__to__main__event"
            app:destination="@id/main__event"/>

        <action
            android:id="@+id/main__requests_to_eventDetailedFragment"
            app:destination="@id/eventDetailedFragment"/>
        <action
            android:id="@+id/action_main__requests_to_requestsStatusFilterFragment"
            app:destination="@id/requestsStatusFilterFragment"/>

    </fragment>

    <fragment
        android:id="@+id/main__task"
        android:name="ru.dobro.main.task.TaskFragment"
        android:label=""
        tools:layout="@layout/main__task">

        <action
            android:id="@+id/main__task__to__main__task_report"
            app:destination="@id/main__task_report"/>

        <action
            android:id="@+id/main__task__to__main__profile_me"
            app:destination="@id/main__profile_me"
            app:popUpTo="@id/main_navigation"/>

        <action
            android:id="@+id/main__task__to__main__profile"
            app:destination="@id/public_profile"/>

        <action
            android:id="@+id/main__task__to__main__organization"
            app:destination="@id/main__organization"/>
    </fragment>

    <fragment
        android:id="@+id/main__task_report"
        android:name="ru.dobro.main.task_report.TaskReportFragment"
        android:label=""
        tools:layout="@layout/main__task_report">

    </fragment>

    <!-- TODO сделать фрагменты диалогами-->
    <fragment
        android:id="@+id/authorization"
        android:name="ru.dobro.authorization.AuthorizationFragment"
        android:label=""
        tools:layout="@layout/authorization">

        <action
            android:id="@+id/authorization__to_registration"
            app:destination="@+id/registration"
            app:enterAnim="@anim/core___fade__none__to__full"
            app:exitAnim="@anim/core___fade__full__to__none"
            app:popExitAnim="@anim/core___fade__full__to__none"/>

        <action
            android:id="@+id/authorization__to__reset_password"
            app:destination="@+id/reset_password"
            app:enterAnim="@anim/core___fade__none__to__full"
            app:exitAnim="@anim/core___fade__full__to__none"/>

        <action
            android:id="@+id/authorization__to__authorizationWebView"
            app:destination="@+id/authorizationWebView"
            app:enterAnim="@anim/core___fade__none__to__full"
            app:exitAnim="@anim/core___fade__full__to__none"/>

        <action
            android:id="@+id/authorization__to__account_disabled"
            app:destination="@+id/account_disabled"
            app:launchSingleTop="true"
            app:popExitAnim="@anim/core___fade__full__to__none"/>
    </fragment>

    <fragment
        android:id="@+id/authorizationWebView"
        android:name="ru.dobro.authorization.AuthorizationWebViewFragment"
        android:label="{title}"
        tools:layout="@layout/common__web_view">

        <argument
            android:name="title"
            app:argType="string"/>
    </fragment>

    <fragment
        android:id="@+id/registration"
        android:name="ru.dobro.registration.RegistrationFragment"
        android:label=""
        tools:layout="@layout/registration">

        <action
            android:id="@+id/registration__to__check_email"
            app:destination="@+id/check_email"
            app:enterAnim="@anim/core___fade__none__to__full"
            app:popUpTo="@id/authorization"/>

        <action
            android:id="@+id/registration__to__account_disabled"
            app:destination="@+id/account_disabled"
            app:launchSingleTop="true"
            app:enterAnim="@anim/core___fade__none__to__full"
            app:popUpTo="@id/authorization"/>

        <action
            android:id="@+id/registration__to__main__overview"
            app:destination="@id/main__overview"
            app:enterAnim="@anim/core___fade__none__to__full"
            app:popUpTo="@id/main_navigation"/>

        <action
            android:id="@+id/registration__to__registration__underage_registration"
            app:destination="@id/registration__underage_registration"
            app:enterAnim="@anim/core___fade__none__to__full"/>

        <action
            android:id="@+id/registration__to__authorizationWebView"
            app:destination="@+id/authorizationWebView"
            app:enterAnim="@anim/core___fade__none__to__full"
            app:exitAnim="@anim/core___fade__full__to__none"/>
    </fragment>

    <dialog
        android:id="@+id/account_disabled"
        android:name="ru.dobro.account_disabled.AccountDisabledFragment"
        android:label=""
        tools:layout="@layout/fragment_account_disabled"/>

    <fragment
        android:id="@+id/registration__underage_registration"
        android:name="ru.dobro.registration.parents_or_guardian.UnderageRegistrationFragment"
        android:label="">


        <action
            android:id="@+id/registration__underage_registration__to__check_email"
            app:destination="@+id/check_email"
            app:enterAnim="@anim/core___fade__none__to__full"
            app:popUpTo="@id/authorization"/>

        <action
            android:id="@+id/registration__underage_registration__to__account_disabled"
            app:destination="@+id/account_disabled"
            app:launchSingleTop="true"
            app:enterAnim="@anim/core___fade__none__to__full"
            app:popUpTo="@id/authorization"/>
    </fragment>

    <fragment
        android:id="@+id/main__social_network_registration"
        android:name="ru.dobro.registration_social_network.SocialNetworkRegistrationFragment"
        android:label=""
        tools:layout="@layout/social_network_registration">

        <action
            android:id="@+id/main__social_network_registration__to__registration__underage_registration"
            app:destination="@id/registration__underage_registration"
            app:enterAnim="@anim/core___fade__none__to__full"/>
        <action
            android:id="@+id/main__social_network_registration__to__check_email"
            app:destination="@+id/check_email"
            app:enterAnim="@anim/core___fade__none__to__full"/>

        <action
            android:id="@+id/main__social_network_registration__to__main__overview"
            app:destination="@id/main__overview"
            app:enterAnim="@anim/core___fade__none__to__full"/>

        <action
            android:id="@+id/main__social_network_registration__to__authorization"
            app:destination="@+id/authorization"
            app:enterAnim="@anim/core___fade__none__to__full"
            app:popUpTo="@id/main_navigation"/>
    </fragment>

    <fragment
        android:id="@+id/check_email"
        android:name="ru.dobro.check_email.CheckEmailFragment"
        android:label=""
        tools:layout="@layout/check_email">

        <action
            android:id="@+id/check_email__to__reset_password"
            app:destination="@+id/reset_password"/>

        <action
            android:id="@+id/check_email__to__registration"
            app:popUpTo="@id/authorization"
            app:popUpToInclusive="false"
            app:destination="@+id/registration"/>
    </fragment>

    <fragment
        android:id="@+id/reset_password"
        android:name="ru.dobro.reset_password.ResetPasswordFragment"
        android:label="">

        <action
            android:id="@+id/reset_password___to__check_email"
            app:destination="@+id/check_email"/>

    </fragment>

    <fragment
        android:id="@+id/reset_password__new_password"
        android:name="ru.dobro.reset_password.NewPasswordFragment"/>

    <fragment
        android:id="@+id/main__about"
        android:name="ru.dobro.main.about.AboutFragment"
        android:label=""
        tools:layout="@layout/main__about">

        <action
            android:id="@+id/main__about__to__main__about___description"
            app:destination="@id/main__about___description"/>

        <action
            android:id="@+id/main__about__to__knowledgeBaseFragment"
            app:destination="@id/knowledgeBaseFragment"/>

    </fragment>

    <fragment
        android:id="@+id/knowledgeBaseFragment"
        android:name="ru.dobro.main.about.knowledge_base.KnowledgeBaseFragment"
        android:label="@string/main__about___knowledge_base"
        tools:layout="@layout/fragment_knowledge_base">

        <action
            android:id="@+id/action_knowledgeBaseFragment_to_knowledgeBaseSectionFragment"
            app:destination="@+id/knowledgeBaseSectionFragment"/>

        <action
            android:id="@+id/action_knowledgeBaseFragment_to_KnowledgeBaseArticleFragment"
            app:destination="@+id/knowledgeBaseArticleFragment"/>

    </fragment>

    <fragment
        android:id="@+id/knowledgeBaseSectionFragment"
        android:name="ru.dobro.main.about.knowledge_base.KnowledgeBaseSectionFragment"
        android:label=""
        tools:layout="@layout/fragment_knowledge_base_section">

        <action
            android:id="@+id/action_knowledgeBaseSectionFragment_to_KnowledgeBaseArticleFragment"
            app:destination="@+id/knowledgeBaseArticleFragment"/>

    </fragment>

    <fragment
        android:id="@+id/knowledgeBaseArticleFragment"
        android:name="ru.dobro.main.about.knowledge_base.KnowledgeBaseArticleFragment"
        android:label=""
        tools:layout="@layout/fragment_knowledge_base_section">

        <action
            android:id="@+id/action_KnowledgeBaseArticleFragment_to_mediaViewerImagePager"
            app:destination="@id/media_viewer___image_pager"/>

    </fragment>

    <fragment
        android:id="@+id/main__about___description"
        android:name="ru.dobro.main.about.AboutDescriptionFragment"
        android:label=""
        tools:layout="@layout/main__about___description">
        <action
            android:id="@+id/action_main__about___description_to_main__organization"
            app:destination="@id/main__organization"/>
    </fragment>

    <fragment
        android:id="@+id/main__map"
        android:name="ru.dobro.main.map.MapFragment"
        android:label="@string/main__map"
        tools:layout="@layout/main__map"/>

    <fragment
        android:id="@+id/trophyInfoFragment"
        android:name="ru.dobro.main.profile.achievements.info.AchievementInfoFragment"
        tools:layout="@layout/fragment_achievement_info"/>

    <fragment
        android:id="@+id/achievementInfoFragment"
        android:name="ru.dobro.main.profile.achievements.AchievementsFragment"
        tools:layout="@layout/fragment_achievements">
        <action
            android:id="@+id/action_achievementInfoFragment_to_trophyInfoFragment"
            app:destination="@id/trophyInfoFragment"/>
        <action
            android:id="@+id/action_achievementInfoFragment_to_media_viewer___image_pager"
            app:destination="@id/media_viewer___image_pager"/>
    </fragment>
    <fragment
        android:id="@+id/organizationsAllReviews"
        android:name="ru.dobro.main.organization.AllOrganizationReviews"
        android:label="Все отзывы"
        tools:layout="@layout/main__organizations">
        <action
            android:id="@+id/action_organizationsAllReviews_to_main__vacancy"
            app:destination="@id/main__vacancy"/>
    </fragment>
    <fragment
        android:id="@+id/webView"
        android:name="ru.dobro.common.WebViewFragment"
        tools:layout="@layout/common__web_view"/>

    <dialog
        android:id="@+id/requestNotFoundDialogFragment"
        android:name="ru.dobro.main.home.RequestNotFoundDialogFragment"
        android:label="RequestNotFoundDialogFragment"
        tools:layout="@layout/main__home___no_requests_found">
        <action
            android:id="@+id/action_requestNotFoundDialogFragment_to_main__events"
            app:destination="@id/main__events"/>
    </dialog>
    <fragment
        android:id="@+id/allCoursesFragment"
        android:name="ru.dobro.main.courses.AllCoursesFragment"
        android:label="Добро.Университет"
        tools:layout="@layout/fragment_all_courses">
        <action
            android:id="@+id/action_allCoursesFragment_to_webView"
            app:destination="@id/webView"/>
    </fragment>
    <fragment
        android:id="@+id/profileMeOrganizationFragment"
        android:name="ru.dobro.main.profile.organization.ProfileMeOrganizationFragment"
        tools:layout="@layout/main__profile_me_organizator">
        <action
            android:id="@+id/action_profileMeOrganizationFragment_to__main__events"
            app:destination="@+id/main__events"/>
        <action
            android:id="@+id/action_profileMeOrganizationFragment_to__main__event"
            app:destination="@id/main__event"/>
        <action
            android:id="@+id/action_profileMeOrganizationFragment_to_main__profile_me"
            app:destination="@id/main__profile_me"
            app:popUpTo="@id/main_navigation"
            app:popUpToInclusive="false"/>
        <action
            android:id="@+id/action_profileMeOrganizationFragment_to_main__profileMeOrganizationFragment"
            app:destination="@id/profileMeOrganizationFragment"
            app:popUpTo="@id/main_navigation"
            app:popUpToInclusive="false"/>
        <action
            android:id="@+id/action_profileMeOrganizationFragment_to_media_viewer___list"
            app:destination="@id/media_viewer___list"/>
        <action
            android:id="@+id/action_profileMeOrganizationFragment_to_organizationStatusHelpFragment"
            app:destination="@id/organizationStatusHelpFragment"/>
        <action
            android:id="@+id/action_profileMeOrganizationFragment_to_profileOrganizationEditFragment"
            app:destination="@id/profileOrganizationEditFragment"/>
        <action
            android:id="@+id/action_profileMeOrganizationFragment_to_organizationRequestsFragment"
            app:destination="@id/organizationRequestsFragment"/>
        <action
            android:id="@+id/action_profileMeOrganizationFragment_to_organizationsAllReviews"
            app:destination="@id/organizationsAllReviews"/>
        <action
            android:id="@+id/action_profileMeOrganizationFragment_to_achievementInfoFragment"
            app:destination="@id/achievementInfoFragment"/>
        <action
            android:id="@+id/action_profileMeOrganizationFragment_to_authorization"
            app:destination="@id/authorization"/>
        <action
            android:id="@+id/action_profileMeOrganizationFragment_to_addEventFragment"
            app:destination="@id/addEventFragment"/>
        <action
            android:id="@+id/action_profileMeOrganizationFragment_to_trophyInfoFragment"
            app:destination="@id/trophyInfoFragment"/>
        <action
            android:id="@+id/action_profileMeOrganizationFragment_to_editOrganizationFragment"
            app:destination="@id/editOrganizationFragment"/>
        <action
            android:id="@+id/action_profileMeOrganizationFragment_to_becomeOrganizerFragment"
            app:destination="@id/becomeOrganizerFragment"/>
        <action
            android:id="@+id/action_profileMeOrganizationFragment__to_main__vacancy"
            app:destination="@id/main__vacancy"/>
    </fragment>
    <fragment
        android:id="@+id/profileOrganizationEditFragment"
        android:name="ru.dobro.main.profile_edit.ProfileOrganizationEditFragment"
        android:label="ProfileOrganizationEditFragment"
        tools:layout="@layout/main__profile_edit"/>

    <fragment
        android:id="@+id/organizationRequestsFragment"
        android:name="ru.dobro.main.requests.organization.OrganizationRequestsFragment"
        android:label="Заявки"/>
    <fragment
        android:id="@+id/esiaRequestFragment"
        android:name="ru.dobro.main.vacancy.esia.EsiaRequestFragment"
        android:label=""
        tools:layout="@layout/main__vacancy_esia">
        <action
            android:id="@+id/action_esiaRequestFragment_to_esiaWebViewFragment"
            app:destination="@id/esiaWebViewFragment"/>
    </fragment>
    <fragment
        android:id="@+id/esiaWebViewFragment"
        android:name="ru.dobro.main.vacancy.esia.EsiaWebViewFragment"
        android:label=""
        tools:layout="@layout/common__web_view">
        <action
            android:id="@+id/action_esiaWebViewFragment_to_esiaExceptionFragment"
            app:destination="@id/esiaExceptionFragment"/>
        <action
            android:id="@+id/action_esiaWebViewFragment_to_main__vacancy"
            app:destination="@id/main__vacancy"/>
    </fragment>
    <fragment
        android:id="@+id/esiaExceptionFragment"
        android:name="ru.dobro.main.vacancy.esia.EsiaExceptionFragment"
        android:label="Заявки"
        tools:layout="@layout/main__vacancy_esia_exception">
        <action
            android:id="@+id/action_esiaExceptionFragment_to_authorization"
            app:destination="@id/authorization"
            app:launchSingleTop="true"
            app:popUpTo="@+id/main_navigation"
            app:popUpToInclusive="true"/>
    </fragment>

    <fragment
        android:id="@+id/addEventFragment"
        android:name="ru.dobro.main.profile.organization.addEvent.AddEventFragment"
        android:label="Создание доброго дела"
        tools:layout="@layout/main__profile_me_organizator_add_event">
        <action
            android:id="@+id/action_createOrganizationFragment_to_pickAddressFragment"
            app:destination="@id/pickAddressFragment"/>
        <action
            android:id="@+id/action_addEventFragment_to_image_picker"
            app:destination="@id/image_picker"/>
        <action
            android:id="@+id/main_addEventFragment_to_main_profile_me"
            app:destination="@id/main__event"
            app:popUpTo="@id/main_navigation"
            app:popUpToInclusive="false"/>
    </fragment>
    <fragment
        android:id="@+id/addVariantFragment"
        android:name="ru.dobro.main.profile.organization.addVariant.AddVariantFragment"
        tools:layout="@layout/main__profile_me_organizator_add_variant">
        <action
            android:id="@+id/action_addVariantFragment_to_vacancyFunctionsSelectorFragment"
            app:destination="@id/vacancyFunctionsSelectorFragment"/>
    </fragment>
    <fragment
        android:id="@+id/addVacancyFragment"
        android:name="ru.dobro.main.profile.organization.addVacancy.AddVacancyFragment"
        tools:layout="@layout/main__profile_me_organizator_add_vacancy">
        <action
            android:id="@+id/action_addVacancyFragment_to_vacancyFunctionsSelectorFragment"
            app:destination="@id/vacancyFunctionsSelectorFragment"/>
    </fragment>
    <dialog
        android:id="@+id/vacancyFunctionsSelectorFragment"
        android:name="ru.dobro.main.profile.organization.selectFunctions.VacancyFunctionsSelectorFragment"/>
    <fragment
        android:id="@+id/becomeOrganizerFragment"
        android:name="ru.dobro.main.profile.becomeOrganizator.BecomeOrganizerFragment"
        android:label=""
        tools:layout="@layout/fragment_become_organizer">
        <action
            android:id="@+id/action_becomeOrganizerFragment_to_esiaRequestFragment"
            app:destination="@id/esiaRequestFragment"/>
        <action
            android:id="@+id/action_becomeOrganizerFragment_to_createOrganizationFragment"
            app:destination="@id/createOrganizationFragment"/>
    </fragment>
    <fragment
        android:id="@+id/createOrganizationFragment"
        android:name="ru.dobro.main.profile.becomeOrganizator.CreateOrganizationFragment"
        android:label=""
        tools:layout="@layout/fragment_create_organization">
        <action
            android:id="@+id/action_createOrganizationFragment_to_editOrganizationFragment"
            app:destination="@id/editOrganizationFragment"/>
        <action
            android:id="@+id/action_createOrganizationFragment_to_profileMeOrganizationFragment_pop_main_profile"
            app:popUpTo="@id/main_navigation"
            app:popUpToInclusive="false"
            app:destination="@id/profileMeOrganizationFragment"/>
        <action
            android:id="@+id/action_createOrganizationFragment_to_profileMeOrganizationFragment_pop_profile_me_organisation"
            app:popUpTo="@id/main_navigation"
            app:popUpToInclusive="false"
            app:destination="@id/profileMeOrganizationFragment"/>
        <action
            android:id="@+id/action_createOrganizationFragment_self"
            app:destination="@id/createOrganizationFragment"/>
        <action
            android:id="@+id/action_createOrganizationFragment_to_pickAddressFragment"
            app:destination="@id/pickAddressFragment"/>
    </fragment>
    <fragment
        android:id="@+id/editOrganizationFragment"
        android:name="ru.dobro.main.profile.editOrganization.EditOrganizationFragment"
        android:label="Редактирование">
        <action
            android:id="@+id/action_editOrganizationFragment_to_image_picker"
            app:destination="@id/image_picker"/>
        <action
            android:id="@+id/action_editOrganizationFragment_to_profileMeOrganizationFragment"
            app:destination="@id/profileMeOrganizationFragment"
            app:popUpTo="@id/main_navigation"/>
        <action
            android:id="@+id/action_editOrganizationFragment_to_pickAddressFragment"
            app:destination="@id/pickAddressFragment"/>
    </fragment>

    <fragment
        android:id="@+id/SimpleWebViewFragment"
        android:name="ru.dobro.webview.SimpleWebViewFragment"
        android:label=""
        tools:layout="@layout/common__web_view"/>

    <fragment
        android:id="@+id/pickAddressFragment"
        android:name="ru.dobro.map.pickAddress.PickAddressFragment"
        android:label="">
        <argument
            android:name="request"
            app:argType="ru.dobro.domain.navigation.PickAddress"/>
    </fragment>

    <fragment
        android:id="@+id/appUpdateRequest"
        android:name="ru.dobro.app_update.AppUpdateRequestFragment"
        android:label=""
        tools:layout="@layout/fragment_app_update_request" />

    <dialog
        android:id="@+id/unauthorizedErrorFragment"
        android:name="ru.dobro.errors.unauthorized.UnauthorizedErrorFragment"
        android:label=""
        tools:layout="@layout/fragment__unauthorized_error"/>

    <fragment
        android:id="@+id/authorizationStubFragment"
        android:name="ru.dobro.authorization.AuthorizationStubFragment"
        android:label=""
        tools:layout="@layout/authorization___stub"/>

    <fragment
        android:id="@+id/eventDetailedFragment"
        android:name="ru.dobro.main.requests.EventDetailedFragment"
        android:label=""
        tools:layout="@layout/fragment_event_detailed">
        <action
            android:id="@+id/detailed__event__to__main__organization"
            app:destination="@id/main__organization"/>
        <action
            android:id="@+id/detailed__event__to__main__profile"
            app:destination="@id/public_profile"/>
        <action
            android:id="@+id/detailed__event__to__main__map"
            app:destination="@id/main__map"/>
        <action
            android:id="@+id/detailed__event__to__main__vacancy"
            app:destination="@id/main__vacancy"/>
    </fragment>

    <fragment
        android:id="@+id/requestsStatusFilterFragment"
        android:name="ru.dobro.main.requests.RequestsStatusFilterFragment"
        android:label="Статусы"/>

    <fragment
        android:id="@+id/vacancyRequestAcceptedFragment"
        android:name="ru.dobro.main.vacancy_request.VacancyRequestAcceptedFragment"
        android:label="VacancyRequestAcceptedFragment">
        <action
            android:id="@+id/vacancy__request__accepted__to__main__requests"
            app:destination="@+id/main__requests"
            app:popUpTo="@+id/main__event"
            app:popUpToInclusive="true"/>

        <action
            android:id="@+id/vacancy__request__accepted__to__main__search"
            app:destination="@+id/main__search"
            app:popUpTo="@+id/main__event"
            app:popUpToInclusive="true"/>
    </fragment>

    <fragment
        android:id="@+id/organizationStatusHelpFragment"
        android:name="ru.dobro.main.profile.organization.HelpMessageFragment"
        android:label="">
        <action
            android:id="@+id/action_organizationStatusHelpFragment_to_profileMeOrganizationFragment"
            app:destination="@+id/profileMeOrganizationFragment"
            app:popUpTo="@id/profileMeOrganizationFragment"
            app:popUpToInclusive="true"/>
        <action
            android:id="@+id/action_organizationStatusHelpFragment_to_main__profile_me"
            app:destination="@+id/main__profile_me"
            app:popUpTo="@id/main__profile_me"
            app:popUpToInclusive="true"/>
    </fragment>

    <fragment
        android:id="@+id/donorOnboardingFragment"
        android:name="ru.dobro.main.profile.DonorOnboardingFragment"
        android:label=""/>
</navigation>