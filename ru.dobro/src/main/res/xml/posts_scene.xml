<?xml version="1.0" encoding="utf-8"?>
<MotionScene
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ConstraintSet android:id="@+id/showToolbar">

        <Constraint
            android:id="@id/appBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"/>
    </ConstraintSet>

    <ConstraintSet android:id="@+id/hideToolbar">

        <Constraint
            android:id="@id/appBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="parent"/>
    </ConstraintSet>

    <Transition
        android:id="@+id/transition"
        app:constraintSetEnd="@id/hideToolbar"
        app:constraintSetStart="@id/showToolbar"
        app:duration="500"
        app:motionInterpolator="linear">

        <OnSwipe
            app:dragDirection="dragUp"
            app:touchAnchorId="@id/posts"/>
    </Transition>
</MotionScene>