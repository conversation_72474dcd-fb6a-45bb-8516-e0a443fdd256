<?xml version="1.0" encoding="utf-8"?>
<MotionScene
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:motion="http://schemas.android.com/apk/res-auto">

    <Transition
        android:id="@+id/requests__transition"
        motion:constraintSetEnd="@+id/end"
        motion:constraintSetStart="@id/start"
        motion:motionInterpolator="linear"
        motion:duration="100">

        <OnSwipe
            motion:dragDirection="dragUp"
            motion:touchAnchorId="@+id/requestsPager" />
    </Transition>

    <ConstraintSet android:id="@+id/start">
        <Constraint
            android:id="@+id/filtersContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            motion:layout_constraintTop_toBottomOf="@id/tabs"/>
    </ConstraintSet>

    <ConstraintSet android:id="@+id/end">
        <Constraint
            android:id="@+id/filtersContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            motion:layout_constraintBottom_toBottomOf="@id/tabs"/>
    </ConstraintSet>
</MotionScene>