<?xml version="1.0" encoding="utf-8"?>
<MotionScene
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ConstraintSet android:id="@+id/showToolbar">
        <Constraint
            android:id="@id/appBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"/>

        <!--        <Constraint-->
        <!--            android:id="@+id/addPost"-->
        <!--            android:layout_width="@dimen/application___dimen__x4"-->
        <!--            android:layout_height="@dimen/application___dimen__x4"-->
        <!--            app:layout_constraintEnd_toEndOf="parent"-->
        <!--            android:layout_marginEnd="@dimen/application___dimen__x6"-->
        <!--            android:layout_marginBottom="@dimen/application___dimen__x6"-->
        <!--            app:layout_constraintTop_toBottomOf="parent"/>-->
    </ConstraintSet>

    <ConstraintSet android:id="@+id/hideToolbar">
        <Constraint
            android:id="@id/appBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="parent"/>

        <!--        <Constraint-->
        <!--            android:id="@+id/addPost"-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            app:layout_constraintEnd_toEndOf="parent"-->
        <!--            android:layout_marginEnd="@dimen/application___dimen__x4"-->
        <!--            android:layout_marginBottom="@dimen/application___dimen__x4"-->
        <!--            app:layout_constraintBottom_toBottomOf="parent"/>-->
    </ConstraintSet>

    <Transition
        android:id="@+id/transition"
        app:constraintSetEnd="@id/hideToolbar"
        app:constraintSetStart="@id/showToolbar"
        app:motionInterpolator="linear"
        app:duration="500">
        <OnSwipe
            app:dragDirection="dragUp"
            app:touchAnchorId="@id/posts"/>
    </Transition>

</MotionScene>