<?xml version="1.0" encoding="utf-8"?>
<com.facebook.shimmer.ShimmerFrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main__organizers___skeleton__shimmer"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x1"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:background="@color/application___color__icons"/>

            <View
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:background="@color/application___color__icons"/>

            <include layout="@layout/common___my_organizer___skeleton__item"/>

            <include layout="@layout/common___my_organizer___skeleton__item"/>

            <include layout="@layout/common___my_organizer___skeleton__item"/>

            <include layout="@layout/common___my_organizer___skeleton__item"/>

            <include layout="@layout/common___my_organizer___skeleton__item"/>

            <include layout="@layout/common___my_organizer___skeleton__item"/>

            <include layout="@layout/common___my_organizer___skeleton__item"/>

            <include layout="@layout/common___my_organizer___skeleton__item"/>

            <include layout="@layout/common___my_organizer___skeleton__item"/>

            <include layout="@layout/common___my_organizer___skeleton__item"/>

            <include layout="@layout/common___my_organizer___skeleton__item"/>

            <include layout="@layout/common___my_organizer___skeleton__item"/>

            <include layout="@layout/common___my_organizer___skeleton__item"/>

            <include layout="@layout/common___my_organizer___skeleton__item"/>

            <include layout="@layout/common___my_organizer___skeleton__item"/>

            <include layout="@layout/common___my_organizer___skeleton__item"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</com.facebook.shimmer.ShimmerFrameLayout>