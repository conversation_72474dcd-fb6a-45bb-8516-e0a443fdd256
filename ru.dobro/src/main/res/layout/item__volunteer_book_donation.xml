<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="10dp"
    android:animateLayoutChanges="true"
    android:background="@drawable/application___background__round_corners_no_padding">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="0dp"
        app:cardBackgroundColor="@color/application___color__honorary_donor"
        app:cardCornerRadius="@dimen/application___dimen__x4"
        app:cardElevation="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:strokeWidth="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/donation_icon"
                android:layout_width="@dimen/application___dimen__x11"
                android:layout_height="@dimen/application___dimen__x11"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:padding="@dimen/application___dimen__x3"
                android:src="@drawable/ic_donation"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/donation_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/main__profile___volunteer_book__donations_variant"
                android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                android:textSize="17sp"
                app:layout_constraintBottom_toBottomOf="@+id/donation_icon"
                app:layout_constraintStart_toEndOf="@+id/donation_icon"
                app:layout_constraintTop_toTopOf="@+id/donation_icon"/>

            <ImageView
                android:id="@+id/arrow"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginEnd="@dimen/application___dimen__x2"
                android:src="@drawable/application___arrow_down_variant"
                app:layout_constraintBottom_toBottomOf="@+id/donation_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/donation_title"
                app:tint="@color/application___color__primary"
                tools:ignore="ContentDescription"/>

            <ImageView
                android:id="@+id/honoraryDonorIcon"
                android:layout_width="70dp"
                android:layout_height="100dp"
                android:src="@drawable/ic_honorary_donor"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/arrow"
                app:layout_constraintTop_toTopOf="parent"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/donationsCount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x5"
                android:layout_marginTop="-6dp"
                android:layout_marginEnd="@dimen/application___dimen__x1"
                android:autoSizeMaxTextSize="13sp"
                android:autoSizeMinTextSize="9sp"
                android:autoSizeStepGranularity="1sp"
                android:autoSizeTextType="uniform"
                android:gravity="start"
                android:maxLines="1"
                android:text="@string/main__profile___volunteer_book__donationsCount"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                android:textSize="13sp"
                app:layout_constraintEnd_toStartOf="@+id/honoraryDonorIcon"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/donation_icon"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/honoraryDonorTile"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x5"
                android:layout_marginTop="10dp"
                android:text="Почётный донор России"
                android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                android:textSize="15sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/donationsCount"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/honoraryDonorSubTile"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x5"
                android:layout_marginTop="@dimen/application___dimen__x1"
                android:layout_marginBottom="@dimen/application___dimen__x4"
                android:gravity="center"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/honoraryDonorTile"
                tools:text="12 декабря 2024"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <LinearLayout
        android:id="@+id/datesContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x5"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/card">


    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>