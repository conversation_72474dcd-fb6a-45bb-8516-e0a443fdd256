<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".main.about.knowledge_base.KnowledgeBaseFragment">

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/search_container"
            style="@style/Application.Design.InputFilled.Shade"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginBottom="0dp"
            android:hint="Поиск по базе знаний"
            android:visibility="gone"
            app:boxBackgroundColor="@color/white"
            app:boxCornerRadiusBottomEnd="@dimen/application___dimen__x4"
            app:boxCornerRadiusBottomStart="@dimen/application___dimen__x4"
            app:boxCornerRadiusTopEnd="@dimen/application___dimen__x4"
            app:boxCornerRadiusTopStart="@dimen/application___dimen__x4"
            app:endIconDrawable="@drawable/application___close"
            app:endIconMode="clear_text"
            app:startIconDrawable="@drawable/application___search"
            tools:visibility="visible">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/search"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:imeOptions="actionDone"
                android:inputType="text"/>

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            app:tabMode="scrollable"
            app:tabTextAppearance="@style/Application.Design.Body2.TextAppearance"/>

        <View
            android:id="@+id/tabs_line"
            style="@style/Application.Divider"
            android:background="#E3E3EF"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/requestTitle"/>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/categories"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:nestedScrollingEnabled="false"
        android:orientation="vertical"
        android:paddingBottom="@dimen/application___dimen__x2"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/container"
        tools:listitem="@layout/item_knowledge_base_section"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/search_results"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:nestedScrollingEnabled="false"
        android:orientation="vertical"
        android:paddingBottom="@dimen/application___dimen__x2"
        android:visibility="gone"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/container"
        tools:listitem="@layout/item_knowledge_base_section"/>

    <ProgressBar
        android:id="@+id/progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:elevation="@dimen/application___dimen__x1"
        android:indeterminateTint="@color/application___color__primary"
        android:scaleX="0.5"
        android:scaleY="0.5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>