<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/scroll"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/application___dimen__x3">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/main__profile_edit___avatar"
                android:layout_width="110dp"
                android:layout_height="110dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/application___dimen__x6"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___avatar__edit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:foreground="?selectableItemBackground"
                android:gravity="center"
                android:text="@string/main__profile_edit___avatar__edit"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"/>

            <View
                style="@style/Application.Divider.Wide"
                android:layout_marginTop="@dimen/application___dimen__x4"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___info__title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableEnd="@drawable/application___arrow_down"
                android:foreground="?selectableItemBackground"
                android:padding="@dimen/application___dimen__x4"
                android:text="@string/main__profile_edit___info__title"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"/>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___info__name__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:hint="@string/main__profile_edit___info__name">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___info__name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:digits="ёйцукенгшщзхъфывапролджэячсмитьбю ЁЙЦУКЕНГШЩЗХЪФЫВАПРОЛДЖЭЯЧСМИТЬБЮ"
                    android:imeOptions="actionNext"
                    android:inputType="textPersonName"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___info__surname__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:hint="@string/main__profile_edit___info__surname">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___info__surname"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:digits="ёйцукенгшщзхъфывапролджэячсмитьбю ЁЙЦУКЕНГШЩЗХЪФЫВАПРОЛДЖЭЯЧСМИТЬБЮ"
                    android:imeOptions="actionDone"
                    android:inputType="textPersonName"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___info__second__name__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:hint="@string/main__profile_edit___info__second_name">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___info__second__name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:digits="ёйцукенгшщзхъфывапролджэячсмитьбю ЁЙЦУКЕНГШЩЗХЪФЫВАПРОЛДЖЭЯЧСМИТЬБЮ"
                    android:imeOptions="actionDone"
                    android:inputType="textPersonName"/>
            </com.google.android.material.textfield.TextInputLayout>

            <LinearLayout
                android:id="@+id/main__profile_edit___info__without_second_name__container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:foreground="?selectableItemBackground"
                android:orientation="horizontal"
                android:padding="@dimen/application___dimen__x3">

                <CheckBox
                    android:id="@+id/main__profile_edit___info__without_second_name__check"
                    style="@style/Application.CheckBox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false"/>

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/main__profile_edit___info__has_second_name"
                    android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"/>
            </LinearLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___info__gender__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:hint="@string/main__profile_edit___info__gender"

                app:endIconDrawable="@drawable/application___arrow_drop_down"
                app:endIconMode="custom">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___info__gender"
                    style="@style/Application.EditText.Clickable"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___info__birth__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:enabled="false"
                android:hint="@string/main__profile_edit___info__birth"

                app:endIconDrawable="@drawable/application___calendar"
                app:endIconMode="custom">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___info__birth"
                    style="@style/Application.EditText.Clickable"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            </com.google.android.material.textfield.TextInputLayout>

            <LinearLayout
                android:id="@+id/main__profile_edit___info__russian_citizenship__container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:foreground="?selectableItemBackground"
                android:orientation="horizontal"
                android:padding="@dimen/application___dimen__x3">

                <CheckBox
                    android:id="@+id/main__profile_edit___info__russian_citizenship__check"
                    style="@style/Application.CheckBox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false"/>

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/main__profile_edit___info__russian_citizenship"
                    android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"/>
            </LinearLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___info__type_of_employment__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:hint="@string/main__profile_edit___info__type_of_employment"

                app:endIconDrawable="@drawable/application___arrow_drop_down"
                app:endIconMode="custom">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___info__type_of_employment"
                    style="@style/Application.EditText.Clickable"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___info__t_shirt_size__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:hint="@string/main__profile_edit___info__t_shirt_size"

                app:endIconDrawable="@drawable/application___arrow_drop_down"
                app:endIconMode="custom">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___info__t_shirt_size"
                    style="@style/Application.EditText.Clickable"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___info__organization__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:hint="@string/main__profile_edit___info__organization_name"

                app:endIconDrawable="@drawable/application___arrow_drop_down"
                app:endIconMode="custom">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___info__organization"
                    style="@style/Application.EditText.Clickable"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            </com.google.android.material.textfield.TextInputLayout>

            <LinearLayout
                android:id="@+id/main__profile_edit___info__medical_history__container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:foreground="?selectableItemBackground"
                android:orientation="horizontal"
                android:padding="@dimen/application___dimen__x3">

                <CheckBox
                    android:id="@+id/main__profile_edit___info__medical_history__check"
                    style="@style/Application.CheckBox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false"/>

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/main__profile_edit___info__medical_history"
                    android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"/>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/main__profile_edit___info__driver_licence__container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:foreground="?selectableItemBackground"
                android:orientation="horizontal"
                android:padding="@dimen/application___dimen__x3">

                <CheckBox
                    android:id="@+id/main__profile_edit___info__driver_licence__check"
                    style="@style/Application.CheckBox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false"/>

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/main__profile_edit___info__driver_licence"
                    android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"/>
            </LinearLayout>

            <View style="@style/Application.Divider.Wide"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___languages__title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableEnd="@drawable/application___arrow_down"
                android:foreground="?selectableItemBackground"
                android:padding="@dimen/application___dimen__x4"
                android:text="@string/main__profile_edit___languages"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"/>

            <LinearLayout
                android:id="@+id/main__profile_edit___languages__container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:orientation="vertical"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___languages__add"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginVertical="@dimen/application___dimen__x4"
                android:foreground="@drawable/application___ripple"
                android:text="@string/main__profile_edit___languages__add"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"/>

            <View style="@style/Application.Divider.Wide"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___educations__title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableEnd="@drawable/application___arrow_down"
                android:foreground="?selectableItemBackground"
                android:padding="@dimen/application___dimen__x4"
                android:text="@string/main__profile_edit___educations__title"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"/>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___educations__level__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:hint="@string/main__profile_edit___educations__level"

                app:endIconDrawable="@drawable/application___arrow_drop_down"
                app:endIconMode="custom">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___educations__level"
                    style="@style/Application.EditText.Clickable"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            </com.google.android.material.textfield.TextInputLayout>

            <LinearLayout
                android:id="@+id/main__profile_edit___educations__container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:orientation="vertical"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___educations__add"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginVertical="@dimen/application___dimen__x4"
                android:foreground="@drawable/application___ripple"
                android:text="@string/main__profile_edit___educations__add"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"/>

            <View style="@style/Application.Divider.Wide"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___jobs__title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableEnd="@drawable/application___arrow_down"
                android:foreground="?selectableItemBackground"
                android:padding="@dimen/application___dimen__x4"
                android:text="@string/main__profile_edit___jobs__title"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"/>

            <LinearLayout
                android:id="@+id/main__profile_edit___jobs__container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:orientation="vertical"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___jobs__add"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginVertical="@dimen/application___dimen__x4"
                android:foreground="@drawable/application___ripple"
                android:text="@string/main__profile_edit___jobs__add"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"/>

            <View style="@style/Application.Divider.Wide"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___contacts__title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableEnd="@drawable/application___arrow_down"
                android:foreground="?selectableItemBackground"
                android:padding="@dimen/application___dimen__x4"
                android:text="@string/application___contacts"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"/>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___contacts__phone__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:hint="@string/main__profile_edit___contacts__phone"

                app:endIconDrawable="@drawable/application___phone"
                app:endIconMode="custom">

                <ru.dobro.common.custom.PhoneInputEditText
                    android:id="@+id/main__profile_edit___contacts__phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:imeOptions="actionNext"
                    android:importantForAutofill="noExcludeDescendants"
                    android:inputType="phone"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___contacts__email__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:enabled="false"
                android:hint="@string/main__profile_edit___contacts__email"

                app:endIconDrawable="@drawable/application___email"
                app:endIconMode="custom">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___contacts__email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:imeOptions="actionDone"
                    android:inputType="textPersonName"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___contacts__email__change"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="31dp"
                android:layout_marginEnd="@dimen/application___dimen__x4"
                android:layout_marginBottom="@dimen/application___dimen__x3"
                android:text="@string/main__profile_edit___contacts__edit__email"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"/>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___contacts__country__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginVertical="@dimen/application___dimen__x3"
                android:hint="@string/address_selector___country__input__hint"
                app:endIconDrawable="@drawable/application___arrow_drop_down"
                app:endIconMode="custom">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___contacts__country"
                    style="@style/Application.EditText.Clickable"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___contacts__settlement__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginVertical="@dimen/application___dimen__x3"
                android:hint="@string/address_selector___settlement__input__hint"

                app:endIconDrawable="@drawable/application___arrow_drop_down"
                app:endIconMode="custom">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___contacts__settlement"
                    style="@style/Application.EditText.Clickable"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            </com.google.android.material.textfield.TextInputLayout>

            <View style="@style/Application.Divider.Wide"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___socials__title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableEnd="@drawable/application___arrow_down"
                android:foreground="?selectableItemBackground"
                android:padding="@dimen/application___dimen__x4"
                android:text="@string/main__profile_edit___socials__title"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"/>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___socials__vk__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:hint="@string/main__profile_edit___socials__hint"

                app:startIconDrawable="@drawable/main__profile___social__vk"
                app:startIconTint="@null">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___socials__vk"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:imeOptions="actionNext"
                    android:inputType="textNoSuggestions"/>
            </com.google.android.material.textfield.TextInputLayout>

            <!-- TODO убрали на время пока Meta запрещена. Не удалять, возможно, Meta еще будет доступна -->
            <!--            <com.google.android.material.textfield.TextInputLayout-->
            <!--                android:id="@+id/main__profile_edit___socials__facebook__container"-->
            <!--                style="@style/Application.Design.InputFilled.Shade"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginHorizontal="@dimen/application___dimen__x4"-->
            <!--                android:layout_marginTop="@dimen/application___dimen__x3"-->
            <!--                android:hint="@string/main__profile_edit___socials__hint"-->
            <!--                -->
            <!--                app:startIconDrawable="@drawable/main__profile___social__facebook"-->
            <!--                app:startIconTint="@null">-->

            <!--                <com.google.android.material.textfield.TextInputEditText-->
            <!--                    android:id="@+id/main__profile_edit___socials__facebook"-->
            <!--                    android:layout_width="match_parent"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:imeOptions="actionNext"-->
            <!--                    android:inputType="textNoSuggestions"/>-->
            <!--            </com.google.android.material.textfield.TextInputLayout>-->

            <!-- TODO убрали -->
            <!--            <com.google.android.material.textfield.TextInputLayout-->
            <!--                android:id="@+id/main__profile_edit___socials__youtube__container"-->
            <!--                style="@style/Application.Design.InputFilled.Shade"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginHorizontal="@dimen/application___dimen__x4"-->
            <!--                android:layout_marginTop="@dimen/application___dimen__x3"-->
            <!--                android:hint="@string/main__profile_edit___socials__hint"-->

            <!--                app:startIconDrawable="@drawable/main__profile___social__youtube"-->
            <!--                app:startIconTint="@null">-->

            <!--                <com.google.android.material.textfield.TextInputEditText-->
            <!--                    android:id="@+id/main__profile_edit___socials__youtube"-->
            <!--                    android:layout_width="match_parent"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:imeOptions="actionNext"-->
            <!--                    android:inputType="textNoSuggestions"/>-->
            <!--            </com.google.android.material.textfield.TextInputLayout>-->

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___socials__ok__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:hint="@string/main__profile_edit___socials__hint"

                app:startIconDrawable="@drawable/main__profile___social__ok"
                app:startIconTint="@null">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___socials__ok"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:imeOptions="actionNext"
                    android:inputType="textNoSuggestions"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___socials__telegram__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:hint="@string/main__profile_edit___socials__hint"
                app:startIconDrawable="@drawable/profile_telegram"
                app:startIconTint="@null">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___socials__telegram"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:imeOptions="actionNext"
                    android:inputType="textNoSuggestions"/>
            </com.google.android.material.textfield.TextInputLayout>


            <!-- TODO убрали на время пока Meta запрещена. Не удалять, возможно, Meta еще будет доступна -->
            <!--            <com.google.android.material.textfield.TextInputLayout-->
            <!--                android:id="@+id/main__profile_edit___socials__instagram__container"-->
            <!--                style="@style/Application.Design.InputFilled.Shade"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginHorizontal="@dimen/application___dimen__x4"-->
            <!--                android:layout_marginTop="@dimen/application___dimen__x3"-->
            <!--                android:hint="@string/main__profile_edit___socials__hint"-->
            <!--                -->
            <!--                app:startIconDrawable="@drawable/main__profile___social__instagram"-->
            <!--                app:startIconTint="@null">-->

            <!--                <com.google.android.material.textfield.TextInputEditText-->
            <!--                    android:id="@+id/main__profile_edit___socials__instagram"-->
            <!--                    android:layout_width="match_parent"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:imeOptions="actionNext"-->
            <!--                    android:inputType="textNoSuggestions"/>-->
            <!--            </com.google.android.material.textfield.TextInputLayout>-->

            <!-- TODO убрали -->
            <!--            <com.google.android.material.textfield.TextInputLayout-->
            <!--                android:id="@+id/main__profile_edit___socials__tiktok__container"-->
            <!--                style="@style/Application.Design.InputFilled.Shade"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginHorizontal="@dimen/application___dimen__x4"-->
            <!--                android:layout_marginVertical="@dimen/application___dimen__x3"-->
            <!--                android:hint="@string/main__profile_edit___socials__hint"-->

            <!--                app:startIconDrawable="@drawable/main__profile___social__tiktok"-->
            <!--                app:startIconTint="@null">-->

            <!--                <com.google.android.material.textfield.TextInputEditText-->
            <!--                    android:id="@+id/main__profile_edit___socials__tiktok"-->
            <!--                    android:layout_width="match_parent"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:imeOptions="actionDone"-->
            <!--                    android:inputType="textNoSuggestions"/>-->
            <!--            </com.google.android.material.textfield.TextInputLayout>-->

            <View style="@style/Application.Divider.Wide"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___about__title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableEnd="@drawable/application___arrow_down"
                android:foreground="?selectableItemBackground"
                android:padding="@dimen/application___dimen__x4"
                android:text="@string/main__profile_edit___about__title"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"/>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___about__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginVertical="@dimen/application___dimen__x3"
                android:hint="@string/main__profile_edit___about__hint"
                app:counterEnabled="true"
                app:counterMaxLength="3000">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___about"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:imeOptions="actionDone"
                    android:inputType="textMultiLine"
                    android:maxLength="3000"/>
            </com.google.android.material.textfield.TextInputLayout>

            <View style="@style/Application.Divider.Wide"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___additional__title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableEnd="@drawable/application___arrow_down"
                android:foreground="?selectableItemBackground"
                android:padding="@dimen/application___dimen__x4"
                android:text="@string/main__profile_edit___additional__title"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"/>

            <LinearLayout
                android:id="@+id/main__profile_edit___additional__container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/main__profile_edit___additional__background"
                android:orientation="vertical">

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x4"
                    android:text="@string/main__profile_edit___additional__subtitle"
                    android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"/>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/main__profile_edit___additional__snils__container"
                    style="@style/Application.Design.InputFilled.Shade"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x5"
                    android:hint="@string/main__profile_edit___additional__snils">

                    <ru.dobro.core.MaskedEditText
                        android:id="@+id/main__profile_edit___additional__snils"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:imeOptions="actionNext"
                        android:importantForAutofill="noExcludeDescendants"
                        android:inputType="numberDecimal"
                        app:allowed_chars="1234567890"
                        app:keep_hint="true"
                        app:mask="###-###-### ##"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/main__profile_edit___additional__foreign_passport__number__container"
                    style="@style/Application.Design.InputFilled.Shade"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x8"
                    android:hint="@string/main__profile_edit___additional__foreign_passport__number__main"
                    android:visibility="gone"
                    app:helperText="@string/main__profile_edit___additional__foreign_passport__number__helper">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/main__profile_edit___additional__foreign_passport__number"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="top"
                        android:imeOptions="actionNext"
                        android:maxLines="1"
                        android:textColorHint="@color/application___color__icons"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/main__profile_edit___additional__foreign_passport__issue_date__container"
                    style="@style/Application.Design.InputFilled.Shade"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x4"
                    android:hint="@string/main__profile_edit___additional__foreign_passport__issue_date"
                    android:visibility="gone"
                    app:endIconDrawable="@drawable/application___calendar"
                    app:endIconMode="custom">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/main__profile_edit___additional__foreign_passport__issue_date"
                        style="@style/Application.EditText.Clickable"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:imeOptions="actionNext"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/main__profile_edit___additional__foreign_passport__expiration_date__container"
                    style="@style/Application.Design.InputFilled.Shade"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x4"
                    android:hint="@string/main__profile_edit___additional__foreign_passport__expiration_date"
                    android:visibility="gone"
                    app:endIconDrawable="@drawable/application___calendar"
                    app:endIconMode="custom">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/main__profile_edit___additional__foreign_passport__expiration_date"
                        style="@style/Application.EditText.Clickable"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:imeOptions="actionNext"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/main__profile_edit___additional__foreign_passport__birthplace__container"
                    style="@style/Application.Design.InputFilled.Shade"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x4"
                    android:hint="@string/main__profile_edit___additional__foreign_passport__birth__main"
                    android:visibility="gone"
                    app:helperText="@string/main__profile_edit___additional__foreign_passport__birth__helper">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/main__profile_edit___additional__foreign_passport__birthplace"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:imeOptions="actionNext"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/main__profile_edit___additional__foreign__address__container"
                    style="@style/Application.Design.InputFilled.Shade"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:layout_marginBottom="@dimen/application___dimen__x8"
                    android:hint="@string/main__profile_edit___additional__foreign__address__main"
                    android:visibility="visible"
                    app:helperText="@string/main__profile_edit___additional__foreign__address__helper">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/main__profile_edit___additional__foreign__address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:imeOptions="actionNext"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/main__profile_edit___additional__person_id__container"
                    style="@style/Application.Design.InputFilled.Shade"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x3"
                    android:hint="@string/main__profile_edit___additional__person_id"
                    android:visibility="gone">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/main__profile_edit___additional__person_id"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:imeOptions="actionDone"
                        android:inputType="numberDecimal"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__profile_edit___additional__passport__add"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/application___dimen__x4"
                    android:foreground="?selectableItemBackground"
                    android:text="@string/main__profile_edit___additional__passport__add"
                    android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"/>
            </LinearLayout>

            <View style="@style/Application.Divider.Wide"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___password__title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/application___dimen__x4"
                android:text="@string/main__profile_edit___password__title"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___password__change"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginBottom="@dimen/application___dimen__x4"
                android:foreground="?selectableItemBackground"
                android:text="@string/main__profile_edit___password__change"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"/>

            <View style="@style/Application.Divider.Wide"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___remove_account"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginVertical="@dimen/application___dimen__x4"
                android:drawableStart="@drawable/application___delete"
                android:drawablePadding="@dimen/application___dimen__x2"
                android:foreground="@drawable/application___ripple"
                android:gravity="center"
                android:text="@string/main__profile_edit___remove_account"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance"
                android:textColor="@color/application___color__primary"
                app:drawableTint="@color/application___color__primary"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/main__profile_edit___progress"
        style="@style/Application.Progress"
        android:layout_gravity="center"/>
</FrameLayout>