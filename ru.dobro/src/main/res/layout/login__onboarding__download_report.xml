<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/login__onboarding__download__skip"
        style="@style/Application.Design.Button.Text.Gray"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_margin="@dimen/application___dimen__x4"
        android:text="@string/application___skip"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />


    <TextView
        android:id="@+id/login__onboarding__download__title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:text="@string/login_onboarding_download_title"
        android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/login__onboarding__download__skip"
        app:layout_constraintBottom_toTopOf="@id/login__onboarding__download__reports"/>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/login__onboarding__download__reports"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:listitem="@layout/login_onboarding_download_reports_item"
        android:layout_marginVertical="@dimen/application___dimen__x4"
        app:layout_constraintTop_toBottomOf="@id/login__onboarding__download__title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/login__onboarding__download__next"
       />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/login__onboarding__download__next"
        style="@style/Application.Design.Button.Primary"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginBottom="@dimen/application___dimen__x8"
        android:text="@string/application___next"
        app:iconGravity="end"
        android:enabled="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/login__onboarding__download__reports"
        />

    <ProgressBar
        android:id="@+id/progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleX="0.6"
        android:scaleY="0.6"
        android:indeterminateTint="@color/application___color__primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible"/>

</androidx.constraintlayout.widget.ConstraintLayout>