<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:foreground="?selectableItemBackground"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/application___dimen__x4"
    android:paddingVertical="@dimen/application___dimen__x3">

    <CheckBox
        android:id="@+id/common___multiple_select__menu__item__check"
        style="@style/Application.CheckBox"
        android:layout_width="wrap_content"
        android:clickable="false"
        android:layout_gravity="center_vertical"
        android:layout_height="wrap_content"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/common___multiple_select__menu__item__title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/application___dimen__x1"
        android:layout_gravity="center_vertical"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"/>
</LinearLayout>