<?xml version="1.0" encoding="utf-8"?>
<com.facebook.shimmer.ShimmerFrameLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?selectableItemBackgroundBorderless">

        <View
            android:id="@+id/image"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginStart="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:background="@drawable/application___background__round_corners"
            android:backgroundTint="@color/application___color__icons"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/rounded9ImageView"
            tools:src="@drawable/main__vacancy__request_failed___illustration"/>

        <View
            android:id="@+id/title2"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:layout_marginEnd="@dimen/application___dimen__x4"
            android:background="@drawable/application___background__round_corners"
            android:backgroundTint="@color/application___color__icons"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/image"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/rounded9ImageView"
            tools:src="@drawable/main__vacancy__request_failed___illustration"/>


        <View
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x6"
            android:background="@drawable/application___background__round_corners"
            android:backgroundTint="@color/application___color__icons"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/image"
            tools:text="Станьте волонтёром для праздников в детском лагере"/>

        <View
            android:id="@+id/periodContainer"
            android:layout_width="0dp"
            android:layout_height="30dp"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x3"
            android:background="@drawable/application___background__round_corners"
            android:backgroundTint="@color/application___color__icons"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title"/>

        <View
            android:id="@+id/timeContainer"
            android:layout_width="0dp"
            android:layout_height="30dp"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x3"
            android:background="@drawable/application___background__round_corners"
            android:backgroundTint="@color/application___color__icons"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/periodContainer"/>

        <View
            android:id="@+id/btn"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x3"
            android:background="@drawable/application___background__round_corners"
            android:backgroundTint="@color/application___color__icons"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/timeContainer"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.facebook.shimmer.ShimmerFrameLayout>