<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/application___background__round_corners_no_padding"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x4">

        <androidx.cardview.widget.CardView
            android:id="@+id/avatar"
            android:layout_width="50dp"
            android:layout_height="50dp"
            app:cardCornerRadius="99dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:src="@mipmap/ic_launcher_round"
                tools:ignore="ContentDescription"/>
        </androidx.cardview.widget.CardView>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x4"
            android:layout_marginEnd="@dimen/application___dimen__x1"
            android:drawableEnd="@drawable/ic_banner_pin"
            android:drawablePadding="@dimen/application___dimen__x1"
            android:drawableTint="@color/application___color__icons_dark"
            android:ellipsize="end"
            android:lines="1"
            android:maxLines="1"
            android:text="Добро.рф"
            android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
            android:textSize="15sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@+id/avatar"
            app:layout_constraintTop_toTopOf="parent"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/date"
            style="@style/Application.Design.Body1.TextAppearance.DarkGray"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x1"
            android:layout_marginEnd="@dimen/application___dimen__x1"
            android:textSize="15sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/avatar"
            app:layout_constraintTop_toBottomOf="@+id/name"
            tools:text="22.06.2004"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x3"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
        android:textSize="15sp"
        tools:text="Сегодня на кассе в магазине я увидел, что бабушка не может купить себе с пенсии стандартный набор продуктов. Я решил ей помочь и купил разного на неделю"/>

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/image"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/application___dimen__x3"
        android:adjustViewBounds="true"
        android:scaleType="fitCenter"
        app:layout_constraintBottom_toBottomOf="parent"
        app:shapeAppearanceOverlay="@style/roundedBottomImageView"
        tools:ignore="ContentDescription"/>
</LinearLayout>
