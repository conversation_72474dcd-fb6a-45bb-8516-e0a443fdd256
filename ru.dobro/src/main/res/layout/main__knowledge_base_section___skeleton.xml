<?xml version="1.0" encoding="utf-8"?>
<com.facebook.shimmer.ShimmerFrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main__notifications___skeleton__shimmer"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone"
    tools:visibility="visible">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@color/application___color__icons"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:background="@color/application___color__icons"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:background="@color/application___color__icons"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:background="@color/application___color__icons"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:background="@color/application___color__icons"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:background="@color/application___color__icons"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:background="@color/application___color__icons"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:background="@color/application___color__icons"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:background="@color/application___color__icons"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:background="@color/application___color__icons"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:background="@color/application___color__icons"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:background="@color/application___color__icons"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:background="@color/application___color__icons"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:background="@color/application___color__icons"/>
    </LinearLayout>
</com.facebook.shimmer.ShimmerFrameLayout>