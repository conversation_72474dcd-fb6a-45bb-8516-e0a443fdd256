<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/eventDetailed"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:paddingBottom="@dimen/application___dimen__x2"
        tools:context=".main.requests.EventDetailedFragment">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x5"
            android:background="@drawable/application___background__round_corners"
            android:backgroundTint="@color/application___color__button__gray__15"
            android:paddingHorizontal="@dimen/application___dimen__x3"
            android:paddingVertical="@dimen/application___dimen__x1"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Статус"
            tools:visibility="visible"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x5"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/status"
            tools:text="Преподаватель на обучающем воркшопе искусств и творчества"
            tools:visibility="visible"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/functionContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/functionIcon"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/application__function"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/main__requests___icons"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/functionAssistant"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x2"
                android:background="?selectableItemBackground"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/functionIcon"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Ассистент"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/locationContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/functionContainer"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/locationIcon"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_location_v4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/main__requests___icons"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/location"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x3"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toTopOf="@+id/locationShowMap"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/locationIcon"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Адрес: Королёв, Московская область"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/locationShowMap"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="?selectableItemBackground"
                android:gravity="center_vertical"
                android:text="@string/main__event___location__open_map"
                android:textAppearance="@style/Application.Design.Body2.TextAppearance.Primary"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="@+id/location"
                app:layout_constraintTop_toBottomOf="@+id/location"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/calendarContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/locationContainer"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/calendarIcon"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_calendar"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/main__requests___icons"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x3"
                android:gravity="center_vertical"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/calendarIcon"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="16 — 18 февраля 2024"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/calendarContainer">

            <LinearLayout
                android:id="@+id/rejectContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x5"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:background="@drawable/application___background__round_corners"
                android:backgroundTint="@color/main__requests___status__rejected_background"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/rejectTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x4"
                    android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"
                    tools:text="@string/main__requests___status__reject_by_organization__title"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/rejectText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x1"
                    android:layout_marginBottom="@dimen/application___dimen__x4"
                    android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/calendarIcon"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="тест тест тест тест тест тест тест тест тест тест тест тесттест тест тест тесттест тест тест тесттест тест тест тесттест тест тест тест"/>

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/timetable"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:visibility="gone"
                android:layout_marginHorizontal="@dimen/application___dimen__x1"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/calendarContainer"/>

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/vkContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x5"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/container"
            tools:visibility="visible">

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/vkTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:text="Ссылка на чат мероприятия в ВКонтакте"
                android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/vkOpenChat"
                style="@style/Application.Design.Button.Primary"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:layout_marginEnd="@dimen/application___dimen__x2"
                android:layout_marginBottom="@dimen/application___dimen__x4"
                android:padding="0dp"
                android:paddingVertical="@dimen/application___dimen__x6"
                android:text="@string/main__requests___vacancy__item__vk__chat"
                app:layout_constraintEnd_toStartOf="@+id/vkCopyLink"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/vkTitle"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/vkCopyLink"
                style="@style/Application.Design.Button.Gray"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x2"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:layout_marginBottom="@dimen/application___dimen__x4"
                android:drawablePadding="-50dp"
                android:padding="0dp"
                android:paddingVertical="@dimen/application___dimen__x6"
                android:text="@string/main__requests___vacancy__item__vk__copy"
                android:textColor="@color/application___color__primary"
                app:icon="@drawable/ic_copy"
                app:iconGravity="textStart"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/vkOpenChat"
                app:layout_constraintTop_toBottomOf="@+id/vkTitle"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/organizerTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x5"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:text="Организатор мероприятия"
            android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vkContainer"
            tools:visibility="visible"/>

        <ProgressBar
            android:id="@+id/progressOrganization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:indeterminateTint="@color/application___color__primary"
            android:translationZ="1dp"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/organizerTitle"
            tools:visibility="visible"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/organizerContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/organizerTitle"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/organizerIcon"
                android:layout_width="30dp"
                android:layout_height="30dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription"
                tools:src="@drawable/application___logo"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/organizer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x2"
                android:background="?selectableItemBackground"
                android:gravity="center_vertical"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toTopOf="@+id/organizerRating"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@+id/organizerIcon"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Ассоциация ассоциированных Сергеев"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/organizerRating"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x1"
                android:gravity="center_vertical"
                android:textAppearance="@style/Application.Design.Body2.TextAppearance.Gray"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="@+id/organizer"
                app:layout_constraintTop_toBottomOf="@+id/organizer"
                tools:text="ЗВЕЗДА 4.9"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/phoneContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/organizerContainer"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/phoneIcon"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_phone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/main__requests___icons"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/phoneText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x3"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:text="@string/main__organization___phone_variant"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/phoneIcon"
                app:layout_constraintTop_toTopOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/emailContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/phoneContainer"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/emailIcon"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_mail"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/main__requests___icons"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/emailText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x3"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:text="@string/main__organization___email"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/emailIcon"
                app:layout_constraintTop_toTopOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ProgressBar
            android:id="@+id/progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:indeterminateTint="@color/application___color__primary"
            android:translationZ="1dp"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.5"
            tools:visibility="visible"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>
