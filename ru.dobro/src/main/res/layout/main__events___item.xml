<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    android:id="@+id/cardView_events"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/application___dimen__x4"
    android:layout_marginVertical="@dimen/application___dimen__x2"
    android:foreground="?selectableItemBackgroundBorderless"
    app:contentPaddingBottom="@dimen/application___dimen__x4"
    app:cardCornerRadius="@dimen/application___dimen__x3">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/main__events___item__image"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginBottom="@dimen/application___dimen__x1"
            android:scaleType="centerCrop"
            app:layout_constraintDimensionRatio="H, 328:200"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/main__events___item__title"
            tools:background="@color/black"
            tools:ignore="ContentDescription"
            tools:src="@drawable/application___logo"/>

        <LinearLayout
            android:id="@+id/main__events___item__first_category"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/application___dimen__x2"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:background="@drawable/application___background__white__small"
            android:orientation="horizontal"
            android:textAppearance="@style/Application.Design.Body2.TextAppearance.Primary"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintBottom_toTopOf="@+id/main__events___item__title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0"
            tools:text="Помощь нуждающимся">

            <ImageView
                android:id="@+id/main__events___category__icon"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/application___dimen__x2"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__events__category___text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:maxLines="1"
                android:padding="@dimen/application___dimen__x2"
                android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"
                tools:text="Помощь нуждающимся"/>
        </LinearLayout>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/main__events___item__category_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:layout_marginEnd="@dimen/application___dimen__x2"
            android:background="@drawable/application___background__white__small"
            android:gravity="center"
            android:padding="@dimen/application___dimen__x2"
            android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintBottom_toBottomOf="@+id/main__events___item__first_category"
            app:layout_constraintStart_toEndOf="@+id/main__events___item__first_category"
            app:layout_constraintTop_toTopOf="@+id/main__events___item__first_category"
            tools:text="+3"
            tools:visibility="visible"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/main__events___item__title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x1"
            android:layout_marginBottom="@dimen/application___dimen__x1"
            android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toTopOf="@+id/main__events___item__period"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main__events___item__image"
            tools:text="Станьте добрым чулабком"/>

        <ImageView
            android:id="@+id/main__events___item__period__icon"
            android:layout_width="@dimen/application___dimen__x3"
            android:layout_height="@dimen/application___dimen__x3"
            android:layout_marginStart="@dimen/application___dimen__x5"
            android:src="@drawable/application___calendar"
            app:layout_constraintBottom_toBottomOf="@+id/main__events___item__period"
            app:layout_constraintEnd_toStartOf="@+id/main__events___item__period"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/main__events___item__period"
            tools:ignore="ContentDescription"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/main__events___item__period"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:layout_marginEnd="@dimen/application___dimen__x4"
            android:gravity="center_vertical"
            android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toTopOf="@+id/main__events___item__address"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/main__events___item__period__icon"
            app:layout_constraintTop_toBottomOf="@+id/main__events___item__title"
            tools:text="12 апреля 2021, 19:40"/>

        <ImageView
            android:id="@+id/main__events___item__address__icon"
            android:layout_width="@dimen/application___dimen__x3"
            android:layout_height="@dimen/application___dimen__x3"
            android:layout_marginStart="@dimen/application___dimen__x5"
            android:src="@drawable/application___location"
            app:layout_constraintBottom_toBottomOf="@+id/main__events___item__address"
            app:layout_constraintEnd_toStartOf="@+id/main__events___item__address"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/main__events___item__address"
            tools:ignore="ContentDescription"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/main__events___item__address"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:layout_marginEnd="@dimen/application___dimen__x4"
            android:gravity="center_vertical"
            android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/main__events___item__address__icon"
            app:layout_constraintTop_toBottomOf="@+id/main__events___item__period"
            tools:text="Вольный Аул, г.Нальчик"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>