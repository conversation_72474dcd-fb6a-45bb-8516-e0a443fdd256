<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".main.profile.achievements.info.AchievementInfoFragment">

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/shimmer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x2"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:layout_marginEnd="@dimen/application___dimen__x1"
                        android:layout_weight="1"
                        android:background="@drawable/application___background__round_corners"
                        android:backgroundTint="@color/application___color__icons"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:layout_marginStart="@dimen/application___dimen__x1"
                        android:layout_weight="1"
                        android:background="@drawable/application___background__round_corners"
                        android:backgroundTint="@color/application___color__icons"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:layout_marginEnd="@dimen/application___dimen__x1"
                        android:layout_weight="1"
                        android:background="@drawable/application___background__round_corners"
                        android:backgroundTint="@color/application___color__icons"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:layout_marginStart="@dimen/application___dimen__x1"
                        android:layout_weight="1"
                        android:background="@drawable/application___background__round_corners"
                        android:backgroundTint="@color/application___color__icons"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:layout_marginEnd="@dimen/application___dimen__x1"
                        android:layout_weight="1"
                        android:background="@drawable/application___background__round_corners"
                        android:backgroundTint="@color/application___color__icons"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:layout_marginStart="@dimen/application___dimen__x1"
                        android:layout_weight="1"
                        android:background="@drawable/application___background__round_corners"
                        android:backgroundTint="@color/application___color__icons"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:layout_marginEnd="@dimen/application___dimen__x1"
                        android:layout_weight="1"
                        android:background="@drawable/application___background__round_corners"
                        android:backgroundTint="@color/application___color__icons"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:layout_marginStart="@dimen/application___dimen__x1"
                        android:layout_weight="1"
                        android:background="@drawable/application___background__round_corners"
                        android:backgroundTint="@color/application___color__icons"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:layout_marginEnd="@dimen/application___dimen__x1"
                        android:layout_weight="1"
                        android:background="@drawable/application___background__round_corners"
                        android:backgroundTint="@color/application___color__icons"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:layout_marginStart="@dimen/application___dimen__x1"
                        android:layout_weight="1"
                        android:background="@drawable/application___background__round_corners"
                        android:backgroundTint="@color/application___color__icons"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:layout_marginEnd="@dimen/application___dimen__x1"
                        android:layout_weight="1"
                        android:background="@drawable/application___background__round_corners"
                        android:backgroundTint="@color/application___color__icons"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:layout_marginStart="@dimen/application___dimen__x1"
                        android:layout_weight="1"
                        android:background="@drawable/application___background__round_corners"
                        android:backgroundTint="@color/application___color__icons"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:layout_marginEnd="@dimen/application___dimen__x1"
                        android:layout_weight="1"
                        android:background="@drawable/application___background__round_corners"
                        android:backgroundTint="@color/application___color__icons"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:layout_marginStart="@dimen/application___dimen__x1"
                        android:layout_weight="1"
                        android:background="@drawable/application___background__round_corners"
                        android:backgroundTint="@color/application___color__icons"/>

                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </com.facebook.shimmer.ShimmerFrameLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/achievementsList"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/application___color__shade"
        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:spanCount="2"
        tools:listitem="@layout/item_all_trophies"/>

</androidx.constraintlayout.widget.ConstraintLayout>