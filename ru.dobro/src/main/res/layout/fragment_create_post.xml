<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".main.posts.createPost.CreatePostFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/topContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/application___dimen__x2"
            android:layout_marginStart="@dimen/application___dimen__x4"
            android:gravity="center_vertical"
            android:text="Отменить"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/publishPost"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|center_vertical"
            android:layout_marginVertical="@dimen/application___dimen__x2"
            android:layout_marginEnd="@dimen/application___dimen__x5"
            android:background="@drawable/application___background__white"
            android:backgroundTint="@color/application___color__primary"
            android:paddingHorizontal="@dimen/application___dimen__x2"
            android:paddingVertical="@dimen/application___dimen__x1"
            android:text="@string/publish_post"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance.White"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/scrollContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/scroll"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x3"
                    android:layout_marginEnd="@dimen/application___dimen__x3"
                    android:text="@string/create_post_title"
                    android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                    android:textSize="18sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/titleDescription"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:layout_marginEnd="@dimen/application___dimen__x3"
                    android:text="@string/create_post_description"
                    android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                    android:textSize="15sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/title"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/warning"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x4"
                    android:background="@drawable/application___background__round_corners_small"
                    android:backgroundTint="#1AFF3B30"
                    android:paddingHorizontal="@dimen/application___dimen__x4"
                    android:paddingVertical="@dimen/application___dimen__x3"
                    android:text="@string/create_post_warning"
                    android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                    android:textSize="15sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/titleDescription"/>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/selectCategory"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x4"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/warning"
                    app:strokeWidth="1dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:id="@+id/selectCategoryImage"
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:layout_marginVertical="@dimen/application___dimen__x3"
                            android:layout_marginStart="@dimen/application___dimen__x4"
                            android:padding="@dimen/application___dimen__x2"
                            android:src="@drawable/ic_all_categories_variant"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:tint="@color/application___color__primary"
                            tools:ignore="ContentDescription"/>

                        <com.google.android.material.textview.MaterialTextView
                            android:id="@+id/selectCategoryText"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/application___dimen__x2"
                            android:ellipsize="end"
                            android:lines="1"
                            android:maxLines="1"
                            android:text="@string/create_post_select_category"
                            android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                            android:textSize="15sp"
                            app:layout_constraintBottom_toBottomOf="@+id/selectCategoryImage"
                            app:layout_constraintEnd_toStartOf="@+id/selectCategoryArrow"
                            app:layout_constraintStart_toEndOf="@+id/selectCategoryImage"
                            app:layout_constraintTop_toTopOf="@+id/selectCategoryImage"/>

                        <ImageView
                            android:id="@+id/selectCategoryArrow"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/application___dimen__x4"
                            android:padding="@dimen/application___dimen__x2"
                            android:src="@drawable/application___arrow_down_variant_2"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:ignore="ContentDescription"/>
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/selectCategoryWarning"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:text="@string/create_post_select_category_warning"
                    android:textAppearance="@style/Application.Design.Body1.TextAppearance.Red"
                    android:textSize="15sp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/selectCategory"
                    tools:visibility="visible"/>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/description"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x4"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/selectCategoryWarning"
                    app:strokeWidth="1dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:id="@+id/descriptionAvatar"
                            android:layout_width="@dimen/application___dimen__x10"
                            android:layout_height="@dimen/application___dimen__x10"
                            android:layout_marginStart="@dimen/application___dimen__x4"
                            android:layout_marginTop="@dimen/application___dimen__x4"
                            android:src="@drawable/application___person"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:ignore="ContentDescription"/>

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/descriptionTextContainer"
                            style="@style/Application.Design.Input.Empty"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/application___dimen__x3"
                            android:layout_marginEnd="@dimen/application___dimen__x1"
                            android:layout_marginBottom="@dimen/application___dimen__x2"
                            app:hintEnabled="false"
                            app:layout_constraintBottom_toTopOf="@+id/descriptionHelp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/descriptionAvatar"
                            app:layout_constraintTop_toTopOf="parent">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/descriptionText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="@string/create_post_description_hint"
                                android:imeOptions="actionDone"
                                android:inputType="textMultiLine"
                                android:maxLength="250"
                                android:textColorHint="@color/application___color__gray__60"/>
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textview.MaterialTextView
                            android:id="@+id/descriptionHelp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/application___dimen__x4"
                            android:layout_marginBottom="@dimen/application___dimen__x4"
                            android:gravity="center_vertical"
                            android:text="0/250 • Минимум 20"
                            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"
                            android:textColor="@color/application___color__gray__60"
                            android:textSize="13sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"/>
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/descriptionWarning"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:text="@string/create_post_description_warning"
                    android:textAppearance="@style/Application.Design.Body1.TextAppearance.Red"
                    android:textSize="15sp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/description"
                    tools:visibility="visible"/>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/selectPhoto"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x4"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/descriptionWarning"
                    app:strokeWidth="1dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:id="@+id/selectedPhotoImage"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:adjustViewBounds="true"
                            android:maxHeight="400dp"
                            android:scaleType="fitCenter"
                            android:visibility="gone"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:ignore="ContentDescription"/>

                        <ImageView
                            android:id="@+id/selectPhotoDelete"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/application___dimen__x4"
                            android:layout_marginEnd="@dimen/application___dimen__x4"
                            android:background="@drawable/main__search___calendar_enabler__background"
                            android:backgroundTint="@color/white"
                            android:src="@drawable/ic_delete"
                            android:visibility="gone"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:ignore="ContentDescription"/>

                        <LinearLayout
                            android:id="@+id/selectPhotoContainer"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginVertical="@dimen/application___dimen__x7"
                            android:gravity="center_horizontal"
                            android:orientation="vertical"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@drawable/ic_add_photo"
                                app:tint="@color/application___color__primary"
                                tools:ignore="ContentDescription"/>

                            <com.google.android.material.textview.MaterialTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/application___dimen__x1"
                                android:text="@string/create_post_select_photo"
                                android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                                android:textSize="15sp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"/>
                        </LinearLayout>
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/selectPhotoWarning"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:layout_marginBottom="@dimen/application___dimen__x2"
                    android:text="@string/create_post_select_photo_warning"
                    android:textAppearance="@style/Application.Design.Body1.TextAppearance.Red"
                    android:textSize="15sp"
                    android:visibility="invisible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/selectPhoto"
                    tools:visibility="visible"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
</LinearLayout>