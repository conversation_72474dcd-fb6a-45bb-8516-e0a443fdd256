<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="140dp"
    android:layout_height="196dp"
    android:layout_marginStart="@dimen/application___dimen__x2"
    android:background="@drawable/application___background__round_corners_no_padding">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?selectableItemBackgroundBorderless">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/image"
            android:layout_width="@dimen/application___dimen__x26"
            android:layout_height="@dimen/application___dimen__x26"
            android:layout_marginHorizontal="@dimen/application___dimen__x5"
            android:layout_marginTop="@dimen/application___dimen__x5"
            android:padding="@dimen/application___dimen__x1"
            android:scaleType="centerCrop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/roundedImageView"
            app:strokeColor="@color/application___color__search"
            app:strokeWidth="2dp"
            tools:src="@drawable/main__vacancy__request_failed___illustration"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x1"
            android:layout_marginTop="@dimen/application___dimen__x1"
            android:ellipsize="end"
            android:gravity="center_horizontal"
            android:lines="1"
            android:maxLines="1"
            android:textAppearance="@style/Application.Design.H7.TextAppearance"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/image"
            tools:text="Тихонов Иван"/>

        <LinearLayout
            android:id="@+id/friendAction"
            android:layout_width="match_parent"
            android:layout_height="@dimen/application___dimen__x8"
            android:layout_marginHorizontal="@dimen/application___dimen__x3"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:background="@drawable/application___background__white__small"
            android:backgroundTint="@color/application___color__primary_shade"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/name">

            <ImageView
                android:id="@+id/friendActionIcon"
                android:layout_width="21dp"
                android:layout_height="21dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/application___dimen__x1"
                android:src="@drawable/ic_add_friend_by_post"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/friendActionTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="@string/posts_add_to_friends"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Primary"
                android:textSize="14sp"/>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>