<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingVertical="@dimen/application___dimen__x1"
    android:paddingEnd="@dimen/application___dimen__x3">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/main__profile_knowledge_base___like_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:drawableStart="@drawable/ic_like_outline"
        android:drawablePadding="@dimen/application___dimen__x2"
        android:enabled="false"
        android:gravity="center_vertical"
        android:textAppearance="@style/Application.Design.Body2.TextAppearance.Primary"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/last_update"
        tools:text="0"
        tools:visibility="visible"/>

    <ImageView
        android:id="@+id/main__profile_knowledge_base___share"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/application___dimen__x4"
        android:src="@drawable/application___share"
        tools:ignore="ContentDescription"/>

</LinearLayout>