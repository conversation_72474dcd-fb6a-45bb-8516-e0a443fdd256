<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main__vacancy___request__fields__check__container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:foreground="?selectableItemBackground"
    android:orientation="vertical"
    android:paddingTop="@dimen/application___dimen__x4">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/main__vacancy_request___fields__check__title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
        tools:text="Заголовок"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/main__vacancy_request___fields__check__subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/application___dimen__x2"
        android:textAppearance="@style/Application.Design.Caption.TextAppearance.Gray"
        tools:text="Подзаголовок"/>

    <LinearLayout
        android:id="@+id/check_box_and_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:orientation="horizontal">

        <CheckBox
            android:id="@+id/main__vacancy_request___fields__check"
            style="@style/Application.CheckBox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:paddingEnd="@dimen/application___dimen__x2"
            android:textColor="#CC000000"
            tools:ignore="RtlSymmetry"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/main__vacancy_request___fields__check_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:gravity="start|center_vertical"
            android:paddingVertical="@dimen/application___dimen__x2"
            android:textColor="#CC000000"
            android:textSize="16sp"
            tools:text="I agree to the Terms of Agreement."/>

    </LinearLayout>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/main__vacancy_request___fields__check__error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/application___dimen__x2"
        android:text="* Обязательное поле"
        android:textAppearance="@style/Application.Design.Body2.TextAppearance"
        android:textColor="@color/application___color__accent"
        android:visibility="gone"
        tools:visibility="visible"/>
</LinearLayout>