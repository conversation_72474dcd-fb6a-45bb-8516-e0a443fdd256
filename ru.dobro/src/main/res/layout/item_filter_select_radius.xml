<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/application___dimen__x2"
        android:paddingHorizontal="@dimen/application___dimen__x5"
        android:paddingVertical="@dimen/application___dimen__x2"
        android:text="Радиус поиска"
        android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
        android:textSize="22sp"
        app:layout_constraintEnd_toStartOf="@+id/arrowContainer"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <LinearLayout
        android:id="@+id/arrowContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/application___dimen__x2"
        android:layout_marginEnd="@dimen/application___dimen__x4"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@+id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/title">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/arrowText"
            style="@style/Application.Design.Body2.TextAppearance.Primary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Скрыть"
            android:textSize="17sp"
            android:textStyle="normal"/>

        <ImageView
            android:id="@+id/arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/application___arrow_up_variant"
            tools:ignore="ContentDescription"/>

    </LinearLayout>

    <HorizontalScrollView
        android:id="@+id/radiusRoot"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/application___dimen__x4"
        android:scrollbars="none"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title">

        <RadioGroup
            android:id="@+id/radiusContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="@dimen/application___dimen__x5"
            tools:ignore="UselessParent">

            <RadioButton
                android:id="@+id/radius1"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/application___dimen__x9"
                android:layout_marginStart="@dimen/application___dimen__x5"
                android:background="@drawable/bg_radiobutton_select_radius"
                android:button="@null"
                android:gravity="center"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:paddingVertical="@dimen/application___dimen__x2"
                android:text="1 км"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"
                android:textColor="@drawable/text_color_radiobutton_select_radius"
                android:textSize="17sp"/>

            <RadioButton
                android:id="@+id/radius5"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/application___dimen__x9"
                android:layout_marginStart="@dimen/application___dimen__x3"
                android:background="@drawable/bg_radiobutton_select_radius"
                android:button="@null"
                android:gravity="center"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:paddingVertical="@dimen/application___dimen__x2"
                android:text="5 км"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"
                android:textColor="@drawable/text_color_radiobutton_select_radius"
                android:textSize="17sp"/>

            <RadioButton
                android:id="@+id/radius10"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/application___dimen__x9"
                android:layout_marginStart="@dimen/application___dimen__x3"
                android:background="@drawable/bg_radiobutton_select_radius"
                android:button="@null"
                android:gravity="center"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:paddingVertical="@dimen/application___dimen__x2"
                android:text="10 км"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"
                android:textColor="@drawable/text_color_radiobutton_select_radius"
                android:textSize="17sp"/>

            <RadioButton
                android:id="@+id/radius30"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/application___dimen__x9"
                android:layout_marginStart="@dimen/application___dimen__x3"
                android:background="@drawable/bg_radiobutton_select_radius"
                android:button="@null"
                android:gravity="center"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:paddingVertical="@dimen/application___dimen__x2"
                android:text="30 км"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"
                android:textColor="@drawable/text_color_radiobutton_select_radius"
                android:textSize="17sp"/>

            <RadioButton
                android:id="@+id/radius50"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/application___dimen__x9"
                android:layout_marginStart="@dimen/application___dimen__x3"
                android:background="@drawable/bg_radiobutton_select_radius"
                android:button="@null"
                android:gravity="center"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:paddingVertical="@dimen/application___dimen__x2"
                android:text="50 км"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"
                android:textColor="@drawable/text_color_radiobutton_select_radius"
                android:textSize="17sp"/>

            <RadioButton
                android:id="@+id/radius100"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/application___dimen__x9"
                android:layout_marginStart="@dimen/application___dimen__x3"
                android:layout_marginEnd="@dimen/application___dimen__x2"
                android:background="@drawable/bg_radiobutton_select_radius"
                android:button="@null"
                android:gravity="center"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:paddingVertical="@dimen/application___dimen__x2"
                android:text="100 км"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"
                android:textColor="@drawable/text_color_radiobutton_select_radius"
                android:textSize="17sp"/>
        </RadioGroup>

    </HorizontalScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>