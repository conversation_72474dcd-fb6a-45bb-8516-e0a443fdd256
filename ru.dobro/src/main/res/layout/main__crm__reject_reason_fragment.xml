<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/application___background__round_corners_inset">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/main__crm__reject_reason_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x3"
        android:layout_marginTop="@dimen/application___dimen__x5"
        android:gravity="center_horizontal"
        android:padding="@dimen/application___dimen__x2"
        android:text="Отклонить заявку"
        android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/main__crm__reject_reason_description"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:id="@+id/main__crm__reject_reason_dismiss"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_margin="@dimen/application___dimen__x2"
        android:layout_marginEnd="@dimen/application___dimen__x4"
        android:foreground="@drawable/application___ripple__circle"
        android:src="@drawable/application___cancel"
        app:layout_constraintBottom_toBottomOf="@id/main__crm__reject_reason_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/main__crm__reject_reason_title"
        app:tint="@color/application___color__button__gray"
        tools:ignore="ContentDescription"/>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/main__crm__reject_reason_description"
        style="@style/Application.Design.InputFilled.Shade"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="0dp"
        android:layout_marginTop="@dimen/application___dimen__x3"
        android:hint="Укажите причину"
        app:counterEnabled="true"
        app:counterMaxLength="300"
        app:helperTextEnabled="true"
        app:layout_constraintBottom_toTopOf="@id/main__crm__reject_reason_submit"
        app:layout_constraintEnd_toEndOf="@+id/main__crm__reject_reason_submit"
        app:layout_constraintStart_toStartOf="@+id/main__crm__reject_reason_submit"
        app:layout_constraintTop_toBottomOf="@id/main__crm__reject_reason_title">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/main__crm__reject_reason_description_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:imeOptions="actionNext"
            android:inputType="textMultiLine|textCapSentences"
            android:maxLines="12"
            android:maxLength="300"/>
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/main__crm__reject_reason_submit"
        style="@style/Application.Design.Button.Primary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/application___dimen__x2"
        android:enabled="false"
        android:paddingHorizontal="@dimen/application___dimen__x21"
        android:text="Отправить"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main__crm__reject_reason_description"/>
</androidx.constraintlayout.widget.ConstraintLayout>