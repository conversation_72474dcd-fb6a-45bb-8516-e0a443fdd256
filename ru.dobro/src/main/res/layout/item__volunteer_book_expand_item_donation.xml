<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/dateTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="Год: "
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
        android:textSize="13sp"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/dateNumber"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        tools:text="2020"
        app:layout_constraintTop_toTopOf="@+id/dateTitle"
        app:layout_constraintBottom_toBottomOf="@+id/dateTitle"
        app:layout_constraintStart_toEndOf="@+id/dateTitle"
        android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
        android:textSize="13sp"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        tools:text="Количество: 5"
        android:drawableEnd="@drawable/ic_blood"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
        android:textSize="13sp"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/donations_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="Даты донаций:"
        app:layout_constraintTop_toBottomOf="@+id/dateTitle"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="parent"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
        android:textSize="13sp"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/dates"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="start"
        tools:text="24.12   10.12   27.11   20.10"
        app:layout_constraintStart_toEndOf="@+id/donations_date"
        android:layout_marginStart="@dimen/application___dimen__x1"
        app:layout_constraintTop_toTopOf="@+id/donations_date"
        app:layout_constraintEnd_toEndOf="parent"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
        android:textSize="13sp"/>

    <View
        android:id="@+id/divider"
        style="@style/Application.Divider.Thin"
        app:layout_constraintTop_toBottomOf="@+id/dates"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginVertical="@dimen/application___dimen__x4"/>
</androidx.constraintlayout.widget.ConstraintLayout>