<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/main__task___scroll"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/main__task___confirm"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/main__task___image"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:foreground="@drawable/main__task___image__gradient"
                app:layout_constraintBottom_toTopOf="@+id/main__task___title_alternative"
                app:layout_constraintDimensionRatio="1.4:1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription"
                tools:visibility="gone"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__task___title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:layout_marginBottom="@dimen/application___dimen__x3"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.White"
                app:layout_constraintBottom_toBottomOf="@+id/main__task___image"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="1"
                tools:text="title"
                tools:visibility="gone"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__task___title_alternative"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/main__task___main_information"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__task___image"
                tools:text="title"
                tools:visibility="visible"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__task___main_information"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:padding="@dimen/application___dimen__x4"
                android:text="@string/application___main_information"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__task__date"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__task___title_alternative"
                app:layout_constraintVertical_bias="0"
                app:layout_constraintVertical_chainStyle="packed"/>

            <ImageView
                android:id="@+id/main__task__date__icon"
                android:layout_width="@dimen/application___dimen__x6"
                android:layout_height="@dimen/application___dimen__x6"
                android:src="@drawable/application___time"
                app:layout_constraintBottom_toBottomOf="@+id/main__task__date"
                app:layout_constraintEnd_toEndOf="@+id/main__task__organization__logo"
                app:layout_constraintStart_toStartOf="@+id/main__task__organization__logo"
                app:layout_constraintTop_toTopOf="@+id/main__task__date"
                app:tint="@color/application___color__icons"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__task__date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:gravity="center_vertical"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__task__address"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/main__task__organization__logo"
                app:layout_constraintTop_toBottomOf="@+id/main__task___main_information"/>

            <ImageView
                android:id="@+id/main__task__address__icon"
                android:layout_width="@dimen/application___dimen__x6"
                android:layout_height="@dimen/application___dimen__x6"
                android:src="@drawable/application___location"
                app:layout_constraintBottom_toBottomOf="@+id/main__task__address__subtitle"
                app:layout_constraintEnd_toEndOf="@+id/main__task__organization__logo"
                app:layout_constraintStart_toStartOf="@+id/main__task__organization__logo"
                app:layout_constraintTop_toTopOf="@+id/main__task__address"
                app:tint="@color/application___color__icons"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__task__address"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__task__address__subtitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/main__task__organization__logo"
                app:layout_constraintTop_toBottomOf="@+id/main__task__date"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__task__address__subtitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:textAppearance="@style/Application.Design.Body2.TextAppearance"
                app:layout_constraintBottom_toTopOf="@+id/main__task__organization__name"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/main__task__organization__logo"
                app:layout_constraintTop_toBottomOf="@+id/main__task__address"/>

            <ImageView
                android:id="@+id/main__task__organization__logo"
                android:layout_width="@dimen/application___dimen__x10"
                android:layout_height="@dimen/application___dimen__x10"
                android:layout_marginStart="@dimen/application___dimen__x4"
                app:layout_constraintBottom_toBottomOf="@+id/main__task__organization__rating"
                app:layout_constraintEnd_toStartOf="@+id/main__task__organization__name"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/main__task__organization__name"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__task__organization__name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:ellipsize="end"
                android:maxLines="2"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance"
                android:textColor="@color/application___color__primary"
                app:layout_constraintBottom_toTopOf="@+id/main__task__organization__rating"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/main__task__organization__logo"
                app:layout_constraintTop_toBottomOf="@+id/main__task__address__subtitle"/>

            <ImageView
                android:id="@+id/main__task__organization__rating__icon"
                android:layout_width="@dimen/application___dimen__x3"
                android:layout_height="@dimen/application___dimen__x3"
                android:layout_marginStart="@dimen/application___dimen__x4"
                android:layout_marginEnd="@dimen/application___dimen__x1"
                android:src="@drawable/application___star"
                app:layout_constraintBottom_toBottomOf="@+id/main__task__organization__rating"
                app:layout_constraintEnd_toStartOf="@+id/main__task__organization__rating"
                app:layout_constraintStart_toEndOf="@+id/main__task__organization__logo"
                app:layout_constraintTop_toTopOf="@+id/main__task__organization__rating"
                app:tint="@color/application___color__icons"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__task__organization__rating"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:textAppearance="@style/Application.Design.Body2.TextAppearance.Gray"
                app:layout_constraintBottom_toTopOf="@+id/main__task__description__title"
                app:layout_constraintEnd_toEndOf="@+id/main__task__organization__name"
                app:layout_constraintStart_toEndOf="@+id/main__task__organization__rating__icon"
                app:layout_constraintTop_toBottomOf="@+id/main__task__organization__name"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__task__description__title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:paddingTop="@dimen/application___dimen__x6"
                android:text="@string/application___description"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__task__description"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__task__organization__rating"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__task__description"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x1"
                android:ellipsize="end"
                android:maxLines="6"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__task__description__expand"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__task__description__title"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__task__description__expand"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:text="@string/application___expand"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance"
                android:textColor="@color/application___color__primary"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/main__task__volunteers__title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__task__description"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__task__volunteers__title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x6"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:text="@string/main__task___volunteers__title"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__task__volunteers__list"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__task__description__expand"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/main__task__volunteers__list"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:nestedScrollingEnabled="false"
                android:orientation="vertical"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toTopOf="@+id/main__task__volunteers__all"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__task__volunteers__title"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__task__volunteers__all"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:text="@string/main__task___volunteers__all"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__task__volunteers__list"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/main__task___confirm"
        style="@style/Application.Design.Button.Primary"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x4"
        android:text="@string/main__task___confirm"
        app:layout_constraintBottom_toTopOf="@+id/main__task___dismiss"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main__task___scroll"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/main__task___dismiss"
        style="@style/Application.Design.Button.Text.Gray"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:text="@string/application___dismiss"
        app:layout_constraintBottom_toTopOf="@+id/main__task___report"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main__task___confirm"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/main__task___report"
        style="@style/Application.Design.Button.Primary"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x4"
        android:text="@string/main__task___report"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main__task___dismiss"/>

    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/main__task___progress"
        style="@style/Application.Progress"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>