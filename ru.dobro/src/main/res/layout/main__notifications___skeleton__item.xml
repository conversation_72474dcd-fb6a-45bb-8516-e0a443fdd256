<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/application___dimen__x4"
    android:foreground="?selectableItemBackground"
    android:orientation="vertical">

    <View
        android:id="@+id/main__notifications__skeleton__item__avatar"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:background="@drawable/application___background__round_corners"
        android:backgroundTint="@color/application___color__icons"
        app:layout_constraintEnd_toStartOf="@+id/main__notifications__skeleton__item__title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:id="@+id/main__notifications__skeleton__item__title"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginStart="@dimen/application___dimen__x4"
        android:background="@drawable/application___background__round_corners_extra_small_stroke"
        android:backgroundTint="@color/application___color__icons"
        app:layout_constraintBottom_toTopOf="@+id/main__notifications__skeleton__item__subtitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/main__notifications__skeleton__item__avatar"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:id="@+id/main__notifications__skeleton__item__subtitle"
        android:layout_width="0dp"
        android:layout_height="16dp"
        android:layout_marginTop="@dimen/application___dimen__x1"
        android:background="@drawable/application___background__round_corners_extra_small_stroke"
        android:backgroundTint="@color/application___color__icons"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/main__notifications__skeleton__item__title"
        app:layout_constraintStart_toStartOf="@+id/main__notifications__skeleton__item__title"
        app:layout_constraintTop_toBottomOf="@+id/main__notifications__skeleton__item__title"/>
</androidx.constraintlayout.widget.ConstraintLayout>