<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/application___background__round_corners_no_padding"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x4">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/date"
            style="@style/Application.Design.Body1.TextAppearance.DarkGray"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="15sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="22.06.2004"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x3"
            android:background="@drawable/application___background__round_corners"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/application___dimen__x2"
            android:paddingVertical="@dimen/application___dimen__x1"
            android:textAppearance="@style/Application.Design.Caption.TextAppearance"
            app:layout_constraintBottom_toBottomOf="@+id/date"
            app:layout_constraintStart_toEndOf="@+id/date"
            app:layout_constraintTop_toTopOf="@+id/date"
            tools:backgroundTint="#FBE8E8"
            tools:text="Отклонена"
            tools:textColor="@color/alizarin_crimson"/>

        <ImageView
            android:id="@+id/delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/main__search___calendar_enabler__background"
            android:backgroundTint="#12FF3B30"
            android:src="@drawable/ic_delete"
            app:layout_constraintBottom_toBottomOf="@+id/date"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/date"/>

        <ImageView
            android:id="@+id/edit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/application___dimen__x4"
            android:background="@drawable/main__search___calendar_enabler__background"
            android:backgroundTint="@color/main__vacancy___check_in__continue"
            android:src="@drawable/ic_edit"
            app:layout_constraintBottom_toBottomOf="@+id/date"
            app:layout_constraintEnd_toStartOf="@+id/delete"
            app:layout_constraintTop_toTopOf="@+id/date"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x3"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
        android:textSize="15sp"
        tools:text="Сегодня на кассе в магазине я увидел, что бабушка не может купить себе с пенсии стандартный набор продуктов. Я решил ей помочь и купил разного на неделю"/>

    <LinearLayout
        android:id="@+id/rejectContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x3"
        android:background="@drawable/application___background__round_corners"
        android:backgroundTint="#FEF5F9"
        android:orientation="vertical"
        android:padding="@dimen/application___dimen__x4"
        android:visibility="gone"
        tools:visibility="visible">

        <com.google.android.material.textview.MaterialTextView
            style="@style/Application.Design.H7.TextAppearance.Black"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:text="Причина отклонения"
            android:textSize="15sp"
            android:textStyle="normal"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/rejectedReason"
            style="@style/Application.Design.Body1.TextAppearance.Black"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:drawablePadding="@dimen/application___dimen__x2"
            android:gravity="center_vertical"
            android:textSize="15sp"
            android:textStyle="normal"
            tools:text="Причина отклонения Причина отклонения Причина отклонения Причина отклонения"/>
    </LinearLayout>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x4"
        android:background="@drawable/main__search___calendar_enabler__background"
        android:backgroundTint="#F3F5F8"
        android:drawablePadding="2dp"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:paddingVertical="@dimen/application___dimen__x2"
        android:paddingEnd="10dp"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
        android:textSize="12sp"
        tools:drawableStart="@drawable/ic_edit"
        tools:text="Адресная помощь"/>

    <ImageView
        android:id="@+id/image"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/application___dimen__x4"
        android:adjustViewBounds="true"
        android:maxHeight="400dp"
        android:scaleType="fitCenter"
        tools:ignore="ContentDescription"/>

    <LinearLayout
        android:id="@+id/like"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/application___dimen__x2"
        android:layout_marginTop="@dimen/application___dimen__x2"
        android:layout_marginBottom="@dimen/application___dimen__x2"
        android:background="@drawable/bg_like_post"
        android:backgroundTint="#F2F2F7"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/application___dimen__x3"
        android:paddingVertical="@dimen/application___dimen__x2">

        <ImageView
            android:id="@+id/likeHeart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="2dp"
            android:src="@drawable/ic_like_filled_variant"
            tools:ignore="ContentDescription"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/likeCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:maxLines="1"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
            android:textSize="13sp"
            tools:text="32"/>

    </LinearLayout>

</LinearLayout>