<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#E6FFFFFF">

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabs"
        android:layout_width="match_parent"
        android:layout_height="@dimen/application___dimen__x2"
        android:layout_marginHorizontal="@dimen/application___dimen__x5"
        android:layout_marginTop="@dimen/application___dimen__x1"
        android:background="@color/application___color__shade"
        android:elevation="1dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tabBackground="@drawable/tab_posts_onboarding_selector"
        app:tabIndicatorHeight="0dp"
        app:tabMode="fixed"/>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/pages"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tabs"/>

    <View
        android:id="@+id/create_post"
        android:layout_width="280dp"
        android:layout_height="65dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.7"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/skip"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x10"
        android:layout_marginBottom="@dimen/application___dimen__x3"
        android:elevation="1dp"
        android:gravity="center"
        android:paddingVertical="@dimen/application___dimen__x2"
        android:text="@string/application___skip"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>