<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/main__profile_me___edit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/application___dimen__x1"
        android:layout_marginStart="@dimen/application___dimen__x4"
        android:src="@drawable/main__profile_me___edit_variant_2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription"/>

    <ImageView
        android:id="@+id/main__profile_me___log_out"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/application___dimen__x1"
        android:layout_marginStart="@dimen/application___dimen__x3"
        android:visibility="gone"
        tools:visibility="visible"
        android:src="@drawable/main__profile_me___logout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/main__profile_me___edit"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription"/>

    <LinearLayout
        android:id="@+id/main__profile_me___become_organizer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="invisible"
        android:clickable="false"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginEnd="@dimen/application___dimen__x4"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/application___dimen__x1"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:src="@drawable/main__profile_me___add_organisation"
            tools:ignore="ContentDescription"/>

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x1"
            android:layout_gravity="center"
            android:foreground="?selectableItemBackground"
            android:textAppearance="@style/Application.Design.Subtitle2.TextAppearance.Primary"
            android:text="@string/main__profile___become_organizer"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/main__profile_me___change_profile"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="invisible"
        android:clickable="false"
        tools:visibility="visible"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginEnd="@dimen/application___dimen__x4"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/application___dimen__x1"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:src="@drawable/main__profile_me___change_profile"
            tools:ignore="ContentDescription"/>

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x1"
            android:layout_gravity="center"
            android:foreground="?selectableItemBackground"
            android:textAppearance="@style/Application.Design.Subtitle2.TextAppearance.Primary"
            android:text="@string/main__profile___change_profile"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>