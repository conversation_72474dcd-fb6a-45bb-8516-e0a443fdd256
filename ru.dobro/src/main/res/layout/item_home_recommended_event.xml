<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/eventCard"
    android:layout_width="173dp"
    android:layout_height="280dp"
    android:background="@drawable/application___background__round_corners_no_padding">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?selectableItemBackgroundBorderless">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/image"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginHorizontal="6dp"
            android:layout_marginTop="6dp"
            android:scaleType="centerCrop"
            app:cardCornerRadius="6dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/rounded9ImageView"
            tools:src="@drawable/main__vacancy__request_failed___illustration"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x1"
            android:layout_marginTop="@dimen/application___dimen__x1"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="@+id/image"
            app:layout_constraintTop_toTopOf="@+id/image">

            <androidx.cardview.widget.CardView
                android:id="@+id/verifiedContainer"
                android:layout_width="20dp"
                android:layout_height="20dp"
                app:cardCornerRadius="@dimen/application___dimen__x2"
                app:cardElevation="0dp">

                <ImageView
                    android:id="@+id/verified"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/application___dimen__x1"
                    android:src="@drawable/main__profile_me_organization___verified"
                    tools:ignore="ContentDescription"/>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/categoryContainer"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginStart="@dimen/application___dimen__x1"
                app:cardCornerRadius="@dimen/application___dimen__x2"
                app:cardElevation="0dp">

                <ImageView
                    android:id="@+id/category"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/application___dimen__x1"
                    android:src="@drawable/main__profile_me_organization___verified"
                    tools:ignore="ContentDescription"/>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <androidx.cardview.widget.CardView
            android:id="@+id/ageContainer"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="@dimen/application___dimen__x1"
            android:layout_marginTop="@dimen/application___dimen__x1"
            android:layout_marginEnd="@dimen/application___dimen__x1"
            app:cardCornerRadius="@dimen/application___dimen__x2"
            app:cardElevation="0dp"
            app:layout_constraintEnd_toEndOf="@+id/image"
            app:layout_constraintTop_toTopOf="@+id/image">

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/age"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                android:textSize="9sp"
                tools:text="16+"/>

        </androidx.cardview.widget.CardView>

        <ImageView
            android:id="@+id/recommendation"
            android:layout_width="79dp"
            android:layout_height="20dp"
            android:layout_marginStart="@dimen/application___dimen__x1"
            android:layout_marginBottom="@dimen/application___dimen__x1"
            android:visibility="gone"
            tools:visibility="visible"
            android:src="@drawable/ic_recommended_home_event"
            app:layout_constraintBottom_toBottomOf="@+id/image"
            app:layout_constraintStart_toStartOf="@+id/image"
            tools:ignore="ContentDescription"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="6dp"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:lines="2"
            android:textAppearance="@style/Application.Design.H7.TextAppearance"
            android:textSize="15sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/image"
            tools:text="Станьте волонтёром для праздников в детском лагере"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/periodContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="10dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title">

            <ImageView
                android:id="@+id/periodIcon"
                android:layout_width="@dimen/application___dimen__x4"
                android:layout_height="@dimen/application___dimen__x4"
                android:src="@drawable/ic_calendar_variant"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/application___color__icons_dark"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/period"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x1"
                android:gravity="center_vertical"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                android:textSize="11sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/periodIcon"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="28 июня – 19 июля"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/timeContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="6dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/periodContainer">

            <ImageView
                android:id="@+id/timeIcon"
                android:layout_width="@dimen/application___dimen__x4"
                android:layout_height="@dimen/application___dimen__x4"
                android:src="@drawable/application___time_variant"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/application___color__icons_dark"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x1"
                android:gravity="center_vertical"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                android:textSize="11sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/timeIcon"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="12:00 – 19:00"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/locationContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="6dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/timeContainer">

            <ImageView
                android:id="@+id/locationIcon"
                android:layout_width="@dimen/application___dimen__x4"
                android:layout_height="@dimen/application___dimen__x4"
                android:src="@drawable/ic_location_v3"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/application___color__icons_dark"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/location"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x1"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                android:textSize="11sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/locationIcon"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Санкт‑Петербург"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>