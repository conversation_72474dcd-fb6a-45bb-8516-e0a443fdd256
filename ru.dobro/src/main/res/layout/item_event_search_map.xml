<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/icon"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginStart="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x1"
        android:src="@drawable/main__profile___volunteer_book__empty"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/rounded12ImageView"/>

    <ImageView
        android:id="@+id/cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/application___dimen__x3"
        android:padding="@dimen/application___dimen__x2"
        android:src="@drawable/application___cancel_variant"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription"/>

    <LinearLayout
        android:id="@+id/content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@+id/icon"
        app:layout_constraintEnd_toStartOf="@+id/cancel"
        app:layout_constraintStart_toEndOf="@+id/icon"
        app:layout_constraintTop_toTopOf="@+id/icon">

        <ImageView
            android:id="@+id/recommendation"
            android:layout_width="79dp"
            android:layout_height="20dp"
            android:layout_marginBottom="@dimen/application___dimen__x1"
            android:src="@drawable/ic_recommended_event"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/image"
            app:layout_constraintStart_toStartOf="@+id/image"
            tools:ignore="ContentDescription"
            tools:visibility="visible"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="2"
            android:textAppearance="@style/Application.Design.H7.TextAppearance"
            android:textSize="15sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/image"
            tools:text="Станьте волонтёром для праздников в детском лагере бубубубубуббуубуббуб"/>

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/periodContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x3"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/icon">

        <ImageView
            android:id="@+id/periodIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_calendar_variant2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/application___color__icons_dark"
            tools:ignore="ContentDescription"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/period"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:gravity="center_vertical"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
            android:textSize="15sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/periodIcon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="28 июня – 19 июля"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/timeContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x1"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/periodContainer">

        <ImageView
            android:id="@+id/timeIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/application___time_variant2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/application___color__icons_dark"
            tools:ignore="ContentDescription"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/time"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:gravity="center_vertical"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
            android:textSize="15sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/timeIcon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="12:00 – 19:00"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/locationContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x1"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/timeContainer">

        <ImageView
            android:id="@+id/locationIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_location_v4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/application___color__icons_dark"
            tools:ignore="ContentDescription"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/location"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="2"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
            android:textSize="15sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/locationIcon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Санкт‑Петербург"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x4"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/locationContainer">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/infoWhite"
            style="@style/Application.Design.Button.White"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Подробнее"
            android:visibility="gone"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/infoPrimary"
            style="@style/Application.Design.Button.Primary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Подробнее"/>

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>