<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".main.profile.organization.HelpMessageFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        android:paddingTop="@dimen/application___dimen__x3"
        android:paddingBottom="@dimen/application___dimen__x2"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/application___dimen__x5">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
            tools:text="@string/main__organization___help__draft__title"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x3"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
            tools:text="@string/main__organization___help__draft__subtitle"/>

        <LinearLayout
            android:id="@+id/rejectContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/application___dimen__x6"
            android:background="@drawable/application___background__round_corners"
            android:backgroundTint="@color/main__requests___status__rejected_background"
            android:visibility="gone"
            tools:visibility="visible">

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/rejectTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:text="@string/main__requests___status__reject_by_organization__title"
                android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/rejectText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x1"
                android:layout_marginBottom="@dimen/application___dimen__x4"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                tools:text="тест тест тест тест тест тест тест тест тест тест тест тесттест тест тест тесттест тест тест тесттест тест тест тесттест тест тест тест"/>
        </LinearLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/confirm_filled"
            style="@style/Application.Design.Button.Primary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x8"
            tools:text="@string/main__organization___help__draft__confirm__filled__text"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/confirm"
            style="@style/Application.Design.Button.OutlinedButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x5"
            android:visibility="gone"
            tools:text="@string/main__organization___help__draft__confirm__text"
            tools:visibility="visible"/>

    </LinearLayout>
</androidx.core.widget.NestedScrollView>