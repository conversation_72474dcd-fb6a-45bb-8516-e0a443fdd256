<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"

    android:padding="@dimen/application___dimen__x6">

    <LinearLayout
        android:id="@+id/checkboxLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/checkbox1Container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/checkbox1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                 />

            <TextView
                android:id="@+id/checkbox1Text"
                android:paddingVertical="8dp"
                android:paddingHorizontal="18dp"
                android:textAppearance="@style/Application.Design.Caption.TextAppearance.Gray"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/StatusCheckBoxText"
                android:text="@string/main__requests___status__accepted" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/checkbox2Container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/checkbox2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                 />

            <TextView
                android:id="@+id/checkbox2Text"
                android:paddingVertical="8dp"
                android:paddingHorizontal="18dp"
                android:textAppearance="@style/Application.Design.Caption.TextAppearance.Gray"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/StatusCheckBoxText"
                android:text="@string/main__requests___status__new" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/checkbox3Container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/checkbox3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                />

            <TextView
                android:id="@+id/checkbox3Text"
                android:paddingVertical="8dp"
                android:paddingHorizontal="18dp"
                android:textAppearance="@style/Application.Design.Caption.TextAppearance.Gray"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/StatusCheckBoxText"
                android:text="@string/main__requests___status__canceled_by_volunteer" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/checkbox4Container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/checkbox4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                />

            <TextView
                android:id="@+id/checkbox4Text"
                android:paddingVertical="8dp"
                android:paddingHorizontal="18dp"
                android:textAppearance="@style/Application.Design.Caption.TextAppearance.Gray"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/StatusCheckBoxText"
                android:text="@string/main__requests___status__reserved" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/checkbox5Container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/checkbox5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                />

            <TextView
                android:id="@+id/checkbox5Text"
                android:paddingVertical="8dp"
                android:paddingHorizontal="18dp"
                android:textAppearance="@style/Application.Design.Caption.TextAppearance.Gray"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/StatusCheckBoxText"
                android:text="@string/main__requests___status__non_attendance" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/checkbox6Container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/checkbox6"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                />

            <TextView
                android:id="@+id/checkbox6Text"
                android:paddingVertical="8dp"
                android:paddingHorizontal="18dp"
                android:textAppearance="@style/Application.Design.Caption.TextAppearance.Gray"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/StatusCheckBoxText"
                android:text="@string/main__requests___status__removed" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/checkbox7Container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/checkbox7"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                />

            <TextView
                android:id="@+id/checkbox7Text"
                android:paddingVertical="8dp"
                android:paddingHorizontal="18dp"
                android:textAppearance="@style/Application.Design.Caption.TextAppearance.Gray"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/StatusCheckBoxText"
                android:text="@string/main__requests___status__reject_by_organization" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/checkbox8Container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/checkbox8"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                />

            <TextView
                android:id="@+id/checkbox8Text"
                android:paddingVertical="8dp"
                android:paddingHorizontal="18dp"
                android:textAppearance="@style/Application.Design.Caption.TextAppearance.Gray"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/StatusCheckBoxText"
                android:text="@string/main__requests___status__all" />
        </LinearLayout>

    </LinearLayout>

    <Button
        android:id="@+id/button"
        style="@style/Application.Design.Button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_alignParentBottom="true"
        android:text="@string/main__requests___status_button_text" />

</RelativeLayout>
