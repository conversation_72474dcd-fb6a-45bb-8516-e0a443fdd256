<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fillViewport="true"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:elevation="1dp"
        android:padding="@dimen/application___dimen__x3"
        android:src="@drawable/application___arrow_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/application___color__primary"
        tools:ignore="ContentDescription"/>

    <ImageView
        android:id="@+id/illustration"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@drawable/ic_illustration_profile_unremovable"
        app:layout_constraintDimensionRatio="360:300"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x6"
        android:layout_marginBottom="@dimen/application___dimen__x3"
        android:text="@string/profile_unremovable___title"
        android:textAlignment="center"
        app:lineHeight="22dp"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
        app:layout_constraintBottom_toTopOf="@+id/profile_unremovalbe_detailed_instruction"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/illustration"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/profile_unremovalbe_detailed_instruction"
        style="@style/Application.Design.Button.OutlinedButton"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginBottom="@dimen/application___dimen__x3"
        android:text="@string/profile_unremovable___detailed_instruction"
        android:textColor="@color/application___color__main"
        app:layout_constraintBottom_toTopOf="@+id/profile_unremovalbe__perform"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:strokeColor="@color/application___color__primary"
        tools:layout_editor_absoluteX="24dp"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/profile_unremovalbe__perform"
        style="@style/Application.Design.Button.Primary"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginBottom="@dimen/application___dimen__x4"
        android:text="@string/profile_unremovable___perform"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>