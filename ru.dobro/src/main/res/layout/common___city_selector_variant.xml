<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/application___dimen__x5">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/text"
        style="@style/Application.Design.Body2.TextAppearance.Black"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/application___dimen__x5"
        android:layout_marginEnd="@dimen/application___dimen__x2"
        tools:text="Все города"
        android:textSize="17sp"
        android:textStyle="normal"
        app:layout_constraintBottom_toTopOf="@+id/divider"
        app:layout_constraintEnd_toStartOf="@+id/arrow"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:id="@+id/arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/application___dimen__x2"
        android:layout_marginEnd="@dimen/application___dimen__x4"
        android:src="@drawable/application___arrow_down_variant"
        app:layout_constraintBottom_toTopOf="@+id/divider"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/text"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:id="@+id/divider"
        style="@style/Application.Divider"
        android:layout_width="0dp"
        android:layout_marginTop="@dimen/application___dimen__x1"
        android:background="#E3E3EF"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/text"
        app:layout_constraintTop_toBottomOf="@+id/arrow"/>

</androidx.constraintlayout.widget.ConstraintLayout>