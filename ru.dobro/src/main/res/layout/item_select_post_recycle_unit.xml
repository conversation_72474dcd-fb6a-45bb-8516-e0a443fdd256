<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
        android:textSize="15sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Бумага"/>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/amount"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/application___dimen__x2"
        android:layout_marginEnd="@dimen/application___dimen__x2"
        app:cardBackgroundColor="@color/application___color__search"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp"
        app:layout_constraintEnd_toStartOf="@+id/unit"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintWidth_percent="0.66"
        app:strokeWidth="1dp">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/amountTextContainer"
            style="@style/Application.Design.Input.Empty"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x1"
            android:layout_marginVertical="@dimen/application___dimen__x1"
            android:background="@color/application___color__search"
            app:hintEnabled="false"
            app:layout_constraintBottom_toTopOf="@+id/descriptionHelp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/descriptionAvatar"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/amountText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/application___color__search"
                android:hint="0.0"
                android:imeOptions="actionDone"
                android:inputType="numberDecimal"
                android:textColorHint="@color/application___color__gray__60"/>
        </com.google.android.material.textfield.TextInputLayout>

    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/unit"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/application___dimen__x2"
        app:cardBackgroundColor="@color/application___color__search"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/amount"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="1.0"
        app:layout_constraintStart_toEndOf="@+id/amount"
        app:layout_constraintTop_toTopOf="@+id/amount"
        app:strokeWidth="1dp">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/unitText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/application___dimen__x4"
            android:layout_marginStart="@dimen/application___dimen__x4"
            android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"
            android:textSize="17sp"
            tools:text="Кг"/>

        <ImageView
            android:id="@+id/unitArrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|center_vertical"
            android:layout_marginEnd="@dimen/application___dimen__x0"
            android:padding="@dimen/application___dimen__x2"
            android:src="@drawable/application___arrow_down_variant_2"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription"
            tools:visibility="visible"/>

    </com.google.android.material.card.MaterialCardView>

    <View
        android:id="@+id/separator"
        android:layout_width="match_parent"
        android:layout_height="@dimen/application___dimen__x2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/amount"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/amountWarning"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/application___dimen__x4"
        android:text="@string/create_post_unit_warning"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Red"
        android:textSize="15sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/separator"
        tools:visibility="visible"/>

</androidx.constraintlayout.widget.ConstraintLayout>