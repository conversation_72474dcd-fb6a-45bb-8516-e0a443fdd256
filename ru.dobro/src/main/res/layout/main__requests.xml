<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.motion.widget.MotionLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    app:layoutDescription="@xml/main__requests_scene">

    <LinearLayout
        android:id="@+id/filtersContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/application___dimen__x5"
        android:paddingTop="@dimen/application___dimen__x4"
        android:paddingBottom="@dimen/application___dimen__x5"
        app:layout_constraintTop_toBottomOf="@id/tabs">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/statusContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="6dp"
            android:layout_weight="1">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/selectStatus"
                style="@style/Application.Design.Button.Small.Shade"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:layout_weight="1"
                android:drawableEnd="@drawable/application___arrow_down"
                android:drawableTint="@color/application___color__primary"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/application___dimen__x1"
                android:paddingVertical="0dp"
                android:text="@string/main__requests___select_status"
                app:iconPadding="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/statusCircle"
                style="@style/Application.Counter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/selectStatus"
                app:layout_constraintEnd_toEndOf="@id/selectStatus"
                app:layout_constraintHorizontal_bias="0.95"
                app:layout_constraintStart_toStartOf="@id/selectStatus"
                app:layout_constraintTop_toTopOf="@id/selectStatus"
                tools:text="2"/>

        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.google.android.material.button.MaterialButton
            android:id="@+id/selectDate"
            style="@style/Application.Design.Button.Small.Shade"
            android:layout_width="0dp"
            android:layout_height="38dp"
            android:layout_marginStart="6dp"
            android:layout_weight="1"
            android:drawableEnd="@drawable/ic_calendar"
            android:drawableTint="@color/application___color__primary"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/application___dimen__x1"
            android:paddingVertical="0dp"
            android:text="@string/main__requests___select_date"
            app:iconPadding="0dp"/>


    </LinearLayout>


    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/requestsPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:nestedScrollingEnabled="false"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/filtersContainer"/>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        android:background="@color/application___color_general_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tabGravity="center"
        app:tabIndicator="@drawable/tab_bottom_line"
        app:tabIndicatorHeight="2dp"
        app:tabMode="scrollable"
        app:tabTextAppearance="@style/Application.Design.Semibold.TextAppearance"/>
</androidx.constraintlayout.motion.widget.MotionLayout>