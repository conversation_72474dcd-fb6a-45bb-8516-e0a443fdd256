<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="@dimen/application___dimen__x2"
    android:layout_marginHorizontal="@dimen/application___dimen__x4"
    app:cardCornerRadius="10dp"
    app:cardElevation="0dp"
    app:cardBackgroundColor="@color/onboarding_download_default">

    <LinearLayout
        android:id="@+id/login__onboarding__download__reports_item_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/login__onboarding__download__reports_item_icon"
            android:layout_margin="@dimen/application___dimen__x4"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:scaleType="centerCrop"
            android:layout_gravity="center_vertical"
            tools:ignore="ContentDescription"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/login__onboarding__download__reports_item_text"
            android:layout_marginEnd="@dimen/application___dimen__x4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center|start"
            android:minLines="2"
            android:maxLines="2"
            tools:text="Ресурсный центр или региональный представитель"
            android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"
            android:textSize="16sp"
            android:layout_gravity="center_vertical" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
