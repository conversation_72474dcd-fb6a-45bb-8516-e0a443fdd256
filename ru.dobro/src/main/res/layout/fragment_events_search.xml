<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/shimmer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x4">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <include
                        layout="@layout/item_home_recommended_event_shimmer"
                        android:layout_width="match_parent"
                        android:layout_height="280dp"
                        android:layout_marginEnd="@dimen/application___dimen__x1"
                        android:layout_weight="1"/>

                    <include
                        layout="@layout/item_home_recommended_event_shimmer"
                        android:layout_width="match_parent"
                        android:layout_height="280dp"
                        android:layout_marginStart="@dimen/application___dimen__x1"
                        android:layout_weight="1"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:orientation="horizontal">

                    <include
                        layout="@layout/item_home_recommended_event_shimmer"
                        android:layout_width="match_parent"
                        android:layout_height="280dp"
                        android:layout_marginEnd="@dimen/application___dimen__x1"
                        android:layout_weight="1"/>

                    <include
                        layout="@layout/item_home_recommended_event_shimmer"
                        android:layout_width="match_parent"
                        android:layout_height="280dp"
                        android:layout_marginStart="@dimen/application___dimen__x1"
                        android:layout_weight="1"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:orientation="horizontal">

                    <include
                        layout="@layout/item_home_recommended_event_shimmer"
                        android:layout_width="match_parent"
                        android:layout_height="280dp"
                        android:layout_marginEnd="@dimen/application___dimen__x1"
                        android:layout_weight="1"/>

                    <include
                        layout="@layout/item_home_recommended_event_shimmer"
                        android:layout_width="match_parent"
                        android:layout_height="280dp"
                        android:layout_marginStart="@dimen/application___dimen__x1"
                        android:layout_weight="1"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:orientation="horizontal">

                    <include
                        layout="@layout/item_home_recommended_event_shimmer"
                        android:layout_width="match_parent"
                        android:layout_height="280dp"
                        android:layout_marginEnd="@dimen/application___dimen__x1"
                        android:layout_weight="1"/>

                    <include
                        layout="@layout/item_home_recommended_event_shimmer"
                        android:layout_width="match_parent"
                        android:layout_height="280dp"
                        android:layout_marginStart="@dimen/application___dimen__x1"
                        android:layout_weight="1"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:orientation="horizontal">

                    <include
                        layout="@layout/item_home_recommended_event_shimmer"
                        android:layout_width="match_parent"
                        android:layout_height="280dp"
                        android:layout_marginEnd="@dimen/application___dimen__x1"
                        android:layout_weight="1"/>

                    <include
                        layout="@layout/item_home_recommended_event_shimmer"
                        android:layout_width="match_parent"
                        android:layout_height="280dp"
                        android:layout_marginStart="@dimen/application___dimen__x1"
                        android:layout_weight="1"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:orientation="horizontal">

                    <include
                        layout="@layout/item_home_recommended_event_shimmer"
                        android:layout_width="match_parent"
                        android:layout_height="280dp"
                        android:layout_marginEnd="@dimen/application___dimen__x1"
                        android:layout_weight="1"/>

                    <include
                        layout="@layout/item_home_recommended_event_shimmer"
                        android:layout_width="match_parent"
                        android:layout_height="280dp"
                        android:layout_marginStart="@dimen/application___dimen__x1"
                        android:layout_weight="1"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:orientation="horizontal">

                    <include
                        layout="@layout/item_home_recommended_event_shimmer"
                        android:layout_width="match_parent"
                        android:layout_height="280dp"
                        android:layout_marginEnd="@dimen/application___dimen__x1"
                        android:layout_weight="1"/>

                    <include
                        layout="@layout/item_home_recommended_event_shimmer"
                        android:layout_width="match_parent"
                        android:layout_height="280dp"
                        android:layout_marginStart="@dimen/application___dimen__x1"
                        android:layout_weight="1"/>

                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </com.facebook.shimmer.ShimmerFrameLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/events"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/application___dimen__x3"
        android:clipToPadding="false"
        android:orientation="vertical"
        android:paddingTop="@dimen/application___dimen__x2"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:spanCount="2"
        tools:listitem="@layout/item_home_recommended_event"/>

</androidx.constraintlayout.widget.ConstraintLayout>