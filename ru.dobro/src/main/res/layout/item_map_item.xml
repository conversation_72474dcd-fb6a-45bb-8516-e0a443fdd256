<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="140dp"
    android:layout_marginVertical="@dimen/application___dimen__x2"
    android:paddingVertical="@dimen/application___dimen__x2"
    app:cardBackgroundColor="@color/application___color__search"
    app:cardCornerRadius="@dimen/application___dimen__x4"
    app:rippleColor="@android:color/transparent"
    app:strokeColor="@color/application___color__primary">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="140dp"
        android:layout_gravity="center_vertical"
        android:orientation="vertical">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:lines="2"
            android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"
            tools:text="Волонтерский центр РЭУолонтерский центр РЭУ"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/phone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:layout_marginTop="@dimen/application___dimen__x1"
            android:layout_marginEnd="@dimen/application___dimen__x4"
            android:drawableStart="@drawable/ic_phone_variant"
            android:drawablePadding="@dimen/application___dimen__x4"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:lines="1"
            android:textAppearance="@style/Application.Design.Body2.TextAppearance.Black"
            tools:text="******-800-12-00"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/address"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:layout_marginEnd="@dimen/application___dimen__x4"
            android:drawableStart="@drawable/ic_location_v2"
            android:drawablePadding="@dimen/application___dimen__x4"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:lines="1"
            android:textAppearance="@style/Application.Design.Body2.TextAppearance.Black"
            tools:text="г Москва, Стремянный пер, д 36"/>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>