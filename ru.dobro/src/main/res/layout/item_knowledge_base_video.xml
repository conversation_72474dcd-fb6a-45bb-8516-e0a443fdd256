<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/application___dimen__x4"
    android:layout_marginTop="@dimen/application___dimen__x4">

    <androidx.cardview.widget.CardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:foreground="?attr/selectableItemBackgroundBorderless"
        app:cardCornerRadius="@dimen/application___dimen__x3"
        app:cardElevation="@dimen/application___dimen__x0">

        <ImageView
            android:id="@+id/image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"/>

        <ImageView
            android:id="@+id/play_video"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/ic_play_video"/>

    </androidx.cardview.widget.CardView>

</LinearLayout>