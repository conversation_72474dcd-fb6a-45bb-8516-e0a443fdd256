<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/application___color_general_background">

    <com.facebook.shimmer.ShimmerFrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/shimmer">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <HorizontalScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x5"
                    android:orientation="vertical"
                    android:scrollbars="none"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingHorizontal="@dimen/application___dimen__x2"
                        android:translationZ="2dp">

                        <View
                            android:layout_width="140dp"
                            android:layout_height="196dp"
                            android:background="@drawable/application___background__round_corners"
                            android:backgroundTint="@color/application___color__icons"
                            app:shapeAppearanceOverlay="@style/rounded9ImageView"/>

                        <View
                            android:layout_width="140dp"
                            android:layout_height="196dp"
                            android:layout_marginStart="@dimen/application___dimen__x2"
                            android:background="@drawable/application___background__round_corners"
                            android:backgroundTint="@color/application___color__icons"
                            app:shapeAppearanceOverlay="@style/rounded9ImageView"/>

                        <View
                            android:layout_width="140dp"
                            android:layout_height="196dp"
                            android:layout_marginStart="@dimen/application___dimen__x2"
                            android:background="@drawable/application___background__round_corners"
                            android:backgroundTint="@color/application___color__icons"
                            app:shapeAppearanceOverlay="@style/rounded9ImageView"/>

                        <View
                            android:layout_width="140dp"
                            android:layout_height="196dp"
                            android:layout_marginStart="@dimen/application___dimen__x2"
                            android:background="@drawable/application___background__round_corners"
                            android:backgroundTint="@color/application___color__icons"
                            app:shapeAppearanceOverlay="@style/rounded9ImageView"/>

                        <View
                            android:layout_width="140dp"
                            android:layout_height="196dp"
                            android:layout_marginStart="@dimen/application___dimen__x2"
                            android:background="@drawable/application___background__round_corners"
                            android:backgroundTint="@color/application___color__icons"
                            app:shapeAppearanceOverlay="@style/rounded9ImageView"/>

                        <View
                            android:layout_width="140dp"
                            android:layout_height="196dp"
                            android:layout_marginStart="@dimen/application___dimen__x2"
                            android:background="@drawable/application___background__round_corners"
                            android:backgroundTint="@color/application___color__icons"
                            app:shapeAppearanceOverlay="@style/rounded9ImageView"/>
                    </LinearLayout>

                </HorizontalScrollView>

                <androidx.core.widget.NestedScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x5">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <include
                            layout="@layout/common___post__item__skeleton"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"/>

                        <include
                            layout="@layout/common___post__item__skeleton"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/application___dimen__x2"/>

                        <include
                            layout="@layout/common___post__item__skeleton"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/application___dimen__x2"/>

                    </LinearLayout>
                </androidx.core.widget.NestedScrollView>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </com.facebook.shimmer.ShimmerFrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>