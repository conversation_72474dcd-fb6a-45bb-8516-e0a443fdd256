<?xml version="1.0" encoding="utf-8"?>
<com.facebook.shimmer.ShimmerFrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/shimmer"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <include
                layout="@layout/common___my_post__item__skeleton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <include
                layout="@layout/common___my_post__item__skeleton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x2"/>

            <include
                layout="@layout/common___my_post__item__skeleton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x2"/>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</com.facebook.shimmer.ShimmerFrameLayout>