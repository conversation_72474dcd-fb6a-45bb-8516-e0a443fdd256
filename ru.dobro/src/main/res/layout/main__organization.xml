<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/shimmer"
        layout="@layout/main__organization___skeleton"/>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/main__organization___content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            xmlns:app="http://schemas.android.com/apk/res-auto"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/main__organization___logo"
                android:layout_width="110dp"
                android:layout_height="110dp"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___name"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0"
                app:layout_constraintVertical_chainStyle="packed"
                tools:ignore="ContentDescription"/>

            <LinearLayout
                android:id="@+id/main__organization___rating__container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:background="@drawable/application___background__circle"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/application___dimen__x2"
                android:paddingVertical="@dimen/application___dimen__x1"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@+id/main__organization___logo"
                app:layout_constraintTop_toTopOf="@+id/main__organization___logo">

                <ImageView
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/application___star"
                    tools:ignore="ContentDescription"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__organization___rating"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"/>

            </LinearLayout>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x5"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:gravity="center"
                android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___id"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___logo"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___id"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:foreground="?selectableItemBackground"
                android:gravity="center_vertical"
                android:textAppearance="@style/Application.Design.Subtitle2.TextAppearance.Gray"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___join"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___name"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/main__organization___join"
                style="@style/Application.Design.Button.Primary"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_marginHorizontal="@dimen/application___dimen__x6"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:text="@string/main__organization___join"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___joined"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___id"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___joined"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:drawableStart="@drawable/main__organizations___check"
                android:drawablePadding="@dimen/application___dimen__x2"
                android:gravity="center"
                android:text="@string/main__organization___joined"
                android:textAppearance="@style/Application.Design.Body2.TextAppearance.Gray"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___leave"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___join"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/main__organization___leave"
                style="@style/Application.Design.Button.Dismiss"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_marginHorizontal="@dimen/application___dimen__x6"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:text="@string/main__organization___leave"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___events__title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___joined"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___events__title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x8"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:text="@string/main__organization___events"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___events__list"
                app:layout_constraintEnd_toStartOf="@+id/main__organization___events__all"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___leave"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/main__organization___events__all"
                style="@style/Application.Design.Button.Text.Primary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/application___dimen__x4"
                android:gravity="end|center_vertical"
                android:text="@string/application___show_all"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/main__organization___events__title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/main__organization___events__title"
                app:layout_constraintTop_toTopOf="@+id/main__organization___events__title"
                tools:visibility="visible"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/main__organization___events__list"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:paddingTop="@dimen/application___dimen__x3"
                android:paddingBottom="@dimen/application___dimen__x2"
                android:visibility="gone"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___events__empty__icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___events__title"
                tools:visibility="visible"/>

            <ImageView
                android:id="@+id/main__organization___events__empty__icon"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginTop="@dimen/application___dimen__x6"
                android:src="@drawable/application___calendar"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___events__empty__message"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___events__list"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___events__empty__message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:gravity="center"
                android:text="@string/main__organization___events__empty"
                android:textAppearance="@style/Application.Design.Body2.TextAppearance.Gray"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___vacancies__title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___events__empty__icon"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___vacancies__title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x8"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:text="@string/main__organization___vacancies"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___vacancies__list"
                app:layout_constraintEnd_toStartOf="@+id/main__organization___vacancies__all"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___events__empty__message"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/main__organization___vacancies__all"
                style="@style/Application.Design.Button.Text.Primary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/application___dimen__x4"
                android:gravity="end|center_vertical"
                android:text="@string/application___show_all"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/main__organization___vacancies__title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/main__organization___vacancies__title"
                app:layout_constraintTop_toTopOf="@+id/main__organization___vacancies__title"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/main__organization___vacancies__list"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:paddingTop="@dimen/application___dimen__x3"
                android:paddingBottom="@dimen/application___dimen__x2"
                android:visibility="gone"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___vacancies__empty__icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___vacancies__title"/>

            <ImageView
                android:id="@+id/main__organization___vacancies__empty__icon"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginTop="@dimen/application___dimen__x6"
                android:src="@drawable/application___speciality"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___vacancies__empty__message"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___vacancies__list"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___vacancies__empty__message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/main__organization___vacancies__empty"
                android:textAppearance="@style/Application.Design.Body2.TextAppearance.Gray"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___categories__title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___vacancies__empty__icon"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___categories__title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x8"
                android:text="@string/main__organization___categories"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___categories__list"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___vacancies__empty__message"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/main__organization___categories__list"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:nestedScrollingEnabled="false"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___info__title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___categories__title"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___info__title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x8"
                android:text="@string/application___main_information"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___info__full_name"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___categories__list"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___info__full_name"
                style="@style/Main.Organization.Info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/main__organization"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___info__volunteers"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___info__title"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___info__volunteers"
                style="@style/Main.Organization.Info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/application___account_group"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___info__images"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___info__full_name"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___info__images"
                style="@style/Main.Organization.Info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:drawableStart="@drawable/application___gallery"
                android:foreground="?selectableItemBackground"
                android:paddingVertical="@dimen/application___dimen__x2"
                android:text="@string/main__organization___images"
                android:textColor="@color/application___color__primary"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___info__videos"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___info__volunteers"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___info__videos"
                style="@style/Main.Organization.Info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:drawableStart="@drawable/application___video"
                android:foreground="?selectableItemBackground"
                android:paddingVertical="@dimen/application___dimen__x2"
                android:text="@string/main__organization___videos"
                android:textColor="@color/application___color__primary"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___contacts"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___info__images"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___contacts"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:text="@string/main__profile___info__contacts"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___contacts__phone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___info__videos"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___contacts__phone"
                style="@style/Main.Organization.Info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/application___phone"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___contacts__email"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___contacts"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___contacts__email"
                style="@style/Main.Organization.Info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/application___email"
                android:foreground="?selectableItemBackground"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___contacts__address"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___contacts__phone"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___contacts__address"
                style="@style/Main.Organization.Info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/application___location"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___contacts__leader"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___contacts__email"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___contacts__leader"
                style="@style/Main.Organization.Info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/application___person"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___contacts__site"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___contacts__address"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___contacts__site"
                style="@style/Main.Organization.Info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/application___web"
                android:foreground="?selectableItemBackground"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___contacts__socials"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___contacts__leader"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___contacts__socials__title"
                style="@style/Main.Organization.Info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="0dp"
                android:layout_marginStart="@dimen/application___dimen__x5"
                android:layout_marginEnd="@dimen/application___dimen__x3"
                android:drawableStart="@drawable/application___web"
                android:gravity="center_vertical"
                android:text="@string/main__organization___socials"
                app:layout_constraintBottom_toBottomOf="@+id/main__organization___contacts__socials"
                app:layout_constraintEnd_toStartOf="@+id/main__organization___contacts__socials"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/main__organization___contacts__socials"
                app:layout_constraintVertical_bias="0"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/main__organization___contacts__socials"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___analytics__title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/main__organization___contacts__socials__title"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___contacts__site"
                app:spanCount="3"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___analytics__title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:text="@string/application___analytics"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___description"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___contacts__socials"/>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/main__organization___analytics__container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingVertical="14dp"
                app:layout_constraintBottom_toTopOf="@id/main__organization___description__title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/main__organization___analytics__title">

                <LinearLayout
                    android:id="@+id/main__organization___analytics__volunteers"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/shape_simple_frame"
                    android:orientation="vertical"
                    android:padding="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/main__organization___analytics__hours"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/main__organization___analytics__volunteers_count"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:maxLines="1"
                        android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                        android:textSize="16sp"
                        tools:text="64 354"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/main__organization___analytics__volunteers_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/main__organization___analytics__volunteers"
                        android:textAppearance="@style/Application.Design.H7.TextAppearance.Gray"
                        android:textSize="12sp"/>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/main__organization___analytics__hours"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/shape_simple_frame"
                    android:orientation="vertical"
                    android:padding="16dp"
                    app:layout_constraintBottom_toBottomOf="@id/main__organization___analytics__volunteers"
                    app:layout_constraintEnd_toStartOf="@id/main__organization___analytics__events"
                    app:layout_constraintStart_toEndOf="@id/main__organization___analytics__volunteers"
                    app:layout_constraintTop_toTopOf="@id/main__organization___analytics__volunteers">

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/main__organization___analytics__hours_count"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:maxLines="1"
                        android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                        android:textSize="16sp"
                        tools:text="102 990"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/main__organization___analytics__hours_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/main__organization___analytics__hours"
                        android:textAppearance="@style/Application.Design.H7.TextAppearance.Gray"
                        android:textSize="12sp"/>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/main__organization___analytics__events"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/shape_simple_frame"
                    android:orientation="vertical"
                    android:padding="16dp"
                    app:layout_constraintBottom_toBottomOf="@id/main__organization___analytics__volunteers"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/main__organization___analytics__hours"
                    app:layout_constraintTop_toTopOf="@id/main__organization___analytics__volunteers">

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/main__organization___analytics__events_count"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:maxLines="1"
                        android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                        android:textSize="16sp"
                        tools:text="243"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/main__organization___analytics__events_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/main__organization___analytics__events"
                        android:textAppearance="@style/Application.Design.H7.TextAppearance.Gray"
                        android:textSize="12sp"/>

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___description__title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:text="@string/application___description"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___description"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___analytics__container"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___description"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___results__title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___description__title"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___results__title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:text="@string/main__organization___results"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___results"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___description"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___results"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___documents__title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___results__title"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___documents__title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:text="@string/main__organization___documents"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___documents__list"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___results"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/main__organization___documents__list"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:nestedScrollingEnabled="false"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___review__title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___documents__title"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__organization___review__title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:text="@string/main__profile___comments"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___review__list"
                app:layout_constraintEnd_toStartOf="@+id/main__organization___review__all"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___documents__list"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/main__organization___review__all"
                style="@style/Application.Design.Button.Text.Primary"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginEnd="@dimen/application___dimen__x4"
                android:gravity="end|center_vertical"
                android:text="@string/application___show_all"
                app:layout_constraintBottom_toBottomOf="@+id/main__organization___review__title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/main__organization___review__title"
                app:layout_constraintTop_toTopOf="@+id/main__organization___review__title"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/main__organization___review__list"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:nestedScrollingEnabled="false"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toTopOf="@+id/main__organization___bottom_space"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___review__title"
                tools:itemCount="1"
                tools:listitem="@layout/main__organization___review__item"/>

            <View
                android:id="@+id/main__organization___bottom_space"
                android:layout_width="0dp"
                android:layout_height="@dimen/application___dimen__x5"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/main__organization___review__list"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</FrameLayout>