<?xml version="1.0" encoding="utf-8"?>
<com.facebook.shimmer.ShimmerFrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main__notifications___skeleton__shimmer"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x5"
        android:layout_marginTop="@dimen/application___dimen__x4"
        android:orientation="vertical">

        <include layout="@layout/main__notifications___skeleton__item"/>

        <include layout="@layout/main__notifications___skeleton__item"/>

        <include layout="@layout/main__notifications___skeleton__item"/>

        <include layout="@layout/main__notifications___skeleton__item"/>

        <include layout="@layout/main__notifications___skeleton__item"/>

        <include layout="@layout/main__notifications___skeleton__item"/>

        <include layout="@layout/main__notifications___skeleton__item"/>

        <include layout="@layout/main__notifications___skeleton__item"/>

        <include layout="@layout/main__notifications___skeleton__item"/>

        <include layout="@layout/main__notifications___skeleton__item"/>

        <include layout="@layout/main__notifications___skeleton__item"/>

        <include layout="@layout/main__notifications___skeleton__item"/>
    </LinearLayout>
</com.facebook.shimmer.ShimmerFrameLayout>
