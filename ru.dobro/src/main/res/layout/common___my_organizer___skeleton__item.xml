<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/application___dimen__x3">

    <View
        android:id="@+id/logo"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@color/application___color__icons"
        app:layout_constraintBottom_toBottomOf="@+id/name"
        app:layout_constraintEnd_toStartOf="@+id/name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/name"/>

    <View
        android:id="@+id/name"
        android:layout_width="0dp"
        android:layout_height="25dp"
        android:layout_marginStart="@dimen/application___dimen__x2"
        android:layout_marginEnd="@dimen/application___dimen__x2"
        android:background="@color/application___color__icons"
        android:ellipsize="end"
        android:maxLines="2"
        android:textAppearance="@style/Application.Design.H7.TextAppearance"
        app:layout_constraintEnd_toStartOf="@+id/join_or_leave"
        app:layout_constraintStart_toEndOf="@+id/logo"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"/>

    <View
        android:id="@+id/join_or_leave"
        android:layout_width="100dp"
        android:layout_height="30dp"
        android:layout_marginVertical="@dimen/application___dimen__x2"
        android:background="@color/application___color__icons"
        app:layout_constraintBottom_toBottomOf="@+id/name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/name"/>

    <View
        android:id="@+id/rating_container"
        android:layout_width="50dp"
        android:layout_height="25dp"
        android:layout_marginTop="@dimen/application___dimen__x1"
        android:background="@color/application___color__icons"
        android:orientation="horizontal"
        android:paddingVertical="@dimen/application___dimen__x1"
        app:layout_constraintStart_toStartOf="@+id/name"
        app:layout_constraintTop_toBottomOf="@+id/name"/>

    <View
        android:id="@+id/main__organization___info__volunteers"
        android:layout_width="50dp"
        android:layout_height="25dp"
        android:layout_marginStart="@dimen/application___dimen__x2"
        android:background="@color/application___color__icons"
        app:layout_constraintBottom_toBottomOf="@+id/rating_container"
        app:layout_constraintStart_toEndOf="@+id/rating_container"
        app:layout_constraintTop_toTopOf="@+id/rating_container"
        tools:text="24 тыс."/>

</androidx.constraintlayout.widget.ConstraintLayout>