<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardViewNearbyEvent"
    android:layout_width="173dp"
    android:layout_height="280dp"
    android:background="@drawable/application___background__round_corners_no_padding">

    <com.facebook.shimmer.ShimmerFrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?selectableItemBackgroundBorderless">

            <View
                android:id="@+id/image"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginHorizontal="6dp"
                android:layout_marginTop="6dp"
                android:background="@drawable/application___background__round_corners"
                android:backgroundTint="@color/application___color__icons"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:shapeAppearanceOverlay="@style/rounded9ImageView"
                tools:src="@drawable/main__vacancy__request_failed___illustration"/>

            <View
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="6dp"
                android:background="@drawable/application___background__round_corners_extra_small_stroke"
                android:backgroundTint="@color/application___color__icons"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/image"
                tools:text="Станьте волонтёром для праздников в детском лагере"/>

            <View
                android:id="@+id/periodContainer"
                android:layout_width="0dp"
                android:layout_height="20dp"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="10dp"
                android:background="@drawable/application___background__round_corners_extra_small_stroke"
                android:backgroundTint="@color/application___color__icons"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title"/>

            <View
                android:id="@+id/timeContainer"
                android:layout_width="0dp"
                android:layout_height="20dp"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="6dp"
                android:background="@drawable/application___background__round_corners_extra_small_stroke"
                android:backgroundTint="@color/application___color__icons"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/periodContainer"/>

            <View
                android:id="@+id/locationContainer"
                android:layout_width="0dp"
                android:layout_height="20dp"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="6dp"
                android:background="@drawable/application___background__round_corners_extra_small_stroke"
                android:backgroundTint="@color/application___color__icons"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/timeContainer"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.facebook.shimmer.ShimmerFrameLayout>

</FrameLayout>