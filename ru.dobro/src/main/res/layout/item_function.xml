<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/application___background__round_corners_frame"
    android:paddingBottom="@dimen/application___dimen__x4">

    <FrameLayout
        android:id="@+id/clickableContent"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:translationZ="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x4"
        android:layout_marginBottom="@dimen/application___dimen__x1"
        android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Роль волонтера помощь и обучение, участие в мероприятии"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/isVariant"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x2"
        android:background="@drawable/main__vacancy__item___requirement__background"
        android:text="Регистрация на мероприятие в качестве участника"
        android:textAppearance="@style/Application.Design.Button.TextAppearance.Primary"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title"
        tools:visibility="visible"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x2"
        android:visibility="gone"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
        android:maxLines="6"
        android:ellipsize="end"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/isVariant"
        tools:text="Нужно сопроводить бабушку до больницы. Бабушке 71 год, плохо ходит, нужно пройти обследование сердца у врача. Процедура займёт примерно 2 часа и далее необходи..."
        tools:visibility="visible"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/requirements"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x2"
        android:orientation="horizontal"
        app:layoutManager="com.google.android.flexbox.FlexboxLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/description"
        tools:itemCount="3"
        tools:listitem="@layout/main__vacancy___requirement"/>
</androidx.constraintlayout.widget.ConstraintLayout>