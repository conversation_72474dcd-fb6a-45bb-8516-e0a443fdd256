<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="2dp"
    android:animateLayoutChanges="true"
    app:cardCornerRadius="@dimen/application___dimen__x3">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        android:padding="@dimen/application___dimen__x4">

        <ImageView
            android:id="@+id/main__organization___review__item_avatar"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@android:color/holo_red_light"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/main__organization___review__item_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/main__organization___review__item_avatar"
            app:layout_constraintTop_toTopOf="@id/main__organization___review__item_avatar"
            tools:text="Александра"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/main__organization___review__item_event"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x1"
            android:background="?selectableItemBackground"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:text="@string/main__vacancy___show_event"
            android:textAppearance="@style/Application.Design.Body2.TextAppearance.Primary"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@+id/main__organization___review__item_avatar"
            app:layout_constraintEnd_toEndOf="@id/main__organization___review__item_name"
            app:layout_constraintStart_toStartOf="@id/main__organization___review__item_name"
            app:layout_constraintTop_toBottomOf="@id/main__organization___review__item_name"/>

        <androidx.appcompat.widget.AppCompatRatingBar
            android:id="@+id/main__organization___review__item_rating"
            style="?android:attr/ratingBarStyleSmall"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x1"
            android:background="@null"
            android:numStars="5"
            android:stepSize="1"
            android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"
            android:theme="@style/RatingBar"
            app:layout_constraintStart_toStartOf="@id/main__organization___review__item_event"
            app:layout_constraintTop_toBottomOf="@+id/main__organization___review__item_event"
            tools:rating="2"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/main__organization___review__item_comment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="6"
            android:textAlignment="textStart"
            android:textAppearance="@style/Application.Design.Body2.TextAppearance.Black"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/main__organization___review__item_rating"
            tools:text="Мероприятие очень важное, организаторы занимаются важным делом, однако все, что касается организации мероприятия, было проведено на четверочку, потому что с самого ..."/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/main__organization___review__item_comment_expand"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:text="@string/application___expand"
            android:textAppearance="@style/Application.Design.Body2.TextAppearance"
            android:textColor="@color/application___color__primary"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/main__organization___review__item_comment"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/main__organization___review__item_date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="6"
            android:textAlignment="textStart"
            android:textAppearance="@style/Application.Design.Body2.TextAppearance.Gray"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/main__organization___review__item_comment_expand"
            tools:text="09:29, 30 октября 2021 "/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>