<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    android:id="@+id/onboarding_download_dialog_card"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    app:cardCornerRadius="20dp"
    android:background="@null"
    android:elevation="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/onboarding_download_dialog_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/application___dimen__x4">

        <ImageButton
            android:id="@+id/onboarding_download_close"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/application___close"
            android:backgroundTint="@color/application___color__button__gray__60"
            android:background="@drawable/application___background__round_corners"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>


        <TextView
            android:id="@+id/onboarding_download_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Свой вариант"
            android:gravity="center"
            android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/onboarding_download_hint"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Откуда вы узнали про наше приложение"
            app:layout_constraintTop_toBottomOf="@id/onboarding_download_title"
            app:layout_constraintStart_toStartOf="parent"/>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/onboarding_download_input_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            app:counterEnabled="true"
            app:counterMaxLength="300"
            app:hintEnabled="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:boxBackgroundMode="none"
            app:layout_constraintTop_toBottomOf="@id/onboarding_download_hint">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/onboarding_download_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="10dp"
                android:hint="Введите текст"
                android:gravity="start"
                android:textCursorDrawable="@drawable/purple_cursor"
                android:background="@drawable/onboarding_edit_text"
                android:textSize="17sp"
                android:minHeight="150dp"
            />

        </com.google.android.material.textfield.TextInputLayout>


        <Button
            android:id="@+id/onboarding_download_next"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:text="Сохранить"
            style="@style/Application.Design.Button.Primary"
            app:layout_constraintTop_toBottomOf="@id/onboarding_download_input_container"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
