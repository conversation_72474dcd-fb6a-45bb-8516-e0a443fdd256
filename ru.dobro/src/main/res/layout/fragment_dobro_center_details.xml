<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:scrollbars="none">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/application___dimen__x4"
            app:cardElevation="0dp"
            tools:ignore="NegativeMargin">

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                android:textStyle="normal"
                tools:text="Запись на сервис «Подбор эксперта»"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:maxLines="14"
                android:textAppearance="@style/Application.Design.Body2.TextAppearance.Black"
                tools:text="Проведение онлайн и офлайн консультаций: - по индивидуальному запросу граждан по вопросам развития добровольчества, благотворительности, гражданских и социальных инициатив на территории на федеральном и региональном уровнях; - по реализации региональных и федеральных проектов; - о формах, методах, возможностях и мерах поддержки  в сфере развития гражданских и социальных инициатив, добровольчества и благотворительности на региональном и федеральном уровне; - иное. \nКонсультации предоставляются посредством..."/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/descriptionExpand"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:paddingVertical="@dimen/application___dimen__x4"
                android:text="@string/application___expand"
                android:textAppearance="@style/Application.Design.Body2.TextAppearance.Primary"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/main__organization___review__item_comment"
                tools:visibility="visible"/>

            <LinearLayout
                android:id="@+id/documentsContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:background="@drawable/application___background__round_corners"
                android:orientation="vertical"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:paddingVertical="@dimen/application___dimen__x4"
                android:visibility="gone"
                tools:visibility="visible">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/requestTitle"
                    style="@style/Application.Design.H7.TextAppearance.Black"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Документы"
                    android:textSize="15sp"
                    android:textStyle="normal"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/documents"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    android:orientation="vertical"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:listitem="@layout/item_simple_file"/>

            </LinearLayout>

            <ProgressBar
                android:id="@+id/progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:indeterminateTint="@color/application___color__orange"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/submit"
                style="@style/Application.Design.Button.Primary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginTop="40dp"
                android:text="Подать заявку"
                android:visibility="gone"
                tools:visibility="visible"/>

        </LinearLayout>

    </LinearLayout>

</ScrollView>