<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="@dimen/application___dimen__x8">

    <ImageView
        android:id="@+id/logo"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:src="@drawable/application___logo"
        app:layout_constraintBottom_toTopOf="@+id/title"
        app:layout_constraintDimensionRatio="222:97"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/application___dimen__x5"
        android:layout_marginBottom="@dimen/application___dimen__x8"
        android:text="@string/app_update_request___go_to_googleplay"
        android:textAlignment="center"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/update"
        style="@style/Application.Design.Button.Primary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x2"
        android:layout_marginTop="@dimen/application___dimen__x8"
        android:text="@string/app_update_request___update"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/dismiss"
        style="@style/Application.Design.Button.White"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x2"
        android:layout_marginTop="@dimen/application___dimen__x4"
        android:text="@string/app_update_request___dismiss"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/update"/>
</androidx.constraintlayout.widget.ConstraintLayout>