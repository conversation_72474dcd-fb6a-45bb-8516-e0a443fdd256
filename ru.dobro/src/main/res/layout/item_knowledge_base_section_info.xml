<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/application___dimen__x4"
    android:layout_marginTop="@dimen/application___dimen__x4"
    android:background="@drawable/application___background__white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x4"
        android:layout_marginEnd="@dimen/application___dimen__x2"
        android:layout_marginBottom="@dimen/application___dimen__x4"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/section_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:textAppearance="@style/Application.Design.Body2.TextAppearance.Black"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/last_update"
            tools:text="Доступ к аккаунту"
            tools:visibility="visible"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Как получить бумажную копию личной электронной книжки волонтера (ЛЭКВ)"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/last_update"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:textAppearance="@style/Application.Design.Body2.TextAppearance.DarkGray"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title"
            tools:text="Последнее обновление 20 декабря 2022 г."/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/delails"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:drawableEnd="@drawable/application___arrow_right"
            android:gravity="center_vertical"
            android:text="Читать подробнее"
            android:textAppearance="@style/Application.Design.Body2.TextAppearance.Primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/last_update"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>