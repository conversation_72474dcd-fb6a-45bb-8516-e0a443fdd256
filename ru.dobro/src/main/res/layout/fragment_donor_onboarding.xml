<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_donor_onboardiing"
    android:fitsSystemWindows="false"
    tools:context=".main.profile.DonorOnboardingFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:elevation="1dp">

        <ImageView
            android:id="@+id/logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/application___logo_variant"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription"/>

        <ImageView
            android:id="@+id/close"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginVertical="@dimen/application___dimen__x2"
            android:layout_marginEnd="@dimen/application___dimen__x5"
            android:src="@drawable/ic_close_variant"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/image"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/content"
        app:layout_constraintVertical_bias="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:scaleType="fitCenter"
        android:adjustViewBounds="true"
        android:paddingTop="@dimen/application___dimen__x4"
        app:layout_constraintBottom_toTopOf="@+id/guideline_horizontal"
        android:src="@drawable/ic_donor_onboarding"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.5"/>

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/guideline_horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintVertical_bias="0.2"
        app:layout_constraintBottom_toTopOf="@+id/bottomContainer"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:orientation="vertical">

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="Статистика донаций\nуже на платформе"
            android:textAppearance="@style/Application.Design.H5.TextAppearance.White"
            android:textSize="26sp"
            android:maxLines="2"
            android:autoSizeTextType="uniform"
            android:autoSizeMinTextSize="12sp"
            android:autoSizeMaxTextSize="26sp"
            android:autoSizeStepGranularity="1sp" />

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:gravity="center"
            android:text="Теперь вы сможете отслеживать\nсвой вклад в донорство крови\nи её компонентов"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance.White"
            android:textSize="20sp"
            android:maxLines="3"
            android:autoSizeTextType="uniform"
            android:autoSizeMinTextSize="12sp"
            android:autoSizeMaxTextSize="20sp"
            android:autoSizeStepGranularity="1sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/bottomContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x6"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/learnMore"
            style="@style/Application.Design.Button.OutlinedButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/white"
            android:text="Узнать больше"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"
            android:textColor="@color/application___color__primary"
            android:textSize="15sp"
            app:cornerRadius="14dp"
            app:strokeColor="@color/white"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/volunteerBook"
            style="@style/Application.Design.Button.OutlinedButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:visibility="gone"
            tools:visibility="visible"
            android:backgroundTint="@color/application___color__primary"
            android:text="Книжка волонтёра"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance.White"
            android:textColor="@color/white"
            android:textSize="15sp"
            app:cornerRadius="14dp"
            app:strokeColor="@color/white"/>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>