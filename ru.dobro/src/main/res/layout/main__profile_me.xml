<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/skeleton"
        layout="@layout/main__profile_me___skeleton"
        tools:visibility="gone"/>

    <include
        android:id="@+id/authorization"
        layout="@layout/authorization__plug___profile"
        android:visibility="gone"/>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/main__profile_me___logged_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="@dimen/application___dimen__x1">

            <include
                android:id="@+id/toolbar"
                layout="@layout/element_profile_toolbar"/>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/avatarContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x4">

                <ImageView
                    android:id="@+id/main__profile_me___avatar"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_marginStart="@dimen/application___dimen__x4"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="ContentDescription"
                    tools:src="@drawable/application___person"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__profile_me___name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:gravity="start"
                    android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                    android:textSize="20sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/main__profile_me___avatar"
                    app:layout_constraintTop_toTopOf="@+id/main__profile_me___avatar"
                    tools:text="Иванова Анаcтаcия"/>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x1"
                    android:orientation="horizontal"
                    app:layout_constraintStart_toEndOf="@+id/main__profile_me___avatar"
                    app:layout_constraintTop_toBottomOf="@+id/main__profile_me___name">

                    <LinearLayout
                        android:id="@+id/main__profile_me___rating__container"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/application___background__circle"
                        android:backgroundTint="@color/white"
                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/application___dimen__x1"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/application___star"
                            app:tint="@color/application___color__tertiary"
                            tools:ignore="ContentDescription"/>

                        <com.google.android.material.textview.MaterialTextView
                            android:id="@+id/main__profile_me___rating"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="@dimen/application___dimen__x1"
                            android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"
                            android:textSize="13sp"
                            tools:text="3,8"/>

                    </LinearLayout>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/main__profile_me___id"
                        android:layout_width="wrap_content"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        android:drawableEnd="@drawable/application___copy_variant"
                        android:foreground="?selectableItemBackground"
                        android:gravity="center_vertical"
                        android:textAppearance="@style/Application.Design.Subtitle2.TextAppearance.Primary"
                        android:textSize="13sp"
                        tools:text="ID: 91701111"/>

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/addEvent"
                style="@style/Application.Design.Button.Primary"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_margin="@dimen/application___dimen__x4"
                android:text="@string/main_profile_organization__create_event"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/main__profile_me___id"/>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/eventsHeader"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x4">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/eventsTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="@dimen/application___dimen__x4"
                    android:text="@string/main__organization___events"
                    android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                    app:layout_constraintEnd_toStartOf="@+id/showAll"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/showAll"
                    style="@style/Application.Design.Button.Text.Primary"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_marginEnd="@dimen/application___dimen__x4"
                    android:gravity="end|center_vertical"
                    android:text="@string/application___show_all"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@+id/eventsTitle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/eventsTitle"
                    app:layout_constraintTop_toTopOf="@+id/eventsTitle"
                    tools:visibility="visible"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/events"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:paddingTop="@dimen/application___dimen__x3"
                android:paddingBottom="@dimen/application___dimen__x2"
                android:visibility="gone"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/eventsTitle"
                tools:itemCount="2"
                tools:listitem="@layout/main__home___event__item"
                tools:visibility="visible"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/emptyEvents"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:gravity="center"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:text="@string/main__organization___events__empty"
                android:textAppearance="@style/Application.Design.Body2.TextAppearance.Gray"
                android:visibility="gone"
                tools:visibility="visible"/>

            <include
                android:id="@+id/emailVerificationRequired"
                layout="@layout/element_email_verification_required"/>

            <include
                android:id="@+id/esiaVerificationRequired"
                layout="@layout/element_esia_verification_required"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:background="@drawable/application___background__white"
                android:foreground="@drawable/application___ripple"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/main__profile_me___requests"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/requestsIcon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginVertical="@dimen/application___dimen__x2"
                        android:layout_marginStart="@dimen/application___dimen__x4"
                        android:src="@drawable/main__profile_me___tasks"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:ignore="ContentDescription"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/requestTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/application___dimen__x4"
                        android:text="@string/main__profile___invites"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/requestsIcon"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/requests_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/request_arrow"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="12"
                        tools:visibility="visible"/>

                    <ImageView
                        android:id="@+id/request_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/application___dimen__x4"
                        android:src="@drawable/application___arrow_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/black"
                        tools:ignore="ContentDescription"/>

                    <View
                        style="@style/Application.Divider.Wide"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:background="#E3E3EF"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="@+id/requestTitle"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/main__profile_me___favorites"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/favouriteIcon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginVertical="@dimen/application___dimen__x2"
                        android:layout_marginStart="@dimen/application___dimen__x4"
                        android:src="@drawable/main__profile_me___favorites"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.0"
                        tools:ignore="ContentDescription"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/favouriteTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/application___dimen__x4"
                        android:text="@string/main__profile___favorites"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/favouriteIcon"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/favourite_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/favourite_arrow"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="12"
                        tools:visibility="visible"/>

                    <ImageView
                        android:id="@+id/favourite_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/application___dimen__x4"
                        android:src="@drawable/application___arrow_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/black"
                        tools:ignore="ContentDescription"/>

                    <View
                        style="@style/Application.Divider.Wide"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:background="#E3E3EF"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="@+id/favouriteTitle"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/main__profile_me___volunteer_book"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/volunteerBookIcon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginVertical="@dimen/application___dimen__x2"
                        android:layout_marginStart="@dimen/application___dimen__x4"
                        android:src="@drawable/main__profile_me___volunteer_book"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:ignore="ContentDescription"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/volunteerBookTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/application___dimen__x4"
                        android:text="@string/main__profile___volunteer_book"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/volunteerBookIcon"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/application___dimen__x4"
                        android:src="@drawable/application___arrow_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/black"
                        tools:ignore="ContentDescription"/>

                    <View
                        style="@style/Application.Divider.Wide"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:background="#E3E3EF"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="@+id/volunteerBookTitle"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/myFriendsContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:animateLayoutChanges="true"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/myFriendsIcon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginVertical="@dimen/application___dimen__x2"
                        android:layout_marginStart="@dimen/application___dimen__x4"
                        android:src="@drawable/main__profile_me___friends"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:ignore="ContentDescription"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/myFriendsTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/application___dimen__x4"
                        android:text="@string/profile_friends"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/myFriendsIcon"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/myFriendsRequestsCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/application___background__white"
                        android:backgroundTint="#EAE5F6"
                        android:paddingHorizontal="@dimen/application___dimen__x2"
                        android:paddingVertical="@dimen/application___dimen__x1"
                        android:gravity="center"
                        app:layout_constraintHorizontal_bias="0"
                        android:textAppearance="@style/Application.Design.Body2.TextAppearance.Primary"
                        android:visibility="gone"
                        android:autoSizeTextType="uniform"
                        android:autoSizeMinTextSize="5sp"
                        android:autoSizeMaxTextSize="14sp"
                        android:autoSizeStepGranularity="1sp"
                        android:layout_marginStart="@dimen/application___dimen__x2"
                        android:layout_marginEnd="@dimen/application___dimen__x1"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/myFriendsTitle"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/my_friends_count"
                        tools:text="5 заявок"
                        tools:visibility="visible"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/my_friends_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/my_friends_arrow"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="12"
                        tools:visibility="visible"/>

                    <ImageView
                        android:id="@+id/my_friends_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/application___dimen__x4"
                        android:src="@drawable/application___arrow_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/black"
                        tools:ignore="ContentDescription"/>

                    <View
                        style="@style/Application.Divider.Wide"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:background="#E3E3EF"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="@+id/myFriendsTitle"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/myOrganizationsContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/myOrganizationsIcon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginVertical="@dimen/application___dimen__x2"
                        android:layout_marginStart="@dimen/application___dimen__x4"
                        android:src="@drawable/main__profile_me___organizations"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:ignore="ContentDescription"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/myOrganizationsTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/application___dimen__x4"
                        android:text="@string/profile_organizations"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/myOrganizationsIcon"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/my_organizations_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/my_organizations_arrow"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="12"
                        tools:visibility="visible"/>

                    <ImageView
                        android:id="@+id/my_organizations_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/application___dimen__x4"
                        android:src="@drawable/application___arrow_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/black"
                        tools:ignore="ContentDescription"/>

                    <View
                        style="@style/Application.Divider.Wide"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:background="#E3E3EF"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="@id/myOrganizationsTitle"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/main__profile_me___comments"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/commentsIcon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginVertical="@dimen/application___dimen__x2"
                        android:layout_marginStart="@dimen/application___dimen__x4"
                        android:src="@drawable/main__profile_me___comments"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:ignore="ContentDescription"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/commentsTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/application___dimen__x4"
                        android:text="@string/main__profile___comments"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/commentsIcon"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/comments_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/comments_arrow"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="12"
                        tools:visibility="visible"/>

                    <ImageView
                        android:id="@+id/comments_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/application___dimen__x4"
                        android:src="@drawable/application___arrow_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/black"
                        tools:ignore="ContentDescription"/>

                    <View
                        style="@style/Application.Divider.Wide"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:background="#E3E3EF"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="@id/commentsTitle"/>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/main__profile_me___my_posts"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/myPostsIcon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginVertical="@dimen/application___dimen__x2"
                        android:layout_marginStart="@dimen/application___dimen__x4"
                        android:src="@drawable/main__profile_me___my_posts"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:ignore="ContentDescription"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/myPostsTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/application___dimen__x4"
                        android:text="@string/main__profile___my_posts"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/myPostsIcon"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/myPostsNew"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/application___background__white"
                        android:backgroundTint="#EAE5F6"
                        android:paddingHorizontal="@dimen/application___dimen__x2"
                        android:paddingVertical="@dimen/application___dimen__x1"
                        android:text="@string/main__profile___new"
                        android:textAppearance="@style/Application.Design.Body2.TextAppearance.Primary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/my_posts_arrow"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <ImageView
                        android:id="@+id/my_posts_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/application___dimen__x4"
                        android:src="@drawable/application___arrow_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/black"
                        tools:ignore="ContentDescription"/>
                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___categories__title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x6"
                android:text="@string/main__profile___my_categories"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/main__profile_me___categories__list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:nestedScrollingEnabled="false"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/main__profile_me___categories__change"
                style="@style/Application.Design.Button.TextIcon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x2"
                app:icon="@drawable/main__profile_me___edit_variant"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___info__title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:text="@string/main__profile___info"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___info__gender"
                style="@style/Main.Profile.Info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/main___profile"
                android:visibility="gone"
                tools:visibility="visible"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___info__birth_date"
                style="@style/Main.Profile.Info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/main__profile___birth"
                android:visibility="gone"
                tools:visibility="visible"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___info__registration_date"
                style="@style/Main.Profile.Info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/application___person_add"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___info__languages"
                style="@style/Main.Profile.Info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/main__profile___languages"
                android:visibility="gone"
                tools:visibility="visible"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___info__russian_citizenship"
                style="@style/Main.Profile.Info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/main__profile___citizenship"
                android:text="@string/main__profile___info__citizenship"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___info__type_of_employment"
                style="@style/Main.Profile.Info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/application___speciality"
                android:visibility="gone"
                tools:visibility="visible"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___info__t_shirt_size"
                style="@style/Main.Profile.Info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/main__profile___t_shirt"
                android:visibility="gone"
                tools:visibility="visible"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___info__organization_name"
                style="@style/Main.Profile.Info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/application___account_group"
                android:visibility="gone"
                tools:visibility="visible"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___info__medical_history"
                style="@style/Main.Profile.Info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/main__profile___medical_history"
                android:text="@string/main__profile___info__has_medical_history"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___education"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:text="@string/main__profile___info__education"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                android:visibility="gone"
                tools:visibility="visible"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___education__level"
                style="@style/Main.Profile.Info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/main__profile___education__level"
                android:visibility="gone"
                tools:visibility="visible"/>

            <LinearLayout
                android:id="@+id/main__profile_me___education__container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___job"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:text="@string/main__profile___info__job"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                android:visibility="gone"
                tools:visibility="visible"/>

            <LinearLayout
                android:id="@+id/main__profile_me___job__container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___contacts"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:text="@string/main__profile___info__contacts"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                android:visibility="gone"
                tools:visibility="visible"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___contacts__phone"
                style="@style/Main.Profile.Info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/application___phone"
                android:visibility="gone"
                tools:visibility="visible"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___contacts__email"
                style="@style/Main.Profile.Info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:drawableStart="@drawable/application___email"
                android:visibility="gone"
                tools:visibility="visible"/>

            <LinearLayout
                android:id="@+id/main__profile_me___contacts__address_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__profile_me___contacts__country"
                    style="@style/Main.Profile.Info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x3"
                    android:drawableStart="@drawable/application___location"
                    android:visibility="gone"
                    tools:visibility="visible"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__profile_me___contacts__address"
                    style="@style/Main.Profile.Info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x3"
                    android:drawableStart="@drawable/application___location"
                    android:visibility="gone"
                    tools:visibility="visible"/>
            </LinearLayout>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___contacts__socials__title"
                style="@style/Main.Profile.Info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="0dp"
                android:layout_marginStart="@dimen/application___dimen__x5"
                android:layout_marginTop="@dimen/application___dimen__x1"
                android:layout_marginEnd="@dimen/application___dimen__x3"
                android:drawableStart="@drawable/application___web"
                android:gravity="center_vertical"
                android:text="@string/main__profile___info__socials"
                android:visibility="gone"
                tools:visibility="visible"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/main__profile_me___contacts__socials"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:orientation="vertical"
                android:visibility="gone"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="3"
                tools:visibility="visible"/>

            <include
                android:id="@+id/analytics"
                layout="@layout/include_analytics_organization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___about__title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:text="@string/main__profile___info__about"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                android:visibility="gone"
                tools:visibility="visible"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_me___about"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                android:visibility="gone"
                tools:visibility="visible"/>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/main__profile_me___about__trophies"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__profile_me___about__trophies_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/application___dimen__x4"
                    android:text="@string/main__profile___trophies__title"
                    android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                    app:layout_constraintBottom_toTopOf="@+id/main__profile_me___about__trophies_list"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__profile_me___about__trophies_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/application___dimen__x2"
                    android:textAppearance="@style/Application.Design.H6.TextAppearance.Gray"
                    app:layout_constraintBottom_toTopOf="@+id/main__profile_me___about__trophies_list"
                    app:layout_constraintStart_toEndOf="@id/main__profile_me___about__trophies_title"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="5"/>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/main__profile_me___about__trophies_show_all"
                    style="@style/Application.Design.Button.Text.Primary"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_marginEnd="@dimen/application___dimen__x4"
                    android:gravity="end|center_vertical"
                    android:insetTop="0dp"
                    android:insetBottom="0dp"
                    android:text="@string/application___show_all"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/main__profile_me___about__trophies_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:nestedScrollingEnabled="false"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/application___dimen__x4"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/main__profile_me___about__trophies_show_all"
                    tools:listitem="@layout/item_trophy"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/main__profile_me___about__edu_certificates"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:visibility="gone"
                tools:visibility="visible">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__profile_me___about__edu_certificates_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/main__profile___edu_certificates__title"
                    android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                    app:layout_constraintBottom_toTopOf="@+id/main__profile_me___about__edu_certificates_list"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__profile_me___about__edu_certificates_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/application___dimen__x2"
                    android:textAppearance="@style/Application.Design.H6.TextAppearance.Gray"
                    app:layout_constraintBottom_toTopOf="@+id/main__profile_me___about__edu_certificates_list"
                    app:layout_constraintStart_toEndOf="@id/main__profile_me___about__edu_certificates_title"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="5"/>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/main__profile_me___about__edu_certificates_show_all"
                    style="@style/Application.Design.Button.Text.Primary"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:gravity="end|center_vertical"
                    android:insetTop="0dp"
                    android:insetBottom="0dp"
                    android:text="@string/application___show_all"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/main__profile_me___about__edu_certificates_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:nestedScrollingEnabled="false"
                    android:orientation="vertical"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/main__profile_me___about__edu_certificates_show_all"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_edu_certificates"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/main__profile_me___about__certificates"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:layout_marginTop="@dimen/application___dimen__x5">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__profile_me___about__certificates_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:text="@string/main__profile___certificates__title"
                    android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                    app:layout_constraintBottom_toTopOf="@+id/main__profile_me___about__certificates_list"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__profile_me___about__certificates_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/application___dimen__x2"
                    android:textAppearance="@style/Application.Design.H6.TextAppearance.Gray"
                    app:layout_constraintBottom_toTopOf="@+id/main__profile_me___about__certificates_list"
                    app:layout_constraintStart_toEndOf="@id/main__profile_me___about__certificates_title"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="5"/>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/main__profile_me___about__certificates_show_all"
                    style="@style/Application.Design.Button.Text.Primary"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:gravity="end|center_vertical"
                    android:insetTop="0dp"
                    android:insetBottom="0dp"
                    android:text="@string/application___show_all"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/main__profile_me___about__certificates_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:nestedScrollingEnabled="false"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/application___dimen__x4"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/main__profile_me___about__certificates_show_all"
                    tools:listitem="@layout/item_certificates"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:id="@+id/main__profile_me___bottom_space"
                android:layout_width="match_parent"
                android:layout_height="@dimen/application___dimen__x5"/>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</FrameLayout>