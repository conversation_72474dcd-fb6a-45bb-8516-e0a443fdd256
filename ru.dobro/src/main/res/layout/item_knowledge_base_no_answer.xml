<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/application___dimen__x4"
    android:layout_marginTop="@dimen/application___dimen__x4"
    android:background="@drawable/application___background__white__selected">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x4"
        android:layout_marginEnd="@dimen/application___dimen__x2"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="@string/main__about___no_answer_title"
        android:textAppearance="@style/Application.Design.H6.TextAppearance.Primary"
        android:textSize="14sp"
        app:layout_constraintEnd_toStartOf="@+id/logo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x2"
        android:layout_marginEnd="@dimen/application___dimen__x2"
        android:layout_marginBottom="@dimen/application___dimen__x4"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="@string/main__about___no_answer_text"
        android:textAppearance="@style/Application.Design.Body2.TextAppearance.Primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/logo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"/>

    <ImageView
        android:id="@+id/logo"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="@dimen/application___dimen__x2"
        android:src="@drawable/ic_info_variant"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>