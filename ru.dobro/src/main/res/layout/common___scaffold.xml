<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/common___scaffold"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/common___scaffold__coordinator"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        tools:background="#AC3">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <FrameLayout
                android:id="@+id/common___scaffold__toolbar__no_internet__container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@color/application___color__accent_variant"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:paddingTop="@dimen/application___dimen__x3"
                android:paddingBottom="@dimen/application___dimen__x3"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/common___scaffold__toolbar__shift__container"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible">

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal|bottom"
                    android:text="@string/application___error__no_connection"
                    android:textAlignment="center"
                    android:textAppearance="@style/Application.Design.Body2.TextAppearance.White"/>
            </FrameLayout>

            <FrameLayout
                android:id="@+id/common___scaffold__toolbar__shift__container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toTopOf="@+id/common___scaffold__container"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/common___scaffold__toolbar__no_internet__container"
                tools:background="#A4A3"
                tools:minHeight="50dp"/>

            <FrameLayout
                android:id="@+id/common___scaffold__container"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/common___scaffold__toolbar__shift__container"/>

            <FrameLayout
                android:id="@+id/common___scaffold__toolbar__overlap__container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/common___scaffold__toolbar__no_internet__container"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <FrameLayout
        android:id="@+id/common___scaffold__bottom_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:background="#44A"
        tools:minHeight="50dp"/>
</LinearLayout>