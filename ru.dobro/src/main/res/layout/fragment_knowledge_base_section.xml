<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".main.about.knowledge_base.KnowledgeBaseSectionFragment">

    <include
        android:id="@+id/shimmer"
        layout="@layout/main__knowledge_base_section___skeleton"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/sections"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:nestedScrollingEnabled="false"
        android:orientation="vertical"
        android:paddingBottom="@dimen/application___dimen__x2"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>