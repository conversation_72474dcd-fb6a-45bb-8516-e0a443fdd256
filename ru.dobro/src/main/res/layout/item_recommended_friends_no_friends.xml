<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:layout_marginHorizontal="@dimen/application___dimen__x4"
    android:backgroundTint="@color/application___color__primary"
    android:text="@string/application___save"
    android:textAllCaps="false"
    app:cardCornerRadius="14dp">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/application___search_variant"
            tools:ignore="ContentDescription"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x1"
            android:text="Найти друзей"
            android:textAppearance="@style/Application.Design.H5.TextAppearance.White"
            android:textSize="17sp"/>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>