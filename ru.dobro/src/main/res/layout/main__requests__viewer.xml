<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/skeleton"
        layout="@layout/main__requests___skeleton"
        tools:visibility="gone"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/requests"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/application___dimen__x4"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

    <LinearLayout
        android:id="@+id/main__requests___empty"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="37dp"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:gravity="center"
            android:text="@string/main__requests___empty__title"
            android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"/>

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:text="@string/main__requests___empty__subtitle"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/main__requests_search"
            style="@style/Application.Design.Button.OutlinedButton"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/application___dimen__x12"
            android:layout_marginVertical="@dimen/application___dimen__x6"
            android:text="@string/request_search_button_title"/>
    </LinearLayout>
</FrameLayout>
