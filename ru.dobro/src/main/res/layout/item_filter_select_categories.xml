<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/application___dimen__x5">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/application___dimen__x2"
        android:paddingHorizontal="@dimen/application___dimen__x5"
        android:paddingVertical="@dimen/application___dimen__x2"
        android:text="@string/main__events__filter___event_directions"
        android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
        android:textSize="22sp"
        app:layout_constraintEnd_toStartOf="@+id/arrowContainer"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <LinearLayout
        android:id="@+id/arrowContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/application___dimen__x2"
        android:layout_marginEnd="@dimen/application___dimen__x4"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@+id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/title">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/arrowText"
            style="@style/Application.Design.Body2.TextAppearance.Primary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Скрыть"
            android:textSize="17sp"
            android:textStyle="normal"/>

        <ImageView
            android:id="@+id/arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/application___arrow_up_variant"
            tools:ignore="ContentDescription"/>

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/categories"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x2"
        android:layout_marginBottom="@dimen/application___dimen__x4"
        android:nestedScrollingEnabled="false"
        android:paddingStart="-4dp"
        android:paddingTop="-4dp"
        app:layoutManager="androidx.recyclerview.widget.StaggeredGridLayoutManager"
        tools:ignore="RtlSymmetry"
        tools:itemCount="2"
        tools:listitem="@layout/common___categories__item"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>