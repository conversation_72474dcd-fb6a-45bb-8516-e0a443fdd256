<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".main.posts.myPosts.MyPostsFragment">

    <androidx.constraintlayout.motion.widget.MotionLayout
        android:id="@+id/motionLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        app:layoutDescription="@xml/my_posts_scene">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            android:theme="@style/ThemeOverlay.MaterialComponents.Dark.ActionBar"
            app:elevation="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:navigationIcon="?attr/homeAsUpIndicator"
                app:navigationIconTint="@color/application___color__primary"
                app:title="@string/main__profile___my_posts"
                app:titleTextAppearance="@style/Application.Design.H6.TextAppearance.Black"
                app:titleTextColor="@color/black">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/createPost"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_marginEnd="@dimen/application___dimen__x4"
                    android:background="@drawable/application___background__white"
                    android:backgroundTint="@color/application___color__primary"
                    android:paddingHorizontal="@dimen/application___dimen__x2"
                    android:paddingVertical="@dimen/application___dimen__x1"
                    android:text="@string/create_post"
                    android:textAppearance="@style/Application.Design.Body1.TextAppearance"/>
            </com.google.android.material.appbar.MaterialToolbar>
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/posts"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/appBar">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/postsList"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:orientation="vertical"
                android:paddingBottom="@dimen/application___dimen__x4"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        <ImageView
            android:id="@+id/addPost"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/application___dimen__x4"
            android:layout_marginBottom="@dimen/application___dimen__x4"
            android:elevation="10dp"
            android:src="@drawable/ic_add_post"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:ignore="ContentDescription"/>

    </androidx.constraintlayout.motion.widget.MotionLayout>

    <include
        android:id="@+id/skeleton"
        layout="@layout/fragment_my_posts_skeleton"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="?attr/actionBarSize"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>