<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/application___dimen__x1"
        android:paddingStart="@dimen/application___dimen__x5"
        android:paddingVertical="@dimen/application___dimen__x2"
        tools:text="Формат доброго дела"
        android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
        android:textSize="22sp"
        app:layout_constraintEnd_toStartOf="@+id/arrowContainer"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <LinearLayout
        android:id="@+id/arrowContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/application___dimen__x4"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@+id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/title">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/arrowText"
            style="@style/Application.Design.Body2.TextAppearance.Primary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Скрыть"
            android:textSize="17sp"
            android:textStyle="normal"/>

        <ImageView
            android:id="@+id/arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/application___arrow_up_variant"
            tools:ignore="ContentDescription"/>

    </LinearLayout>

    <RadioGroup
        android:id="@+id/filterContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginBottom="@dimen/application___dimen__x2"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <RadioButton
            android:id="@+id/main__events_filter__online_type___all"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/application___dimen__x2"
            android:text="@string/main__events__filter___type__all"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance"/>

        <RadioButton
            android:id="@+id/main__events_filter__online_type___online"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/application___dimen__x2"
            android:text="@string/main__events__filter___type__online"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance"/>

        <RadioButton
            android:id="@+id/main__events_filter__online_type___offline"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/application___dimen__x2"
            android:text="@string/main__events__filter___type__offline"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance"/>
    </RadioGroup>

</androidx.constraintlayout.widget.ConstraintLayout>