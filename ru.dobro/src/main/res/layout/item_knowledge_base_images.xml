<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/application___dimen__x4"
    android:layout_marginTop="@dimen/application___dimen__x4"
    android:orientation="vertical">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/images_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

    <com.tbuonomo.viewpagerdotsindicator.DotsIndicator
        android:id="@+id/dots"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginTop="@dimen/application___dimen__x2"
        android:layout_marginBottom="@dimen/application___dimen__x4"
        android:visibility="gone"
        app:dotsColor="@color/login__onboarding___dots__default"
        app:dotsCornerRadius="20dp"
        app:dotsSize="@dimen/application___dimen__x2"
        app:dotsSpacing="@dimen/application___dimen__x2"
        app:dotsWidthFactor="3"
        app:selectedDotColor="@color/application___color__primary"
        tools:visibility="visible"/>

</LinearLayout>