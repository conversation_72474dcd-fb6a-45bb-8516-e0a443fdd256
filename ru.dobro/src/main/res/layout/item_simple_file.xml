<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/application___dimen__x2"
    tools:ignore="RtlSymmetry">

    <ImageView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_file"
        app:layout_constraintBottom_toBottomOf="@+id/fileName"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/application___color__primary"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/fileName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/application___dimen__x4"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"
        app:layout_constraintBottom_toTopOf="@id/fileSize"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/icon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Устав спорт.pdf"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/fileSize"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textAppearance="@style/Application.Design.Caption.TextAppearance.Gray"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/fileName"
        app:layout_constraintTop_toBottomOf="@id/fileName"
        tools:text="2 Мб"/>

</androidx.constraintlayout.widget.ConstraintLayout>