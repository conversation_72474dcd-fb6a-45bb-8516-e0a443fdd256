<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/application___dimen__x5"
    android:layout_marginTop="@dimen/application___dimen__x5">

    <ImageView
        android:id="@+id/logo"
        android:layout_width="40dp"
        android:layout_height="40dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/application___dimen__x2"
        android:layout_marginEnd="@dimen/application___dimen__x2"
        android:ellipsize="end"
        android:maxLines="2"
        android:textAppearance="@style/Application.Design.H7.TextAppearance"
        android:textSize="15sp"
        app:layout_constraintEnd_toStartOf="@+id/join_or_leave_container"
        app:layout_constraintStart_toEndOf="@+id/logo"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Ассоциация волонтерск бубубубубуббуб"/>

    <LinearLayout
        android:id="@+id/join_or_leave_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/leave"
            style="@style/Application.Design.Button.Small.Shade"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/application___dimen__x2"
            android:text="@string/main__organization___joined_short"
            android:textSize="13sp"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/join"
            style="@style/Application.Design.Button.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/main__organization___joined_short_variant"
            android:textSize="13sp"
            android:visibility="gone"/>

    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/application___dimen__x1"
        android:orientation="horizontal"
        app:layout_constraintEnd_toStartOf="@+id/join_or_leave_container"
        app:layout_constraintStart_toStartOf="@+id/name"
        app:layout_constraintTop_toBottomOf="@+id/name">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/rating"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/application___dimen__x1"
            android:drawableStart="@drawable/application___star_big"
            android:drawablePadding="@dimen/application___dimen__x1"
            android:gravity="center_vertical"
            android:textAppearance="@style/Application.Design.Body2.TextAppearance.Black"
            android:textSize="13sp"
            tools:text="3,8"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/volunteers_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:drawableStart="@drawable/application___two_persons"
            android:drawablePadding="@dimen/application___dimen__x1"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:textAppearance="@style/Application.Design.Body2.TextAppearance.Black"
            android:textSize="13sp"
            tools:text="24 тыс."/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>