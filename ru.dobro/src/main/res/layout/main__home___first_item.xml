<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="165dp"
    android:layout_height="280dp"
    app:cardCornerRadius="18dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_home_recommended_events_first">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x3"
            android:layout_marginTop="@dimen/application___dimen__x6"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:lines="2"
            android:textSize="22sp"
            android:text="Все Добрые дела"
            android:textAppearance="@style/Application.Design.H5.TextAppearance.White"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/application___dimen__x4"
            android:layout_marginBottom="@dimen/application___dimen__x4"
            android:background="@color/white"
            app:cardCornerRadius="@dimen/application___dimen__x4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/application___dimen__x2"
                android:src="@drawable/application___arrow_right"/>

        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>