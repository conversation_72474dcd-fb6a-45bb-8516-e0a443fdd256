<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:padding="@dimen/application___dimen__x4">

    <View
        android:id="@+id/main__profile___volunteer_book__skeleton__category__item__title"
        android:layout_width="0dp"
        android:layout_height="@dimen/application___dimen__x4"
        android:layout_marginEnd="@dimen/application___dimen__x4"
        android:background="@color/application___color__icons"
        app:layout_constraintBottom_toTopOf="@+id/main__profile___volunteer_book__skeleton__category__item__subtitle"
        app:layout_constraintEnd_toStartOf="@+id/main__profile___volunteer_book__skeleton__category__item__arrow"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:id="@+id/main__profile___volunteer_book__skeleton__category__item__subtitle"
        android:layout_width="match_parent"
        android:layout_height="@dimen/application___dimen__x3"
        android:layout_marginTop="@dimen/application___dimen__x3"
        android:background="@color/application___color__icons"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main__profile___volunteer_book__skeleton__category__item__title"/>

    <ImageView
        android:id="@+id/main__profile___volunteer_book__skeleton__category__item__arrow"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="@dimen/application___dimen__x2"
        android:src="@drawable/application___arrow_down"
        app:layout_constraintBottom_toBottomOf="@+id/main__profile___volunteer_book__skeleton__category__item__title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/main__profile___volunteer_book__skeleton__category__item__title"
        app:layout_constraintTop_toTopOf="@+id/main__profile___volunteer_book__skeleton__category__item__title"
        app:tint="@color/application___color__icons"
        tools:ignore="ContentDescription"/>
</androidx.constraintlayout.widget.ConstraintLayout>