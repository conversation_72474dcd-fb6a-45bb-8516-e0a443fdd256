<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".main.events.events_search.EventsSearchMapFragment">

    <LinearLayout
        android:id="@+id/searchContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        android:background="@color/white"
        android:elevation="1dp"
        android:orientation="vertical"
        android:paddingTop="@dimen/application___dimen__x4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:translationZ="10dp">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/queryContainer"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x4"
                android:layout_marginEnd="@dimen/application___dimen__x4"
                android:hint="@string/main__search___query_hint"
                android:textColor="@color/application___color__gray__60"
                app:boxBackgroundColor="@color/application___color__gray__12"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:endIconDrawable="@drawable/application___close"
                app:endIconMode="clear_text"
                app:endIconTint="@color/application___color__gray__60"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/calendar"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:startIconDrawable="@drawable/application___search"
                app:startIconTint="@color/application___color__gray__60">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/query"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:imeOptions="actionDone"
                    android:inputType="text"/>
            </com.google.android.material.textfield.TextInputLayout>

            <androidx.cardview.widget.CardView
                android:id="@+id/calendar"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginEnd="@dimen/application___dimen__x4"
                app:cardBackgroundColor="@color/application___color__gray__12"
                app:cardCornerRadius="10dp"
                app:cardElevation="0dp"
                app:layout_constraintBottom_toBottomOf="@+id/queryContainer"
                app:layout_constraintDimensionRatio="H,1:1"
                app:layout_constraintEnd_toStartOf="@+id/filter"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintTop_toTopOf="@+id/queryContainer">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/application___dimen__x3"
                    android:scaleType="centerCrop"
                    android:src="@drawable/application___calendar_variant_2"/>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/filter"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginEnd="@dimen/application___dimen__x4"
                app:cardBackgroundColor="@color/application___color__gray__12"
                app:cardCornerRadius="10dp"
                app:cardElevation="0dp"
                app:layout_constraintBottom_toBottomOf="@+id/queryContainer"
                app:layout_constraintDimensionRatio="H,1:1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintTop_toTopOf="@+id/queryContainer">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/application___dimen__x3"
                    android:scaleType="centerCrop"
                    android:src="@drawable/application___filter_variant"
                    app:tint="#3C3C43"/>

            </androidx.cardview.widget.CardView>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/calendarContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="1dp"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/searchContainer"
        tools:visibility="gone">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/calendarTitle"
            style="@style/Application.Design.Body1.TextAppearance.Gray"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/application___dimen__x4"
            android:paddingVertical="@dimen/application___dimen__x2"
            android:text="Когда проводится? "/>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/calendarFromInput"
            style="@style/Application.Design.InputFilled.Shade"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Начало"
            android:paddingHorizontal="@dimen/application___dimen__x4"
            android:paddingBottom="@dimen/application___dimen__x4"
            android:textColor="@color/application___color__gray__60"
            app:boxBackgroundColor="@color/application___color__gray__12"
            app:boxCornerRadiusBottomEnd="10dp"
            app:boxCornerRadiusBottomStart="10dp"
            app:boxCornerRadiusTopEnd="10dp"
            app:boxCornerRadiusTopStart="10dp"
            app:startIconDrawable="@drawable/application___calendar_variant_2"
            app:startIconTint="@color/application___color__gray__60">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/calendarFrom"
                style="@style/Application.EditText.Clickable"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/calendarToInput"
            style="@style/Application.Design.InputFilled.Shade"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Окончание"
            android:paddingHorizontal="@dimen/application___dimen__x4"
            android:textColor="@color/application___color__gray__60"
            app:boxBackgroundColor="@color/application___color__gray__12"
            app:boxCornerRadiusBottomEnd="10dp"
            app:boxCornerRadiusBottomStart="10dp"
            app:boxCornerRadiusTopEnd="10dp"
            app:boxCornerRadiusTopStart="10dp"
            app:endIconDrawable="@drawable/application___close"
            app:endIconMode="custom"
            app:endIconTint="@color/application___color__gray__60"
            app:startIconDrawable="@drawable/application___calendar_variant_2"
            app:startIconTint="@color/application___color__gray__60">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/calendarTo"
                style="@style/Application.EditText.Clickable"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </com.google.android.material.textfield.TextInputLayout>

    </LinearLayout>

    <ProgressBar
        android:id="@+id/contentProgress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/application___dimen__x1"
        android:background="@drawable/application___background__circle_variant"
        android:elevation="1dp"
        android:indeterminateTint="@color/application___color__primary"
        android:scaleX="0.5"
        android:scaleY="0.5"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/calendarContainer"
        tools:visibility="visible"/>

    <FrameLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/searchContainer">

        <com.yandex.mapkit.mapview.MapView
            android:id="@+id/map"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

    </FrameLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/application___dimen__x5"
        android:layout_marginBottom="@dimen/application___dimen__x4"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@+id/bottomSheet"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/searchContainer">

        <ImageView
            android:id="@+id/plus"
            android:layout_width="@dimen/application___dimen__x11"
            android:layout_height="@dimen/application___dimen__x11"
            android:background="@drawable/application___background__circle"
            android:backgroundTint="#E6FFFFFF"
            android:padding="@dimen/application___dimen__x1"
            android:src="@drawable/ic_events_map_plus"
            android:translationZ="1dp"
            tools:ignore="ContentDescription"/>

        <ImageView
            android:id="@+id/minus"
            android:layout_width="@dimen/application___dimen__x11"
            android:layout_height="@dimen/application___dimen__x11"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:background="@drawable/application___background__circle"
            android:backgroundTint="#E6FFFFFF"
            android:padding="@dimen/application___dimen__x1"
            android:src="@drawable/ic_events_map_minus"
            android:translationZ="1dp"
            tools:ignore="ContentDescription"/>

        <LinearLayout
            android:layout_width="@dimen/application___dimen__x11"
            android:layout_height="@dimen/application___dimen__x11"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:layout_marginBottom="@dimen/application___dimen__x1"
            android:background="@drawable/application___background__circle"
            android:backgroundTint="#E6FFFFFF"
            android:orientation="vertical"
            android:padding="10dp"
            android:translationZ="1dp">

            <ImageView
                android:id="@+id/location"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_events_map_location"
                android:visibility="gone"
                tools:ignore="ContentDescription"
                tools:visibility="visible"/>

            <ProgressBar
                android:id="@+id/locationProgress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:indeterminateTint="@color/application___color__primary"
                android:scaleX="0.8"
                android:scaleY="0.8"
                tools:visibility="gone"/>

        </LinearLayout>

    </LinearLayout>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/openList"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/application___dimen__x5"
        android:layout_marginBottom="@dimen/application___dimen__x2"
        android:background="@color/white"
        android:elevation="1dp"
        app:cardCornerRadius="@dimen/application___dimen__x5"
        app:layout_constraintBottom_toTopOf="@+id/bottomSheet"
        app:layout_constraintStart_toStartOf="parent"
        app:strokeColor="@color/application___color__primary"
        app:strokeWidth="1dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x3"
            android:layout_marginVertical="10dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/application___dimen__x6"
                android:layout_height="@dimen/application___dimen__x6"
                android:layout_gravity="center_vertical"
                android:src="@drawable/ic_events_list_search"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                style="@style/Application.Design.Body2.TextAppearance.Primary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/application___dimen__x2"
                android:text="@string/main__list"
                android:textSize="17sp"/>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <FrameLayout
        android:id="@+id/bottomSheet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_cardview_corners_variant"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <include
            android:id="@+id/bottomSheetShimmer"
            layout="@layout/main__events_search_map__skeleton__item"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:visibility="gone"/>

        <include
            android:id="@+id/bottomSheetContent"
            layout="@layout/item_event_search_map"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x3"
            android:layout_marginBottom="@dimen/application___dimen__x2"
            tools:visibility="visible"
            android:visibility="gone"/>

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>