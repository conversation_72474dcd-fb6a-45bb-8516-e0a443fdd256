<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginBottom="-4dp">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/application___dimen__x5"
        android:text="@string/profile_organizations"
        android:textAppearance="@style/Application.Design.H4.TextAppearance.Black"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x2"
        android:textAppearance="@style/Application.Design.H4.TextAppearance.Gray"
        app:layout_constraintBottom_toBottomOf="@+id/title"
        app:layout_constraintStart_toEndOf="@+id/title"
        app:layout_constraintTop_toTopOf="@+id/title"
        tools:text="14"
        tools:visibility="visible"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/comment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x5"
        android:layout_marginTop="@dimen/application___dimen__x1"
        android:text="@string/main__organization___comment"
        android:textAppearance="@style/Application.Design.Body2.TextAppearance.Black"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"/>

</androidx.constraintlayout.widget.ConstraintLayout>