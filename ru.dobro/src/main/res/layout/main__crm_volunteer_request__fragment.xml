<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/main__crm_volunteer_request__fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        tools:visibility="visible">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/main__crm_volunteer_request__fragment_left"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_begin="@dimen/application___dimen__x4"/>

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/main__crm_volunteer_request__fragment_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_end="@dimen/application___dimen__x4"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__crm_volunteer_request__fragment_event_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/application___dimen__x4"
                android:background="?selectableItemBackground"
                android:drawablePadding="@dimen/application___dimen__x7"
                android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Спортивные старты для всех"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__crm_volunteer_request__fragment_event_period"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:layout_margin="@dimen/application___dimen__x4"
                android:layout_marginTop="6dp"
                android:drawableStart="@drawable/application___calendar"
                android:drawablePadding="12dp"
                android:gravity="center_vertical"
                android:text="@string/main__requests__vacancy__item__period_title"
                android:textAppearance="@style/Application.Design.Caption.TextAppearance.Gray"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/main__crm_volunteer_request__fragment_event_name"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__crm_volunteer_request__fragment_event_period_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/application___dimen__x4"
                android:layout_marginStart="4dp"
                android:drawablePadding="12dp"
                android:gravity="center_vertical"
                android:text="16 мая 2022, 13:00 — 18:00"
                android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"
                app:layout_constraintBottom_toBottomOf="@id/main__crm_volunteer_request__fragment_event_period"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/main__crm_volunteer_request__fragment_event_period"
                app:layout_constraintTop_toTopOf="@id/main__crm_volunteer_request__fragment_event_period"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__crm_volunteer_request__fragment_event_requests"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:layout_margin="@dimen/application___dimen__x4"
                android:layout_marginTop="6dp"
                android:drawableStart="@drawable/application___two_persons"
                android:drawablePadding="12dp"
                android:gravity="center_vertical"
                android:text="Заявка"
                android:textAppearance="@style/Application.Design.Caption.TextAppearance.Gray"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/main__crm_volunteer_request__fragment_event_period"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__crm_volunteer_request__fragment_event_requests_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/application___dimen__x4"
                android:layout_marginStart="4dp"
                android:drawablePadding="12dp"
                android:gravity="center_vertical"
                android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"
                app:layout_constraintBottom_toBottomOf="@id/main__crm_volunteer_request__fragment_event_requests"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/main__crm_volunteer_request__fragment_event_requests"
                app:layout_constraintTop_toTopOf="@id/main__crm_volunteer_request__fragment_event_requests"
                tools:text="Администратор офиса"/>

            <LinearLayout
                android:id="@+id/main__crm_volunteer_request__fragment_volunteer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:background="@drawable/application___background__white"
                android:foreground="@drawable/application___ripple"
                android:orientation="vertical"
                android:padding="@dimen/application___dimen__x4"
                app:layout_constraintBottom_toTopOf="@+id/main__crm_volunteer_request__fragment_trophy"
                app:layout_constraintEnd_toEndOf="@id/main__crm_volunteer_request__fragment_right"
                app:layout_constraintStart_toStartOf="@id/main__crm_volunteer_request__fragment_left"
                app:layout_constraintTop_toBottomOf="@+id/main__crm_volunteer_request__fragment_event_requests">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/application___background__round_corners"
                    android:padding="-16dp"
                    android:paddingBottom="@dimen/application___dimen__x4">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/main__crm__accept__item_avatar"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginTop="@dimen/application___dimen__x2"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/application___account"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/main__crm__accept__item_name"
                        style="@style/Application.Design.Body2.TextAppearance.Primary"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/application___dimen__x2"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toTopOf="@id/main__crm__accept__item_id"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/main__crm__accept__item_avatar"
                        app:layout_constraintTop_toTopOf="@id/main__crm__accept__item_avatar"
                        tools:text="Константинов Александр"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/main__crm__accept__item_id"
                        style="@style/Application.Design.Caption.TextAppearance.Gray"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toBottomOf="@id/main__crm__accept__item_avatar"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="@+id/main__crm__accept__item_name"
                        app:layout_constraintTop_toBottomOf="@id/main__crm__accept__item_name"
                        tools:text="ID 2383442"/>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/main__crm__accept__item_reject"
                        style="@style/Application.Design.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/application___dimen__x3"
                        android:layout_marginEnd="@dimen/application___dimen__x2"
                        android:text="Отклонить"
                        android:textAppearance="@style/Application.Design.Body2.TextAppearance.Black"
                        android:textColor="@color/black"
                        app:icon="@drawable/application___cancel_outline"
                        app:iconTint="@color/application___color__accent_variant"
                        app:layout_constraintEnd_toStartOf="@id/main__crm__accept__item_accept"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/main__crm__accept__item_avatar"
                        app:strokeColor="@color/application___color__accent_variant"/>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/main__crm__accept__item_accept"
                        style="@style/Application.Design.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/application___dimen__x2"
                        android:text="Одобрить"
                        android:textAppearance="@style/Application.Design.Body2.TextAppearance.Black"
                        android:textColor="@color/black"
                        app:icon="@drawable/application___check__circle_outline"
                        app:iconTint="@color/application___color__secondary_variant"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/main__crm__accept__item_reject"
                        app:layout_constraintTop_toTopOf="@+id/main__crm__accept__item_reject"
                        app:strokeColor="@color/application___color__secondary_variant"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__crm_volunteer_request__fragment_info__title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x4"
                    android:text="Основная информация"
                    android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_gravity="center_horizontal"
                        android:padding="@dimen/application___dimen__x1"
                        android:src="@drawable/ic_location_v4"
                        app:tint="@color/application___color__icons"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/main__crm_volunteer_request__fragment_contacts__location"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/application___dimen__x2"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        tools:text="Населенный пункт: Москва"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_gravity="center_horizontal"
                        android:padding="@dimen/application___dimen__x2"
                        android:src="@drawable/application___email"
                        app:tint="@color/application___color__icons"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/main__crm_volunteer_request__fragment_contacts__email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/application___dimen__x2"
                        android:foreground="?attr/selectableItemBackgroundBorderless"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        tools:text="Email: <EMAIL>"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_gravity="center_horizontal"
                        android:padding="@dimen/application___dimen__x2"
                        android:src="@drawable/application___calendar"
                        app:tint="@color/application___color__icons"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/main__crm_volunteer_request__fragment_contacts__events"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/application___dimen__x2"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        tools:text="Мероприятий: 7"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_gravity="center_horizontal"
                        android:padding="@dimen/application___dimen__x2"
                        android:src="@drawable/application___timetable"
                        app:tint="@color/application___color__icons"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/main__crm_volunteer_request__fragment_contacts__hours"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/application___dimen__x2"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        tools:text="Отработанных часов: 17"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_gravity="center_horizontal"
                        android:padding="@dimen/application___dimen__x2"
                        android:src="@drawable/application___check__circle_outline"
                        app:tint="@color/application___color__icons"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/main__crm_volunteer_request__fragment_contacts__verified_hours"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/application___dimen__x2"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        tools:text="Верифицированных часов: 17"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_gravity="center_horizontal"
                        android:padding="@dimen/application___dimen__x2"
                        android:src="@drawable/application___trophy"
                        app:tint="@color/application___color__icons"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/main__crm_volunteer_request__fragment_contacts__trophy"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/application___dimen__x2"
                        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                        tools:text="Награды: 7"/>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/main__crm_volunteer_request__fragment_contacts__socials__container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="36dp"
                            android:layout_height="36dp"
                            android:layout_gravity="center_horizontal"
                            android:padding="@dimen/application___dimen__x2"
                            android:src="@drawable/application___web"
                            app:tint="@color/application___color__icons"/>

                        <com.google.android.material.textview.MaterialTextView
                            android:id="@+id/main__crm_volunteer_request__fragment_contacts__socials__title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="@dimen/application___dimen__x2"
                            android:text="@string/main__profile___info__socials"
                            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"/>
                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/main__crm_volunteer_request__fragment_contacts__socials"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/application___dimen__x4"
                        android:orientation="vertical"
                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                        app:spanCount="3"/>

                </LinearLayout>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__crm_volunteer_request__fragment_contacts__fields_tilte"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x4"
                    android:text="Требования:"
                    android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"
                    android:visibility="gone"
                    tools:visibility="visible"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/main__crm_volunteer_request__fragment_contacts__fields"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/application___dimen__x2"
                    android:nestedScrollingEnabled="false"
                    android:orientation="vertical"
                    android:scrollbars="none"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="2"
                    tools:listitem="@layout/item_request_field"/>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/main__crm_volunteer_request__fragment_volunteer_book"
                    style="@style/Application.Design.Button.Primary"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="@dimen/application___dimen__x2"
                    android:text="@string/main__profile___volunteer_book"/>

            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/main__crm_volunteer_request__fragment_trophy"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:background="@drawable/application___background__white"
                android:foreground="@drawable/application___ripple"
                android:orientation="horizontal"
                android:padding="@dimen/application___dimen__x4"
                app:layout_constraintBottom_toTopOf="@+id/main__crm_volunteer_request__fragment__review__title"
                app:layout_constraintEnd_toEndOf="@id/main__crm_volunteer_request__fragment_right"
                app:layout_constraintStart_toStartOf="@id/main__crm_volunteer_request__fragment_left"
                app:layout_constraintTop_toBottomOf="@+id/main__crm_volunteer_request__fragment_volunteer">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__crm_volunteer_request__fragment_trophies_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/main__profile___trophies__title"
                    android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                    app:layout_constraintBottom_toTopOf="@+id/main__crm_volunteer_request__fragment_trophies_list"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__crm_volunteer_request__fragment_trophies_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/application___dimen__x2"
                    android:textAppearance="@style/Application.Design.H6.TextAppearance.Gray"
                    app:layout_constraintBottom_toTopOf="@+id/main__crm_volunteer_request__fragment_trophies_list"
                    app:layout_constraintStart_toEndOf="@id/main__crm_volunteer_request__fragment_trophies_title"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="5"/>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/main__crm_volunteer_request__fragment_trophies_show_all"
                    style="@style/Application.Design.Button.Text.Primary"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:gravity="end|center_vertical"
                    android:insetTop="0dp"
                    android:insetBottom="0dp"
                    android:text="@string/application___show_all"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/main__crm_volunteer_request__fragment_trophies_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:nestedScrollingEnabled="false"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/main__crm_volunteer_request__fragment_trophies_show_all"
                    tools:listitem="@layout/item_trophy"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__crm_volunteer_request__fragment__review__title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:text="@string/main__profile___comments"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                app:layout_constraintBottom_toTopOf="@id/main__crm_volunteer_request__fragment__review__list"
                app:layout_constraintStart_toStartOf="@id/main__crm_volunteer_request__fragment_left"
                app:layout_constraintTop_toBottomOf="@id/main__crm_volunteer_request__fragment_trophy"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__crm_volunteer_request__fragment__review__count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x2"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Gray"
                app:layout_constraintBottom_toBottomOf="@+id/main__crm_volunteer_request__fragment__review__title"
                app:layout_constraintStart_toEndOf="@id/main__crm_volunteer_request__fragment__review__title"
                app:layout_constraintTop_toTopOf="@id/main__crm_volunteer_request__fragment__review__title"
                tools:text="5"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/main__crm_volunteer_request__fragment__review__list"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:nestedScrollingEnabled="false"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="@id/main__crm_volunteer_request__fragment_right"
                app:layout_constraintStart_toStartOf="@id/main__crm_volunteer_request__fragment_left"
                app:layout_constraintTop_toBottomOf="@id/main__crm_volunteer_request__fragment__review__title"
                tools:itemCount="2"
                tools:listitem="@layout/main__crm___review__item"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/main__crm_volunteer_request__fragment_review__list_more"
                style="@style/Application.Design.Button.Primary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginVertical="@dimen/application___dimen__x2"
                android:text="Показать еще"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/main__crm_volunteer_request__fragment__review__list"
                tools:visibility="visible"/>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</FrameLayout>