<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/shimmer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/application___dimen__x4">

        <include
            layout="@layout/item_friend_shimmer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x2"/>

        <include
            layout="@layout/item_friend_shimmer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x2"/>

        <include
            layout="@layout/item_friend_shimmer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x2"/>

        <include
            layout="@layout/item_friend_shimmer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x2"/>

        <include
            layout="@layout/item_friend_shimmer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x2"/>

        <include
            layout="@layout/item_friend_shimmer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/application___dimen__x2"/>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/application___dimen__x4"
        android:paddingTop="@dimen/application___dimen__x4"
        android:paddingBottom="@dimen/application___dimen__x4"
        android:clipToPadding="false"
        android:clipChildren="false"
        android:visibility="gone"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:itemCount="3"
        tools:listitem="@layout/item_friend"
        tools:visibility="visible"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/noFriends"
        style="@style/Application.Design.H6.TextAppearance.Gray"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:text="@string/friends_empty"
        android:textAlignment="center"
        android:visibility="gone"
        tools:visibility="visible"/>

</FrameLayout>