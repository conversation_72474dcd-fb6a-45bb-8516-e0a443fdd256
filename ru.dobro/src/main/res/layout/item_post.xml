<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/application___background__round_corners_no_padding"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x4">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/volunteerContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/friendAction"
            android:layout_marginEnd="@dimen/application___dimen__x1">

            <ImageView
                android:id="@+id/avatar"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:src="@drawable/application___person"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x4"
                android:ellipsize="end"
                android:lines="1"
                android:maxLines="1"
                android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                android:textSize="15sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@+id/avatar"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Тихонов Иван"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/date"
                style="@style/Application.Design.Body1.TextAppearance.DarkGray"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x1"
                android:textSize="15sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@id/avatar"
                app:layout_constraintTop_toBottomOf="@+id/name"
                tools:text="22.06.2004"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/friendAction"
            android:layout_width="@dimen/application___dimen__x8"
            android:layout_height="@dimen/application___dimen__x8"
            android:layout_marginEnd="@dimen/application___dimen__x1"
            android:src="@drawable/ic_add_friend_by_post"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x3"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
        android:textSize="15sp"
        tools:text="Сегодня на кассе в магазине я увидел, что бабушка не может купить себе с пенсии стандартный набор продуктов. Я решил ей помочь и купил разного на неделю"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x4"
        android:background="@drawable/main__search___calendar_enabler__background"
        android:backgroundTint="#F3F5F8"
        android:drawablePadding="2dp"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:paddingVertical="@dimen/application___dimen__x2"
        android:paddingEnd="10dp"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
        android:textSize="12sp"
        tools:drawableStart="@drawable/ic_edit"
        tools:text="Адресная помощь"/>

    <ImageView
        android:id="@+id/image"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/application___dimen__x4"
        android:adjustViewBounds="true"
        android:maxHeight="400dp"
        android:scaleType="fitCenter"
        tools:ignore="ContentDescription"/>

    <LinearLayout
        android:id="@+id/like"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/application___dimen__x2"
        android:layout_marginTop="@dimen/application___dimen__x2"
        android:layout_marginBottom="@dimen/application___dimen__x2"
        android:background="@drawable/bg_like_post"
        android:backgroundTint="#F2F2F7"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/application___dimen__x3"
        android:paddingVertical="@dimen/application___dimen__x2">

        <ImageView
            android:id="@+id/likeHeart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="2dp"
            android:src="@drawable/ic_like_filled_variant"
            tools:ignore="ContentDescription"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/likeCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:maxLines="1"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
            android:textSize="13sp"
            tools:text="32"/>

    </LinearLayout>

</LinearLayout>
