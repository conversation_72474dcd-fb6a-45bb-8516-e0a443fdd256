<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="@dimen/application___dimen__x4"
    tools:context=".main.posts.createPost.selectPostType.SelectPostTypeFragment">

    <ImageView
        android:id="@+id/close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/application___dimen__x2"
        android:foreground="@drawable/application___ripple__circle"
        android:padding="@dimen/application___dimen__x2"
        android:src="@drawable/application___close"
        app:layout_constraintBottom_toTopOf="@+id/scroll"
        app:layout_constraintEnd_toStartOf="@+id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintVertical_chainStyle="packed"
        app:tint="@color/application___color__primary"
        tools:ignore="ContentDescription"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/application___dimen__x3"
        android:text="@string/create_post_select_category"
        android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
        app:layout_constraintBottom_toBottomOf="@+id/close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/close"
        app:layout_constraintTop_toTopOf="@+id/close"/>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:layout_marginTop="@dimen/application___dimen__x4"
        app:layout_constraintBottom_toTopOf="@+id/save"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintVertical_bias="0">

        <androidx.constraintlayout.motion.widget.MotionLayout
            android:id="@+id/motionLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            app:layoutDescription="@xml/select_post_type_scene">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/typesList"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

            <LinearLayout
                android:id="@+id/recycleContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="parent">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/recycleContainerRecycle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/warning"
                    app:strokeWidth="1dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:id="@+id/selectCategoryImage"
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:layout_marginVertical="@dimen/application___dimen__x3"
                            android:layout_marginStart="@dimen/application___dimen__x3"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:ignore="ContentDescription"
                            tools:src="@drawable/ic_all_categories_variant"/>

                        <com.google.android.material.textview.MaterialTextView
                            android:id="@+id/selectCategoryText"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:text="Переработка"
                            android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                            android:textSize="15sp"
                            app:layout_constraintBottom_toBottomOf="@+id/selectCategoryImage"
                            app:layout_constraintStart_toEndOf="@+id/selectCategoryImage"
                            app:layout_constraintTop_toTopOf="@+id/selectCategoryImage"/>

                        <ImageView
                            android:id="@+id/selectCategoryClose"
                            android:layout_width="@dimen/application___dimen__x5"
                            android:layout_height="@dimen/application___dimen__x5"
                            android:layout_marginVertical="@dimen/application___dimen__x3"
                            android:layout_marginEnd="@dimen/application___dimen__x4"
                            android:src="@drawable/application___close"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:ignore="ContentDescription"/>
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/recycleContainerItems"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/warning"
                    app:strokeWidth="1dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.google.android.material.textview.MaterialTextView
                            android:id="@+id/recycleContainerItemsTitle"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/application___dimen__x4"
                            android:layout_marginTop="@dimen/application___dimen__x4"
                            android:text="@string/create_post_select_category_variant"
                            android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                            android:textSize="17sp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"/>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recycleItems"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/application___dimen__x4"
                            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/recycleContainerItemsTitle"
                            app:spanCount="6"
                            tools:listitem="@layout/item_select_post_recycle_type"/>
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/recycleUnitContainerItems"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:visibility="gone"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/warning"
                    app:strokeWidth="1dp"
                    tools:visibility="visible">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <com.google.android.material.textview.MaterialTextView
                            android:id="@+id/recycleUnitContainerItemsTitle"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/application___dimen__x4"
                            android:layout_marginTop="@dimen/application___dimen__x4"
                            android:text="Заполните все поля"
                            android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                            android:textSize="17sp"/>

                        <com.google.android.material.textview.MaterialTextView
                            android:id="@+id/recycleUnitContainerItemsDescription"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/application___dimen__x4"
                            android:layout_marginTop="@dimen/application___dimen__x1"
                            android:text="@string/create_post_select_category_variant_description"
                            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                            android:textSize="13sp"/>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recycleUnitItems"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/application___dimen__x4"
                            android:layout_marginTop="@dimen/application___dimen__x3"
                            android:layout_marginBottom="@dimen/application___dimen__x4"
                            android:nestedScrollingEnabled="false"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <View
                    android:id="@+id/recycleUnitItemsSeparator"
                    android:layout_width="match_parent"
                    android:layout_height="108dp"
                    android:visibility="gone"/>
            </LinearLayout>
        </androidx.constraintlayout.motion.widget.MotionLayout>
    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/save"
        style="@style/Application.Design.Button.Primary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/application___dimen__x4"
        android:text="@string/application___save"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible"/>

</androidx.constraintlayout.widget.ConstraintLayout>