<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/application___dimen__x4"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/icon"
        android:layout_width="42dp"
        android:layout_height="42dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription"
        tools:src="@drawable/ic_all_categories_variant"/>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:gravity="center"
        android:maxLines="1"
        android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
        android:textSize="9sp"
        app:layout_constraintEnd_toEndOf="@+id/icon"
        app:layout_constraintStart_toStartOf="@+id/icon"
        app:layout_constraintTop_toBottomOf="@+id/icon"
        tools:text="Бумага"/>

</androidx.constraintlayout.widget.ConstraintLayout>