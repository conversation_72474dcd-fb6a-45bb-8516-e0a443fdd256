<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ProgressBar
        android:id="@+id/progress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginVertical="@dimen/application___dimen__x8"
        android:indeterminateTint="@color/application___color__primary"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:paddingBottom="@dimen/application___dimen__x5"
        android:layout_height="wrap_content">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/icon"
            android:layout_width="60dp"
            app:shapeAppearanceOverlay="@style/roundedImageView"
            android:layout_height="60dp"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:src="@drawable/main__profile___volunteer_book__empty"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x1"
            android:text="Название мероприятия, может быть длинным"
            android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@id/headerBarier"
            app:layout_constraintEnd_toStartOf="@+id/cancel"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/icon"
            app:layout_constraintTop_toTopOf="parent"/>

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/headerBarier"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="icon,title"
            app:layout_constraintTop_toBottomOf="@id/title"/>

        <ImageView
            android:id="@+id/periodIcon"
            android:layout_width="@dimen/application___dimen__x3"
            android:layout_height="@dimen/application___dimen__x3"
            android:layout_marginStart="@dimen/application___dimen__x5"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:src="@drawable/application___calendar"
            app:layout_constraintEnd_toStartOf="@+id/period"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/headerBarier"
            tools:ignore="ContentDescription"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/period"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:layout_marginEnd="@dimen/application___dimen__x4"
            android:gravity="center_vertical"
            android:text="12 апреля 2021, 19:40"
            android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@+id/periodIcon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/periodIcon"
            app:layout_constraintTop_toTopOf="@+id/periodIcon"/>

        <ImageView
            android:id="@+id/phoneIcon"
            android:layout_width="@dimen/application___dimen__x3"
            android:layout_height="@dimen/application___dimen__x3"
            android:layout_marginStart="@dimen/application___dimen__x5"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:src="@drawable/application___phone"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@+id/periodIcon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/periodIcon"
            tools:ignore="ContentDescription"
            tools:visibility="visible"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/phone"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:layout_marginEnd="@dimen/application___dimen__x4"
            android:gravity="center_vertical"
            android:text="****** 843 83 83"
            android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@+id/phoneIcon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/periodIcon"
            app:layout_constraintTop_toTopOf="@+id/phoneIcon"
            tools:visibility="visible"/>

        <ImageView
            android:id="@+id/addressIcon"
            android:layout_width="@dimen/application___dimen__x3"
            android:layout_height="@dimen/application___dimen__x3"
            android:layout_marginStart="@dimen/application___dimen__x5"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:src="@drawable/application___location"
            app:layout_constraintEnd_toStartOf="@+id/address"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/phoneIcon"
            tools:ignore="ContentDescription"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/address"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/application___dimen__x2"
            android:layout_marginEnd="@dimen/application___dimen__x4"
            android:gravity="center_vertical"
            android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@+id/addressIcon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/addressIcon"
            app:layout_constraintTop_toTopOf="@id/addressIcon"
            tools:text="Вольный Аул, г.Нальчик"/>

        <ImageView
            android:id="@+id/cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/application___dimen__x2"
            android:padding="@dimen/application___dimen__x2"
            android:src="@drawable/application___cancel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/icon"
            app:tint="@color/application___color__primary"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/info"
            style="@style/Application.Design.Button.Primary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x8"
            android:layout_marginBottom="@dimen/application___dimen__x8"
            android:text="Подробнее"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/address"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>