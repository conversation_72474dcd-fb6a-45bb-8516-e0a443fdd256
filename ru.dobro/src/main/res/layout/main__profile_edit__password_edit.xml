<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/application___dimen__x3">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___password_edit__old_password__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:hint="@string/main__profile_edit___password_edit__old_password"
                app:passwordToggleEnabled="true">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___password_edit__old_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:imeOptions="actionNext"
                    android:inputType="textPassword"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile_edit___password_edit__forgot"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:foreground="?selectableItemBackground"
                android:text="@string/main__profile_edit___password_edit__forgot"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.Primary"/>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___password_edit__new_password__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x6"
                android:hint="@string/main__profile_edit___password_edit__new_password"
                app:passwordToggleEnabled="true">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___password_edit__new_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:imeOptions="actionNext"
                    android:inputType="textPassword"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/main__profile_edit___password_edit__repeat_password__container"
                style="@style/Application.Design.InputFilled.Shade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x3"
                android:hint="@string/main__profile_edit___password_edit__repeat_password"
                app:passwordToggleEnabled="true">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/main__profile_edit___password_edit__repeat_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:imeOptions="actionNext"
                    android:inputType="textPassword"/>
            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/main__profile_edit___password_edit__progress"
        style="@style/Application.Progress"
        android:layout_gravity="center"/>
</FrameLayout>