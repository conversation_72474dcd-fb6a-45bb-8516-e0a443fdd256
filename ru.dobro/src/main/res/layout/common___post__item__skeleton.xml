<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:foreground="?selectableItemBackgroundBorderless"
    app:cardCornerRadius="18dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x4">

            <View
                android:id="@+id/avatar"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:background="@drawable/application___background__round_corners"
                android:backgroundTint="@color/application___color__icons"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

            <View
                android:id="@+id/name"
                android:layout_width="100dp"
                android:layout_height="20dp"
                android:layout_marginStart="@dimen/application___dimen__x4"
                android:background="@drawable/application___background__round_corners_extra_small_stroke"
                android:backgroundTint="@color/application___color__icons"
                app:layout_constraintStart_toEndOf="@+id/avatar"
                app:layout_constraintTop_toTopOf="@+id/avatar"/>

            <View
                android:id="@+id/date"
                android:layout_width="100dp"
                android:layout_height="20dp"
                android:layout_marginStart="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x1"
                android:background="@drawable/application___background__round_corners_extra_small_stroke"
                android:backgroundTint="@color/application___color__icons"
                app:layout_constraintStart_toEndOf="@+id/avatar"
                app:layout_constraintTop_toBottomOf="@+id/name"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/description"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x3"
            android:background="@drawable/application___background__round_corners_extra_small_stroke"
            android:backgroundTint="@color/application___color__icons"/>

        <View
            android:id="@+id/title"
            android:layout_width="130dp"
            android:layout_height="30dp"
            android:layout_marginStart="@dimen/application___dimen__x3"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:background="@drawable/application___background__round_corners_extra_small_stroke"
            android:backgroundTint="@color/application___color__icons"/>

        <View
            android:id="@+id/image"
            android:layout_width="match_parent"
            android:layout_height="300dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:background="@color/application___color__icons"/>

        <View
            android:id="@+id/like"
            android:layout_width="65dp"
            android:layout_height="35dp"
            android:layout_marginStart="@dimen/application___dimen__x3"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:layout_marginBottom="@dimen/application___dimen__x2"
            android:background="@drawable/application___background__round_corners_extra_small_stroke"
            android:backgroundTint="@color/application___color__icons"/>

    </LinearLayout>

</androidx.cardview.widget.CardView>