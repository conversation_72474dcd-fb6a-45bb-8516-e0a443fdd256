<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/scroll"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/application___color__orange"
    android:scrollbars="none">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_dobro_centers"
            android:orientation="vertical">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/appBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/transparent"
                android:theme="@style/ThemeOverlay.MaterialComponents.Dark.ActionBar"
                app:elevation="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.appbar.MaterialToolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:navigationIcon="?attr/homeAsUpIndicator"
                    app:navigationIconTint="@color/black">

                </com.google.android.material.appbar.MaterialToolbar>
            </com.google.android.material.appbar.AppBarLayout>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="-16dp"
                android:text="Добро.Центры"
                android:textAppearance="@style/Application.Design.H4.TextAppearance.Black"
                tools:ignore="NegativeMargin"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/subtitle"
                style="@style/Application.Design.Body2.TextAppearance.Black"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:text="Центры социальной активности, развития волонтёрства и благотворительности"
                android:textSize="17sp"
                android:textStyle="normal"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="-40dp"
            android:background="@drawable/application___background__round_top_corners"
            android:backgroundTint="#F2F2F7"
            android:minHeight="800dp"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/application___dimen__x4"
            app:cardElevation="0dp"
            tools:ignore="NegativeMargin">

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/myServiceRequests"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:text="Мои заявки на сервисы"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                android:textStyle="normal"/>

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/requestsServicesShimmer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="visible"
                tools:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x4"
                    android:background="@drawable/application___background__round_corners"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/application___dimen__x4"
                    android:paddingVertical="@dimen/application___dimen__x4">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_marginTop="@dimen/application___dimen__x2"
                        android:background="@color/application___color__icons"/>

                </LinearLayout>
            </com.facebook.shimmer.ShimmerFrameLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/requestsServicesList"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/application___dimen__x4"
                android:nestedScrollingEnabled="false"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="2"
                tools:listitem="@layout/item_service_request"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/availableServices"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:text="Доступные сервисы"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                android:textStyle="normal"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/availableServicesList"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/application___dimen__x4"
                android:nestedScrollingEnabled="false"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="4"
                tools:listitem="@layout/item_available_service"/>

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/availableServicesShimmer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="visible"
                tools:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x4"
                    android:orientation="vertical"
                    android:paddingVertical="@dimen/application___dimen__x4">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/application___background__round_corners"
                        android:padding="@dimen/application___dimen__x4">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="46dp"
                            android:background="@color/application___color__icons"/>

                    </FrameLayout>

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/application___dimen__x4"
                        android:background="@drawable/application___background__round_corners"
                        android:padding="@dimen/application___dimen__x4">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="46dp"
                            android:background="@color/application___color__icons"/>

                    </FrameLayout>

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/application___dimen__x4"
                        android:background="@drawable/application___background__round_corners"
                        android:padding="@dimen/application___dimen__x4">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="46dp"
                            android:background="@color/application___color__icons"/>

                    </FrameLayout>

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/application___dimen__x4"
                        android:background="@drawable/application___background__round_corners"
                        android:padding="@dimen/application___dimen__x4">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="46dp"
                            android:background="@color/application___color__icons"/>

                    </FrameLayout>

                </LinearLayout>
            </com.facebook.shimmer.ShimmerFrameLayout>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/serviceNearby"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x5"
                android:text="Центр рядом с вами"
                android:textAppearance="@style/Application.Design.H6.TextAppearance.Black"
                android:textStyle="normal"/>

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/nearbyShimmer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="visible"
                tools:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x4"
                    android:background="@drawable/application___background__round_corners"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/application___dimen__x4"
                    android:paddingVertical="@dimen/application___dimen__x4">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="20dp"
                        android:background="@color/application___color__icons"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="64dp"
                        android:layout_marginTop="@dimen/application___dimen__x2"
                        android:background="@color/application___color__icons"/>

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_marginTop="@dimen/application___dimen__x2"
                        android:background="@color/application___color__icons"/>

                </LinearLayout>
            </com.facebook.shimmer.ShimmerFrameLayout>

            <LinearLayout
                android:id="@+id/nearbyContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:background="@drawable/application___background__round_corners"
                android:orientation="vertical"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:paddingVertical="@dimen/application___dimen__x4"
                android:visibility="gone"
                tools:visibility="visible">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/serviceNearbyTitle"
                    style="@style/Application.Design.H7.TextAppearance.Black"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="15sp"
                    android:textStyle="normal"
                    tools:text="Волонтерский центр РЭУ им. Плеханова"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/serviceNearbyAddress"
                    style="@style/Application.Design.Body1.TextAppearance.Black"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:drawableStart="@drawable/ic_location_v2"
                    android:drawablePadding="@dimen/application___dimen__x2"
                    android:drawableTint="@color/application___color__icons"
                    android:gravity="center_vertical"
                    android:textSize="15sp"
                    android:textStyle="normal"
                    tools:text="г. Москва, Озерковская наб, 22/24 стр 2\nПроложить маршрут"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/serviceNearbyProfile"
                    style="@style/Application.Design.Body1.TextAppearance.Primary"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:drawableStart="@drawable/ic_medical_card"
                    android:drawablePadding="@dimen/application___dimen__x2"
                    android:foreground="?selectableItemBackgroundBorderless"
                    android:gravity="center_vertical"
                    android:text="Посмотреть профиль организатора"
                    android:textSize="15sp"
                    android:textStyle="normal"/>

            </LinearLayout>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/findOtherServices"
                style="@style/Application.Design.Body1.TextAppearance"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:layout_marginBottom="@dimen/application___dimen__x4"
                android:background="@drawable/application___background__round_corners"
                android:drawableEnd="@drawable/application___arrow_right"
                android:drawableTint="@color/application___color__icons"
                android:foreground="?selectableItemBackgroundBorderless"
                android:orientation="vertical"
                android:paddingHorizontal="@dimen/application___dimen__x4"
                android:paddingVertical="@dimen/application___dimen__x4"
                android:text="Найти другой Добро.Центр"/>

        </LinearLayout>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>