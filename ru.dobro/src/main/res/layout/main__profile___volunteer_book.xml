<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/skeleton"
        layout="@layout/main__profile___volunteer_book__skeleton"
        tools:visibility="gone"/>

    <LinearLayout
        android:id="@+id/main__profile___volunteer_book__empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="gone">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:layout_weight="1"
            android:src="@drawable/main__profile___volunteer_book__empty"
            tools:ignore="ContentDescription"/>

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:text="@string/main__profile___volunteer_book__empty__title"
            android:textAppearance="@style/Application.Design.H4.TextAppearance.Black"/>

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x2"
            android:text="@string/main__profile___volunteer_book__empty__subtitle"
            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/main__profile___volunteer_book__vacancies"
            style="@style/Application.Design.Button.Primary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/application___dimen__x4"
            android:layout_marginTop="@dimen/application___dimen__x4"
            android:layout_marginBottom="@dimen/application___dimen__x6"
            android:text="@string/main__profile___volunteer_book__empty__vacancies"/>
    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/main__profile___volunteer_book__content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x6">

                <ImageView
                    android:id="@+id/main__profile___volunteer_book__avatar"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/main__profile___volunteer_book__name"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="ContentDescription"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__profile___volunteer_book__id"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/application___dimen__x2"
                    android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"
                    app:layout_constraintBottom_toTopOf="@+id/main__profile___volunteer_book__name"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/main__profile___volunteer_book__avatar"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed"
                    tools:text="ID 1323123123"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__profile___volunteer_book__name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/application___dimen__x2"
                    android:layout_marginTop="@dimen/application___dimen__x1"
                    android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"
                    android:textSize="17sp"
                    app:layout_constraintBottom_toTopOf="@+id/main__profile___volunteer_book__address"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/main__profile___volunteer_book__avatar"
                    app:layout_constraintTop_toBottomOf="@+id/main__profile___volunteer_book__id"
                    tools:text="Алексеев Алексей"/>

                <ImageView
                    android:id="@+id/main__profile___volunteer_book__rating__icon"
                    android:layout_width="@dimen/application___dimen__x4"
                    android:layout_height="@dimen/application___dimen__x4"
                    android:src="@drawable/application___star"
                    app:layout_constraintBottom_toBottomOf="@+id/main__profile___volunteer_book__address"
                    app:layout_constraintEnd_toStartOf="@+id/main__profile___volunteer_book__rating"
                    app:layout_constraintStart_toStartOf="@+id/main__profile___volunteer_book__name"
                    app:layout_constraintTop_toTopOf="@+id/main__profile___volunteer_book__address"
                    app:tint="@color/application___color__tertiary"
                    tools:ignore="ContentDescription"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__profile___volunteer_book__rating"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/application___dimen__x1"
                    android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"
                    app:layout_constraintBottom_toBottomOf="@+id/main__profile___volunteer_book__address"
                    app:layout_constraintEnd_toStartOf="@+id/main__profile___volunteer_book__address"
                    app:layout_constraintStart_toEndOf="@+id/main__profile___volunteer_book__rating__icon"
                    app:layout_constraintTop_toTopOf="@+id/main__profile___volunteer_book__address"
                    tools:text="4.9"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__profile___volunteer_book__address"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textAppearance="@style/Application.Design.Caption.TextAppearance.Black"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/main__profile___volunteer_book__rating"
                    app:layout_constraintTop_toBottomOf="@+id/main__profile___volunteer_book__name"
                    tools:text="г. Москва"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/application___dimen__x1"
                        android:layout_weight="1"
                        android:background="@drawable/application___background_r14"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingVertical="@dimen/application___dimen__x3">

                        <com.google.android.material.textview.MaterialTextView
                            android:id="@+id/eventsCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                            android:textSize="17sp"
                            tools:text="1 034"/>

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/application___dimen__x1"
                            android:layout_marginTop="6dp"
                            android:gravity="center"
                            android:text="@string/main__profile___events"
                            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                            android:textSize="13sp"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/application___dimen__x1"
                        android:layout_weight="1"
                        android:background="@drawable/application___background_r14"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingVertical="@dimen/application___dimen__x3">

                        <com.google.android.material.textview.MaterialTextView
                            android:id="@+id/hoursCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                            android:textSize="17sp"
                            tools:text="10 658"/>

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/application___dimen__x1"
                            android:layout_marginTop="6dp"
                            android:gravity="center"
                            android:text="@string/main__profile___volunteer_book__completed_hours_variant"
                            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                            android:textSize="13sp"/>
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/verified_hours_container"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/application___background_r14"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingVertical="@dimen/application___dimen__x3">

                        <com.google.android.material.textview.MaterialTextView
                            android:id="@+id/verifiedHoursCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                            android:textSize="17sp"
                            tools:text="1 034"/>

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/application___dimen__x1"
                            android:layout_marginTop="6dp"
                            android:gravity="center"
                            android:text="@string/main__profile___volunteer_book__verified_hours_variant"
                            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                            android:textSize="13sp"/>
                    </LinearLayout>

                    <View
                        android:id="@+id/bloodCountDevider"
                        android:layout_width="@dimen/application___dimen__x2"
                        android:layout_height="@dimen/application___dimen__x1"
                        android:visibility="gone"/>

                    <LinearLayout
                        android:id="@+id/donationContainer"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/application___background_r14"
                        android:backgroundTint="#FDEBEA"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingVertical="@dimen/application___dimen__x3"
                        android:visibility="gone">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center">

                            <com.google.android.material.textview.MaterialTextView
                                android:id="@+id/donationsCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                                android:textSize="17sp"
                                tools:text="134"/>

                            <ImageView
                                android:layout_width="@dimen/application___dimen__x5"
                                android:layout_height="@dimen/application___dimen__x5"
                                android:layout_marginStart="2dp"
                                android:src="@drawable/ic_blood_donation"/>
                        </LinearLayout>

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="6dp"
                            android:text="@string/main__profile___volunteer_book__donations"
                            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                            android:textSize="13sp"/>
                    </LinearLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/honoraryDonationContainer"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:animateLayoutChanges="true"
                        android:background="@drawable/application___background_r14"
                        android:backgroundTint="@color/application___color__honorary_donor"
                        android:padding="@dimen/application___dimen__x3"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <ImageView
                            android:id="@+id/honorary_donor_icon"
                            android:layout_width="@dimen/application___dimen__x10"
                            android:layout_height="@dimen/application___dimen__x15"
                            android:src="@drawable/ic_honorary_donor"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"/>

                        <com.google.android.material.textview.MaterialTextView
                            android:id="@+id/honorary_donor_title"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/application___dimen__x1"
                            android:text="@string/main__profile___volunteer_book__honorary_donor"
                            android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                            android:textSize="13sp"
                            app:layout_constraintEnd_toStartOf="@+id/honorary_donor_icon"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"/>

                        <com.google.android.material.textview.MaterialTextView
                            android:id="@+id/honoraryDonationsCount"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="6dp"
                            android:layout_marginEnd="2dp"
                            android:autoSizeMaxTextSize="13sp"
                            android:autoSizeMinTextSize="9sp"
                            android:autoSizeStepGranularity="1sp"
                            android:autoSizeTextType="uniform"
                            android:gravity="start"
                            android:maxLines="1"
                            android:textAppearance="@style/Application.Design.Body1.TextAppearance.Black"
                            android:textSize="13sp"
                            app:layout_constraintEnd_toStartOf="@+id/honorary_donor_icon"
                            app:layout_constraintHorizontal_bias="0"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/honorary_donor_title"
                            tools:text="35 донаций"/>
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>
            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/main__profile___volunteer_book__banner_content"
                android:layout_width="match_parent"
                android:layout_height="@dimen/application___dimen__x26"
                android:layout_margin="@dimen/application___dimen__x4"
                android:background="@drawable/banner_volunteer_book"
                tools:visibility="gone">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__profile___volunteer_book__banner_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x4"
                    android:layout_marginEnd="101dp"
                    android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"
                    app:layout_constraintBottom_toTopOf="@id/main__profile___volunteer_book__banner_extra"
                    app:layout_constraintEnd_toStartOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="@string/volunteer_book_enough_hours"
                    tools:textColor="@color/white"/>

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/main__profile___volunteer_book__banner_extra"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/application___dimen__x4"
                    android:layout_marginTop="@dimen/application___dimen__x2"
                    android:layout_marginBottom="@dimen/application___dimen__x4"
                    android:textAppearance="@style/Application.Design.H7.TextAppearance.Black"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/main__profile___volunteer_book__banner_title"
                    tools:text="@string/volunteer_book_extra"
                    tools:textColor="@color/application___color__violet"/>


                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/main__profile___volunteer_book__image"
                    android:layout_width="149dp"
                    android:layout_height="104dp"
                    android:src="@drawable/volunteer_book_banner_image"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1.0"
                    app:layout_constraintStart_toEndOf="@id/main__profile___volunteer_book__banner_title"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/main__profile___volunteer_book__download"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginTop="@dimen/application___dimen__x2"
                android:layout_marginStart="@dimen/application___dimen__x4"
                android:background="@drawable/application___background__white"
                android:backgroundTint="@color/application___color__primary"
                android:paddingHorizontal="@dimen/application___dimen__x2"
                android:paddingVertical="@dimen/application___dimen__x1"
                android:text="@string/main__profile___volunteer_book__download"
                android:textAppearance="@style/Application.Design.Body1.TextAppearance.White"/>

            <com.google.android.material.textview.MaterialTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/application___dimen__x4"
                android:layout_marginTop="@dimen/application___dimen__x4"
                android:text="@string/main__profile___volunteer_book__works"
                android:textAppearance="@style/Application.Design.H5.TextAppearance.Black"
                android:textSize="22sp"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/main__profile___volunteer_book__user_activity__list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:orientation="vertical"
                android:paddingBottom="@dimen/application___dimen__x3"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</FrameLayout>