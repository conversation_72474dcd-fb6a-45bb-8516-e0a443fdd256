<?xml version="1.0" encoding="utf-8"?>
<menu
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <item
        android:id="@+id/main__profile_knowledge_base___article_layout"
        app:actionLayout="@layout/main__profile_knowledge_base___article_layout"
        android:title=""
        app:showAsAction="ifRoom"
        tools:iconTint="@color/application___color__primary"/>
</menu>