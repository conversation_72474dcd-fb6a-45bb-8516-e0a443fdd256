package common.library.serialization.gson.adapter

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import common.library.core.lazyGet
import java.lang.reflect.Type
import java.time.LocalTime
import java.time.format.DateTimeFormatter

class LocalTimeAdapter(
    private val _formatter: DateTimeFormatter
) : JsonAdapter<LocalTime> {
    companion object {
        val iso8601 by lazyGet { LocalTimeAdapter(DateTimeFormatter.ISO_LOCAL_TIME) }
    }

    override fun serialize(
        src: LocalTime,
        typeOfSrc: Type,
        context: JsonSerializationContext
    ): JsonElement {
        return JsonPrimitive(src.format(_formatter))
    }

    override fun deserialize(
        json: JsonElement,
        typeOfT: Type,
        context: JsonDeserializationContext
    ): LocalTime {
        return LocalTime.parse(json.asString, _formatter)
    }
}
